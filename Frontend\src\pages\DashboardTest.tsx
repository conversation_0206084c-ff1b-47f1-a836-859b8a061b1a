import React, { useState } from 'react';
import AdminDashboard from './admin/Dashboard';
import StudentDashboardContent from '../components/dashboard/StudentDashboardContent';
import TeacherDashboardContent from '../components/dashboard/TeacherDashboardContent';
import ParentDashboardContent from '../components/dashboard/ParentDashboardContent';

const DashboardTest: React.FC = () => {
  const [selectedDashboard, setSelectedDashboard] = useState<'admin' | 'student' | 'teacher' | 'parent'>('admin');

  const dashboards = [
    { key: 'admin', label: 'Admin Dashboard', component: AdminDashboard },
    { key: 'student', label: 'Student Dashboard', component: StudentDashboardContent },
    { key: 'teacher', label: 'Teacher Dashboard', component: TeacherDashboardContent },
    { key: 'parent', label: 'Parent Dashboard', component: ParentDashboardContent },
  ];

  const SelectedComponent = dashboards.find(d => d.key === selectedDashboard)?.component || AdminDashboard;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Dashboard Selector */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
              Dashboard Test - All Academic Data
            </h1>
            <div className="flex space-x-2">
              {dashboards.map((dashboard) => (
                <button
                  key={dashboard.key}
                  onClick={() => setSelectedDashboard(dashboard.key as any)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                    selectedDashboard === dashboard.key
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  {dashboard.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Dashboard Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            {dashboards.find(d => d.key === selectedDashboard)?.label}
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Testing dashboard with real academic data: 28 classes, 17 subjects, 7 departments, 4 streams
          </p>
        </div>
        
        <SelectedComponent />
      </div>
    </div>
  );
};

export default DashboardTest;
