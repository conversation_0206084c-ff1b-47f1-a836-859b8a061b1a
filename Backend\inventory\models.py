from django.db import models
from django.utils import timezone
from schools.models import SchoolBranch
from core.models import CustomUser

class AssetCategory(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='asset_categories')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = 'Asset Categories'
        ordering = ['name']
        unique_together = ['name', 'school_branch']

class Asset(models.Model):
    ASSET_STATUS_CHOICES = [
        ('AVAILABLE', 'Available'),
        ('IN_USE', 'In Use'),
        ('UNDER_MAINTENANCE', 'Under Maintenance'),
        ('DAMAGED', 'Damaged'),
        ('DISPOSED', 'Disposed'),
        ('LOST', 'Lost'),
    ]

    name = models.CharField(max_length=255)
    asset_number = models.CharField(max_length=50, unique=True)
    category = models.ForeignKey(AssetCategory, on_delete=models.CASCADE, related_name='assets')
    description = models.TextField(blank=True, null=True)
    purchase_date = models.DateField()
    purchase_price = models.DecimalField(max_digits=10, decimal_places=2)
    supplier = models.ForeignKey('Supplier', on_delete=models.SET_NULL, null=True, related_name='supplied_assets')
    warranty_expiry = models.DateField(blank=True, null=True)
    location = models.CharField(max_length=100, help_text="Physical location of the asset")
    assigned_to = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_assets')
    status = models.CharField(max_length=20, choices=ASSET_STATUS_CHOICES, default='AVAILABLE')
    condition = models.CharField(max_length=100, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    image = models.ImageField(upload_to='asset_images/', blank=True, null=True)
    documents = models.FileField(upload_to='asset_documents/', blank=True, null=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='assets')
    added_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='added_assets')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.asset_number})"

    class Meta:
        ordering = ['name', 'asset_number']
        indexes = [
            models.Index(fields=['asset_number']),
            models.Index(fields=['status']),
            models.Index(fields=['purchase_date']),
        ]

class SupplyCategory(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='supply_categories')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = 'Supply Categories'
        ordering = ['name']
        unique_together = ['name', 'school_branch']

class Supply(models.Model):
    name = models.CharField(max_length=255)
    category = models.ForeignKey(SupplyCategory, on_delete=models.CASCADE, related_name='supplies')
    description = models.TextField(blank=True, null=True)
    unit = models.CharField(max_length=50, help_text="Unit of measurement (e.g., box, piece, kg)")
    quantity = models.PositiveIntegerField(default=0)
    minimum_quantity = models.PositiveIntegerField(default=1, help_text="Minimum quantity before reordering")
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    supplier = models.ForeignKey('Supplier', on_delete=models.SET_NULL, null=True, related_name='supplied_items')
    location = models.CharField(max_length=100, help_text="Storage location")
    expiry_date = models.DateField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    image = models.ImageField(upload_to='supply_images/', blank=True, null=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='supplies')
    added_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='added_supplies')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.quantity} {self.unit})"

    @property
    def total_value(self):
        return self.quantity * self.unit_price

    @property
    def needs_reordering(self):
        return self.quantity <= self.minimum_quantity

    class Meta:
        verbose_name_plural = 'Supplies'
        ordering = ['name']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['category']),
            models.Index(fields=['expiry_date']),
        ]

class SupplyTransaction(models.Model):
    TRANSACTION_TYPE_CHOICES = [
        ('PURCHASE', 'Purchase'),
        ('USAGE', 'Usage'),
        ('ADJUSTMENT', 'Adjustment'),
        ('RETURN', 'Return to Supplier'),
        ('DISPOSAL', 'Disposal'),
    ]

    supply = models.ForeignKey(Supply, on_delete=models.CASCADE, related_name='transactions')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPE_CHOICES)
    quantity = models.IntegerField(help_text="Positive for additions, negative for removals")
    transaction_date = models.DateField(default=timezone.now)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    supplier = models.ForeignKey('Supplier', on_delete=models.SET_NULL, null=True, blank=True, related_name='supply_transactions')
    purchase_order = models.ForeignKey('PurchaseOrder', on_delete=models.SET_NULL, null=True, blank=True, related_name='supply_transactions')
    notes = models.TextField(blank=True, null=True)
    performed_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='supply_transactions')
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='supply_transactions')
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.get_transaction_type_display()} of {self.supply.name}: {self.quantity} {self.supply.unit}"

    def save(self, *args, **kwargs):
        # Update supply quantity
        if not self.pk:  # New transaction
            self.supply.quantity += self.quantity
            self.supply.save()

        super().save(*args, **kwargs)

    class Meta:
        ordering = ['-transaction_date', '-created_at']
        indexes = [
            models.Index(fields=['transaction_date']),
            models.Index(fields=['transaction_type']),
        ]

class Maintenance(models.Model):
    MAINTENANCE_STATUS_CHOICES = [
        ('SCHEDULED', 'Scheduled'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
    ]

    MAINTENANCE_TYPE_CHOICES = [
        ('PREVENTIVE', 'Preventive'),
        ('CORRECTIVE', 'Corrective'),
        ('EMERGENCY', 'Emergency'),
    ]

    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, related_name='maintenance_records')
    maintenance_type = models.CharField(max_length=20, choices=MAINTENANCE_TYPE_CHOICES)
    description = models.TextField()
    status = models.CharField(max_length=20, choices=MAINTENANCE_STATUS_CHOICES, default='SCHEDULED')
    scheduled_date = models.DateField()
    start_date = models.DateField(blank=True, null=True)
    completion_date = models.DateField(blank=True, null=True)
    cost = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    performed_by = models.CharField(max_length=255, blank=True, null=True, help_text="Person or company who performed the maintenance")
    vendor = models.ForeignKey('Supplier', on_delete=models.SET_NULL, null=True, blank=True, related_name='maintenance_jobs')
    notes = models.TextField(blank=True, null=True)
    documents = models.FileField(upload_to='maintenance_documents/', blank=True, null=True)
    created_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='created_maintenance_records')
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='maintenance_records')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.get_maintenance_type_display()} maintenance for {self.asset.name}"

    def save(self, *args, **kwargs):
        # Update asset status when maintenance status changes
        if self.status == 'IN_PROGRESS':
            self.asset.status = 'UNDER_MAINTENANCE'
            self.asset.save()
        elif self.status == 'COMPLETED' and self.asset.status == 'UNDER_MAINTENANCE':
            self.asset.status = 'AVAILABLE'
            self.asset.save()

        super().save(*args, **kwargs)

    class Meta:
        ordering = ['-scheduled_date', 'status']
        indexes = [
            models.Index(fields=['scheduled_date']),
            models.Index(fields=['status']),
            models.Index(fields=['maintenance_type']),
        ]

class Supplier(models.Model):
    name = models.CharField(max_length=255)
    contact_person = models.CharField(max_length=255, blank=True, null=True)
    phone = models.CharField(max_length=20)
    email = models.EmailField()
    address = models.TextField()
    website = models.URLField(blank=True, null=True)
    tax_id = models.CharField(max_length=50, blank=True, null=True)
    payment_terms = models.CharField(max_length=100, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='suppliers')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['is_active']),
        ]

class PurchaseOrder(models.Model):
    PO_STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('PENDING_APPROVAL', 'Pending Approval'),
        ('APPROVED', 'Approved'),
        ('ORDERED', 'Ordered'),
        ('PARTIALLY_RECEIVED', 'Partially Received'),
        ('RECEIVED', 'Received'),
        ('CANCELLED', 'Cancelled'),
    ]

    po_number = models.CharField(max_length=50, unique=True)
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, related_name='purchase_orders')
    order_date = models.DateField()
    expected_delivery_date = models.DateField(blank=True, null=True)
    delivery_date = models.DateField(blank=True, null=True)
    status = models.CharField(max_length=20, choices=PO_STATUS_CHOICES, default='DRAFT')
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    shipping_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    notes = models.TextField(blank=True, null=True)
    payment_status = models.CharField(max_length=20, default='UNPAID')
    payment_date = models.DateField(blank=True, null=True)
    payment_method = models.CharField(max_length=50, blank=True, null=True)
    documents = models.FileField(upload_to='purchase_order_documents/', blank=True, null=True)
    created_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='created_purchase_orders')
    approved_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_purchase_orders')
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='purchase_orders')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"PO #{self.po_number} - {self.supplier.name}"

    @property
    def grand_total(self):
        return self.total_amount + self.shipping_cost + self.tax_amount - self.discount_amount

    class Meta:
        ordering = ['-order_date', 'status']
        indexes = [
            models.Index(fields=['po_number']),
            models.Index(fields=['order_date']),
            models.Index(fields=['status']),
            models.Index(fields=['payment_status']),
        ]

class PurchaseOrderItem(models.Model):
    purchase_order = models.ForeignKey(PurchaseOrder, on_delete=models.CASCADE, related_name='items')
    item_name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    quantity = models.PositiveIntegerField()
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    received_quantity = models.PositiveIntegerField(default=0)
    supply = models.ForeignKey(Supply, on_delete=models.SET_NULL, null=True, blank=True, related_name='purchase_order_items')
    asset_category = models.ForeignKey(AssetCategory, on_delete=models.SET_NULL, null=True, blank=True, related_name='purchase_order_items')
    notes = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"{self.item_name} ({self.quantity} x {self.unit_price})"

    @property
    def total_price(self):
        return self.quantity * self.unit_price

    @property
    def is_fully_received(self):
        return self.received_quantity >= self.quantity

    class Meta:
        ordering = ['id']

class InventoryReport(models.Model):
    REPORT_TYPE_CHOICES = [
        ('ASSET', 'Asset Report'),
        ('SUPPLY', 'Supply Report'),
        ('MAINTENANCE', 'Maintenance Report'),
        ('PURCHASE', 'Purchase Report'),
        ('VALUATION', 'Inventory Valuation'),
    ]

    title = models.CharField(max_length=255)
    report_type = models.CharField(max_length=20, choices=REPORT_TYPE_CHOICES)
    start_date = models.DateField()
    end_date = models.DateField()
    generated_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='generated_inventory_reports')
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='inventory_reports')
    report_data = models.JSONField(blank=True, null=True)
    file = models.FileField(upload_to='inventory_reports/', blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title} ({self.start_date} to {self.end_date})"

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['report_type']),
            models.Index(fields=['start_date']),
            models.Index(fields=['end_date']),
        ]