from rest_framework import serializers
from .models import (
    CalendarEvent, Stream, ClassRoom, Subject, SubjectMaterial,
    Exam, ExamResult, Assignment, AssignmentSubmission,
    Notice, Announcement, Term, TimeSlot, Timetable,
    StreamPerformance, SubjectPerformance, TopStudent,
    StreamGradingSystem, PerformanceAnalytics, TeacherPerformanceMetrics,
    AcademicTarget, AcademicIntervention, InterventionSession, ImprovementPlan, ImprovementStrategy,
    ClassProgressionRule, AcademicYear, Department, GradingSystem, GradeScale
)
from .curriculum_models import EducationLevel
from users.serializers import TeacherSerializer, StaffSerializer
from .curriculum_serializers import EducationLevelSerializer

class DepartmentSerializer(serializers.ModelSerializer):
    head_of_department_details = TeacherSerializer(source='head_of_department', read_only=True)
    school_name = serializers.CharField(source='school.name', read_only=True)
    school_branch_name = serializers.CharField(source='school_branch.name', read_only=True)

    class Meta:
        model = Department
        fields = '__all__'

    def validate(self, data):
        # Print the data for debugging
        print(f"Validating department data: {data}")

        # Check if a department with the same code already exists in the same school and branch
        code = data.get('code')
        school = data.get('school')
        school_branch = data.get('school_branch')

        if code and school and school_branch:
            # Check if we're updating an existing department
            instance = self.instance

            # Query for existing departments with the same code, school, and branch
            existing_departments = Department.objects.filter(
                code=code,
                school=school,
                school_branch=school_branch
            )

            # If we're updating, exclude the current instance
            if instance:
                existing_departments = existing_departments.exclude(pk=instance.pk)

            if existing_departments.exists():
                raise serializers.ValidationError({
                    'code': 'A department with this code already exists in this school branch.'
                })

        return data

class StreamSerializer(serializers.ModelSerializer):
    class Meta:
        model = Stream
        fields = '__all__'

class ClassRoomSerializer(serializers.ModelSerializer):
    education_level_details = EducationLevelSerializer(source='education_level', read_only=True)
    next_class_options = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ClassRoom
        fields = '__all__'

    def get_next_class_options(self, obj):
        """Get possible next classes based on curriculum progression rules"""
        if not obj.education_level:
            return []

        options = obj.get_next_class_options()
        return [{
            'education_level_id': option['education_level'].id,
            'education_level_name': option['education_level'].name,
            'education_level_code': option['education_level'].code,
            'is_default': option['is_default'],
            'requirements': option['requirements']
        } for option in options]

class SubjectSerializer(serializers.ModelSerializer):
    teachers = TeacherSerializer(many=True, read_only=True)
    department_details = DepartmentSerializer(source='department', read_only=True)
    class_name_details = ClassRoomSerializer(source='class_name', read_only=True)
    stream_details = StreamSerializer(source='stream', read_only=True)

    class Meta:
        model = Subject
        fields = '__all__'
        ref_name = 'AcademicsSubject'

    def create(self, validated_data):
        # Extract students data, defaulting to an empty list
        students_data = validated_data.pop('students', [])

        # Create the subject with the remaining data
        subject = Subject.objects.create(**validated_data)

        # Set the students (works with empty list too)
        subject.students.set(students_data)

        return subject

    def update(self, instance, validated_data):
        # Extract students data, defaulting to None (to distinguish from empty list)
        students_data = validated_data.pop('students', None)

        # Update the subject fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update students if provided in the request
        # This allows for partial updates where students field might not be included
        if students_data is not None:
            instance.students.set(students_data)  # Works with empty list too

        return instance

class SubjectMaterialSerializer(serializers.ModelSerializer):
    uploaded_by = TeacherSerializer(read_only=True)

    class Meta:
        model = SubjectMaterial
        fields = '__all__'

class ExamSerializer(serializers.ModelSerializer):
    class Meta:
        model = Exam
        fields = '__all__'

class ExamResultSerializer(serializers.ModelSerializer):
    class Meta:
        model = ExamResult
        fields = '__all__'

class AssignmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Assignment
        fields = '__all__'

class AssignmentSubmissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = AssignmentSubmission
        fields = '__all__'

class NoticeSerializer(serializers.ModelSerializer):
    published_by = TeacherSerializer(read_only=True)

    class Meta:
        model = Notice
        fields = '__all__'

class AnnouncementSerializer(serializers.ModelSerializer):
    published_by = StaffSerializer(read_only=True)

    class Meta:
        model = Announcement
        fields = '__all__'

class TermSerializer(serializers.ModelSerializer):
    academic_year_name = serializers.CharField(source='academic_year.year', read_only=True)
    school_name_display = serializers.CharField(source='school.name', read_only=True)
    school_branch_name = serializers.CharField(source='school_branch.name', read_only=True, allow_null=True)

    class Meta:
        model = Term
        fields = ['id', 'name', 'academic_year', 'academic_year_name', 'school', 'school_name_display',
                 'school_branch', 'school_branch_name', 'start_date', 'end_date', 'is_current', 'is_archived']

    def validate(self, data):
        # Validate that term dates are within academic year dates
        academic_year = data.get('academic_year')
        start_date = data.get('start_date')
        end_date = data.get('end_date')

        if academic_year and start_date and end_date:
            # Validate term dates are within academic year dates
            if start_date < academic_year.start_date:
                raise serializers.ValidationError({
                    'start_date': 'Term start date cannot be before academic year start date'
                })
            if end_date > academic_year.end_date:
                raise serializers.ValidationError({
                    'end_date': 'Term end date cannot be after academic year end date'
                })
            if start_date >= end_date:
                raise serializers.ValidationError({
                    'start_date': 'Term start date must be before end date',
                    'end_date': 'Term end date must be after start date'
                })

            # Check for overlapping terms in the same academic year
            overlapping_terms = Term.objects.filter(
                academic_year=academic_year,
                start_date__lte=end_date,
                end_date__gte=start_date
            )

            # Exclude current instance if updating
            if self.instance:
                overlapping_terms = overlapping_terms.exclude(pk=self.instance.pk)

            if overlapping_terms.exists():
                raise serializers.ValidationError({
                    'start_date': 'This term overlaps with another term in the same academic year',
                    'end_date': 'This term overlaps with another term in the same academic year'
                })

        return data

class TimeSlotSerializer(serializers.ModelSerializer):
    class Meta:
        model = TimeSlot
        fields = '__all__'

class TimetableSerializer(serializers.ModelSerializer):
    time_slot_display = serializers.StringRelatedField(source='time_slot', read_only=True)
    subject_name = serializers.StringRelatedField(source='subject', read_only=True)
    teacher_name = serializers.SerializerMethodField()

    class Meta:
        model = Timetable
        fields = '__all__'

    def get_teacher_name(self, obj):
        return f"{obj.teacher.first_name} {obj.teacher.last_name}"

class StreamPerformanceSerializer(serializers.ModelSerializer):
    stream_name = serializers.CharField(source='stream.name', read_only=True)
    term_name = serializers.CharField(source='term.name', read_only=True)

    class Meta:
        model = StreamPerformance
        fields = '__all__'
        read_only_fields = ('position', 'previous_position', 'improvement')

class SubjectPerformanceSerializer(serializers.ModelSerializer):
    subject_name = serializers.CharField(source='subject.name', read_only=True)
    teacher_name = serializers.CharField(source='teacher.get_full_name', read_only=True)

    class Meta:
        model = SubjectPerformance
        fields = '__all__'
        read_only_fields = ('average_score', 'highest_score', 'lowest_score',
                          'number_of_students', 'number_of_passes', 'pass_rate')

class TopStudentSerializer(serializers.ModelSerializer):
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)
    stream_name = serializers.CharField(source='stream.name', read_only=True)
    class_name = serializers.CharField(source='class_name.name', read_only=True)

    class Meta:
        model = TopStudent
        fields = '__all__'
        read_only_fields = ('position_in_class', 'position_in_stream', 'position_in_form')

class StreamGradingSystemSerializer(serializers.ModelSerializer):
    stream_name = serializers.CharField(source='stream.name', read_only=True)
    term_name = serializers.CharField(source='term.name', read_only=True)

    class Meta:
        model = StreamGradingSystem
        fields = '__all__'

class PerformanceAnalyticsSerializer(serializers.ModelSerializer):
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)
    subject_name = serializers.CharField(source='subject.name', read_only=True)

    class Meta:
        model = PerformanceAnalytics
        fields = '__all__'
        read_only_fields = ('value_addition', 'improvement_percentage', 'intervention_needed')

class AcademicTargetSerializer(serializers.ModelSerializer):
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)
    subject_name = serializers.CharField(source='subject.name', read_only=True)
    set_by_name = serializers.CharField(source='set_by.get_full_name', read_only=True)

    class Meta:
        model = AcademicTarget
        fields = '__all__'
        read_only_fields = ('is_achieved',)

class AcademicInterventionSerializer(serializers.ModelSerializer):
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)
    subject_name = serializers.CharField(source='subject.name', read_only=True)
    teacher_name = serializers.CharField(source='assigned_teacher.get_full_name', read_only=True)

    class Meta:
        model = AcademicIntervention
        fields = '__all__'

class InterventionSessionSerializer(serializers.ModelSerializer):
    class Meta:
        model = InterventionSession
        fields = '__all__'

class ImprovementPlanSerializer(serializers.ModelSerializer):
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    strategies = serializers.SerializerMethodField()

    class Meta:
        model = ImprovementPlan
        fields = '__all__'

    def get_strategies(self, obj):
        return ImprovementStrategySerializer(obj.strategies.all(), many=True).data

class ImprovementStrategySerializer(serializers.ModelSerializer):
    subject_name = serializers.CharField(source='subject.name', read_only=True)

    class Meta:
        model = ImprovementStrategy
        fields = '__all__'

class CalendarEventSerializer(serializers.ModelSerializer):
    class Meta:
        model = CalendarEvent
        fields = ['id', 'title', 'start_date', 'end_date', 'all_day',
                 'event_level', 'school_branch', 'created_by']
        read_only_fields = ['created_by']

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)

class DashboardMetricsSerializer(serializers.Serializer):
    students = serializers.DictField()
    male = serializers.DictField()
    female = serializers.DictField()
    classes = serializers.DictField()
    teachers = serializers.DictField()
    subjects = serializers.DictField()
    parents = serializers.DictField()
    departments = serializers.DictField()

class AcademicYearSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source='year', read_only=False)
    terms = serializers.SerializerMethodField()
    school_branch_name = serializers.SerializerMethodField()
    terms_data = serializers.ListField(write_only=True, required=False)

    class Meta:
        model = AcademicYear
        fields = ['id', 'year', 'name', 'start_date', 'end_date', 'school_name', 'school_branch',
                 'school_branch_name', 'is_current', 'is_archived', 'terms', 'terms_data']
        validators = [
            serializers.UniqueTogetherValidator(
                queryset=AcademicYear.objects.all(),
                fields=('year', 'school_branch'),
                message="An academic year with this name already exists for this branch."
            )
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Make school_branch optional if this is a global template
        request = self.context.get('request')
        if request and request.data.get('is_global') == 'true':
            self.fields['school_branch'].required = False

    def validate(self, data):
        # Print the data for debugging
        print(f"Validating academic year data: {data}")

        # Validate start_date is before end_date
        start_date = data.get('start_date')
        end_date = data.get('end_date')

        if start_date and end_date and start_date >= end_date:
            raise serializers.ValidationError({
                'start_date': 'Start date must be before end date',
                'end_date': 'End date must be after start date'
            })

        # Validate no overlapping academic years for the same branch
        school_branch = data.get('school_branch')
        is_global = self.context.get('request') and self.context.get('request').data.get('is_global') == 'true'

        # Skip branch validation for global templates if no branch is specified
        if not is_global and school_branch and start_date and end_date:
            # Check for overlapping academic years
            overlapping_years = AcademicYear.objects.filter(
                school_branch=school_branch,
                start_date__lte=end_date,
                end_date__gte=start_date
            )

            # Exclude current instance if updating
            if self.instance:
                overlapping_years = overlapping_years.exclude(pk=self.instance.pk)

            if overlapping_years.exists():
                raise serializers.ValidationError({
                    'start_date': 'This date range overlaps with an existing academic year for this branch',
                    'end_date': 'This date range overlaps with an existing academic year for this branch'
                })

        return data

    def get_terms(self, obj):
        terms = obj.terms.all().order_by('start_date')
        return TermSerializer(terms, many=True).data

    def get_school_branch_name(self, obj):
        if obj.school_branch:
            return obj.school_branch.name
        return None

    def create(self, validated_data):
        # Print the validated data for debugging
        print(f"Creating academic year with validated data: {validated_data}")

        # Extract terms data if present
        terms_data = validated_data.pop('terms_data', [])
        print(f"Terms data: {terms_data}")

        # If this is set as current, unset any other current academic years for the same branch
        if validated_data.get('is_current', False):
            filters = {}
            if validated_data.get('school_branch'):
                filters['school_branch'] = validated_data['school_branch']
            else:
                filters['school_name'] = validated_data['school_name']

            AcademicYear.objects.filter(is_current=True, **filters).update(is_current=False)

        # Create the academic year
        try:
            academic_year = super().create(validated_data)
            print(f"Created academic year: {academic_year.id}")
        except Exception as e:
            print(f"Error creating academic year: {str(e)}")
            raise

        # Create terms if provided
        if terms_data:
            from django.db import transaction
            with transaction.atomic():
                for term_data in terms_data:
                    print(f"Processing term data: {term_data}")

                    # Skip terms with temporary IDs
                    if 'id' in term_data and str(term_data['id']).startswith('new-'):
                        term_data.pop('id')

                    # Set academic year reference
                    term_data['academic_year'] = academic_year.id

                    # Set school and branch if not provided
                    if 'school' not in term_data or not term_data['school']:
                        term_data['school'] = academic_year.school_name.id if academic_year.school_name else None
                    if 'school_branch' not in term_data or not term_data['school_branch']:
                        term_data['school_branch'] = academic_year.school_branch.id if academic_year.school_branch else None

                    # Create the term
                    term_serializer = TermSerializer(data=term_data)
                    if term_serializer.is_valid():
                        term = term_serializer.save()
                        print(f"Created term: {term.id}")
                    else:
                        # Log validation errors but continue with other terms
                        print(f"Term validation error: {term_serializer.errors}")

        return academic_year

    def update(self, instance, validated_data):
        # Extract terms data if present
        terms_data = validated_data.pop('terms_data', [])

        # If this is set as current, unset any other current academic years for the same branch
        if validated_data.get('is_current', False) and not instance.is_current:
            filters = {}
            if instance.school_branch:
                filters['school_branch'] = instance.school_branch
            else:
                filters['school_name'] = instance.school_name

            AcademicYear.objects.filter(is_current=True, **filters).exclude(pk=instance.pk).update(is_current=False)

        # Update the academic year
        academic_year = super().update(instance, validated_data)

        # Update terms if provided
        if terms_data:
            from django.db import transaction
            with transaction.atomic():
                for term_data in terms_data:
                    term_id = None
                    # Skip terms with temporary IDs
                    if 'id' in term_data and not str(term_data['id']).startswith('new-'):
                        term_id = term_data['id']

                    # Set academic year reference
                    term_data['academic_year'] = academic_year.id

                    # Set school and branch if not provided
                    if 'school' not in term_data or not term_data['school']:
                        term_data['school'] = academic_year.school_name.id if academic_year.school_name else None
                    if 'school_branch' not in term_data or not term_data['school_branch']:
                        term_data['school_branch'] = academic_year.school_branch.id if academic_year.school_branch else None

                    if term_id:
                        # Update existing term
                        try:
                            term = Term.objects.get(id=term_id)
                            term_serializer = TermSerializer(term, data=term_data, partial=True)
                            if term_serializer.is_valid():
                                term_serializer.save()
                            else:
                                # Log validation errors but continue with other terms
                                print(f"Term update validation error: {term_serializer.errors}")
                        except Term.DoesNotExist:
                            # Term doesn't exist, create it
                            term_serializer = TermSerializer(data=term_data)
                            if term_serializer.is_valid():
                                term_serializer.save()
                            else:
                                # Log validation errors but continue with other terms
                                print(f"Term creation validation error: {term_serializer.errors}")
                    else:
                        # Create new term
                        term_serializer = TermSerializer(data=term_data)
                        if term_serializer.is_valid():
                            term_serializer.save()
                        else:
                            # Log validation errors but continue with other terms
                            print(f"Term creation validation error: {term_serializer.errors}")

        return academic_year

class ClassProgressionRuleSerializer(serializers.ModelSerializer):
    current_level_details = EducationLevelSerializer(source='current_level', read_only=True)
    next_level_details = EducationLevelSerializer(source='next_level', read_only=True)

    class Meta:
        model = ClassProgressionRule
        fields = ['id', 'current_level', 'next_level', 'is_default_progression', 'requirements',
                 'school', 'created_at', 'updated_at', 'current_level_details', 'next_level_details']
        read_only_fields = ['created_at', 'updated_at']

class GradeScaleSerializer(serializers.ModelSerializer):
    class Meta:
        model = GradeScale
        fields = '__all__'

class GradeSerializer(serializers.ModelSerializer):
    """Serializer for education levels as grades"""
    class Meta:
        model = EducationLevel
        fields = ['id', 'name', 'code', 'sequence']

    def to_representation(self, instance):
        data = super().to_representation(instance)
        # Map the fields to match the frontend Grade interface
        return {
            'id': data['id'],
            'name': data['name'],
            'level': data['sequence']
        }

class GradingSystemSerializer(serializers.ModelSerializer):
    grades = GradeScaleSerializer(many=True, read_only=True)

    class Meta:
        model = GradingSystem
        fields = '__all__'


# Syllabus serializers have been moved to the syllabus app
