"""
Management command to migrate existing settings to enhanced models.
"""
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from settings_app.models import SchoolProfile, SystemConfiguration
from settings_app.enhanced_models import AdvancedSchoolProfile, SystemSettings
import json


class Command(BaseCommand):
    help = 'Migrate existing settings to enhanced models'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be migrated without making changes',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force migration even if enhanced models exist',
        )

    def handle(self, *args, **options):
        dry_run = options.get('dry_run', False)
        force = options.get('force', False)

        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))

        # Migrate SchoolProfile to AdvancedSchoolProfile
        profiles_migrated = self.migrate_school_profiles(dry_run, force)
        
        # Migrate SystemConfiguration to SystemSettings
        settings_migrated = self.migrate_system_settings(dry_run, force)

        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Migration preview completed:\n'
                    f'  - {profiles_migrated} school profiles would be migrated\n'
                    f'  - {settings_migrated} system configurations would be migrated'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Migration completed:\n'
                    f'  - {profiles_migrated} school profiles migrated\n'
                    f'  - {settings_migrated} system configurations migrated'
                )
            )

    def migrate_school_profiles(self, dry_run, force):
        """Migrate SchoolProfile to AdvancedSchoolProfile"""
        profiles = SchoolProfile.objects.all()
        migrated_count = 0

        self.stdout.write(f'Migrating {profiles.count()} school profiles...')

        for profile in profiles:
            try:
                # Check if enhanced profile already exists
                if AdvancedSchoolProfile.objects.filter(school_branch=profile.school_branch).exists():
                    if not force:
                        self.stdout.write(
                            self.style.WARNING(
                                f'  - Skipping {profile.school_branch.name} (enhanced profile exists)'
                            )
                        )
                        continue
                    elif not dry_run:
                        # Delete existing enhanced profile if force is True
                        AdvancedSchoolProfile.objects.filter(school_branch=profile.school_branch).delete()

                if not dry_run:
                    with transaction.atomic():
                        # Create enhanced profile with migrated data
                        enhanced_profile = AdvancedSchoolProfile.objects.create(
                            school_branch=profile.school_branch,
                            motto=profile.motto or '',
                            vision=profile.vision or '',
                            mission=profile.mission or '',
                            phone=profile.phone or '',
                            email=profile.email or '',
                            website=profile.website or '',
                            address_line_1=profile.address or '',
                            timezone=profile.timezone or 'UTC',
                            language=profile.language or 'en',
                            currency=profile.currency or 'USD',
                            academic_year_start_month=profile.academic_year_start_month or 1,
                            academic_year_end_month=profile.academic_year_end_month or 12,
                            updated_by=profile.updated_by,
                            # Set default values for new fields
                            school_type='private',
                            primary_color='#1f2937',
                            secondary_color='#3b82f6',
                            accent_color='#10b981',
                            terms_per_year=3,
                            enabled_features={
                                'online_payments': True,
                                'sms_notifications': True,
                                'email_notifications': True,
                                'parent_portal': True,
                                'student_portal': True,
                            },
                            is_active=True,
                        )

                        # Copy logo if exists
                        if profile.logo:
                            enhanced_profile.logo = profile.logo
                            enhanced_profile.save()

                migrated_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'  ✓ Migrated profile for {profile.school_branch.name}')
                )

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'  ✗ Failed to migrate profile for {profile.school_branch.name}: {str(e)}'
                    )
                )

        return migrated_count

    def migrate_system_settings(self, dry_run, force):
        """Migrate SystemConfiguration to SystemSettings"""
        configurations = SystemConfiguration.objects.all()
        migrated_count = 0

        self.stdout.write(f'Migrating {configurations.count()} system configurations...')

        for config in configurations:
            try:
                # Check if enhanced settings already exist
                if SystemSettings.objects.filter(school_branch=config.school_branch).exists():
                    if not force:
                        self.stdout.write(
                            self.style.WARNING(
                                f'  - Skipping {config.school_branch.name} (enhanced settings exist)'
                            )
                        )
                        continue
                    elif not dry_run:
                        # Delete existing enhanced settings if force is True
                        SystemSettings.objects.filter(school_branch=config.school_branch).delete()

                if not dry_run:
                    with transaction.atomic():
                        # Create enhanced settings with migrated data
                        enhanced_settings = SystemSettings.objects.create(
                            school_branch=config.school_branch,
                            maintenance_mode=getattr(config, 'maintenance_mode', False),
                            email_notifications_enabled=getattr(config, 'email_notifications', True),
                            sms_notifications_enabled=getattr(config, 'sms_notifications', False),
                            auto_backup_enabled=getattr(config, 'backup_enabled', True),
                            max_file_size=getattr(config, 'max_file_size', 10485760),
                            session_timeout=getattr(config, 'session_timeout', 3600),
                            updated_by=config.updated_by,
                            # Set default values for new fields
                            environment='production',
                            debug_mode=False,
                            password_expiry_days=90,
                            max_login_attempts=5,
                            lockout_duration=1800,
                            require_mfa=False,
                            backup_frequency='daily',
                            backup_retention_days=30,
                            smtp_port=587,
                            smtp_use_tls=True,
                            allowed_file_types=['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.jpg', '.png'],
                            cache_timeout=3600,
                            page_size=20,
                            max_export_records=10000,
                            api_rate_limit=1000,
                            webhook_timeout=30,
                        )

                migrated_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'  ✓ Migrated settings for {config.school_branch.name}')
                )

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'  ✗ Failed to migrate settings for {config.school_branch.name}: {str(e)}'
                    )
                )

        return migrated_count

    def create_backup(self):
        """Create backup of existing data before migration"""
        import os
        import datetime
        from django.core.management import call_command
        from io import StringIO

        backup_dir = 'backups'
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = f'{backup_dir}/settings_backup_{timestamp}.json'

        try:
            with open(backup_file, 'w') as f:
                call_command('dumpdata', 'settings_app', stdout=f, indent=2)
            
            self.stdout.write(
                self.style.SUCCESS(f'Backup created: {backup_file}')
            )
            return backup_file
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to create backup: {str(e)}')
            )
            return None
