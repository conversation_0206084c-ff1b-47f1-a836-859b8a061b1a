from django.core.management.base import BaseCommand
from settings_app.models import Permission

class Command(BaseCommand):
    help = 'Populates the database with default permissions'

    def handle(self, *args, **options):
        # Define default permissions
        default_permissions = [
            # Users Management
            {'name': 'View Users', 'code': 'USERS_VIEW', 'module': 'USERS', 'action': 'VIEW', 'description': 'View user accounts'},
            {'name': 'Create Users', 'code': 'USERS_CREATE', 'module': 'USERS', 'action': 'CREATE', 'description': 'Create new user accounts'},
            {'name': 'Edit Users', 'code': 'USERS_EDIT', 'module': 'USERS', 'action': 'EDIT', 'description': 'Edit existing user accounts'},
            {'name': 'Delete Users', 'code': 'USERS_DELETE', 'module': 'USERS', 'action': 'DELETE', 'description': 'Delete user accounts'},
            
            # Academics
            {'name': 'View Academics', 'code': 'ACADEMICS_VIEW', 'module': 'ACADEMICS', 'action': 'VIEW', 'description': 'View academic information'},
            {'name': 'Create Academic Records', 'code': 'ACADEMICS_CREATE', 'module': 'ACADEMICS', 'action': 'CREATE', 'description': 'Create academic records'},
            {'name': 'Edit Academic Records', 'code': 'ACADEMICS_EDIT', 'module': 'ACADEMICS', 'action': 'EDIT', 'description': 'Edit academic records'},
            {'name': 'Delete Academic Records', 'code': 'ACADEMICS_DELETE', 'module': 'ACADEMICS', 'action': 'DELETE', 'description': 'Delete academic records'},
            
            # Students
            {'name': 'View Students', 'code': 'STUDENTS_VIEW', 'module': 'STUDENTS', 'action': 'VIEW', 'description': 'View student information'},
            {'name': 'Create Students', 'code': 'STUDENTS_CREATE', 'module': 'STUDENTS', 'action': 'CREATE', 'description': 'Create student records'},
            {'name': 'Edit Students', 'code': 'STUDENTS_EDIT', 'module': 'STUDENTS', 'action': 'EDIT', 'description': 'Edit student records'},
            {'name': 'Delete Students', 'code': 'STUDENTS_DELETE', 'module': 'STUDENTS', 'action': 'DELETE', 'description': 'Delete student records'},
            
            # Teachers
            {'name': 'View Teachers', 'code': 'TEACHERS_VIEW', 'module': 'TEACHERS', 'action': 'VIEW', 'description': 'View teacher information'},
            {'name': 'Create Teachers', 'code': 'TEACHERS_CREATE', 'module': 'TEACHERS', 'action': 'CREATE', 'description': 'Create teacher records'},
            {'name': 'Edit Teachers', 'code': 'TEACHERS_EDIT', 'module': 'TEACHERS', 'action': 'EDIT', 'description': 'Edit teacher records'},
            {'name': 'Delete Teachers', 'code': 'TEACHERS_DELETE', 'module': 'TEACHERS', 'action': 'DELETE', 'description': 'Delete teacher records'},
            
            # Parents
            {'name': 'View Parents', 'code': 'PARENTS_VIEW', 'module': 'PARENTS', 'action': 'VIEW', 'description': 'View parent information'},
            {'name': 'Create Parents', 'code': 'PARENTS_CREATE', 'module': 'PARENTS', 'action': 'CREATE', 'description': 'Create parent records'},
            {'name': 'Edit Parents', 'code': 'PARENTS_EDIT', 'module': 'PARENTS', 'action': 'EDIT', 'description': 'Edit parent records'},
            {'name': 'Delete Parents', 'code': 'PARENTS_DELETE', 'module': 'PARENTS', 'action': 'DELETE', 'description': 'Delete parent records'},
            
            # Staff
            {'name': 'View Staff', 'code': 'STAFF_VIEW', 'module': 'STAFF', 'action': 'VIEW', 'description': 'View staff information'},
            {'name': 'Create Staff', 'code': 'STAFF_CREATE', 'module': 'STAFF', 'action': 'CREATE', 'description': 'Create staff records'},
            {'name': 'Edit Staff', 'code': 'STAFF_EDIT', 'module': 'STAFF', 'action': 'EDIT', 'description': 'Edit staff records'},
            {'name': 'Delete Staff', 'code': 'STAFF_DELETE', 'module': 'STAFF', 'action': 'DELETE', 'description': 'Delete staff records'},
            
            # Library
            {'name': 'View Library', 'code': 'LIBRARY_VIEW', 'module': 'LIBRARY', 'action': 'VIEW', 'description': 'View library information'},
            {'name': 'Create Library Records', 'code': 'LIBRARY_CREATE', 'module': 'LIBRARY', 'action': 'CREATE', 'description': 'Create library records'},
            {'name': 'Edit Library Records', 'code': 'LIBRARY_EDIT', 'module': 'LIBRARY', 'action': 'EDIT', 'description': 'Edit library records'},
            {'name': 'Delete Library Records', 'code': 'LIBRARY_DELETE', 'module': 'LIBRARY', 'action': 'DELETE', 'description': 'Delete library records'},
            
            # Inventory
            {'name': 'View Inventory', 'code': 'INVENTORY_VIEW', 'module': 'INVENTORY', 'action': 'VIEW', 'description': 'View inventory information'},
            {'name': 'Create Inventory Records', 'code': 'INVENTORY_CREATE', 'module': 'INVENTORY', 'action': 'CREATE', 'description': 'Create inventory records'},
            {'name': 'Edit Inventory Records', 'code': 'INVENTORY_EDIT', 'module': 'INVENTORY', 'action': 'EDIT', 'description': 'Edit inventory records'},
            {'name': 'Delete Inventory Records', 'code': 'INVENTORY_DELETE', 'module': 'INVENTORY', 'action': 'DELETE', 'description': 'Delete inventory records'},
            
            # Finance
            {'name': 'View Finance', 'code': 'FINANCE_VIEW', 'module': 'FINANCE', 'action': 'VIEW', 'description': 'View financial information'},
            {'name': 'Create Finance Records', 'code': 'FINANCE_CREATE', 'module': 'FINANCE', 'action': 'CREATE', 'description': 'Create financial records'},
            {'name': 'Edit Finance Records', 'code': 'FINANCE_EDIT', 'module': 'FINANCE', 'action': 'EDIT', 'description': 'Edit financial records'},
            {'name': 'Delete Finance Records', 'code': 'FINANCE_DELETE', 'module': 'FINANCE', 'action': 'DELETE', 'description': 'Delete financial records'},
            {'name': 'Approve Finance Records', 'code': 'FINANCE_APPROVE', 'module': 'FINANCE', 'action': 'APPROVE', 'description': 'Approve financial transactions'},
            
            # Settings
            {'name': 'View Settings', 'code': 'SETTINGS_VIEW', 'module': 'SETTINGS', 'action': 'VIEW', 'description': 'View system settings'},
            {'name': 'Edit Settings', 'code': 'SETTINGS_EDIT', 'module': 'SETTINGS', 'action': 'EDIT', 'description': 'Edit system settings'},
            
            # Reports
            {'name': 'View Reports', 'code': 'REPORTS_VIEW', 'module': 'REPORTS', 'action': 'VIEW', 'description': 'View reports'},
            {'name': 'Create Reports', 'code': 'REPORTS_CREATE', 'module': 'REPORTS', 'action': 'CREATE', 'description': 'Create reports'},
            {'name': 'Export Reports', 'code': 'REPORTS_EXPORT', 'module': 'REPORTS', 'action': 'EXPORT', 'description': 'Export reports'},
            
            # Communication
            {'name': 'View Communication', 'code': 'COMMUNICATION_VIEW', 'module': 'COMMUNICATION', 'action': 'VIEW', 'description': 'View communication records'},
            {'name': 'Create Communication', 'code': 'COMMUNICATION_CREATE', 'module': 'COMMUNICATION', 'action': 'CREATE', 'description': 'Create communication records'},
            {'name': 'Edit Communication', 'code': 'COMMUNICATION_EDIT', 'module': 'COMMUNICATION', 'action': 'EDIT', 'description': 'Edit communication records'},
            {'name': 'Delete Communication', 'code': 'COMMUNICATION_DELETE', 'module': 'COMMUNICATION', 'action': 'DELETE', 'description': 'Delete communication records'},
            
            # Attendance
            {'name': 'View Attendance', 'code': 'ATTENDANCE_VIEW', 'module': 'ATTENDANCE', 'action': 'VIEW', 'description': 'View attendance records'},
            {'name': 'Create Attendance Records', 'code': 'ATTENDANCE_CREATE', 'module': 'ATTENDANCE', 'action': 'CREATE', 'description': 'Create attendance records'},
            {'name': 'Edit Attendance Records', 'code': 'ATTENDANCE_EDIT', 'module': 'ATTENDANCE', 'action': 'EDIT', 'description': 'Edit attendance records'},
            {'name': 'Delete Attendance Records', 'code': 'ATTENDANCE_DELETE', 'module': 'ATTENDANCE', 'action': 'DELETE', 'description': 'Delete attendance records'},
            
            # Exams
            {'name': 'View Exams', 'code': 'EXAMS_VIEW', 'module': 'EXAMS', 'action': 'VIEW', 'description': 'View exam records'},
            {'name': 'Create Exams', 'code': 'EXAMS_CREATE', 'module': 'EXAMS', 'action': 'CREATE', 'description': 'Create exam records'},
            {'name': 'Edit Exams', 'code': 'EXAMS_EDIT', 'module': 'EXAMS', 'action': 'EDIT', 'description': 'Edit exam records'},
            {'name': 'Delete Exams', 'code': 'EXAMS_DELETE', 'module': 'EXAMS', 'action': 'DELETE', 'description': 'Delete exam records'},
            
            # Timetable
            {'name': 'View Timetable', 'code': 'TIMETABLE_VIEW', 'module': 'TIMETABLE', 'action': 'VIEW', 'description': 'View timetable'},
            {'name': 'Create Timetable', 'code': 'TIMETABLE_CREATE', 'module': 'TIMETABLE', 'action': 'CREATE', 'description': 'Create timetable'},
            {'name': 'Edit Timetable', 'code': 'TIMETABLE_EDIT', 'module': 'TIMETABLE', 'action': 'EDIT', 'description': 'Edit timetable'},
            {'name': 'Delete Timetable', 'code': 'TIMETABLE_DELETE', 'module': 'TIMETABLE', 'action': 'DELETE', 'description': 'Delete timetable'},
        ]
        
        # Create permissions
        created_count = 0
        updated_count = 0
        
        for perm_data in default_permissions:
            perm, created = Permission.objects.update_or_create(
                code=perm_data['code'],
                defaults={
                    'name': perm_data['name'],
                    'module': perm_data['module'],
                    'action': perm_data['action'],
                    'description': perm_data['description'],
                    'is_active': True
                }
            )
            
            if created:
                created_count += 1
            else:
                updated_count += 1
        
        self.stdout.write(self.style.SUCCESS(f'Successfully created {created_count} permissions and updated {updated_count} permissions.'))
