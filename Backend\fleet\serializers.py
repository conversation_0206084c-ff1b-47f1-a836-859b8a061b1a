from rest_framework import serializers
from .models import (
    Vehicle, Driver, Route, Schedule, VehicleMaintenance,
    StudentTransport, TransportAttendance, FuelConsumption
)
from core.Serializers import UserSerializer
from schools.serializers import SchoolBranchSerializer

class VehicleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Vehicle
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']

class DriverSerializer(serializers.ModelSerializer):
    user_details = UserSerializer(source='user', read_only=True)

    class Meta:
        model = Driver
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']

class RouteSerializer(serializers.ModelSerializer):
    class Meta:
        model = Route
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']

class ScheduleSerializer(serializers.ModelSerializer):
    vehicle_details = VehicleSerializer(source='vehicle', read_only=True)
    driver_details = DriverSerializer(source='driver', read_only=True)
    route_details = RouteSerializer(source='route', read_only=True)

    class Meta:
        model = Schedule
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']

class VehicleMaintenanceSerializer(serializers.ModelSerializer):
    vehicle_details = VehicleSerializer(source='vehicle', read_only=True)

    class Meta:
        model = VehicleMaintenance
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']

class StudentTransportSerializer(serializers.ModelSerializer):
    route_details = RouteSerializer(source='route', read_only=True)
    student_name = serializers.SerializerMethodField()
    usage_type_display = serializers.SerializerMethodField()

    class Meta:
        model = StudentTransport
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']

    def get_student_name(self, obj):
        return f"{obj.student.user.first_name} {obj.student.user.last_name}"

    def get_usage_type_display(self, obj):
        return obj.get_usage_type_display()

class TransportAttendanceSerializer(serializers.ModelSerializer):
    student_transport_details = StudentTransportSerializer(source='student_transport', read_only=True)
    schedule_details = ScheduleSerializer(source='schedule', read_only=True)

    class Meta:
        model = TransportAttendance
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']

class FuelConsumptionSerializer(serializers.ModelSerializer):
    vehicle_details = VehicleSerializer(source='vehicle', read_only=True)

    class Meta:
        model = FuelConsumption
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']

# Dashboard serializers
class FleetDashboardSerializer(serializers.Serializer):
    total_vehicles = serializers.IntegerField()
    active_vehicles = serializers.IntegerField()
    maintenance_vehicles = serializers.IntegerField()
    total_drivers = serializers.IntegerField()
    active_drivers = serializers.IntegerField()
    total_routes = serializers.IntegerField()
    active_routes = serializers.IntegerField()
    total_students_using_transport = serializers.IntegerField()
    upcoming_maintenance = serializers.ListField(
        child=VehicleMaintenanceSerializer()
    )
    recent_fuel_consumption = serializers.ListField(
        child=FuelConsumptionSerializer()
    )
    vehicle_utilization = serializers.DictField(
        child=serializers.FloatField()
    )

class VehicleReportSerializer(serializers.Serializer):
    vehicle = VehicleSerializer()
    total_maintenance_cost = serializers.DecimalField(max_digits=12, decimal_places=2)
    total_fuel_cost = serializers.DecimalField(max_digits=12, decimal_places=2)
    total_distance_traveled = serializers.IntegerField()
    fuel_efficiency = serializers.FloatField()
    maintenance_history = serializers.ListField(
        child=VehicleMaintenanceSerializer()
    )
    fuel_history = serializers.ListField(
        child=FuelConsumptionSerializer()
    )
    utilization_rate = serializers.FloatField()
    scheduled_maintenance = serializers.ListField(
        child=VehicleMaintenanceSerializer()
    )
