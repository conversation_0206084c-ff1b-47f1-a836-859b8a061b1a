from django.db import models
from django.conf import settings

class TeacherPerformanceMetrics(models.Model):
    teacher = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='performance_metrics')
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE, related_name='teacher_performance_metrics')
    term = models.ForeignKey('academics.Term', on_delete=models.CASCADE, related_name='teacher_performance_metrics')
    class_room = models.ForeignKey('academics.ClassRoom', on_delete=models.CASCADE, related_name='teacher_performance_metrics', null=True, blank=True)
    class_average = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    pass_rate = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    completion_rate = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    avg_value_addition = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    number_of_distinctions = models.PositiveIntegerField(default=0)
    number_of_failures = models.PositiveIntegerField(default=0)
    remarks = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'academics'
        unique_together = ['teacher', 'subject', 'term', 'class_room']
        ordering = ['-term', 'teacher']

    def __str__(self):
        return f"{self.teacher} - {self.subject} - {self.term}" 