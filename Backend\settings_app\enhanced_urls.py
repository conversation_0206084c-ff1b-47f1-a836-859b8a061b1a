"""
Enhanced URLs for the settings module with comprehensive routing.
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .enhanced_views import (
    AdvancedSchoolProfileViewSet,
    SystemSettingsViewSet,
    NotificationTemplateViewSet,
    IntegrationSettingsViewSet,
    CustomFieldViewSet,
    AuditLogViewSet,
    SchoolProfileViewSet,
    SystemConfigurationViewSet
)

# Create router for enhanced views
enhanced_router = DefaultRouter()
enhanced_router.register(r'advanced-profiles', AdvancedSchoolProfileViewSet, basename='advanced-school-profile')
enhanced_router.register(r'system-settings', SystemSettingsViewSet, basename='system-settings')
enhanced_router.register(r'notification-templates', NotificationTemplateViewSet, basename='notification-template')
enhanced_router.register(r'integrations', IntegrationSettingsViewSet, basename='integration-settings')
enhanced_router.register(r'custom-fields', CustomFieldViewSet, basename='custom-field')
enhanced_router.register(r'audit-logs', AuditLogViewSet, basename='audit-log')

# Backward compatibility router
compat_router = DefaultRouter()
compat_router.register(r'profiles', SchoolProfileViewSet, basename='school-profile')
compat_router.register(r'configurations', SystemConfigurationViewSet, basename='system-configuration')

app_name = 'settings_app'

urlpatterns = [
    # Enhanced API endpoints
    path('api/v2/', include(enhanced_router.urls)),
    
    # Backward compatibility endpoints
    path('api/v1/', include(compat_router.urls)),
    path('', include(compat_router.urls)),  # Default to v1 for backward compatibility
    
    # Additional utility endpoints
    path('api/v2/health/', include([
        path('system/', SystemSettingsViewSet.as_view({'get': 'system_health'}), name='system-health'),
    ])),
    
    path('api/v2/templates/', include([
        path('variables/', NotificationTemplateViewSet.as_view({'get': 'available_variables'}), name='template-variables'),
    ])),
    
    path('api/v2/fields/', include([
        path('by-model/', CustomFieldViewSet.as_view({'get': 'by_model'}), name='fields-by-model'),
    ])),
    
    path('api/v2/audit/', include([
        path('statistics/', AuditLogViewSet.as_view({'get': 'statistics'}), name='audit-statistics'),
    ])),
]
