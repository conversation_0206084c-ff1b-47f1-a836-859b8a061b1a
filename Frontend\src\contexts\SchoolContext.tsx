import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import { useAuth } from '../context/AuthContext';
import { schoolApi } from '../api/schoolApi';

// Define types for school and branch data
export interface School {
  id: number;
  name: string;
  code: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  registration_number: string;
  logo?: string;
  established_date: string;
  is_active: boolean;
  is_archived: boolean;
  archived_at?: string;
  created_at: string;
  updated_at: string;
  branch_count: number;
}

export interface Branch {
  id: number;
  name: string;
  code: string;
  school: number;
  school_name: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  registration_number: string;
  logo?: string;
  established_date: string;
  is_active: boolean;
  is_archived: boolean;
  archived_at?: string;
  created_at: string;
  updated_at: string;
  is_head_office: boolean;
}

interface SchoolContextType {
  schools: School[];
  branches: Branch[];
  selectedSchool: School | null;
  selectedBranch: Branch | null;
  isLoading: boolean;
  error: string | null;
  setSelectedSchool: (school: School) => void;
  setSelectedBranch: (branch: Branch) => void;
  refreshSchoolData: () => Promise<void>;
}

// Create the context with a default value
const SchoolContext = createContext<SchoolContextType>({
  schools: [],
  branches: [],
  selectedSchool: null,
  selectedBranch: null,
  isLoading: false,
  error: null,
  setSelectedSchool: () => {},
  setSelectedBranch: () => {},
  refreshSchoolData: async () => {},
});

// Custom hook to use the school context
export const useSchool = () => useContext(SchoolContext);

interface SchoolProviderProps {
  children: ReactNode;
}

export const SchoolProvider: React.FC<SchoolProviderProps> = ({ children }) => {
  const [schools, setSchools] = useState<School[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [selectedSchool, setSelectedSchool] = useState<School | null>(null);
  const [selectedBranch, setSelectedBranch] = useState<Branch | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState<number>(0);
  const { isAuthenticated } = useAuth();

  // Function to fetch schools and branches
  const fetchSchoolData = async () => {
    // Don't attempt to fetch data if not authenticated
    if (!isAuthenticated) {
      console.log('Not authenticated, skipping school data fetch');
      setIsLoading(false);
      setError('Authentication required');
      setSchools([]);
      setBranches([]);
      setSelectedSchool(null);
      setSelectedBranch(null);
      return;
    }

    // Don't retry too many times
    if (retryCount > 3) {
      console.log('Too many retry attempts, giving up');
      setIsLoading(false);
      setError('Failed to load school data after multiple attempts');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {

      // Fetch schools using the schoolApi
      const schoolsResponse = await schoolApi.getSchools();

      // Ensure we have a results array
      if (!schoolsResponse || !schoolsResponse.results) {
        setError('Failed to load school data: Invalid response format');
        setSchools([]);
        setIsLoading(false);
        return;
      }

      // Check if we have any schools
      if (!schoolsResponse.results || schoolsResponse.results.length === 0) {
        setError('No schools found. Please add your first school to get started.');
        setSchools([]);
        setIsLoading(false);
        return;
      }

      // Map the results to our School interface
      const schoolsData: School[] = schoolsResponse.results.map((school: any) => ({
        id: school.id,
        name: school.name || 'Unnamed School',
        code: school.code || '',
        address: school.address || '',
        phone: school.phone || '',
        email: school.email || '',
        website: school.website || '',
        registration_number: school.registration_number || '',
        logo: school.logo || '',
        established_date: school.established_date || '',
        is_active: school.is_active !== undefined ? school.is_active : true,
        is_archived: school.is_archived !== undefined ? school.is_archived : false,
        archived_at: school.archived_at || '',
        created_at: school.created_at || '',
        updated_at: school.updated_at || '',
        branch_count: school.branch_count || 0
      }));


      setSchools(schoolsData);
      // Reset retry count on success
      setRetryCount(0);

      // If there are schools, fetch branches for the first school
      if (schoolsData.length > 0) {
        // If no school is selected yet, select the first one
        if (!selectedSchool) {
          setSelectedSchool(schoolsData[0]);

          // Fetch branches for the selected school using the schoolApi
          const branchesResponse = await schoolApi.getSchoolBranches(schoolsData[0].id);

          // Ensure we have a results array
          if (!branchesResponse || !branchesResponse.results) {
            setBranches([]);
            return;
          }

          // Map the results to our Branch interface
          const branchesData: Branch[] = branchesResponse.results.map((branch: any) => ({
            id: branch.id,
            name: branch.name || 'Unnamed Branch',
            code: branch.code || '',
            school: branch.school || schoolsData[0].id,
            school_name: branch.school_name || schoolsData[0].name,
            address: branch.address || '',
            phone: branch.phone || '',
            email: branch.email || '',
            website: branch.website || '',
            registration_number: branch.registration_number || '',
            logo: branch.logo || '',
            established_date: branch.established_date || '',
            is_active: branch.is_active !== undefined ? branch.is_active : true,
            is_archived: branch.is_archived !== undefined ? branch.is_archived : false,
            archived_at: branch.archived_at || '',
            created_at: branch.created_at || '',
            updated_at: branch.updated_at || '',
            is_head_office: branch.is_head_office !== undefined ? branch.is_head_office : false
          }));


          setBranches(branchesData);

          // Select the first branch (or head office if available)
          if (branchesData.length > 0) {
            const headOffice = branchesData.find(branch => branch.is_head_office);
            setSelectedBranch(headOffice || branchesData[0]);
          }
        } else {
          // Fetch branches for the already selected school using the schoolApi
          const branchesResponse = await schoolApi.getSchoolBranches(selectedSchool.id);

          // Ensure we have a results array
          if (!branchesResponse || !branchesResponse.results) {
            setBranches([]);
            return;
          }

          // Map the results to our Branch interface
          const branchesData: Branch[] = branchesResponse.results.map((branch: any) => ({
            id: branch.id,
            name: branch.name || 'Unnamed Branch',
            code: branch.code || '',
            school: branch.school || selectedSchool.id,
            school_name: branch.school_name || selectedSchool.name,
            address: branch.address || '',
            phone: branch.phone || '',
            email: branch.email || '',
            website: branch.website || '',
            registration_number: branch.registration_number || '',
            logo: branch.logo || '',
            established_date: branch.established_date || '',
            is_active: branch.is_active !== undefined ? branch.is_active : true,
            is_archived: branch.is_archived !== undefined ? branch.is_archived : false,
            archived_at: branch.archived_at || '',
            created_at: branch.created_at || '',
            updated_at: branch.updated_at || '',
            is_head_office: branch.is_head_office !== undefined ? branch.is_head_office : false
          }));


          setBranches(branchesData);

          // Make sure the selected branch still exists in the updated list
          if (selectedBranch && !branchesData.some(branch => branch.id === selectedBranch.id)) {
            const headOffice = branchesData.find(branch => branch.is_head_office);
            setSelectedBranch(headOffice || branchesData[0] || null);
          }
        }
      } else {
        // No schools available
        setSchools([]);
        setBranches([]);
        setSelectedSchool(null);
        setSelectedBranch(null);
        setError('No schools available. Please add a school first.');
      }
    } catch (err) {
      console.error('Error fetching school data:', err);
      setError('Failed to load school data. Please try again later.');

      // Clear any existing data on error
      setSchools([]);
      setBranches([]);
      setSelectedSchool(null);
      setSelectedBranch(null);

      // Increment retry count
      setRetryCount(prev => prev + 1);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to handle school selection
  const handleSchoolSelection = async (school: School) => {
    setSelectedSchool(school);
    setIsLoading(true);

    try {
      // Fetch branches for the selected school using the schoolApi
      const branchesResponse = await schoolApi.getSchoolBranches(school.id);

      // Ensure we have a results array
      if (!branchesResponse || !branchesResponse.results) {
        setBranches([]);
        setError(`Failed to load branches for ${school.name}`);
        return;
      }

      // Map the results to our Branch interface
      const branchesData: Branch[] = branchesResponse.results.map((branch: any) => ({
        id: branch.id,
        name: branch.name || 'Unnamed Branch',
        code: branch.code || '',
        school: branch.school || school.id,
        school_name: branch.school_name || school.name,
        address: branch.address || '',
        phone: branch.phone || '',
        email: branch.email || '',
        website: branch.website || '',
        registration_number: branch.registration_number || '',
        logo: branch.logo || '',
        established_date: branch.established_date || '',
        is_active: branch.is_active !== undefined ? branch.is_active : true,
        is_archived: branch.is_archived !== undefined ? branch.is_archived : false,
        archived_at: branch.archived_at || '',
        created_at: branch.created_at || '',
        updated_at: branch.updated_at || '',
        is_head_office: branch.is_head_office !== undefined ? branch.is_head_office : false
      }));


      setBranches(branchesData);

      // Select the head office or first branch
      if (branchesData.length > 0) {
        const headOffice = branchesData.find(branch => branch.is_head_office);
        setSelectedBranch(headOffice || branchesData[0]);
      } else {
        setBranches([]);
        setSelectedBranch(null);
        setError(`No branches available for ${school.name}. Please add a branch first.`);
      }
    } catch (err) {
      console.error('Error fetching branches:', err);
      setError('Failed to load branches. Please try again later.');

      // Clear branch data on error
      setBranches([]);
      setSelectedBranch(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Load schools and branches when authentication state changes
  useEffect(() => {
    // Skip data fetching on auth pages to prevent loops
    const currentPath = window.location.pathname;
    const authPages = ['/signin', '/signup', '/forgot-password', '/register', '/complete-profile'];
    const isAuthPage = authPages.some(page => currentPath.includes(page));

    // Only fetch data if authenticated AND user exists AND not on auth page
    if (isAuthenticated && user && user.id && !isAuthPage) {
      console.log('🏫 SchoolProvider: User authenticated, fetching school data...');
      fetchSchoolData();
    } else {
      // Clear data when not authenticated or on auth pages
      if (!isAuthenticated) {
        console.log('🏫 SchoolProvider: Not authenticated, clearing data');
      } else if (isAuthPage) {
        console.log('🏫 SchoolProvider: On auth page, skipping data fetch');
      } else {
        console.log('🏫 SchoolProvider: No user data, clearing data');
      }
      setSchools([]);
      setBranches([]);
      setSelectedSchool(null);
      setSelectedBranch(null);
      setIsLoading(false);
      setError(null);
      setRetryCount(0);
    }
  }, [isAuthenticated, user]);

  // Save selected school and branch to localStorage
  useEffect(() => {
    if (selectedSchool) {
      localStorage.setItem('selectedSchoolId', selectedSchool.id.toString());
    }

    if (selectedBranch) {
      localStorage.setItem('selectedBranchId', selectedBranch.id.toString());
    }
  }, [selectedSchool, selectedBranch]);

  // Context value
  const value = {
    schools,
    branches,
    selectedSchool,
    selectedBranch,
    isLoading,
    error,
    setSelectedSchool: handleSchoolSelection,
    setSelectedBranch,
    refreshSchoolData: fetchSchoolData
  };

  return (
    <SchoolContext.Provider value={value}>
      {children}
    </SchoolContext.Provider>
  );
};

export default SchoolContext;
