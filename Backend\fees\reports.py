from django.db.models import Sum, Count
from datetime import datetime
import pandas as pd
from .models import FeePayment, FeeBalance, FeeStructure, FeeConcession

class FeeReportGenerator:
    def generate_collection_report(self, school, term):
        """Generate fee collection summary report"""
        collections = FeePayment.objects.filter(
            student__school=school,
            term=term
        ).aggregate(
            total_collected=Sum('amount'),
            total_transactions=Count('id')
        )
        
        payment_methods = FeePayment.objects.filter(
            student__school=school,
            term=term
        ).values('payment_method').annotate(
            total=Sum('amount'),
            count=Count('id')
        )

        balances = FeeBalance.objects.filter(
            student__school=school,
            term=term
        ).aggregate(
            total_balance=Sum('balance'),
            total_students=Count('student')
        )

        return {
            'collections': collections,
            'payment_methods': payment_methods,
            'balances': balances,
            'collection_rate': self._calculate_collection_rate(school, term)
        }

    def generate_defaulters_report(self, school, term):
        """Generate report of fee defaulters"""
        defaulters = FeeBalance.objects.filter(
            student__school=school,
            term=term,
            balance__gt=0
        ).select_related('student', 'term')
        
        return [{
            'student_name': d.student.get_full_name(),
            'class': d.student.current_class,
            'balance': d.balance,
            'last_payment': d.last_payment_date,
            'contact': d.student.parent_contact,
            'reminders_sent': d.feereminder_set.count()
        } for d in defaulters]

    def generate_concession_report(self, school, term):
        """Generate report of fee concessions"""
        concessions = FeeConcession.objects.filter(
            student__school=school,
            term=term,
            is_active=True
        ).select_related('student')
        
        total_concession = concessions.aggregate(
            total_amount=Sum('amount')
        )['total_amount'] or 0
        
        return {
            'concessions': [{
                'student': c.student.get_full_name(),
                'type': c.concession_type,
                'amount': c.amount,
                'reason': c.reason
            } for c in concessions],
            'total_concession': total_concession
        }

    def export_to_excel(self, data, report_type):
        """Export report data to Excel"""
        df = pd.DataFrame(data)
        filename = f"{report_type}_{datetime.now().strftime('%Y%m%d')}.xlsx"
        df.to_excel(filename, index=False)
        return filename
