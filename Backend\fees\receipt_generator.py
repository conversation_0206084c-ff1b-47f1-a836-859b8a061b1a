"""
Receipt Generation Service
Generates professional payment receipts in PDF format
"""

import os
import logging
from datetime import datetime
from decimal import Decimal
from django.conf import settings
from django.template.loader import render_to_string
from django.core.files.base import ContentFile
from django.utils import timezone

try:
    from weasyprint import HTML, CSS
    WEASYPRINT_AVAILABLE = True
except ImportError:
    WEASYPRINT_AVAILABLE = False
    logging.warning("WeasyPrint not available. PDF generation will be disabled.")

from .school_billing_models import PaymentReceipt

logger = logging.getLogger(__name__)


class ReceiptGenerator:
    """
    Service for generating payment receipts
    """
    
    def __init__(self):
        self.templates_dir = os.path.join(settings.BASE_DIR, 'fees', 'templates', 'receipts')
        self.static_dir = os.path.join(settings.BASE_DIR, 'static')
    
    def generate_receipt(self, payment, template_name='default'):
        """
        Generate a payment receipt
        """
        try:
            # Check if receipt already exists
            existing_receipt = getattr(payment, 'receipt', None)
            if existing_receipt:
                return existing_receipt
            
            # Prepare receipt data
            receipt_data = self._prepare_receipt_data(payment)
            
            # Generate HTML content
            html_content = self._generate_html_receipt(receipt_data, template_name)
            
            # Generate PDF if WeasyPrint is available
            pdf_file = None
            if WEASYPRINT_AVAILABLE:
                pdf_file = self._generate_pdf_receipt(html_content, payment)
            
            # Create receipt record
            receipt = PaymentReceipt.objects.create(
                payment=payment,
                receipt_data=receipt_data,
                pdf_file=pdf_file,
                template_used=template_name
            )
            
            logger.info(f"Receipt generated: {receipt.receipt_number}")
            return receipt
            
        except Exception as e:
            logger.error(f"Error generating receipt for payment {payment.payment_reference}: {e}")
            return None
    
    def _prepare_receipt_data(self, payment):
        """
        Prepare all data needed for the receipt
        """
        school = payment.school
        invoice = payment.invoice
        billing_account = school.billing_account
        
        # Calculate tax breakdown
        tax_amount = Decimal('0.00')
        if invoice and billing_account.tax_rate:
            tax_amount = (payment.amount * billing_account.tax_rate) / 100
        
        subtotal = payment.amount - tax_amount
        
        receipt_data = {
            # Receipt information
            'receipt_number': None,  # Will be set when PaymentReceipt is created
            'generated_date': timezone.now().isoformat(),
            'generated_timestamp': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
            
            # Payment information
            'payment': {
                'reference': payment.payment_reference,
                'amount': float(payment.amount),
                'amount_formatted': self._format_currency(payment.amount),
                'method': payment.get_payment_method_display(),
                'date': payment.payment_date.strftime('%Y-%m-%d'),
                'date_formatted': payment.payment_date.strftime('%B %d, %Y'),
                'external_reference': payment.external_reference,
                'notes': payment.notes,
                'status': payment.get_status_display(),
            },
            
            # School information
            'school': {
                'name': school.name,
                'email': school.email,
                'phone': school.phone,
                'address': getattr(school, 'address', ''),
                'city': getattr(school, 'city', ''),
                'country': getattr(school, 'country', 'Kenya'),
            },
            
            # Billing information
            'billing': {
                'contact_name': billing_account.billing_contact_name,
                'contact_email': billing_account.billing_contact_email,
                'contact_phone': billing_account.billing_contact_phone,
                'address': billing_account.billing_address,
                'city': billing_account.billing_city,
                'country': billing_account.billing_country,
                'tax_id': billing_account.tax_id,
                'tax_rate': float(billing_account.tax_rate),
            },
            
            # Invoice information (if applicable)
            'invoice': None,
            
            # Financial breakdown
            'financial': {
                'subtotal': float(subtotal),
                'subtotal_formatted': self._format_currency(subtotal),
                'tax_amount': float(tax_amount),
                'tax_amount_formatted': self._format_currency(tax_amount),
                'total_amount': float(payment.amount),
                'total_amount_formatted': self._format_currency(payment.amount),
                'currency': 'KES',
            },
            
            # License information
            'license': self._get_license_info(school),
            
            # Company information
            'company': {
                'name': getattr(settings, 'COMPANY_NAME', 'ShuleXcel'),
                'address': getattr(settings, 'COMPANY_ADDRESS', ''),
                'phone': getattr(settings, 'COMPANY_PHONE', ''),
                'email': getattr(settings, 'COMPANY_EMAIL', ''),
                'website': getattr(settings, 'COMPANY_WEBSITE', ''),
                'logo_url': getattr(settings, 'COMPANY_LOGO_URL', ''),
            }
        }
        
        # Add invoice details if payment is linked to an invoice
        if invoice:
            receipt_data['invoice'] = {
                'number': invoice.invoice_number,
                'date': invoice.invoice_date.strftime('%Y-%m-%d'),
                'date_formatted': invoice.invoice_date.strftime('%B %d, %Y'),
                'due_date': invoice.due_date.strftime('%Y-%m-%d'),
                'due_date_formatted': invoice.due_date.strftime('%B %d, %Y'),
                'billing_period_start': invoice.billing_period_start.strftime('%Y-%m-%d'),
                'billing_period_end': invoice.billing_period_end.strftime('%Y-%m-%d'),
                'billing_period_formatted': f"{invoice.billing_period_start.strftime('%B %d')} - {invoice.billing_period_end.strftime('%B %d, %Y')}",
                'total_amount': float(invoice.total_amount),
                'total_amount_formatted': self._format_currency(invoice.total_amount),
                'paid_amount': float(invoice.paid_amount),
                'paid_amount_formatted': self._format_currency(invoice.paid_amount),
                'status': invoice.get_status_display(),
            }
        
        return receipt_data
    
    def _get_license_info(self, school):
        """Get license information for the school"""
        try:
            license_sub = school.license
            return {
                'package_type': license_sub.package_type.title(),
                'status': license_sub.get_subscription_status_display(),
                'expiry_date': license_sub.expiry_date.strftime('%Y-%m-%d'),
                'expiry_date_formatted': license_sub.expiry_date.strftime('%B %d, %Y'),
                'max_students': license_sub.max_students,
                'max_staff': license_sub.max_staff,
            }
        except:
            return None
    
    def _format_currency(self, amount):
        """Format currency amount"""
        return f"KES {amount:,.2f}"
    
    def _generate_html_receipt(self, receipt_data, template_name):
        """
        Generate HTML receipt content
        """
        template_path = f'receipts/{template_name}.html'
        
        try:
            html_content = render_to_string(template_path, {
                'receipt': receipt_data,
                'current_year': datetime.now().year,
            })
            return html_content
        except Exception as e:
            logger.error(f"Error generating HTML receipt: {e}")
            # Fallback to default template
            return self._generate_default_html_receipt(receipt_data)
    
    def _generate_default_html_receipt(self, receipt_data):
        """
        Generate a basic HTML receipt when template is not available
        """
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Payment Receipt - {receipt_data['payment']['reference']}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                .company-name {{ font-size: 24px; font-weight: bold; color: #2563eb; }}
                .receipt-title {{ font-size: 20px; margin: 20px 0; }}
                .info-section {{ margin: 20px 0; }}
                .info-row {{ display: flex; justify-content: space-between; margin: 5px 0; }}
                .amount {{ font-size: 18px; font-weight: bold; color: #059669; }}
                .footer {{ margin-top: 40px; text-align: center; font-size: 12px; color: #6b7280; }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="company-name">{receipt_data['company']['name']}</div>
                <div>Payment Receipt</div>
            </div>
            
            <div class="receipt-title">Receipt #{receipt_data.get('receipt_number', 'PENDING')}</div>
            
            <div class="info-section">
                <div class="info-row">
                    <span><strong>School:</strong></span>
                    <span>{receipt_data['school']['name']}</span>
                </div>
                <div class="info-row">
                    <span><strong>Payment Reference:</strong></span>
                    <span>{receipt_data['payment']['reference']}</span>
                </div>
                <div class="info-row">
                    <span><strong>Payment Date:</strong></span>
                    <span>{receipt_data['payment']['date_formatted']}</span>
                </div>
                <div class="info-row">
                    <span><strong>Payment Method:</strong></span>
                    <span>{receipt_data['payment']['method']}</span>
                </div>
                <div class="info-row">
                    <span><strong>Amount Paid:</strong></span>
                    <span class="amount">{receipt_data['financial']['total_amount_formatted']}</span>
                </div>
            </div>
            
            <div class="footer">
                <p>Thank you for your payment!</p>
                <p>Generated on {receipt_data['generated_timestamp']}</p>
            </div>
        </body>
        </html>
        """
        return html
    
    def _generate_pdf_receipt(self, html_content, payment):
        """
        Generate PDF from HTML content
        """
        if not WEASYPRINT_AVAILABLE:
            return None
        
        try:
            # Create CSS for better PDF styling
            css_content = """
                @page {
                    size: A4;
                    margin: 2cm;
                }
                body {
                    font-family: 'DejaVu Sans', Arial, sans-serif;
                    font-size: 12px;
                    line-height: 1.4;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 2px solid #2563eb;
                    padding-bottom: 20px;
                }
                .amount {
                    font-weight: bold;
                    color: #059669;
                }
            """
            
            # Generate PDF
            html_doc = HTML(string=html_content)
            css_doc = CSS(string=css_content)
            pdf_content = html_doc.write_pdf(stylesheets=[css_doc])
            
            # Create file name
            filename = f"receipt_{payment.payment_reference}_{timezone.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            
            # Save to file field
            pdf_file = ContentFile(pdf_content, name=filename)
            
            return pdf_file
            
        except Exception as e:
            logger.error(f"Error generating PDF receipt: {e}")
            return None
    
    def regenerate_receipt(self, payment, template_name='default'):
        """
        Regenerate receipt (useful for template updates)
        """
        try:
            # Delete existing receipt if it exists
            existing_receipt = getattr(payment, 'receipt', None)
            if existing_receipt:
                existing_receipt.delete()
            
            # Generate new receipt
            return self.generate_receipt(payment, template_name)
            
        except Exception as e:
            logger.error(f"Error regenerating receipt: {e}")
            return None
