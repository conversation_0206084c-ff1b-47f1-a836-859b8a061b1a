from __future__ import absolute_import
import os
from celery import Celery
from django.conf import settings
from celery.schedules import crontab

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')

app = Celery('Backend')
app.config_from_object('django.conf:settings', namespace='CELERY')
app.autodiscover_tasks(lambda: settings.INSTALLED_APPS)

# Configure periodic tasks
app.conf.beat_schedule = {
    'check-daily-interventions': {
        'task': 'academics.tasks.run_daily_intervention_checks',
        'schedule': crontab(hour=6, minute=0),  # Run daily at 6 AM
    },
    'update-performance-predictions': {
        'task': 'academics.tasks.update_performance_predictions',
        'schedule': crontab(day_of_week='monday', hour=5, minute=0),  # Weekly on Monday
    },
    'check-intervention-effectiveness': {
        'task': 'academics.tasks.check_intervention_effectiveness',
        'schedule': crontab(day_of_week='monday,thursday', hour=7, minute=0),  # Twice weekly
    },
    'analyze-student-patterns': {
        'task': 'academics.tasks.analyze_learning_patterns',
        'schedule': crontab(hour='*/4'),  # Every 4 hours
    },
}
