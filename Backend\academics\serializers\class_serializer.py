from rest_framework import serializers
from academics.models import ClassRoom, Stream, AcademicYear
from academics.curriculum_serializers import EducationLevelSerializer
from users.serializers import TeacherSerializer
from schools.serializers import SchoolSerializer

class ClassRoomSerializer(serializers.ModelSerializer):
    # Add nested serializers for related objects
    education_level_details = EducationLevelSerializer(source='education_level', read_only=True)
    stream_details = serializers.SerializerMethodField(read_only=True)
    academic_year_details = serializers.SerializerMethodField(read_only=True)
    school_details = SchoolSerializer(source='school', read_only=True)
    class_teacher_details = TeacherSerializer(source='class_teacher', read_only=True)
    next_class_options = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ClassRoom
        fields = '__all__'

    def get_stream_details(self, obj):
        if obj.stream:
            return {
                'id': obj.stream.id,
                'name': obj.stream.name,
                'code': obj.stream.code
            }
        return None

    def get_academic_year_details(self, obj):
        if obj.academic_year:
            return {
                'id': obj.academic_year.id,
                'year': obj.academic_year.year,
                'start_date': obj.academic_year.start_date,
                'end_date': obj.academic_year.end_date,
                'is_current': obj.academic_year.is_current
            }
        return None

    def get_next_class_options(self, obj):
        """Get possible next classes based on curriculum progression rules"""
        if not hasattr(obj, 'get_next_class_options') or not obj.education_level:
            return []

        try:
            options = obj.get_next_class_options()
            return [{
                'education_level_id': option['education_level'].id,
                'education_level_name': option['education_level'].name,
                'education_level_code': option['education_level'].code,
                'is_default': option['is_default'],
                'requirements': option['requirements']
            } for option in options]
        except Exception as e:
            print(f"Error getting next class options: {e}")
            return []
