from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from django.conf import settings
from decimal import Decimal


def default_decimal() -> Decimal:
    return Decimal('0.00')


class LiveStreamingPlatform(models.Model):
    """Supported live streaming platforms"""
    name = models.CharField(max_length=50)
    code = models.CharField(max_length=20, unique=True)
    api_endpoint = models.URLField(blank=True)
    
    # Configuration
    requires_api_key = models.BooleanField(default=True)
    supports_recording = models.BooleanField(default=True)
    supports_chat = models.BooleanField(default=True)
    max_participants = models.PositiveIntegerField(null=True, blank=True)
    
    is_active = models.BooleanField(default=True)
    
    class Meta:
        app_label = 'academics'
        
    def __str__(self):
        return self.name


class LiveSession(models.Model):
    """Live streaming sessions for classes"""
    SESSION_TYPES = [
        ('lesson', 'Regular Lesson'),
        ('exam', 'Exam Session'),
        ('meeting', 'Class Meeting'),
        ('assembly', 'Assembly'),
        ('event', 'Special Event'),
    ]
    
    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('live', 'Live'),
        ('ended', 'Ended'),
        ('cancelled', 'Cancelled'),
        ('paused', 'Paused'),
    ]
    
    # Basic information
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    session_type = models.CharField(max_length=10, choices=SESSION_TYPES)
    
    # Academic context
    class_name = models.ForeignKey('academics.ClassRoom', on_delete=models.CASCADE)
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE)
    teacher = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='live_sessions')
    term = models.ForeignKey('academics.Term', on_delete=models.CASCADE)
    
    # Scheduling
    scheduled_start = models.DateTimeField()
    scheduled_end = models.DateTimeField()
    actual_start = models.DateTimeField(null=True, blank=True)
    actual_end = models.DateTimeField(null=True, blank=True)
    
    # Platform and technical details
    platform = models.ForeignKey(LiveStreamingPlatform, on_delete=models.CASCADE)
    stream_url = models.URLField(blank=True)
    stream_key = models.CharField(max_length=200, blank=True)
    meeting_id = models.CharField(max_length=100, blank=True)
    meeting_password = models.CharField(max_length=50, blank=True)
    
    # Settings
    is_recorded = models.BooleanField(default=True)
    recording_url = models.URLField(blank=True)
    allow_chat = models.BooleanField(default=True)
    require_approval = models.BooleanField(default=False)
    max_participants = models.PositiveIntegerField(null=True, blank=True)
    
    # Status
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='scheduled')
    is_public = models.BooleanField(default=False)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        app_label = 'academics'
        ordering = ['-scheduled_start']
        
    def clean(self):
        if self.scheduled_start >= self.scheduled_end:
            raise ValidationError("End time must be after start time")
            
    def __str__(self):
        return f"{self.title} - {self.class_name} ({self.scheduled_start})"


class SessionParticipant(models.Model):
    """Track session participants"""
    PARTICIPANT_TYPES = [
        ('student', 'Student'),
        ('teacher', 'Teacher'),
        ('parent', 'Parent'),
        ('guest', 'Guest'),
    ]
    
    session = models.ForeignKey(LiveSession, on_delete=models.CASCADE)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='session_participations')
    participant_type = models.CharField(max_length=10, choices=PARTICIPANT_TYPES)
    
    # Participation details
    joined_at = models.DateTimeField(null=True, blank=True)
    left_at = models.DateTimeField(null=True, blank=True)
    duration = models.DurationField(null=True, blank=True)
    
    # Permissions
    can_speak = models.BooleanField(default=True)
    can_chat = models.BooleanField(default=True)
    can_share_screen = models.BooleanField(default=False)
    is_moderator = models.BooleanField(default=False)
    
    # Status
    is_approved = models.BooleanField(default=True)
    is_present = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        app_label = 'academics'
        unique_together = ['session', 'user']
        
    def save(self, *args, **kwargs):
        # Calculate duration if both joined_at and left_at are set
        if self.joined_at and self.left_at:
            self.duration = self.left_at - self.joined_at
        super().save(*args, **kwargs)
        
    def __str__(self):
        return f"{self.user.get_full_name()} - {self.session.title}"


class SessionAttendance(models.Model):
    """Detailed attendance tracking for live sessions"""
    session = models.ForeignKey(LiveSession, on_delete=models.CASCADE)
    student = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='session_attendances')
    
    # Attendance status
    is_present = models.BooleanField(default=False)
    attendance_percentage = models.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        default=default_decimal
    )
    
    # Participation metrics
    questions_asked = models.PositiveIntegerField(default=0)
    chat_messages = models.PositiveIntegerField(default=0)
    engagement_score = models.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        default=default_decimal
    )
    
    # Technical issues
    connection_issues = models.BooleanField(default=False)
    technical_notes = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        app_label = 'academics'
        unique_together = ['session', 'student']
        
    def __str__(self):
        return f"{self.student} - {self.session.title}"


class SessionRecording(models.Model):
    """Manage session recordings"""
    session = models.OneToOneField(LiveSession, on_delete=models.CASCADE)
    
    # Recording details
    recording_url = models.URLField()
    file_size = models.BigIntegerField(null=True, blank=True)  # in bytes
    duration = models.DurationField(null=True, blank=True)
    
    # Processing status
    is_processed = models.BooleanField(default=False)
    processing_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('processing', 'Processing'),
            ('completed', 'Completed'),
            ('failed', 'Failed'),
        ],
        default='pending'
    )
    
    # Access control
    is_public = models.BooleanField(default=False)
    allowed_viewers = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        blank=True,
        related_name='accessible_recordings'
    )
    
    # Metadata
    thumbnail_url = models.URLField(blank=True)
    transcript = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        app_label = 'academics'
        
    def __str__(self):
        return f"Recording - {self.session.title}"


class SessionChat(models.Model):
    """Chat messages during live sessions"""
    session = models.ForeignKey(LiveSession, on_delete=models.CASCADE)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='session_chats')
    
    message = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    
    # Message type
    message_type = models.CharField(
        max_length=10,
        choices=[
            ('text', 'Text'),
            ('question', 'Question'),
            ('answer', 'Answer'),
            ('system', 'System'),
        ],
        default='text'
    )
    
    # Moderation
    is_flagged = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    
    class Meta:
        app_label = 'academics'
        ordering = ['timestamp']
        
    def __str__(self):
        return f"{self.user.get_full_name()}: {self.message[:50]}..."


class ResultPublication(models.Model):
    """Manage result publication settings"""
    PUBLICATION_TYPES = [
        ('term_results', 'Term Results'),
        ('exam_results', 'Exam Results'),
        ('progress_reports', 'Progress Reports'),
        ('annual_reports', 'Annual Reports'),
    ]
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('review', 'Under Review'),
        ('approved', 'Approved'),
        ('published', 'Published'),
        ('archived', 'Archived'),
    ]
    
    # Basic information
    title = models.CharField(max_length=200)
    publication_type = models.CharField(max_length=20, choices=PUBLICATION_TYPES)
    description = models.TextField(blank=True)
    
    # Academic context
    academic_year = models.ForeignKey('academics.AcademicYear', on_delete=models.CASCADE)
    term = models.ForeignKey('academics.Term', on_delete=models.CASCADE)
    classes = models.ManyToManyField('academics.ClassRoom', blank=True)
    
    # Publication settings
    publish_date = models.DateTimeField()
    expiry_date = models.DateTimeField(null=True, blank=True)
    
    # Access control
    is_public = models.BooleanField(default=False)
    requires_authentication = models.BooleanField(default=True)
    allowed_user_types = models.JSONField(default=list)  # ['student', 'parent', 'teacher']
    
    # Notification settings
    notify_students = models.BooleanField(default=True)
    notify_parents = models.BooleanField(default=True)
    notification_message = models.TextField(blank=True)
    
    # Status and workflow
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='draft')
    
    # Approval workflow
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='created_publications')
    reviewed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reviewed_publications'
    )
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_publications'
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    published_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        app_label = 'academics'
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.title} - {self.term}"


class PublicationAccess(models.Model):
    """Track who has accessed published results"""
    publication = models.ForeignKey(ResultPublication, on_delete=models.CASCADE)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='publication_accesses')
    
    # Access details
    first_accessed = models.DateTimeField(auto_now_add=True)
    last_accessed = models.DateTimeField(auto_now=True)
    access_count = models.PositiveIntegerField(default=1)
    
    # Device/location info
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    
    class Meta:
        app_label = 'academics'
        unique_together = ['publication', 'user']
        
    def __str__(self):
        return f"{self.user.get_full_name()} accessed {self.publication.title}"
