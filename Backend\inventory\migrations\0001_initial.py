# Generated by Django 5.2.1 on 2025-06-01 13:25

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('schools', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AssetCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='asset_categories', to='schools.schoolbranch')),
            ],
            options={
                'verbose_name_plural': 'Asset Categories',
                'ordering': ['name'],
                'unique_together': {('name', 'school_branch')},
            },
        ),
        migrations.CreateModel(
            name='Asset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('asset_number', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('purchase_date', models.DateField()),
                ('purchase_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('warranty_expiry', models.DateField(blank=True, null=True)),
                ('location', models.CharField(help_text='Physical location of the asset', max_length=100)),
                ('status', models.CharField(choices=[('AVAILABLE', 'Available'), ('IN_USE', 'In Use'), ('UNDER_MAINTENANCE', 'Under Maintenance'), ('DAMAGED', 'Damaged'), ('DISPOSED', 'Disposed'), ('LOST', 'Lost')], default='AVAILABLE', max_length=20)),
                ('condition', models.CharField(blank=True, max_length=100, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='asset_images/')),
                ('documents', models.FileField(blank=True, null=True, upload_to='asset_documents/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('added_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='added_assets', to=settings.AUTH_USER_MODEL)),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_assets', to=settings.AUTH_USER_MODEL)),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assets', to='schools.schoolbranch')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assets', to='inventory.assetcategory')),
            ],
            options={
                'ordering': ['name', 'asset_number'],
            },
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('contact_person', models.CharField(blank=True, max_length=255, null=True)),
                ('phone', models.CharField(max_length=20)),
                ('email', models.EmailField(max_length=254)),
                ('address', models.TextField()),
                ('website', models.URLField(blank=True, null=True)),
                ('tax_id', models.CharField(blank=True, max_length=50, null=True)),
                ('payment_terms', models.CharField(blank=True, max_length=100, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='suppliers', to='schools.schoolbranch')),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('po_number', models.CharField(max_length=50, unique=True)),
                ('order_date', models.DateField()),
                ('expected_delivery_date', models.DateField(blank=True, null=True)),
                ('delivery_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('PENDING_APPROVAL', 'Pending Approval'), ('APPROVED', 'Approved'), ('ORDERED', 'Ordered'), ('PARTIALLY_RECEIVED', 'Partially Received'), ('RECEIVED', 'Received'), ('CANCELLED', 'Cancelled')], default='DRAFT', max_length=20)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('shipping_cost', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('notes', models.TextField(blank=True, null=True)),
                ('payment_status', models.CharField(default='UNPAID', max_length=20)),
                ('payment_date', models.DateField(blank=True, null=True)),
                ('payment_method', models.CharField(blank=True, max_length=50, null=True)),
                ('documents', models.FileField(blank=True, null=True, upload_to='purchase_order_documents/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_purchase_orders', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_purchase_orders', to=settings.AUTH_USER_MODEL)),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchase_orders', to='schools.schoolbranch')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchase_orders', to='inventory.supplier')),
            ],
            options={
                'ordering': ['-order_date', 'status'],
            },
        ),
        migrations.CreateModel(
            name='Maintenance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('maintenance_type', models.CharField(choices=[('PREVENTIVE', 'Preventive'), ('CORRECTIVE', 'Corrective'), ('EMERGENCY', 'Emergency')], max_length=20)),
                ('description', models.TextField()),
                ('status', models.CharField(choices=[('SCHEDULED', 'Scheduled'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled')], default='SCHEDULED', max_length=20)),
                ('scheduled_date', models.DateField()),
                ('start_date', models.DateField(blank=True, null=True)),
                ('completion_date', models.DateField(blank=True, null=True)),
                ('cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('performed_by', models.CharField(blank=True, help_text='Person or company who performed the maintenance', max_length=255, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('documents', models.FileField(blank=True, null=True, upload_to='maintenance_documents/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='maintenance_records', to='inventory.asset')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_maintenance_records', to=settings.AUTH_USER_MODEL)),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='maintenance_records', to='schools.schoolbranch')),
                ('vendor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='maintenance_jobs', to='inventory.supplier')),
            ],
            options={
                'ordering': ['-scheduled_date', 'status'],
            },
        ),
        migrations.AddField(
            model_name='asset',
            name='supplier',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='supplied_assets', to='inventory.supplier'),
        ),
        migrations.CreateModel(
            name='Supply',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('unit', models.CharField(help_text='Unit of measurement (e.g., box, piece, kg)', max_length=50)),
                ('quantity', models.PositiveIntegerField(default=0)),
                ('minimum_quantity', models.PositiveIntegerField(default=1, help_text='Minimum quantity before reordering')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('location', models.CharField(help_text='Storage location', max_length=100)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='supply_images/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('added_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='added_supplies', to=settings.AUTH_USER_MODEL)),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='supplies', to='schools.schoolbranch')),
                ('supplier', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='supplied_items', to='inventory.supplier')),
            ],
            options={
                'verbose_name_plural': 'Supplies',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item_name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('quantity', models.PositiveIntegerField()),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('received_quantity', models.PositiveIntegerField(default=0)),
                ('notes', models.TextField(blank=True, null=True)),
                ('asset_category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='purchase_order_items', to='inventory.assetcategory')),
                ('purchase_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.purchaseorder')),
                ('supply', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='purchase_order_items', to='inventory.supply')),
            ],
            options={
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='SupplyCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='supply_categories', to='schools.schoolbranch')),
            ],
            options={
                'verbose_name_plural': 'Supply Categories',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='supply',
            name='category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='supplies', to='inventory.supplycategory'),
        ),
        migrations.CreateModel(
            name='SupplyTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('PURCHASE', 'Purchase'), ('USAGE', 'Usage'), ('ADJUSTMENT', 'Adjustment'), ('RETURN', 'Return to Supplier'), ('DISPOSAL', 'Disposal')], max_length=20)),
                ('quantity', models.IntegerField(help_text='Positive for additions, negative for removals')),
                ('transaction_date', models.DateField(default=django.utils.timezone.now)),
                ('unit_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('performed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='supply_transactions', to=settings.AUTH_USER_MODEL)),
                ('purchase_order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='supply_transactions', to='inventory.purchaseorder')),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='supply_transactions', to='schools.schoolbranch')),
                ('supplier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='supply_transactions', to='inventory.supplier')),
                ('supply', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='inventory.supply')),
            ],
            options={
                'ordering': ['-transaction_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InventoryReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('report_type', models.CharField(choices=[('ASSET', 'Asset Report'), ('SUPPLY', 'Supply Report'), ('MAINTENANCE', 'Maintenance Report'), ('PURCHASE', 'Purchase Report'), ('VALUATION', 'Inventory Valuation')], max_length=20)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('report_data', models.JSONField(blank=True, null=True)),
                ('file', models.FileField(blank=True, null=True, upload_to='inventory_reports/')),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('generated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='generated_inventory_reports', to=settings.AUTH_USER_MODEL)),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventory_reports', to='schools.schoolbranch')),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['report_type'], name='inventory_i_report__dd3179_idx'), models.Index(fields=['start_date'], name='inventory_i_start_d_25ef5d_idx'), models.Index(fields=['end_date'], name='inventory_i_end_dat_35b94c_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='supplier',
            index=models.Index(fields=['name'], name='inventory_s_name_d435cf_idx'),
        ),
        migrations.AddIndex(
            model_name='supplier',
            index=models.Index(fields=['is_active'], name='inventory_s_is_acti_3743da_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaseorder',
            index=models.Index(fields=['po_number'], name='inventory_p_po_numb_319f55_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaseorder',
            index=models.Index(fields=['order_date'], name='inventory_p_order_d_472534_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaseorder',
            index=models.Index(fields=['status'], name='inventory_p_status_43427b_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaseorder',
            index=models.Index(fields=['payment_status'], name='inventory_p_payment_bd88d1_idx'),
        ),
        migrations.AddIndex(
            model_name='maintenance',
            index=models.Index(fields=['scheduled_date'], name='inventory_m_schedul_218f80_idx'),
        ),
        migrations.AddIndex(
            model_name='maintenance',
            index=models.Index(fields=['status'], name='inventory_m_status_fbcd67_idx'),
        ),
        migrations.AddIndex(
            model_name='maintenance',
            index=models.Index(fields=['maintenance_type'], name='inventory_m_mainten_3c0001_idx'),
        ),
        migrations.AddIndex(
            model_name='asset',
            index=models.Index(fields=['asset_number'], name='inventory_a_asset_n_320a69_idx'),
        ),
        migrations.AddIndex(
            model_name='asset',
            index=models.Index(fields=['status'], name='inventory_a_status_69dd7e_idx'),
        ),
        migrations.AddIndex(
            model_name='asset',
            index=models.Index(fields=['purchase_date'], name='inventory_a_purchas_9f3c26_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='supplycategory',
            unique_together={('name', 'school_branch')},
        ),
        migrations.AddIndex(
            model_name='supply',
            index=models.Index(fields=['name'], name='inventory_s_name_493fa6_idx'),
        ),
        migrations.AddIndex(
            model_name='supply',
            index=models.Index(fields=['category'], name='inventory_s_categor_e6f3ca_idx'),
        ),
        migrations.AddIndex(
            model_name='supply',
            index=models.Index(fields=['expiry_date'], name='inventory_s_expiry__6ee075_idx'),
        ),
        migrations.AddIndex(
            model_name='supplytransaction',
            index=models.Index(fields=['transaction_date'], name='inventory_s_transac_dcda57_idx'),
        ),
        migrations.AddIndex(
            model_name='supplytransaction',
            index=models.Index(fields=['transaction_type'], name='inventory_s_transac_d941a7_idx'),
        ),
    ]
