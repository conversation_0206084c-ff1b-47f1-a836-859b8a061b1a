from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.conf import settings


class LessonPlan(models.Model):
    """Detailed lesson planning"""
    LESSON_TYPES = [
        ('introduction', 'Introduction'),
        ('development', 'Development'),
        ('revision', 'Revision'),
        ('assessment', 'Assessment'),
        ('practical', 'Practical'),
        ('field_trip', 'Field Trip'),
    ]
    
    # Basic information
    title = models.CharField(max_length=200)
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE)
    class_name = models.ForeignKey('academics.ClassRoom', on_delete=models.CASCADE)
    teacher = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='lesson_plans')
    
    # Lesson details
    lesson_type = models.CharField(max_length=15, choices=LESSON_TYPES)
    topic = models.CharField(max_length=200)
    subtopic = models.Char<PERSON>ield(max_length=200, blank=True)
    
    # Curriculum alignment
    curriculum_strand = models.CharField(max_length=100, blank=True)
    learning_outcomes = models.TextField()
    success_criteria = models.TextField()
    
    # Lesson structure
    duration = models.DurationField()
    introduction_activities = models.TextField()
    main_activities = models.TextField()
    conclusion_activities = models.TextField()
    
    # Resources and materials
    required_materials = models.TextField(blank=True)
    digital_resources = models.TextField(blank=True)
    
    # Assessment
    assessment_methods = models.TextField(blank=True)
    homework_assignment = models.TextField(blank=True)
    
    # Differentiation
    differentiation_strategies = models.TextField(blank=True)
    special_needs_accommodations = models.TextField(blank=True)
    
    # Metadata
    date_created = models.DateTimeField(auto_now_add=True)
    last_modified = models.DateTimeField(auto_now=True)
    is_approved = models.BooleanField(default=False)
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_lesson_plans'
    )
    
    class Meta:
        app_label = 'academics'
        ordering = ['-date_created']
        
    def __str__(self):
        return f"{self.title} - {self.subject.name} ({self.class_name})"


class LessonDelivery(models.Model):
    """Track actual lesson delivery"""
    lesson_plan = models.ForeignKey(LessonPlan, on_delete=models.CASCADE)
    scheduled_date = models.DateField()
    actual_date = models.DateField(null=True, blank=True)
    
    # Timing
    scheduled_start = models.TimeField()
    scheduled_end = models.TimeField()
    actual_start = models.TimeField(null=True, blank=True)
    actual_end = models.TimeField(null=True, blank=True)
    
    # Delivery details
    venue = models.CharField(max_length=100)
    students_present = models.PositiveIntegerField(default=0)
    students_absent = models.PositiveIntegerField(default=0)
    
    # Lesson execution
    objectives_achieved = models.TextField(blank=True)
    activities_completed = models.TextField(blank=True)
    challenges_faced = models.TextField(blank=True)
    
    # Assessment and feedback
    student_understanding_level = models.CharField(
        max_length=10,
        choices=[
            ('excellent', 'Excellent'),
            ('good', 'Good'),
            ('average', 'Average'),
            ('poor', 'Poor'),
        ],
        blank=True
    )
    
    # Reflection
    what_went_well = models.TextField(blank=True)
    areas_for_improvement = models.TextField(blank=True)
    next_lesson_adjustments = models.TextField(blank=True)
    
    # Status
    is_completed = models.BooleanField(default=False)
    completion_percentage = models.PositiveIntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        app_label = 'academics'
        unique_together = ['lesson_plan', 'scheduled_date']
        
    def __str__(self):
        return f"{self.lesson_plan.title} - {self.scheduled_date}"


class LessonResource(models.Model):
    """Resources attached to lessons"""
    RESOURCE_TYPES = [
        ('document', 'Document'),
        ('presentation', 'Presentation'),
        ('video', 'Video'),
        ('audio', 'Audio'),
        ('image', 'Image'),
        ('link', 'Web Link'),
        ('interactive', 'Interactive Content'),
    ]
    
    lesson_plan = models.ForeignKey(LessonPlan, on_delete=models.CASCADE)
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    resource_type = models.CharField(max_length=15, choices=RESOURCE_TYPES)
    
    # File or link
    file = models.FileField(upload_to='lesson_resources/', blank=True, null=True)
    url = models.URLField(blank=True)
    
    # Usage tracking
    download_count = models.PositiveIntegerField(default=0)
    view_count = models.PositiveIntegerField(default=0)
    
    # Metadata
    file_size = models.BigIntegerField(null=True, blank=True)
    uploaded_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='uploaded_lesson_resources')
    uploaded_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        app_label = 'academics'
        
    def __str__(self):
        return f"{self.title} - {self.lesson_plan.title}"


class StudentLessonFeedback(models.Model):
    """Student feedback on lessons"""
    lesson_delivery = models.ForeignKey(LessonDelivery, on_delete=models.CASCADE)
    student = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='lesson_feedbacks')
    
    # Understanding ratings (1-5 scale)
    content_clarity = models.PositiveIntegerField(
        choices=[(i, i) for i in range(1, 6)],
        help_text="How clear was the content? (1-5)"
    )
    pace_rating = models.PositiveIntegerField(
        choices=[(i, i) for i in range(1, 6)],
        help_text="Was the pace appropriate? (1-5)"
    )
    engagement_level = models.PositiveIntegerField(
        choices=[(i, i) for i in range(1, 6)],
        help_text="How engaging was the lesson? (1-5)"
    )
    
    # Specific feedback
    what_learned = models.TextField(blank=True)
    questions_raised = models.TextField(blank=True)
    suggestions = models.TextField(blank=True)
    
    # Difficulty level
    difficulty_level = models.CharField(
        max_length=10,
        choices=[
            ('too_easy', 'Too Easy'),
            ('just_right', 'Just Right'),
            ('too_hard', 'Too Hard'),
        ],
        blank=True
    )
    
    submitted_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        app_label = 'academics'
        unique_together = ['lesson_delivery', 'student']
        
    def __str__(self):
        return f"Feedback from {self.student} - {self.lesson_delivery}"


class LessonObservation(models.Model):
    """Lesson observations by supervisors"""
    lesson_delivery = models.ForeignKey(LessonDelivery, on_delete=models.CASCADE)
    observer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='lesson_observations')
    
    # Observation focus areas
    lesson_planning = models.PositiveIntegerField(
        choices=[(i, i) for i in range(1, 6)],
        help_text="Lesson planning quality (1-5)"
    )
    content_delivery = models.PositiveIntegerField(
        choices=[(i, i) for i in range(1, 6)],
        help_text="Content delivery effectiveness (1-5)"
    )
    student_engagement = models.PositiveIntegerField(
        choices=[(i, i) for i in range(1, 6)],
        help_text="Student engagement level (1-5)"
    )
    classroom_management = models.PositiveIntegerField(
        choices=[(i, i) for i in range(1, 6)],
        help_text="Classroom management (1-5)"
    )
    use_of_resources = models.PositiveIntegerField(
        choices=[(i, i) for i in range(1, 6)],
        help_text="Effective use of resources (1-5)"
    )
    
    # Detailed feedback
    strengths = models.TextField()
    areas_for_improvement = models.TextField()
    recommendations = models.TextField()
    
    # Overall rating
    overall_rating = models.CharField(
        max_length=100,
        choices=[
            ('outstanding', 'Outstanding'),
            ('good', 'Good'),
            ('satisfactory', 'Satisfactory'),
            ('needs_improvement', 'Needs Improvement'),
            ('unsatisfactory', 'Unsatisfactory'),
        ]
    )
    
    # Follow-up
    follow_up_required = models.BooleanField(default=False)
    follow_up_date = models.DateField(null=True, blank=True)
    follow_up_notes = models.TextField(blank=True)
    
    observation_date = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        app_label = 'academics'
        
    def __str__(self):
        return f"Observation by {self.observer} - {self.lesson_delivery}"


class LessonSeries(models.Model):
    """Group related lessons into series"""
    title = models.CharField(max_length=200)
    description = models.TextField()
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE)
    class_name = models.ForeignKey('academics.ClassRoom', on_delete=models.CASCADE)
    
    # Series details
    total_lessons = models.PositiveIntegerField()
    estimated_duration = models.DurationField()
    
    # Curriculum alignment
    curriculum_unit = models.CharField(max_length=100)
    learning_objectives = models.TextField()
    
    # Planning
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='created_lesson_series')
    start_date = models.DateField()
    end_date = models.DateField()
    
    # Status
    is_active = models.BooleanField(default=True)
    completion_percentage = models.PositiveIntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        app_label = 'academics'
        
    def __str__(self):
        return f"{self.title} - {self.subject.name}"


class LessonSeriesLesson(models.Model):
    """Link lessons to series with ordering"""
    series = models.ForeignKey(LessonSeries, on_delete=models.CASCADE)
    lesson_plan = models.ForeignKey(LessonPlan, on_delete=models.CASCADE)
    order = models.PositiveIntegerField()
    
    # Prerequisites
    prerequisites = models.TextField(blank=True)
    is_mandatory = models.BooleanField(default=True)
    
    class Meta:
        app_label = 'academics'
        unique_together = ['series', 'lesson_plan']
        ordering = ['order']
        
    def __str__(self):
        return f"{self.series.title} - Lesson {self.order}"
