from rest_framework import viewsets, permissions, status, filters, views
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from django.db.models import Q, Count

from .models import (
    Community, CommunityMember, Post, Comment, ClubMeeting,
    MeetingAttendance, Reaction, Notification
)
from .serializers import (
    CommunitySerializer, CommunityMemberSerializer, PostSerializer, CommentSerializer,
    ClubMeetingSerializer, MeetingAttendanceSerializer, ReactionSerializer, NotificationSerializer,
    CommunityStatisticsSerializer, CommunityDashboardSerializer
)
from core.permissions import BranchBasedPermission

class CommunityViewSet(viewsets.ModelViewSet):
    serializer_class = CommunitySerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description', 'community_type']
    ordering_fields = ['name', 'created_at', 'member_count']
    ordering = ['name']

    def get_queryset(self):
        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            return Community.objects.none()
            
        queryset = Community.objects.filter(school_branch=profile.school_branch)

        # Filter by community type
        community_type = self.request.query_params.get('community_type')
        if community_type:
            queryset = queryset.filter(community_type=community_type)

        return queryset

    def perform_create(self, serializer):
        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            raise PermissionError("User does not have an associated school branch")
            
        serializer.save(
            created_by=self.request.user,
            school_branch=profile.school_branch
        )
        CommunityMember.objects.create(
            user=self.request.user,
            community=serializer.instance,
            role='ADMIN',
            school_branch=profile.school_branch
        )
        serializer.save()

    @action(detail=True, methods=['get'])
    def members(self, request, pk=None):
        community = self.get_object()
        members = CommunityMember.objects.filter(community=community)
        serializer = CommunityMemberSerializer(members, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def posts(self, request, pk=None):
        community = self.get_object()
        posts = Post.objects.filter(community=community)
        serializer = PostSerializer(posts, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def meetings(self, request, pk=None):
        community = self.get_object()
        meetings = ClubMeeting.objects.filter(community=community)
        serializer = ClubMeetingSerializer(meetings, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def statistics(self, request, pk=None):
        community = self.get_object()
        members = CommunityMember.objects.filter(community=community)
        posts = Post.objects.filter(community=community)
        comments = Comment.objects.filter(post__community=community)
        meetings = ClubMeeting.objects.filter(community=community)
        attendance = MeetingAttendance.objects.filter(meeting__community=community)

        serializer = CommunityStatisticsSerializer({
            'community': community,
            'total_members': members.count(),
            'active_members': members.filter(is_active=True).count(),
            'total_posts': posts.count(),
            'total_comments': comments.count(),
            'posts_per_day': posts.count() / (timezone.now() - community.created_at).days,
            'comments_per_post': comments.count() / posts.count(),
            'most_active_members': list(members.values('user').annotate(count=Count('user')).order_by('-count')[:5]),
            'engagement_rate': comments.count() / posts.count(),
            'meeting_attendance_rate': attendance.filter(is_present=True).count() / attendance.count()
        })
        return Response(serializer.data)

class CommunityMemberViewSet(viewsets.ModelViewSet):
    serializer_class = CommunityMemberSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['user__first_name', 'user__last_name', 'role']
    ordering_fields = ['user__first_name', 'user__last_name', 'joined_at', 'is_active']
    ordering = ['user__first_name', 'user__last_name']

    def get_queryset(self):
        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            return CommunityMember.objects.none()
            
        queryset = CommunityMember.objects.filter(school_branch=profile.school_branch)

        # Filter by community
        community_id = self.request.query_params.get('community')
        if community_id:
            queryset = queryset.filter(community_id=community_id)

        return queryset

    def perform_create(self, serializer):
        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            raise PermissionError("User does not have an associated school branch")
            
        serializer.save(school_branch=profile.school_branch)
        serializer.save()

class PostViewSet(viewsets.ModelViewSet):
    serializer_class = PostSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'content', 'author__first_name', 'author__last_name']
    ordering_fields = ['title', 'created_at', 'is_announcement', 'is_pinned']
    ordering = ['-created_at']

    def get_queryset(self):
        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            return Post.objects.none()
            
        queryset = Post.objects.filter(school_branch=profile.school_branch)

        # Filter by community
        community_id = self.request.query_params.get('community')
        if community_id:
            queryset = queryset.filter(community_id=community_id)

        return queryset

    def perform_create(self, serializer):
        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            raise PermissionError("User does not have an associated school branch")
            
        serializer.save(
            author=self.request.user,
            school_branch=profile.school_branch
        )
        serializer.save()

class CommentViewSet(viewsets.ModelViewSet):
    serializer_class = CommentSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['content', 'author__first_name', 'author__last_name']
    ordering_fields = ['created_at', 'is_edited']
    ordering = ['-created_at']

    def get_queryset(self):
        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            return Comment.objects.none()
            
        queryset = Comment.objects.filter(school_branch=profile.school_branch)

        # Filter by post
        post_id = self.request.query_params.get('post')
        if post_id:
            queryset = queryset.filter(post_id=post_id)

        return queryset

    def perform_create(self, serializer):
        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            raise PermissionError("User does not have an associated school branch")
            
        serializer.save(
            author=self.request.user,
            school_branch=profile.school_branch
        )
        serializer.save()

class ClubMeetingViewSet(viewsets.ModelViewSet):
    serializer_class = ClubMeetingSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'description', 'location', 'organizer__first_name', 'organizer__last_name']
    ordering_fields = ['start_time', 'end_time', 'status', 'created_at']
    ordering = ['start_time']

    def get_queryset(self):
        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            return ClubMeeting.objects.none()
            
        queryset = ClubMeeting.objects.filter(school_branch=profile.school_branch)

        # Filter by community
        community_id = self.request.query_params.get('community')
        if community_id:
            queryset = queryset.filter(community_id=community_id)

        return queryset

    def perform_create(self, serializer):
        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            raise PermissionError("User does not have an associated school branch")
            
        serializer.save(
            organizer=self.request.user,
            school_branch=profile.school_branch
        )
        serializer.save()

class MeetingAttendanceViewSet(viewsets.ModelViewSet):
    serializer_class = MeetingAttendanceSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['member__user__first_name', 'member__user__last_name', 'notes']
    ordering_fields = ['created_at', 'is_present']
    ordering = ['-created_at']

    def get_queryset(self):
        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            return MeetingAttendance.objects.none()
            
        queryset = MeetingAttendance.objects.filter(school_branch=profile.school_branch)

        # Filter by meeting
        meeting_id = self.request.query_params.get('meeting')
        if meeting_id:
            queryset = queryset.filter(meeting_id=meeting_id)

        return queryset

    def perform_create(self, serializer):
        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            raise PermissionError("User does not have an associated school branch")
            
        serializer.save(school_branch=profile.school_branch)
        serializer.save()

class ReactionViewSet(viewsets.ModelViewSet):
    serializer_class = ReactionSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['reaction_type', 'user__first_name', 'user__last_name']
    ordering_fields = ['created_at', 'reaction_type']
    ordering = ['-created_at']

    def get_queryset(self):
        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            return Reaction.objects.none()
            
        queryset = Reaction.objects.filter(school_branch=profile.school_branch)

        # Filter by post or comment
        post_id = self.request.query_params.get('post')
        comment_id = self.request.query_params.get('comment')

        if post_id:
            queryset = queryset.filter(post_id=post_id)
        elif comment_id:
            queryset = queryset.filter(comment_id=comment_id)

        return queryset

    def perform_create(self, serializer):
        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            raise PermissionError("User does not have an associated school branch")
            
        serializer.save(
            user=self.request.user,
            school_branch=profile.school_branch
        )
        serializer.save()

class NotificationViewSet(viewsets.ModelViewSet):
    serializer_class = NotificationSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'message', 'notification_type']
    ordering_fields = ['created_at', 'is_read', 'notification_type']
    ordering = ['-created_at']

    def get_queryset(self):
        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            return Notification.objects.none()
            
        # Users should only see their own notifications
        return Notification.objects.filter(
            school_branch=profile.school_branch,
            user=self.request.user
        )

    def perform_create(self, serializer):
        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            raise PermissionError("User does not have an associated school branch")
            
        serializer.save(school_branch=profile.school_branch)
        serializer.save()

    @action(detail=True, methods=['post'])
    def mark_as_read(self, request, pk=None):
        notification = self.get_object()
        notification.mark_as_read()
        return Response({'status': 'notification marked as read'})

    @action(detail=False, methods=['post'])
    def mark_all_as_read(self, request):
        notifications = self.get_queryset().filter(is_read=False)
        count = notifications.count()
        for notification in notifications:
            notification.mark_as_read()
        return Response({'status': f'{count} notifications marked as read'})

# Dashboard View
class CommunityDashboardView(views.APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        # Get school_branch from user's profile
        profile = request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            return Response({'error': 'User does not have an associated school branch'}, status=400)
            
        school_branch = profile.school_branch

        # Get user's communities
        user_memberships = CommunityMember.objects.filter(
            user=request.user,
            school_branch=school_branch,
            is_active=True
        )
        user_communities = [membership.community for membership in user_memberships]

        # Get recent posts from user's communities
        recent_posts = Post.objects.filter(
            community__in=user_communities,
            school_branch=school_branch
        ).order_by('-created_at')[:10]

        # Get upcoming meetings
        upcoming_meetings = ClubMeeting.objects.filter(
            community__in=user_communities,
            school_branch=school_branch,
            start_time__gte=timezone.now(),
            status='SCHEDULED'
        ).order_by('start_time')[:5]

        # Get unread notifications count
        unread_notifications = Notification.objects.filter(
            user=request.user,
            school_branch=school_branch,
            is_read=False
        ).count()

        dashboard_data = {
            'total_communities': Community.objects.filter(school_branch=school_branch).count(),
            'active_communities': Community.objects.filter(school_branch=school_branch, is_active=True).count(),
            'user_communities': CommunitySerializer(user_communities, many=True).data,
            'recent_posts': PostSerializer(recent_posts, many=True, context={'request': request}).data,
            'upcoming_meetings': ClubMeetingSerializer(upcoming_meetings, many=True).data,
            'unread_notifications': unread_notifications
        }

        serializer = CommunityDashboardSerializer(dashboard_data)
        return Response(serializer.data)
