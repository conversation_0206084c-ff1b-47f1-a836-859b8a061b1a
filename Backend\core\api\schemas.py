from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status

# User API Schemas
user_create_schema = swagger_auto_schema(
    operation_description="Create a new user with profile",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['email', 'username', 'password', 'first_name', 'last_name', 'user_type'],
        properties={
            'email': openapi.Schema(type=openapi.TYPE_STRING, format='email'),
            'username': openapi.Schema(type=openapi.TYPE_STRING),
            'password': openapi.Schema(type=openapi.TYPE_STRING, format='password'),
            'confirm_password': openapi.Schema(type=openapi.TYPE_STRING, format='password'),
            'first_name': openapi.Schema(type=openapi.TYPE_STRING),
            'last_name': openapi.Schema(type=openapi.TYPE_STRING),
            'phone': openapi.Schema(type=openapi.TYPE_STRING),
            'user_type': openapi.Schema(type=openapi.TYPE_STRING, enum=[
                'system_admin', 'school_admin', 'deputy_principal', 'branch_admin',
                'department_head', 'ict_admin', 'teacher', 'librarian', 'counselor',
                'accountant', 'secretary', 'nurse', 'maintenance', 'security',
                'driver', 'student', 'parent'
            ]),
            'profile_picture': openapi.Schema(type=openapi.TYPE_STRING, format='binary'),
            'employee_number': openapi.Schema(type=openapi.TYPE_STRING),
            'address': openapi.Schema(type=openapi.TYPE_STRING),
            'is_active': openapi.Schema(type=openapi.TYPE_BOOLEAN),
            'profile': openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'school': openapi.Schema(type=openapi.TYPE_INTEGER),
                    'school_branch': openapi.Schema(type=openapi.TYPE_INTEGER),
                }
            ),
        }
    ),
    responses={
        status.HTTP_201_CREATED: openapi.Response(
            description="User created successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'id': openapi.Schema(type=openapi.TYPE_INTEGER),
                    'email': openapi.Schema(type=openapi.TYPE_STRING),
                    'username': openapi.Schema(type=openapi.TYPE_STRING),
                    'first_name': openapi.Schema(type=openapi.TYPE_STRING),
                    'last_name': openapi.Schema(type=openapi.TYPE_STRING),
                    'user_type': openapi.Schema(type=openapi.TYPE_STRING),
                    'profile': openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'school': openapi.Schema(type=openapi.TYPE_INTEGER),
                            'school_branch': openapi.Schema(type=openapi.TYPE_INTEGER),
                        }
                    ),
                }
            )
        ),
        status.HTTP_400_BAD_REQUEST: "Invalid input data",
    }
)

user_update_schema = swagger_auto_schema(
    operation_description="Update user and profile information",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'email': openapi.Schema(type=openapi.TYPE_STRING, format='email'),
            'username': openapi.Schema(type=openapi.TYPE_STRING),
            'first_name': openapi.Schema(type=openapi.TYPE_STRING),
            'last_name': openapi.Schema(type=openapi.TYPE_STRING),
            'phone': openapi.Schema(type=openapi.TYPE_STRING),
            'user_type': openapi.Schema(type=openapi.TYPE_STRING),
            'profile_picture': openapi.Schema(type=openapi.TYPE_STRING, format='binary'),
            'employee_number': openapi.Schema(type=openapi.TYPE_STRING),
            'address': openapi.Schema(type=openapi.TYPE_STRING),
            'is_active': openapi.Schema(type=openapi.TYPE_BOOLEAN),
            'profile': openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'school': openapi.Schema(type=openapi.TYPE_INTEGER),
                    'school_branch': openapi.Schema(type=openapi.TYPE_INTEGER),
                }
            ),
        }
    ),
    responses={
        status.HTTP_200_OK: "User updated successfully",
        status.HTTP_400_BAD_REQUEST: "Invalid input data",
        status.HTTP_404_NOT_FOUND: "User not found",
    }
)

change_password_schema = swagger_auto_schema(
    operation_description="Change user password",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['old_password', 'new_password'],
        properties={
            'old_password': openapi.Schema(type=openapi.TYPE_STRING, format='password'),
            'new_password': openapi.Schema(type=openapi.TYPE_STRING, format='password'),
        }
    ),
    responses={
        status.HTTP_200_OK: "Password changed successfully",
        status.HTTP_400_BAD_REQUEST: "Invalid old password",
        status.HTTP_404_NOT_FOUND: "User not found",
    }
)

reset_password_schema = swagger_auto_schema(
    operation_description="Request password reset email",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['email'],
        properties={
            'email': openapi.Schema(type=openapi.TYPE_STRING, format='email'),
        }
    ),
    responses={
        status.HTTP_200_OK: "Password reset email sent",
        status.HTTP_404_NOT_FOUND: "User not found",
    }
)

activate_user_schema = swagger_auto_schema(
    operation_description="Activate a user account",
    responses={
        status.HTTP_200_OK: "User activated successfully",
        status.HTTP_404_NOT_FOUND: "User not found",
    }
)

deactivate_user_schema = swagger_auto_schema(
    operation_description="Deactivate a user account",
    responses={
        status.HTTP_200_OK: "User deactivated successfully",
        status.HTTP_404_NOT_FOUND: "User not found",
    }
) 