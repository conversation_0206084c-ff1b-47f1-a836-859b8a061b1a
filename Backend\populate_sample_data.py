#!/usr/bin/env python
"""
Sample Data Population Script

This script populates the system with realistic sample data so users
can see meaningful content in their profiles and modules.
"""

import os
import sys
import django
from datetime import date, timedelta
import random

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
    django.setup()

def create_sample_teachers():
    """Create sample teachers with complete profiles"""
    print("👨‍🏫 Creating sample teachers...")
    
    try:
        from core.models import CustomUser
        from users.models import Teacher
        from schools.models import School, SchoolBranch
        from academics.models import Department
        from django.db.models.signals import post_save
        from users.signals import save_user_role_profile
        
        school = School.objects.first()
        if not school:
            print("  ❌ No school found")
            return False
            
        school_branch = school.branch.first()
        department = Department.objects.first()
        
        teachers_data = [
            {
                'first_name': '<PERSON>',
                'last_name': '<PERSON><PERSON><PERSON>',
                'email': '<EMAIL>',
                'phone': '+254712345678',
                'national_id': '12345678',
                'date_of_birth': date(1985, 3, 15),
                'gender': 'M'
            },
            {
                'first_name': 'Mary',
                'last_name': 'Wanjiku',
                'email': '<EMAIL>',
                'phone': '+254723456789',
                'national_id': '23456789',
                'date_of_birth': date(1988, 7, 22),
                'gender': 'F'
            },
            {
                'first_name': 'Peter',
                'last_name': 'Kiprotich',
                'email': '<EMAIL>',
                'phone': '+254734567890',
                'national_id': '34567890',
                'date_of_birth': date(1982, 11, 8),
                'gender': 'M'
            }
        ]
        
        created_count = 0
        
        for teacher_data in teachers_data:
            # Check if user already exists
            if CustomUser.objects.filter(email=teacher_data['email']).exists():
                print(f"  ⚠️  Teacher {teacher_data['email']} already exists")
                continue
            
            # Temporarily disconnect the signal to avoid conflicts
            post_save.disconnect(save_user_role_profile, sender=CustomUser)
            
            try:
                # Create user
                user = CustomUser.objects.create_user(
                    username=teacher_data['email'],
                    email=teacher_data['email'],
                    password='teacher123',
                    first_name=teacher_data['first_name'],
                    last_name=teacher_data['last_name'],
                    user_type='teacher',
                    phone=teacher_data['phone']
                )
                user.school_branch = school_branch
                user.save()
                
                # Create teacher profile manually
                teacher = Teacher.objects.create(
                    user=user,
                    school_branch=school_branch,
                    teacher_number=f'TCH{random.randint(1000, 9999)}',
                    date_of_birth=teacher_data['date_of_birth'],
                    national_id=teacher_data['national_id'],
                    phone_number=teacher_data['phone'],
                    email=teacher_data['email'],
                    gender=teacher_data['gender'],
                    is_class_teacher=random.choice([True, False])
                )
                
                created_count += 1
                print(f"  ✅ Created teacher: {teacher_data['first_name']} {teacher_data['last_name']}")
                
            finally:
                # Reconnect the signal
                post_save.connect(save_user_role_profile, sender=CustomUser)
        
        print(f"  📊 Created {created_count} teachers")
        return created_count > 0
        
    except Exception as e:
        print(f"  ❌ Error creating teachers: {e}")
        return False

def create_sample_students():
    """Create sample students with complete profiles"""
    print("👨‍🎓 Creating sample students...")
    
    try:
        from core.models import CustomUser
        from users.models import Student
        from schools.models import School, SchoolBranch
        from academics.models import ClassRoom
        from django.db.models.signals import post_save
        from users.signals import save_user_role_profile
        
        school = School.objects.first()
        if not school:
            print("  ❌ No school found")
            return False
            
        school_branch = school.branch.first()
        classroom = ClassRoom.objects.first()
        
        students_data = [
            {
                'first_name': 'Alice',
                'last_name': 'Njeri',
                'email': '<EMAIL>',
                'phone': '+254745678901',
                'date_of_birth': date(2008, 5, 12),
                'gender': 'F'
            },
            {
                'first_name': 'David',
                'last_name': 'Ochieng',
                'email': '<EMAIL>',
                'phone': '+254756789012',
                'date_of_birth': date(2007, 9, 18),
                'gender': 'M'
            },
            {
                'first_name': 'Grace',
                'last_name': 'Mutua',
                'email': '<EMAIL>',
                'phone': '+254767890123',
                'date_of_birth': date(2008, 2, 25),
                'gender': 'F'
            },
            {
                'first_name': 'Brian',
                'last_name': 'Kipchoge',
                'email': '<EMAIL>',
                'phone': '+254778901234',
                'date_of_birth': date(2007, 12, 3),
                'gender': 'M'
            }
        ]
        
        created_count = 0
        
        for student_data in students_data:
            # Check if user already exists
            if CustomUser.objects.filter(email=student_data['email']).exists():
                print(f"  ⚠️  Student {student_data['email']} already exists")
                continue
            
            # Temporarily disconnect the signal to avoid conflicts
            post_save.disconnect(save_user_role_profile, sender=CustomUser)
            
            try:
                # Create user
                user = CustomUser.objects.create_user(
                    username=student_data['email'],
                    email=student_data['email'],
                    password='student123',
                    first_name=student_data['first_name'],
                    last_name=student_data['last_name'],
                    user_type='student',
                    phone=student_data['phone']
                )
                user.school_branch = school_branch
                user.save()
                
                # Create student profile manually
                student = Student.objects.create(
                    user=user,
                    school_branch=school_branch,
                    admission_number=f'STD{random.randint(1000, 9999)}',
                    date_of_birth=student_data['date_of_birth'],
                    gender=student_data['gender'],
                    phone_number=student_data['phone'],
                    current_class=classroom,
                    class_name=classroom,
                    address=f"{random.randint(100, 999)} Sample Street, Nairobi"
                )
                
                created_count += 1
                print(f"  ✅ Created student: {student_data['first_name']} {student_data['last_name']}")
                
            finally:
                # Reconnect the signal
                post_save.connect(save_user_role_profile, sender=CustomUser)
        
        print(f"  📊 Created {created_count} students")
        return created_count > 0
        
    except Exception as e:
        print(f"  ❌ Error creating students: {e}")
        return False

def create_sample_academic_data():
    """Create additional academic data"""
    print("📚 Creating sample academic data...")
    
    try:
        from academics.models import Subject, ClassRoom, Stream, Department, AcademicYear
        from academics.curriculum_models import CurriculumSystem, EducationLevel
        from schools.models import School
        
        school = School.objects.first()
        if not school:
            print("  ❌ No school found")
            return False
            
        school_branch = school.branch.first()
        
        # Create more subjects
        curriculum = CurriculumSystem.objects.first()
        department = Department.objects.first()
        
        if curriculum and department:
            subjects_data = [
                {'name': 'Mathematics', 'code': 'MATH001', 'short_name': 'Math'},
                {'name': 'English Language', 'code': 'ENG001', 'short_name': 'English'},
                {'name': 'Kiswahili', 'code': 'KIS001', 'short_name': 'Kiswahili'},
                {'name': 'Science', 'code': 'SCI001', 'short_name': 'Science'},
                {'name': 'Social Studies', 'code': 'SST001', 'short_name': 'Social Studies'},
                {'name': 'Creative Arts', 'code': 'ART001', 'short_name': 'Arts'},
                {'name': 'Physical Education', 'code': 'PE001', 'short_name': 'PE'},
            ]
            
            created_subjects = 0
            academic_year = AcademicYear.objects.first()
            
            for subject_data in subjects_data:
                subject, created = Subject.objects.get_or_create(
                    name=subject_data['name'],
                    code=subject_data['code'],
                    curriculum_system=curriculum,
                    department=department,
                    school=school,
                    school_branch=school_branch,
                    academic_year=academic_year,
                    defaults={
                        'short_name': subject_data['short_name'],
                        'description': f'{subject_data["name"]} curriculum for primary education',
                        'is_compulsory': True
                    }
                )
                
                if created:
                    created_subjects += 1
                    print(f"  ✅ Created subject: {subject_data['name']}")
            
            print(f"  📊 Created {created_subjects} subjects")
        
        # Create more streams
        streams_data = [
            {'name': 'Blue Stream', 'code': 'BLUE'},
            {'name': 'Red Stream', 'code': 'RED'},
            {'name': 'Green Stream', 'code': 'GREEN'},
        ]
        
        created_streams = 0
        for stream_data in streams_data:
            stream, created = Stream.objects.get_or_create(
                name=stream_data['name'],
                code=stream_data['code'],
                school=school,
                defaults={'description': f'{stream_data["name"]} for academic organization'}
            )
            
            if created:
                created_streams += 1
                print(f"  ✅ Created stream: {stream_data['name']}")
        
        print(f"  📊 Created {created_streams} streams")
        return True
        
    except Exception as e:
        print(f"  ❌ Error creating academic data: {e}")
        return False

def main():
    """Run all sample data creation"""
    print("🚀 ShuleXcel Sample Data Population")
    print("=" * 50)
    
    setup_django()
    
    tasks = [
        create_sample_teachers,
        create_sample_students,
        create_sample_academic_data
    ]
    
    completed = 0
    total = len(tasks)
    
    for task in tasks:
        try:
            if task():
                completed += 1
        except Exception as e:
            print(f"  ❌ Task failed with exception: {e}")
        print()
    
    print("=" * 50)
    print(f"📊 Sample Data Population Results: {completed}/{total} tasks completed")
    
    if completed == total:
        print("🎉 Sample data population completed successfully!")
        print("\n✅ Users will now see meaningful content in:")
        print("   - Teacher Profiles with complete information")
        print("   - Student Profiles with class assignments")
        print("   - Academic modules with subjects and streams")
        print("   - Dashboard with real user counts")
        print("\n🚀 MVP is now ready with rich sample data!")
    else:
        print(f"⚠️  {total - completed} tasks had issues")
        print("Some sample data may be missing")
    
    return completed >= total * 0.75

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
