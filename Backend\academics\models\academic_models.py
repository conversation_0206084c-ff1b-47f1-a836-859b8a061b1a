from django.db import models
from django.utils import timezone
from django.core.mail import send_mail
from django.apps import apps
from datetime import timedelta
from schools.models import School
from users.models import Teacher, Student, Parent
from decimal import Decimal
from django.utils.translation import gettext_lazy as _

# Import curriculum models
from ..curriculum_models import CurriculumSystem, EducationLevel, SchoolCurriculumConfig, BranchCurriculumConfig

# Import template models
from ..academic_templates import AcademicYearTemplate, TermTemplate

# Base model for all models
class BaseModel(models.Model):
    """
    Base model for all models in the academics app
    """
    created_at = models.DateTimeField("Created At", auto_now_add=True)
    updated_at = models.DateTimeField("Updated At", auto_now=True)

    class Meta:
        abstract = True

class AcademicYear(models.Model):
    school_name = models.ForeignKey('schools.School', on_delete=models.CASCADE, db_index=True)
    school_branch = models.ForeignKey('schools.SchoolBranch', on_delete=models.CASCADE, null=True, blank=True, related_name='academic_years', db_index=True)
    year = models.CharField(max_length=9, db_index=True)  # e.g., "2023-2024"
    start_date = models.DateField(db_index=True)
    end_date = models.DateField()
    is_current = models.BooleanField(default=False, db_index=True)
    is_archived = models.BooleanField(default=False, db_index=True)
    is_template = models.BooleanField(default=False, db_index=True, help_text="Whether this academic year is a template that can be applied to multiple branches")

    # Template relationship
    template = models.ForeignKey(AcademicYearTemplate, on_delete=models.SET_NULL, null=True, blank=True, related_name='academic_years')
    is_customized = models.BooleanField(default=False, help_text="Whether this academic year has been customized from its template")

    class Meta:
        unique_together = ['year', 'school_branch']
        ordering = ['-start_date']
        indexes = [
            models.Index(fields=['school_name', 'is_current']),
            models.Index(fields=['school_branch', 'is_current']),
            models.Index(fields=['is_archived', 'is_current']),
        ]

    def __str__(self):
        if self.school_branch:
            return f"{self.year} - {self.school_branch.name}"
        return f"{self.year} - {self.school_name.name}"

    def save(self, *args, **kwargs):
        # If this academic year is being set as current, unset others for the same branch
        if self.is_current:
            if self.school_branch:
                AcademicYear.objects.filter(
                    school_branch=self.school_branch,
                    is_current=True
                ).exclude(pk=self.pk).update(is_current=False)
            else:
                AcademicYear.objects.filter(
                    school_name=self.school_name,
                    is_current=True
                ).exclude(pk=self.pk).update(is_current=False)
        super().save(*args, **kwargs)

class Department(models.Model):
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=10)
    school = models.ForeignKey('schools.School', on_delete=models.CASCADE)
    school_branch = models.ForeignKey('schools.SchoolBranch', on_delete=models.CASCADE, related_name='departments')
    head_of_department = models.ForeignKey(
        'users.Teacher',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='headed_department'
    )
    department_teacher = models.ManyToManyField('users.Teacher', related_name='department_teacher', blank=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'academics'
        unique_together = ['code', 'school', 'school_branch']
        ordering = ['name']

    def __str__(self):
        return f"{self.name} - {self.school.name}"

class Stream(models.Model):
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=10, unique=True)
    school = models.ForeignKey('schools.School', on_delete=models.CASCADE)
    description = models.TextField(blank=True)

    class Meta:
        app_label = 'academics'

    def __str__(self):
        return self.name

class ClassRoom(models.Model):
    """
    Flexible class model supporting all educational systems (CBC, 8-4-4, IGCSE, etc.)
    """
    # Basic Information
    name = models.CharField(max_length=100, help_text="e.g., 'Grade 4', 'Form 2', 'Year 10'")
    display_name = models.CharField(max_length=100, blank=True, help_text="Human-readable name")

    # Educational System Integration
    education_level = models.ForeignKey(
        EducationLevel,
        on_delete=models.CASCADE,
        help_text="Links to curriculum-specific education level (Grade 4 CBC, Form 2 8-4-4, etc.)"
    )

    # Legacy support for systems that use numeric grade levels
    numeric_level = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Numeric representation for sorting (1-12 for most systems)"
    )

    # School Context
    academic_year = models.ForeignKey('AcademicYear', on_delete=models.CASCADE)
    school = models.ForeignKey('schools.School', on_delete=models.CASCADE)
    school_branch = models.ForeignKey(
        'schools.SchoolBranch',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='classes'
    )

    # Class Organization
    stream = models.ForeignKey('Stream', on_delete=models.CASCADE, related_name='classes')
    class_teacher = models.ForeignKey(
        'users.Teacher',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_classes'
    )

    # Capacity and Status
    max_capacity = models.PositiveIntegerField(default=40)
    current_enrollment = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)

    # Relationships
    subjects = models.ManyToManyField('Subject', related_name='assigned_classes', blank=True)
    students = models.ManyToManyField('users.Student', related_name='enrolled_classes', blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'academics'
        verbose_name_plural = "Class Rooms"
        unique_together = (
            ('name', 'stream', 'academic_year', 'school_branch'),
            ('education_level', 'stream', 'academic_year', 'school_branch'),
        )
        ordering = ['education_level__sequence', 'stream__name']

    def save(self, *args, **kwargs):
        # Auto-generate display_name if not provided
        if not self.display_name:
            self.display_name = f"{self.name} {self.stream.name}"

        # Set numeric_level based on education_level sequence if not provided
        if not self.numeric_level and self.education_level:
            self.numeric_level = self.education_level.sequence

        super().save(*args, **kwargs)

        # Update current enrollment
        self.update_enrollment_count()

    def update_enrollment_count(self):
        """Update the current enrollment count"""
        self.current_enrollment = self.students.count()
        ClassRoom.objects.filter(pk=self.pk).update(current_enrollment=self.current_enrollment)

    def get_next_class_options(self):
        """Get possible next classes based on curriculum progression rules"""
        if not self.education_level:
            return []

        # Get progression rules for this education level
        rules = apps.get_model('academics', 'ClassProgressionRule').objects.filter(
            current_level=self.education_level,
            school=self.school
        )

        # If no rules exist, try to find the next sequential education level
        if not rules.exists():
            next_sequence = self.education_level.sequence + 1
            next_levels = EducationLevel.objects.filter(
                curriculum_system=self.education_level.curriculum_system,
                sequence=next_sequence
            )

            if next_levels.exists():
                return [{
                    'education_level': level,
                    'is_default': True,
                    'requirements': None,
                    'curriculum_system': level.curriculum_system.name
                } for level in next_levels]
            return []

        # Return options based on rules
        return [{
            'education_level': rule.next_level,
            'is_default': rule.is_default_progression,
            'requirements': rule.requirements,
            'curriculum_system': rule.next_level.curriculum_system.name
        } for rule in rules]

    def get_curriculum_system(self):
        """Get the curriculum system for this class"""
        return self.education_level.curriculum_system if self.education_level else None

    def is_at_capacity(self):
        """Check if class is at maximum capacity"""
        return self.current_enrollment >= self.max_capacity

    def get_available_spots(self):
        """Get number of available spots in the class"""
        return max(0, self.max_capacity - self.current_enrollment)

    def __str__(self):
        if self.school_branch:
            return f"{self.display_name or self.name} - {self.school_branch.name} ({self.academic_year.year})"
        return f"{self.display_name or self.name} - {self.school.name} ({self.academic_year.year})"

class Term(models.Model):
    name = models.CharField(max_length=100)
    academic_year = models.ForeignKey('AcademicYear', on_delete=models.CASCADE, related_name='terms')
    school = models.ForeignKey('schools.School', on_delete=models.CASCADE)
    school_branch = models.ForeignKey('schools.SchoolBranch', on_delete=models.CASCADE, null=True, blank=True, related_name='terms')
    start_date = models.DateField()
    end_date = models.DateField()
    is_current = models.BooleanField(default=False)
    is_archived = models.BooleanField(default=False)

    # Template relationship
    template = models.ForeignKey(TermTemplate, on_delete=models.SET_NULL, null=True, blank=True, related_name='terms')
    is_customized = models.BooleanField(default=False, help_text="Whether this term has been customized from its template")

    class Meta:
        ordering = ['-academic_year', 'start_date']
        unique_together = ['name', 'academic_year', 'school_branch']

    def __str__(self):
        return f"{self.name} - {self.academic_year.year}"

    def save(self, *args, **kwargs):
        # If this term is being set as current, unset others for the same branch/school
        if self.is_current:
            if self.school_branch:
                Term.objects.filter(
                    school_branch=self.school_branch,
                    is_current=True
                ).exclude(pk=self.pk).update(is_current=False)
            else:
                Term.objects.filter(
                    school=self.school,
                    is_current=True
                ).exclude(pk=self.pk).update(is_current=False)
        super().save(*args, **kwargs)

    def get_branch(self):
        """Get the school branch for this term"""
        return self.school_branch or self.academic_year.school_branch

class TimeSlot(models.Model):
    start_time = models.TimeField()
    end_time = models.TimeField()
    day_of_week = models.IntegerField(choices=[
        (0, 'Monday'),
        (1, 'Tuesday'),
        (2, 'Wednesday'),
        (3, 'Thursday'),
        (4, 'Friday'),
        (5, 'Saturday'),
        (6, 'Sunday'),
    ])

    class Meta:
        app_label = 'academics'
        ordering = ['day_of_week', 'start_time']

    def __str__(self):
        return f"{self.get_day_of_week_display()} {self.start_time}-{self.end_time}"

class GradingSystem(models.Model):
    """
    Flexible grading system supporting all educational systems
    """
    GRADING_TYPES = [
        ('letter', 'Letter Grades (A, B, C, D, E)'),
        ('percentage', 'Percentage Based'),
        ('points', 'Points Based'),
        ('competency', 'Competency Based (CBC)'),
        ('mixed', 'Mixed System'),
    ]

    name = models.CharField(max_length=100)
    grading_type = models.CharField(max_length=20, choices=GRADING_TYPES, default='letter')

    # Curriculum system integration
    curriculum_system = models.ForeignKey(
        CurriculumSystem,
        on_delete=models.CASCADE,
        help_text="Curriculum system this grading applies to"
    )

    # School context
    school = models.ForeignKey('schools.School', on_delete=models.CASCADE)
    academic_year = models.ForeignKey('AcademicYear', on_delete=models.CASCADE)

    # Applicable contexts
    education_levels = models.ManyToManyField(
        EducationLevel,
        blank=True,
        help_text="Education levels this grading system applies to"
    )
    terms = models.ManyToManyField('Term', related_name='grading_systems', blank=True)
    classes = models.ManyToManyField('ClassRoom', related_name='grading_systems', blank=True)

    # Configuration
    description = models.TextField(blank=True)
    max_score = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('100.00'))
    min_score = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'))
    pass_mark = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('50.00'))

    # CBC specific fields
    uses_competency_levels = models.BooleanField(
        default=False,
        help_text="Whether this system uses CBC competency levels"
    )
    competency_config = models.JSONField(
        default=dict,
        blank=True,
        help_text="Configuration for competency-based assessment"
    )

    # Status
    is_active = models.BooleanField(default=True)
    is_default = models.BooleanField(default=False)

    # Metadata
    created_by = models.ForeignKey('users.Teacher', on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'academics'
        unique_together = ['name', 'school', 'academic_year', 'curriculum_system']
        ordering = ['curriculum_system', 'name']

    def save(self, *args, **kwargs):
        # Ensure only one default grading system per curriculum per school
        if self.is_default:
            GradingSystem.objects.filter(
                school=self.school,
                academic_year=self.academic_year,
                curriculum_system=self.curriculum_system,
                is_default=True
            ).exclude(pk=self.pk).update(is_default=False)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} - {self.curriculum_system.name}"

class GradeScale(models.Model):
    grading_system = models.ForeignKey('GradingSystem', on_delete=models.CASCADE, related_name='grades')
    grade = models.CharField(max_length=2)  # A, A-, B+, etc.
    min_score = models.DecimalField(max_digits=5, decimal_places=2)
    max_score = models.DecimalField(max_digits=5, decimal_places=2)
    points = models.DecimalField(max_digits=3, decimal_places=1)  # e.g., 12.0, 11.5, 11.0
    remarks = models.CharField(max_length=100, blank=True)  # e.g., "Excellent", "Very Good"

    class Meta:
        app_label = 'academics'
        ordering = ['-min_score']
        unique_together = ['grading_system', 'grade']

    def __str__(self):
        return f"{self.grade} ({self.min_score}-{self.max_score})"

class Subject(models.Model):
    """
    Flexible subject model supporting all educational systems
    """
    SUBJECT_TYPES = [
        ('core', 'Core Subject'),
        ('optional', 'Optional Subject'),
        ('elective', 'Elective Subject'),
        ('co_curricular', 'Co-curricular Activity'),
        ('life_skills', 'Life Skills'),
    ]

    # Basic Information
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20)  # Increased length for curriculum codes
    short_name = models.CharField(max_length=20, blank=True)
    description = models.TextField(blank=True)

    # Curriculum Integration
    curriculum_system = models.ForeignKey(
        CurriculumSystem,
        on_delete=models.CASCADE,
        help_text="Curriculum system this subject belongs to"
    )
    education_levels = models.ManyToManyField(
        EducationLevel,
        help_text="Education levels where this subject is offered"
    )

    # Subject Classification
    subject_type = models.CharField(max_length=20, choices=SUBJECT_TYPES, default='core')
    department = models.ForeignKey('Department', on_delete=models.CASCADE)

    # School Context
    school = models.ForeignKey('schools.School', on_delete=models.CASCADE)
    school_branch = models.ForeignKey(
        'schools.SchoolBranch',
        on_delete=models.CASCADE,
        related_name='subjects',
        null=True,
        blank=True
    )

    # Academic Context
    academic_year = models.ForeignKey('AcademicYear', on_delete=models.CASCADE)

    # Subject Configuration
    is_compulsory = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    credit_hours = models.PositiveIntegerField(default=1)

    # Assessment Configuration
    grading_system = models.ForeignKey(
        'GradingSystem',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Subject-specific grading system"
    )

    # CBC Specific
    learning_areas = models.JSONField(
        default=list,
        blank=True,
        help_text="CBC learning areas for this subject"
    )
    competencies = models.JSONField(
        default=list,
        blank=True,
        help_text="Key competencies for this subject"
    )

    # Relationships
    teachers = models.ManyToManyField(
        'users.Teacher',
        related_name='taught_subjects',
        blank=True
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'academics'
        unique_together = [
            ['code', 'curriculum_system', 'school'],
            ['name', 'curriculum_system', 'academic_year', 'school_branch']
        ]
        ordering = ['curriculum_system', 'name']

    def __str__(self):
        return f"{self.name} ({self.code})"

    def save(self, *args, **kwargs):
        # Check if this is a new subject or if is_compulsory has changed
        is_new = self.pk is None
        if not is_new:
            old_instance = Subject.objects.get(pk=self.pk)
            is_compulsory_changed = old_instance.is_compulsory != self.is_compulsory
        else:
            is_compulsory_changed = False

        # Generate code if not provided
        if not self.code:
            self.code = self._generate_subject_code()

        super().save(*args, **kwargs)

        # If this is a compulsory subject, ensure all students in applicable classes are enrolled
        if is_new or is_compulsory_changed:
            if self.is_compulsory:
                self.ensure_all_students_enrolled()

    def ensure_all_students_enrolled(self):
        """Ensure all students in applicable classes are enrolled in this subject"""
        applicable_classes = self.get_applicable_classes()
        for class_room in applicable_classes:
            for student in class_room.students.all():
                if not student.subjects.filter(pk=self.pk).exists():
                    student.subjects.add(self)

    def _generate_subject_code(self):
        """Generate a unique subject code"""
        base_code = self.name[:3].upper()
        counter = 1
        code = base_code
        while Subject.objects.filter(code=code).exists():
            code = f"{base_code}{counter}"
            counter += 1
        return code

    def get_applicable_classes(self):
        """Get all classes where this subject is applicable"""
        return ClassRoom.objects.filter(
            education_level__in=self.education_levels.all(),
            academic_year=self.academic_year,
            school_branch=self.school_branch
        )

    def get_grading_system(self):
        """Get the grading system for this subject"""
        if self.grading_system:
            return self.grading_system
        return GradingSystem.objects.filter(
            curriculum_system=self.curriculum_system,
            school=self.school,
            academic_year=self.academic_year,
            is_default=True
        ).first()

class StreamGradingSystem(models.Model):
    stream = models.ForeignKey('Stream', on_delete=models.CASCADE)
    grading_system = models.ForeignKey('GradingSystem', on_delete=models.CASCADE)
    term = models.ForeignKey('Term', on_delete=models.CASCADE)
    min_average = models.DecimalField(max_digits=5, decimal_places=2, help_text="Minimum expected average for the stream")
    target_average = models.DecimalField(max_digits=5, decimal_places=2, help_text="Target average for the stream")

    class Meta:
        app_label = 'academics'
        unique_together = ['stream', 'term']

    def __str__(self):
        return f"{self.stream.name} - {self.term.name}"

    def calculate_improvement(self):
        """Calculate the improvement needed to reach target average"""
        if self.min_average and self.target_average:
            return self.target_average - self.min_average
        return None

    def save(self, *args, **kwargs):
        if self.min_average and self.target_average and self.min_average > self.target_average:
            raise ValueError("Minimum average cannot be greater than target average")
        super().save(*args, **kwargs)

class TeacherDepartmentAssignment(models.Model):
    """Model to manage teacher assignments to departments."""
    teacher = models.ForeignKey(Teacher, on_delete=models.CASCADE, related_name='academic_department_assignments')
    department = models.ForeignKey('Department', on_delete=models.CASCADE, related_name='teacher_assignments')
    is_head = models.BooleanField(default=False, help_text=_('Whether this teacher is the head of the department'))
    date_assigned = models.DateField(auto_now_add=True)
    date_ended = models.DateField(null=True, blank=True)
    notes = models.TextField(blank=True, null=True)

    class Meta:
        app_label = 'academics'
        verbose_name = 'Teacher Department Assignment'
        verbose_name_plural = 'Teacher Department Assignments'
        unique_together = ('teacher', 'department')
        ordering = ['-date_assigned']

    def __str__(self):
        role = 'Head' if self.is_head else 'Member'
        return f"{self.teacher.get_full_name()} - {self.department.name} ({role})" 