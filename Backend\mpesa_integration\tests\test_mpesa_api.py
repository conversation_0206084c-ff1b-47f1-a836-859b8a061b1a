from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from schools.models import School, SchoolBranch
from mpesa_integration.models import MpesaCredential, MpesaTransaction
from fees.models import FeeStructure, FeeType, FeePayment
from academics.models import Term

User = get_user_model()

class MpesaAPITests(TestCase):
    def setUp(self):
        # Create test school
        self.school = School.objects.create(name="Test School", code="TS001")
        self.branch = SchoolBranch.objects.create(
            name="Main Branch",
            school=self.school,
            address="123 Test St",
            phone="1234567890"
        )
        
        # Create test user
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
            school_branch=self.branch
        )
        
        # Create MPESA credentials
        self.credentials = MpesaCredential.objects.create(
            school=self.school,
            consumer_key='test_consumer_key',
            consumer_secret='test_consumer_secret',
            shortcode='174379',
            passkey='test_passkey',
            environment='sandbox',
            is_active=True
        )
        
        # Create test term
        self.term = Term.objects.create(
            name="Term 1 2025",
            start_date="2025-01-01",
            end_date="2025-04-30",
            school=self.school
        )
        
        # Create test fee structure
        self.fee_structure = FeeStructure.objects.create(
            name="2025 Fee Structure",
            school=self.school,
            academic_year="2025",
            term=self.term,
            total_amount=50000.00,
            is_active=True
        )
        
        # Create test fee type
        self.fee_type = FeeType.objects.create(
            name="Tuition Fee",
            school=self.school,
            description="Regular tuition fee",
            amount=30000.00,
            is_mandatory=True,
            fee_structure=self.fee_structure
        )
        
        # Set up API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
    
    def test_mpesa_credentials_list(self):
        """Test retrieving MPESA credentials"""
        url = reverse('mpesa-credentials-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['school'], self.school.id)
    
    def test_stk_push_request(self):
        """Test initiating STK push request"""
        url = reverse('mpesa-stk-push')
        data = {
            'phone_number': '254712345678',
            'amount': 1000.00,
            'reference': 'TEST123',
            'description': 'Test payment'
        }
        
        # Mock the requests.post response
        with patch('requests.post') as mock_post, patch('requests.get') as mock_get:
            # Mock access token response
            mock_get.return_value.json.return_value = {'access_token': 'test_token'}
            
            # Mock STK push response
            mock_post.return_value.json.return_value = {
                'CheckoutRequestID': 'ws_CO_123456789',
                'MerchantRequestID': 'merc_123456789',
                'ResponseCode': '0',
                'ResponseDescription': 'Success. Request accepted for processing'
            }
            
            response = self.client.post(url, data, format='json')
            
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['ResponseCode'], '0')
        
        # Check that transaction was created
        transaction = MpesaTransaction.objects.first()
        self.assertIsNotNone(transaction)
        self.assertEqual(transaction.phone_number, '254712345678')
        self.assertEqual(float(transaction.amount), 1000.00)
        self.assertEqual(transaction.reference, 'TEST123')
        self.assertEqual(transaction.checkout_request_id, 'ws_CO_123456789')
    
    def test_transaction_status_request(self):
        """Test checking transaction status"""
        # Create a test transaction
        transaction = MpesaTransaction.objects.create(
            transaction_type='STK_PUSH',
            phone_number='254712345678',
            amount=1000.00,
            reference='TEST123',
            description='Test payment',
            transaction_id='MPESA123456789',
            status='PENDING',
            school_branch=self.branch
        )
        
        url = reverse('mpesa-transaction-status')
        data = {
            'transaction_id': 'MPESA123456789'
        }
        
        # Mock the requests.post response
        with patch('requests.post') as mock_post, patch('requests.get') as mock_get:
            # Mock access token response
            mock_get.return_value.json.return_value = {'access_token': 'test_token'}
            
            # Mock transaction status response
            mock_post.return_value.json.return_value = {
                'ResponseCode': '0',
                'ResponseDescription': 'Success',
                'ResultCode': '0',
                'ResultDesc': 'The service request has been accepted successfully'
            }
            
            response = self.client.post(url, data, format='json')
            
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['ResponseCode'], '0')
