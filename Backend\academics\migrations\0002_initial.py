# Generated by Django 5.2.1 on 2025-06-01 13:25

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('academics', '0001_initial'),
        ('schools', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='academicyear',
            name='school_branch',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='academic_years', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='academicyear',
            name='school_name',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='schools.school'),
        ),
        migrations.AddField(
            model_name='academicyeartemplate',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_templates', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='academicyeartemplate',
            name='school',
            field=models.ForeignKey(blank=True, help_text='Optional for global templates', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='academic_year_templates', to='schools.school'),
        ),
        migrations.AddField(
            model_name='academicyear',
            name='template',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='academic_years', to='academics.academicyeartemplate'),
        ),
        migrations.AddField(
            model_name='assessment',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_assessments', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='assessmentresult',
            name='assessment',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='results', to='academics.assessment'),
        ),
        migrations.AddField(
            model_name='assessmentresult',
            name='graded_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='graded_results', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='assessmentresult',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assessment_results', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='branchcurriculumconfig',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='curriculum_config', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='careerpathsubjectrequirement',
            name='career_path',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.careerpath'),
        ),
        migrations.AddField(
            model_name='classprogressionrule',
            name='school',
            field=models.ForeignKey(help_text='School-specific progression rules', on_delete=django.db.models.deletion.CASCADE, to='schools.school'),
        ),
        migrations.AddField(
            model_name='classroom',
            name='academic_year',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.academicyear'),
        ),
    ]
