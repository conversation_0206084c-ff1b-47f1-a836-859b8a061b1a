from django.db.models import Avg, Count, Sum
from django.db.models.functions import TruncMonth
from datetime import datetime, timedelta

class BranchAnalyticsService:
    def __init__(self, school_branch):
        self.school_branch = school_branch

    def calculate_fee_metrics(self):
        from fees.models import FeePayment
        current_term = self.school_branch.school.terms.filter(is_current=True).first()
        
        total_expected = self.school_branch.fee_structures.filter(
            term=current_term
        ).aggregate(Sum('total_amount'))['total_amount__sum'] or 0
        
        total_collected = FeePayment.objects.filter(
            student__school_branch=self.school_branch,
            term=current_term
        ).aggregate(Sum('amount'))['amount__sum'] or 0
        
        collection_rate = (total_collected / total_expected * 100) if total_expected > 0 else 0
        
        return {
            'collection_rate': collection_rate,
            'total_expected': total_expected,
            'total_collected': total_collected
        }

    def calculate_performance_metrics(self):
        from academics.models import ExamResult
        current_term = self.school_branch.school.terms.filter(is_current=True).first()
        
        results = ExamResult.objects.filter(
            student__school_branch=self.school_branch,
            exam__term=current_term
        )
        
        return {
            'average_score': results.aggregate(Avg('score'))['score__avg'] or 0,
            'performance_by_subject': self._get_subject_performance(results),
            'performance_by_class': self._get_class_performance(results)
        }

    def get_enrollment_metrics(self):
        current_year = datetime.now().year
        monthly_enrollments = self.school_branch.students.filter(
            admission_date__year=current_year
        ).annotate(
            month=TruncMonth('admission_date')
        ).values('month').annotate(
            count=Count('id')
        ).order_by('month')
        
        return {
            'total_students': self.school_branch.students.count(),
            'monthly_enrollments': list(monthly_enrollments)
        }
