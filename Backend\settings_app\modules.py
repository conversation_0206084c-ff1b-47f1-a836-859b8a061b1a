"""
Module management system for ShuleXcel.
This file defines all available modules and their dependencies.
"""

# Module development status options
MODULE_STATUS = {
    'DEVELOPMENT': 'development',
    'LIVE': 'live'
}

# Define all available modules
AVAILABLE_MODULES = {
    # Core modules (always enabled)
    'core': {
        'name': 'Core',
        'description': 'Core functionality including authentication, user management, and basic school setup',
        'is_core': True,
        'dependencies': [],
        'app_names': ['core', 'users', 'schools'],
        'status': MODULE_STATUS['LIVE'],
    },
    'academics': {
        'name': 'Academics',
        'description': 'Academic management including curriculum, classes, subjects, and grading',
        'is_core': True,
        'dependencies': [],
        'app_names': ['academics', 'syllabus'],
        'status': MODULE_STATUS['LIVE'],
    },
    'dashboard': {
        'name': 'Dashboard',
        'description': 'Main dashboard and reporting',
        'is_core': True,
        'dependencies': [],
        'app_names': ['dashboard'],
        'status': MODULE_STATUS['LIVE'],
    },

    # Optional modules
    'fees': {
        'name': 'Fees & Finance',
        'description': 'Fee management, invoicing, and financial reporting',
        'is_core': False,
        'dependencies': [],
        'app_names': ['fees'],
        'status': MODULE_STATUS['LIVE'],
    },
    'fleet': {
        'name': 'Fleet Management',
        'description': 'Vehicle management, routes, schedules, and student transport',
        'is_core': False,
        'dependencies': [],
        'app_names': ['fleet'],
        'status': MODULE_STATUS['DEVELOPMENT'],
    },
    'library': {
        'name': 'Library',
        'description': 'Library management, book tracking, and borrowing',
        'is_core': False,
        'dependencies': [],
        'app_names': ['library'],
        'status': MODULE_STATUS['DEVELOPMENT'],
    },
    'inventory': {
        'name': 'Inventory',
        'description': 'Inventory management and asset tracking',
        'is_core': False,
        'dependencies': [],
        'app_names': ['inventory'],
        'status': MODULE_STATUS['DEVELOPMENT'],
    },
    'communication': {
        'name': 'Communication',
        'description': 'SMS and email communication with parents and staff',
        'is_core': False,
        'dependencies': [],
        'app_names': ['communication'],
        'status': MODULE_STATUS['DEVELOPMENT'],
    },
    'community': {
        'name': 'Community',
        'description': 'School clubs, events, and community engagement',
        'is_core': False,
        'dependencies': [],
        'app_names': ['community'],
        'status': MODULE_STATUS['DEVELOPMENT'],
    },
    'exams': {
        'name': 'Exams & Grading',
        'description': 'Exam management, grading, and academic reporting',
        'is_core': False,
        'dependencies': [],
        'app_names': ['exams'],
        'status': MODULE_STATUS['LIVE'],
    },
    'attendance': {
        'name': 'Attendance',
        'description': 'Student and staff attendance tracking',
        'is_core': False,
        'dependencies': [],
        'app_names': ['attendance'],
        'status': MODULE_STATUS['LIVE'],
    },
    'timetable': {
        'name': 'Timetable',
        'description': 'Class scheduling and timetable management',
        'is_core': False,
        'dependencies': [],
        'app_names': ['timetable'],
        'status': MODULE_STATUS['DEVELOPMENT'],
    },
    'hostel': {
        'name': 'Hostel Management',
        'description': 'Boarding facilities and hostel management',
        'is_core': False,
        'dependencies': [],
        'app_names': ['hostel'],
        'status': MODULE_STATUS['DEVELOPMENT'],
    },
    'hr': {
        'name': 'HR & Payroll',
        'description': 'Staff management, payroll, and HR functions',
        'is_core': False,
        'dependencies': [],
        'app_names': ['hr', 'payroll'],
        'status': MODULE_STATUS['DEVELOPMENT'],
    },
    'mpesa_integration': {
        'name': 'MPESA Integration',
        'description': 'MPESA payment integration',
        'is_core': False,
        'dependencies': ['fees'],
        'app_names': ['mpesa_integration'],
        'status': MODULE_STATUS['DEVELOPMENT'],
    },
}

# Define package tiers
PACKAGE_TIERS = {
    'basic': {
        'name': 'Basic',
        'description': 'Essential school management features',
        'modules': ['core', 'academics', 'dashboard', 'fees', 'attendance', 'exams'],
    },
    'standard': {
        'name': 'Standard',
        'description': 'Comprehensive school management solution',
        'modules': ['core', 'academics', 'dashboard', 'fees', 'attendance', 'exams', 'communication', 'timetable', 'library'],
    },
    'premium': {
        'name': 'Premium',
        'description': 'Complete school management system with all features',
        'modules': ['core', 'academics', 'dashboard', 'fees', 'attendance', 'exams', 'communication',
                   'timetable', 'library', 'inventory', 'fleet', 'community', 'hostel', 'hr'],
    },
    'custom': {
        'name': 'Custom',
        'description': 'Customized package with selected modules',
        'modules': [],  # Modules are defined per school
    },
}

def get_module_info(module_code):
    """Get information about a specific module"""
    return AVAILABLE_MODULES.get(module_code, None)

def get_package_info(package_code):
    """Get information about a specific package"""
    return PACKAGE_TIERS.get(package_code, None)

def get_package_modules(package_code):
    """Get all modules included in a package"""
    package = get_package_info(package_code)
    if not package:
        return []

    # Start with explicitly included modules
    modules = package['modules'].copy()

    # Add dependencies
    for module_code in package['modules']:
        module = get_module_info(module_code)
        if module:
            for dependency in module['dependencies']:
                if dependency not in modules:
                    modules.append(dependency)

    return modules

def is_module_available(module_code, package_code):
    """Check if a module is available in a package"""
    if module_code in AVAILABLE_MODULES and AVAILABLE_MODULES[module_code]['is_core']:
        return True

    return module_code in get_package_modules(package_code)

def get_module_status(module_code):
    """Get the development status of a module"""
    module = get_module_info(module_code)
    if not module:
        return None
    return module.get('status', MODULE_STATUS['DEVELOPMENT'])

def update_module_status(module_code, status):
    """Update the development status of a module"""
    if module_code not in AVAILABLE_MODULES:
        return False
    if status not in MODULE_STATUS.values():
        return False

    AVAILABLE_MODULES[module_code]['status'] = status
    return True

def is_module_live(module_code):
    """Check if a module is live (not in development)"""
    return get_module_status(module_code) == MODULE_STATUS['LIVE']
