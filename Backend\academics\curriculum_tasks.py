from django.utils import timezone
from django.db.models import Q
from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.contrib.auth import get_user_model

from celery import shared_task
from .models import (
    CurriculumSystem,
    SchoolCurriculumConfig,
    CurriculumConfigTemplate,
    CurriculumComplianceCheck,
    CurriculumComplianceNotification
)
from core.models import School, User

@shared_task
def run_scheduled_compliance_checks():
    """
    Run all scheduled compliance checks that are due
    """
    now = timezone.now()

    # Get all active scheduled checks that are due
    scheduled_checks = CurriculumComplianceCheck.objects.filter(
        is_active=True,
        next_check_at__lte=now
    )

    for check in scheduled_checks:
        # Run the compliance check
        run_compliance_check(check)

        # Update the next check time based on frequency
        if check.frequency == 'daily':
            check.next_check_at = now + timezone.timedelta(days=1)
        elif check.frequency == 'weekly':
            check.next_check_at = now + timezone.timedelta(weeks=1)
        elif check.frequency == 'monthly':
            check.next_check_at = now + timezone.timedelta(days=30)
        elif check.frequency == 'quarterly':
            check.next_check_at = now + timezone.timedelta(days=90)

        check.last_check_at = now
        check.save()

def run_compliance_check(scheduled_check):
    """
    Run a single compliance check and create notifications for non-compliant schools
    """
    template = scheduled_check.template
    threshold = scheduled_check.compliance_threshold

    # Get all schools or filter by specific school
    if scheduled_check.school:
        schools = [scheduled_check.school]
    else:
        schools = School.objects.all()

    # Filter by school name if provided
    if scheduled_check.school_name_filter:
        schools = schools.filter(name__icontains=scheduled_check.school_name_filter)

    # Track compliance issues
    non_compliant_schools = []
    total_schools = schools.count()
    schools_with_config = 0
    schools_without_config = 0

    # Check each school
    for school in schools:
        try:
            config = SchoolCurriculumConfig.objects.get(school=school)
            schools_with_config += 1

            # Compare with template
            comparison = compare_config_with_template(config, template)

            # Check if below threshold
            if comparison['compliance_score'] < threshold:
                non_compliant_schools.append({
                    'school': school,
                    'compliance_score': comparison['compliance_score'],
                    'compliance_status': comparison['compliance_status'],
                    'differences': comparison['differences']
                })
        except SchoolCurriculumConfig.DoesNotExist:
            schools_without_config += 1
            if scheduled_check.notify_missing_configs:
                non_compliant_schools.append({
                    'school': school,
                    'compliance_score': 0,
                    'compliance_status': 'no_config',
                    'differences': []
                })

    # Create a summary
    summary = {
        'total_schools': total_schools,
        'schools_with_config': schools_with_config,
        'schools_without_config': schools_without_config,
        'non_compliant_schools': len(non_compliant_schools),
        'template_name': template.name,
        'threshold': threshold
    }

    # Create notifications for non-compliant schools
    for school_data in non_compliant_schools:
        school = school_data['school']

        # Check if we already have a recent unresolved notification for this school and template
        existing_notification = CurriculumComplianceNotification.objects.filter(
            school=school,
            template=template,
            is_resolved=False,
            created_at__gte=timezone.now() - timezone.timedelta(days=7)
        ).first()

        if existing_notification:
            # Update the existing notification
            existing_notification.compliance_score = school_data['compliance_score']
            existing_notification.compliance_status = school_data['compliance_status']
            existing_notification.differences = school_data['differences']
            existing_notification.save()
        else:
            # Create a new notification
            notification = CurriculumComplianceNotification.objects.create(
                school=school,
                template=template,
                scheduled_check=scheduled_check,
                compliance_score=school_data['compliance_score'],
                compliance_status=school_data['compliance_status'],
                differences=school_data['differences'],
                created_by=scheduled_check.created_by
            )

            # Send email notifications if enabled
            if scheduled_check.send_email_notifications:
                send_compliance_notification_email(notification, scheduled_check)

    return summary

def compare_config_with_template(config, template):
    """
    Compare a school's curriculum configuration with a template
    """
    # Prepare the comparison result
    comparison = {
        'template': {
            'id': template.id,
            'name': template.name,
            'primary_curriculum': {
                'id': template.primary_curriculum.id,
                'name': template.primary_curriculum.name,
                'code': template.primary_curriculum.code
            },
            'secondary_curriculum': {
                'id': template.secondary_curriculum.id,
                'name': template.secondary_curriculum.name,
                'code': template.secondary_curriculum.code
            },
            'is_transition_period': template.is_transition_period,
            'transition_details': template.transition_details,
            'curriculum_modifications': template.curriculum_modifications,
            'is_system_template': template.is_system_template
        },
        'school': {
            'id': config.school.id,
            'name': config.school.name,
            'config': {
                'id': config.id,
                'primary_curriculum': {
                    'id': config.primary_curriculum.id,
                    'name': config.primary_curriculum.name,
                    'code': config.primary_curriculum.code
                },
                'secondary_curriculum': {
                    'id': config.secondary_curriculum.id,
                    'name': config.secondary_curriculum.name,
                    'code': config.secondary_curriculum.code
                },
                'is_transition_period': config.is_transition_period,
                'transition_details': config.transition_details,
                'curriculum_modifications': config.curriculum_modifications,
                'is_provisional': config.is_provisional
            }
        },
        'differences': [],
        'compliance_score': 100  # Start with 100% compliance
    }

    # Compare primary curriculum
    if template.primary_curriculum.id != config.primary_curriculum.id:
        comparison['differences'].append({
            'field': 'primary_curriculum',
            'template_value': template.primary_curriculum.name,
            'school_value': config.primary_curriculum.name,
            'severity': 'high'
        })
        comparison['compliance_score'] -= 20  # Major difference

    # Compare secondary curriculum
    if template.secondary_curriculum.id != config.secondary_curriculum.id:
        comparison['differences'].append({
            'field': 'secondary_curriculum',
            'template_value': template.secondary_curriculum.name,
            'school_value': config.secondary_curriculum.name,
            'severity': 'high'
        })
        comparison['compliance_score'] -= 20  # Major difference

    # Compare transition period
    if template.is_transition_period != config.is_transition_period:
        comparison['differences'].append({
            'field': 'is_transition_period',
            'template_value': 'Yes' if template.is_transition_period else 'No',
            'school_value': 'Yes' if config.is_transition_period else 'No',
            'severity': 'medium'
        })
        comparison['compliance_score'] -= 15  # Medium difference

    # Compare transition details if both are in transition
    if template.is_transition_period and config.is_transition_period:
        if template.transition_details != config.transition_details:
            comparison['differences'].append({
                'field': 'transition_details',
                'template_value': template.transition_details or 'Not specified',
                'school_value': config.transition_details or 'Not specified',
                'severity': 'low'
            })
            comparison['compliance_score'] -= 10  # Minor difference

    # Compare curriculum modifications
    if template.curriculum_modifications != config.curriculum_modifications:
        comparison['differences'].append({
            'field': 'curriculum_modifications',
            'template_value': 'Has modifications' if template.curriculum_modifications else 'No modifications',
            'school_value': 'Has modifications' if config.curriculum_modifications else 'No modifications',
            'severity': 'medium'
        })
        comparison['compliance_score'] -= 15  # Medium difference

    # Ensure compliance score doesn't go below 0
    comparison['compliance_score'] = max(0, comparison['compliance_score'])

    # Determine compliance status
    if comparison['compliance_score'] == 100:
        comparison['compliance_status'] = 'compliant'
    elif comparison['compliance_score'] >= 80:
        comparison['compliance_status'] = 'mostly_compliant'
    elif comparison['compliance_score'] >= 50:
        comparison['compliance_status'] = 'partially_compliant'
    else:
        comparison['compliance_status'] = 'non_compliant'

    return comparison

def send_compliance_notification_email(notification, scheduled_check):
    """
    Send an email notification about a compliance issue
    """
    # Get recipients
    recipients = []

    # Add the scheduled check creator
    if scheduled_check.created_by and scheduled_check.created_by.email:
        recipients.append(scheduled_check.created_by.email)

    # Add system administrators
    User = get_user_model()
    admin_users = User.objects.filter(
        Q(is_superuser=True) | Q(groups__name='System Administrators')
    ).distinct()

    for user in admin_users:
        if user.email and user.email not in recipients:
            recipients.append(user.email)

    # Add school administrators if configured
    if scheduled_check.notify_school_admins:
        school_admins = User.objects.filter(
            school=notification.school,
            groups__name='School Administrators'
        )

        for admin in school_admins:
            if admin.email and admin.email not in recipients:
                recipients.append(admin.email)

    if not recipients:
        return

    # Prepare the email content
    context = {
        'notification': notification,
        'school': notification.school,
        'template': notification.template,
        'compliance_score': notification.compliance_score,
        'compliance_status': notification.compliance_status.replace('_', ' ').title(),
        'differences': notification.differences,
        'app_url': settings.APP_URL
    }

    subject = f'Curriculum Compliance Alert: {notification.school.name}'
    html_message = render_to_string('emails/compliance_notification.html', context)
    plain_message = render_to_string('emails/compliance_notification.txt', context)

    # Send the email
    try:
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=recipients,
            html_message=html_message,
            fail_silently=False
        )
    except Exception as e:
        print(f"Failed to send compliance notification email: {str(e)}")

@shared_task
def cleanup_resolved_notifications():
    """
    Clean up old resolved notifications
    """
    # Delete resolved notifications older than 90 days
    cutoff_date = timezone.now() - timezone.timedelta(days=90)
    CurriculumComplianceNotification.objects.filter(
        is_resolved=True,
        resolved_at__lt=cutoff_date
    ).delete()
