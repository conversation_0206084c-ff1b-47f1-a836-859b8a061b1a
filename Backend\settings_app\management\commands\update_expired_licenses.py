from django.core.management.base import BaseCommand
from django.utils import timezone
from settings_app.license_models import LicenseSubscription

class Command(BaseCommand):
    help = 'Update expired licenses to have EXPIRED status'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options.get('dry_run', False)
        today = timezone.now().date()
        
        # Find licenses that have expired but still have ACTIVE or TRIAL status
        expired_licenses = LicenseSubscription.objects.filter(
            expiry_date__lt=today,
            subscription_status__in=['ACTIVE', 'TRIAL']
        )
        
        count = expired_licenses.count()
        
        if count == 0:
            self.stdout.write(self.style.SUCCESS("No expired licenses found that need updating."))
            return
            
        self.stdout.write(f"Found {count} expired license(s) that need updating.")
        
        if dry_run:
            for license in expired_licenses:
                self.stdout.write(f"Would update: {license.school.name} - {license.license_key} (Expired on {license.expiry_date})")
            self.stdout.write(self.style.WARNING("Dry run - no changes made."))
            return
            
        # Update all expired licenses
        updated_count = 0
        for license in expired_licenses:
            old_status = license.subscription_status
            license.subscription_status = 'EXPIRED'
            license.save(update_fields=['subscription_status', 'updated_at'])
            updated_count += 1
            self.stdout.write(f"Updated: {license.school.name} - {license.license_key} (Status changed from {old_status} to EXPIRED)")
            
        self.stdout.write(self.style.SUCCESS(f"Successfully updated {updated_count} expired license(s)."))
