#!/usr/bin/env python3
"""
Sample Academic Data Population Script for ShuleXcel
This script creates sample curriculum systems, education levels, classes, streams, subjects, and departments.
"""

import os
import sys
import django
from datetime import datetime, date
from decimal import Decimal

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
django.setup()

from academics.models import (
    ClassRoom, Stream, Subject, Department,
    AcademicYear, Term, GradingSystem, GradeScale
)
from academics.curriculum_models import CurriculumSystem, EducationLevel
from schools.models import School, SchoolBranch

def create_curriculum_systems():
    """Create curriculum systems (8-4-4, CBC, etc.)"""
    print("Creating curriculum systems...")
    
    systems = [
        {
            'name': '8-4-4 System',
            'code': '8-4-4',
            'description': 'Traditional Kenyan education system with 8 years primary, 4 years secondary, 4 years university',
            'is_active': True,
            'structure': {
                'levels': ['Class 1-8', 'Form 1-4'],
                'country': 'Kenya',
                'type': 'traditional'
            }
        },
        {
            'name': 'Competency Based Curriculum (CBC)',
            'code': 'CBC',
            'description': 'New Kenyan curriculum focusing on competency development',
            'is_active': True,
            'structure': {
                'levels': ['PP1-PP2', 'Grade 1-6', 'Grade 7-9', 'Grade 10-12'],
                'country': 'Kenya',
                'type': 'competency_based'
            }
        },
        {
            'name': 'Cambridge International',
            'code': 'CAMBRIDGE',
            'description': 'Cambridge International curriculum',
            'is_active': True,
            'structure': {
                'levels': ['Primary', 'Lower Secondary', 'Upper Secondary'],
                'country': 'International',
                'type': 'international'
            }
        }
    ]
    
    created_systems = []
    for system_data in systems:
        system, created = CurriculumSystem.objects.get_or_create(
            code=system_data['code'],
            defaults=system_data
        )
        created_systems.append(system)
        print(f"{'Created' if created else 'Found'} curriculum system: {system.name}")
    
    return created_systems

def create_education_levels(curriculum_systems):
    """Create education levels for each curriculum system"""
    print("Creating education levels...")
    
    # 8-4-4 System levels
    eight_four_four = next(s for s in curriculum_systems if s.code == '8-4-4')
    levels_844 = [
        {'name': 'Class 1', 'code': 'CLS1', 'stage_code': 'PRI', 'sequence': 1},
        {'name': 'Class 2', 'code': 'CLS2', 'stage_code': 'PRI', 'sequence': 2},
        {'name': 'Class 3', 'code': 'CLS3', 'stage_code': 'PRI', 'sequence': 3},
        {'name': 'Class 4', 'code': 'CLS4', 'stage_code': 'PRI', 'sequence': 4},
        {'name': 'Class 5', 'code': 'CLS5', 'stage_code': 'PRI', 'sequence': 5},
        {'name': 'Class 6', 'code': 'CLS6', 'stage_code': 'PRI', 'sequence': 6},
        {'name': 'Class 7', 'code': 'CLS7', 'stage_code': 'PRI', 'sequence': 7},
        {'name': 'Class 8', 'code': 'CLS8', 'stage_code': 'PRI', 'sequence': 8},
        {'name': 'Form 1', 'code': 'F1', 'stage_code': 'SEC', 'sequence': 9},
        {'name': 'Form 2', 'code': 'F2', 'stage_code': 'SEC', 'sequence': 10},
        {'name': 'Form 3', 'code': 'F3', 'stage_code': 'SEC', 'sequence': 11},
        {'name': 'Form 4', 'code': 'F4', 'stage_code': 'SEC', 'sequence': 12},
    ]
    
    # CBC System levels
    cbc = next(s for s in curriculum_systems if s.code == 'CBC')
    levels_cbc = [
        {'name': 'Pre-Primary 1', 'code': 'PP1', 'stage_code': 'PP', 'sequence': 1},
        {'name': 'Pre-Primary 2', 'code': 'PP2', 'stage_code': 'PP', 'sequence': 2},
        {'name': 'Grade 1', 'code': 'G1', 'stage_code': 'PRI', 'sequence': 3},
        {'name': 'Grade 2', 'code': 'G2', 'stage_code': 'PRI', 'sequence': 4},
        {'name': 'Grade 3', 'code': 'G3', 'stage_code': 'PRI', 'sequence': 5},
        {'name': 'Grade 4', 'code': 'G4', 'stage_code': 'PRI', 'sequence': 6},
        {'name': 'Grade 5', 'code': 'G5', 'stage_code': 'PRI', 'sequence': 7},
        {'name': 'Grade 6', 'code': 'G6', 'stage_code': 'PRI', 'sequence': 8},
        {'name': 'Grade 7', 'code': 'G7', 'stage_code': 'JSS', 'sequence': 9},
        {'name': 'Grade 8', 'code': 'G8', 'stage_code': 'JSS', 'sequence': 10},
        {'name': 'Grade 9', 'code': 'G9', 'stage_code': 'JSS', 'sequence': 11},
        {'name': 'Grade 10', 'code': 'G10', 'stage_code': 'SSS', 'sequence': 12},
        {'name': 'Grade 11', 'code': 'G11', 'stage_code': 'SSS', 'sequence': 13},
        {'name': 'Grade 12', 'code': 'G12', 'stage_code': 'SSS', 'sequence': 14},
    ]
    
    created_levels = []
    
    # Create 8-4-4 levels
    for level_data in levels_844:
        level, created = EducationLevel.objects.get_or_create(
            curriculum_system=eight_four_four,
            code=level_data['code'],
            defaults={**level_data, 'curriculum_system': eight_four_four}
        )
        created_levels.append(level)
        print(f"{'Created' if created else 'Found'} education level: {level.name} ({eight_four_four.code})")
    
    # Create CBC levels
    for level_data in levels_cbc:
        level, created = EducationLevel.objects.get_or_create(
            curriculum_system=cbc,
            code=level_data['code'],
            defaults={**level_data, 'curriculum_system': cbc}
        )
        created_levels.append(level)
        print(f"{'Created' if created else 'Found'} education level: {level.name} ({cbc.code})")
    
    return created_levels

def create_departments():
    """Create academic departments"""
    print("Creating departments...")

    # Get the first school and branch for creating departments
    try:
        school = School.objects.first()
        branch = SchoolBranch.objects.filter(school=school).first()

        if not school or not branch:
            print("❌ No school or branch found. Please create a school first.")
            return []

        print(f"Using school: {school.name}, branch: {branch.name}")

    except Exception as e:
        print(f"❌ Error getting school/branch: {e}")
        return []

    departments_data = [
        {'name': 'Mathematics Department', 'code': 'MATH', 'description': 'Mathematics and related subjects'},
        {'name': 'Science Department', 'code': 'SCI', 'description': 'Physics, Chemistry, Biology'},
        {'name': 'Languages Department', 'code': 'LANG', 'description': 'English, Kiswahili, Foreign Languages'},
        {'name': 'Humanities Department', 'code': 'HUM', 'description': 'History, Geography, Religious Studies'},
        {'name': 'Technical Department', 'code': 'TECH', 'description': 'Computer Studies, Technical Drawing'},
        {'name': 'Arts Department', 'code': 'ARTS', 'description': 'Music, Art, Drama'},
        {'name': 'Physical Education', 'code': 'PE', 'description': 'Sports and Physical Education'},
    ]

    created_departments = []
    for dept_data in departments_data:
        # Add school and branch to the data
        dept_data_with_school = {
            **dept_data,
            'school': school,
            'school_branch': branch
        }

        dept, created = Department.objects.get_or_create(
            code=dept_data['code'],
            school=school,
            defaults=dept_data_with_school
        )
        created_departments.append(dept)
        print(f"{'Created' if created else 'Found'} department: {dept.name}")

    return created_departments

def create_subjects(departments):
    """Create subjects and assign to departments"""
    print("Creating subjects...")

    # Get required objects
    try:
        school = School.objects.first()
        branch = SchoolBranch.objects.filter(school=school).first()
        curriculum_system = CurriculumSystem.objects.first()
        academic_year = AcademicYear.objects.filter(is_current=True).first()

        if not academic_year:
            academic_year = AcademicYear.objects.first()

        if not all([school, branch, curriculum_system, academic_year]):
            print("❌ Missing required objects for subjects. Skipping subject creation.")
            return []

        print(f"Using: {school.name}, {branch.name}, {curriculum_system.name}, {academic_year.year}")

    except Exception as e:
        print(f"❌ Error getting required objects: {e}")
        return []

    # Get departments
    math_dept = next(d for d in departments if d.code == 'MATH')
    sci_dept = next(d for d in departments if d.code == 'SCI')
    lang_dept = next(d for d in departments if d.code == 'LANG')
    hum_dept = next(d for d in departments if d.code == 'HUM')
    tech_dept = next(d for d in departments if d.code == 'TECH')
    arts_dept = next(d for d in departments if d.code == 'ARTS')
    pe_dept = next(d for d in departments if d.code == 'PE')
    
    subjects_data = [
        # Mathematics
        {'name': 'Mathematics', 'code': 'MATH', 'department': math_dept, 'is_compulsory': True, 'subject_type': 'core'},

        # Sciences
        {'name': 'Physics', 'code': 'PHY', 'department': sci_dept, 'is_compulsory': False, 'subject_type': 'optional'},
        {'name': 'Chemistry', 'code': 'CHEM', 'department': sci_dept, 'is_compulsory': False, 'subject_type': 'optional'},
        {'name': 'Biology', 'code': 'BIO', 'department': sci_dept, 'is_compulsory': False, 'subject_type': 'optional'},
        {'name': 'General Science', 'code': 'SCI', 'department': sci_dept, 'is_compulsory': True, 'subject_type': 'core'},

        # Languages
        {'name': 'English', 'code': 'ENG', 'department': lang_dept, 'is_compulsory': True, 'subject_type': 'core'},
        {'name': 'Kiswahili', 'code': 'KIS', 'department': lang_dept, 'is_compulsory': True, 'subject_type': 'core'},
        {'name': 'French', 'code': 'FRE', 'department': lang_dept, 'is_compulsory': False, 'subject_type': 'optional'},

        # Humanities
        {'name': 'History', 'code': 'HIST', 'department': hum_dept, 'is_compulsory': False, 'subject_type': 'optional'},
        {'name': 'Geography', 'code': 'GEO', 'department': hum_dept, 'is_compulsory': False, 'subject_type': 'optional'},
        {'name': 'Religious Studies', 'code': 'CRE', 'department': hum_dept, 'is_compulsory': False, 'subject_type': 'optional'},
        {'name': 'Social Studies', 'code': 'SST', 'department': hum_dept, 'is_compulsory': True, 'subject_type': 'core'},

        # Technical
        {'name': 'Computer Studies', 'code': 'COMP', 'department': tech_dept, 'is_compulsory': False, 'subject_type': 'optional'},
        {'name': 'Technical Drawing', 'code': 'TD', 'department': tech_dept, 'is_compulsory': False, 'subject_type': 'optional'},

        # Arts
        {'name': 'Music', 'code': 'MUS', 'department': arts_dept, 'is_compulsory': False, 'subject_type': 'optional'},
        {'name': 'Art & Design', 'code': 'ART', 'department': arts_dept, 'is_compulsory': False, 'subject_type': 'optional'},

        # PE
        {'name': 'Physical Education', 'code': 'PE_SUBJ', 'department': pe_dept, 'is_compulsory': True, 'subject_type': 'core'},
    ]
    
    created_subjects = []
    for subject_data in subjects_data:
        # Add required fields
        subject_data_complete = {
            **subject_data,
            'curriculum_system': curriculum_system,
            'school': school,
            'school_branch': branch,
            'academic_year': academic_year
        }

        subject, created = Subject.objects.get_or_create(
            code=subject_data['code'],
            curriculum_system=curriculum_system,
            school=school,
            defaults=subject_data_complete
        )
        created_subjects.append(subject)
        print(f"{'Created' if created else 'Found'} subject: {subject.name}")

    return created_subjects

def create_streams():
    """Create streams (A, B, C, etc.)"""
    print("Creating streams...")

    # Get school for streams
    try:
        school = School.objects.first()
        if not school:
            print("❌ No school found. Skipping stream creation.")
            return []
        print(f"Using school: {school.name}")
    except Exception as e:
        print(f"❌ Error getting school: {e}")
        return []

    streams_data = [
        {'name': 'Stream A', 'code': 'A', 'description': 'Main stream'},
        {'name': 'Stream B', 'code': 'B', 'description': 'Second stream'},
        {'name': 'Stream C', 'code': 'C', 'description': 'Third stream'},
        {'name': 'Stream D', 'code': 'D', 'description': 'Fourth stream'},
    ]

    created_streams = []
    for stream_data in streams_data:
        # Add school to stream data
        stream_data_with_school = {
            **stream_data,
            'school': school
        }

        stream, created = Stream.objects.get_or_create(
            code=stream_data['code'],
            school=school,
            defaults=stream_data_with_school
        )
        created_streams.append(stream)
        print(f"{'Created' if created else 'Found'} stream: {stream.name}")

    return created_streams

def create_classes(education_levels, streams):
    """Create classes by combining education levels and streams"""
    print("Creating classes...")

    # Get required objects
    try:
        school = School.objects.first()
        branch = SchoolBranch.objects.filter(school=school).first()
        academic_year = AcademicYear.objects.filter(is_current=True).first()

        if not academic_year:
            academic_year = AcademicYear.objects.first()

        if not all([school, branch, academic_year]):
            print("❌ Missing required objects for classes. Skipping class creation.")
            return []

        print(f"Using: {school.name}, {branch.name}, {academic_year.year}")

    except Exception as e:
        print(f"❌ Error getting required objects: {e}")
        return []

    # Get a sample of education levels (primary and secondary)
    primary_levels = [level for level in education_levels if level.stage_code in ['PRI', 'JSS']][:8]
    secondary_levels = [level for level in education_levels if level.stage_code in ['SEC', 'SSS']][:4]

    created_classes = []

    # Create primary classes (usually 1-2 streams each)
    for level in primary_levels:
        for stream in streams[:2]:  # Only A and B streams for primary
            class_name = f"{level.name} {stream.name}"
            classroom, created = ClassRoom.objects.get_or_create(
                name=class_name,
                education_level=level,
                stream=stream,
                academic_year=academic_year,
                school_branch=branch,
                defaults={
                    'max_capacity': 40,
                    'school': school,
                    'academic_year': academic_year,
                    'school_branch': branch,
                    'education_level': level,
                    'stream': stream
                }
            )
            created_classes.append(classroom)
            print(f"{'Created' if created else 'Found'} class: {classroom.name}")

    # Create secondary classes (usually 2-4 streams each)
    for level in secondary_levels:
        for stream in streams[:3]:  # A, B, C streams for secondary
            class_name = f"{level.name} {stream.name}"
            classroom, created = ClassRoom.objects.get_or_create(
                name=class_name,
                education_level=level,
                stream=stream,
                academic_year=academic_year,
                school_branch=branch,
                defaults={
                    'max_capacity': 35,
                    'school': school,
                    'academic_year': academic_year,
                    'school_branch': branch,
                    'education_level': level,
                    'stream': stream
                }
            )
            created_classes.append(classroom)
            print(f"{'Created' if created else 'Found'} class: {classroom.name}")

    return created_classes

def create_academic_years():
    """Create academic years and terms"""
    print("Creating academic years...")

    # Get school and branch for academic years
    try:
        school = School.objects.first()
        branch = SchoolBranch.objects.filter(school=school).first()

        if not school or not branch:
            print("❌ No school or branch found. Skipping academic year creation.")
            return []
        print(f"Using school: {school.name}, branch: {branch.name}")
    except Exception as e:
        print(f"❌ Error getting school/branch: {e}")
        return []

    current_year = datetime.now().year

    academic_years_data = [
        {
            'year': f'{current_year-1}/{current_year}',
            'start_date': date(current_year-1, 1, 15),
            'end_date': date(current_year, 11, 30),
            'is_current': False
        },
        {
            'year': f'{current_year}/{current_year+1}',
            'start_date': date(current_year, 1, 15),
            'end_date': date(current_year+1, 11, 30),
            'is_current': True
        },
        {
            'year': f'{current_year+1}/{current_year+2}',
            'start_date': date(current_year+1, 1, 15),
            'end_date': date(current_year+2, 11, 30),
            'is_current': False
        }
    ]

    created_years = []
    for year_data in academic_years_data:
        # Add school and branch to year data
        year_data_complete = {
            **year_data,
            'school_name': school,
            'school_branch': branch
        }

        academic_year, created = AcademicYear.objects.get_or_create(
            year=year_data['year'],
            school_branch=branch,
            defaults=year_data_complete
        )
        created_years.append(academic_year)
        print(f"{'Created' if created else 'Found'} academic year: {academic_year.year}")

        # Create terms for each academic year
        if created or not hasattr(academic_year, 'terms') or not academic_year.terms.exists():
            create_terms_for_year(academic_year)

    return created_years

def create_terms_for_year(academic_year):
    """Create terms for an academic year"""
    year_start = academic_year.start_date.year

    terms_data = [
        {
            'name': 'Term 1',
            'start_date': date(year_start, 1, 15),
            'end_date': date(year_start, 4, 15),
            'is_current': True
        },
        {
            'name': 'Term 2',
            'start_date': date(year_start, 5, 1),
            'end_date': date(year_start, 8, 15),
            'is_current': False
        },
        {
            'name': 'Term 3',
            'start_date': date(year_start, 9, 1),
            'end_date': date(year_start, 11, 30),
            'is_current': False
        }
    ]

    for term_data in terms_data:
        # Add required fields
        term_data_complete = {
            **term_data,
            'school': academic_year.school_name,
            'school_branch': academic_year.school_branch
        }

        term, created = Term.objects.get_or_create(
            academic_year=academic_year,
            name=term_data['name'],
            school_branch=academic_year.school_branch,
            defaults=term_data_complete
        )
        print(f"  {'Created' if created else 'Found'} term: {term.name} for {academic_year.year}")

def create_grading_system():
    """Create grading system and grade scales"""
    print("Creating grading system...")

    # Get required objects
    try:
        from users.models import Teacher

        school = School.objects.first()
        curriculum_system = CurriculumSystem.objects.first()
        academic_year = AcademicYear.objects.filter(is_current=True).first()
        teacher = Teacher.objects.first()

        if not academic_year:
            academic_year = AcademicYear.objects.first()

        if not all([school, curriculum_system, academic_year, teacher]):
            print("❌ Missing required objects for grading system. Skipping grading system creation.")
            return None

        print(f"Using: {school.name}, {curriculum_system.name}, {academic_year.year}, {teacher.user.email}")

    except Exception as e:
        print(f"❌ Error getting required objects: {e}")
        return None

    grading_system, created = GradingSystem.objects.get_or_create(
        name='Kenyan Grading System',
        school=school,
        academic_year=academic_year,
        defaults={
            'description': 'Standard Kenyan education grading system',
            'curriculum_system': curriculum_system,
            'school': school,
            'academic_year': academic_year,
            'grading_type': 'letter',
            'created_by': teacher
        }
    )
    print(f"{'Created' if created else 'Found'} grading system: {grading_system.name}")

    if created or not grading_system.grade_scales.exists():
        # Create grade scales
        grade_scales_data = [
            {'grade': 'A', 'min_score': Decimal('80'), 'max_score': Decimal('100'), 'points': Decimal('12')},
            {'grade': 'A-', 'min_score': Decimal('75'), 'max_score': Decimal('79'), 'points': Decimal('11')},
            {'grade': 'B+', 'min_score': Decimal('70'), 'max_score': Decimal('74'), 'points': Decimal('10')},
            {'grade': 'B', 'min_score': Decimal('65'), 'max_score': Decimal('69'), 'points': Decimal('9')},
            {'grade': 'B-', 'min_score': Decimal('60'), 'max_score': Decimal('64'), 'points': Decimal('8')},
            {'grade': 'C+', 'min_score': Decimal('55'), 'max_score': Decimal('59'), 'points': Decimal('7')},
            {'grade': 'C', 'min_score': Decimal('50'), 'max_score': Decimal('54'), 'points': Decimal('6')},
            {'grade': 'C-', 'min_score': Decimal('45'), 'max_score': Decimal('49'), 'points': Decimal('5')},
            {'grade': 'D+', 'min_score': Decimal('40'), 'max_score': Decimal('44'), 'points': Decimal('4')},
            {'grade': 'D', 'min_score': Decimal('35'), 'max_score': Decimal('39'), 'points': Decimal('3')},
            {'grade': 'D-', 'min_score': Decimal('30'), 'max_score': Decimal('34'), 'points': Decimal('2')},
            {'grade': 'E', 'min_score': Decimal('0'), 'max_score': Decimal('29'), 'points': Decimal('1')},
        ]

        for scale_data in grade_scales_data:
            scale, created = GradeScale.objects.get_or_create(
                grading_system=grading_system,
                grade=scale_data['grade'],
                defaults=scale_data
            )
            print(f"  {'Created' if created else 'Found'} grade scale: {scale.grade}")

    return grading_system

def main():
    """Main function to populate all academic data"""
    print("🎯 Starting Academic Data Population...")
    print("=" * 50)

    try:
        # Create data in order of dependencies
        curriculum_systems = create_curriculum_systems()
        print()

        education_levels = create_education_levels(curriculum_systems)
        print()

        departments = create_departments()
        print()

        subjects = create_subjects(departments)
        print()

        streams = create_streams()
        print()

        classes = create_classes(education_levels, streams)
        print()

        academic_years = create_academic_years()
        print()

        grading_system = create_grading_system()
        print()

        print("=" * 50)
        print("✅ Academic Data Population Completed Successfully!")
        print(f"Created/Found:")
        print(f"  - {len(curriculum_systems)} Curriculum Systems")
        print(f"  - {len(education_levels)} Education Levels")
        print(f"  - {len(departments)} Departments")
        print(f"  - {len(subjects)} Subjects")
        print(f"  - {len(streams)} Streams")
        print(f"  - {len(classes)} Classes")
        print(f"  - {len(academic_years)} Academic Years")
        print(f"  - 1 Grading System with 12 Grade Scales")
        print()
        print("🚀 The academics dashboard should now show actual data!")

    except Exception as e:
        print(f"❌ Error during data population: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
