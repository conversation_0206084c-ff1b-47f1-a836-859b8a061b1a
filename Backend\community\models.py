from django.db import models
from django.utils import timezone
from schools.models import SchoolBranch
from core.models import CustomUser

class Community(models.Model):
    COMMUNITY_TYPE_CHOICES = [
        ('CLASS', 'Class Community'),
        ('CLUB', 'School Club'),
        ('SUBJECT', 'Subject Discussion'),
        ('GENERAL', 'General Community'),
    ]
    
    name = models.CharField(max_length=100)
    description = models.TextField()
    community_type = models.CharField(max_length=10, choices=COMMUNITY_TYPE_CHOICES)
    created_by = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='created_communities')
    school_class = models.ForeignKey('academics.ClassRoom', on_delete=models.SET_NULL, null=True, blank=True, related_name='class_communities')
    stream = models.ForeignKey('academics.Stream', on_delete=models.SET_NULL, null=True, blank=True, related_name='stream_communities')
    is_active = models.<PERSON>oleanField(default=True)
    is_private = models.BooleanField(default=False)
    cover_image = models.ImageField(upload_to='community_covers/', blank=True, null=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='communities')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name_plural = 'Communities'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['community_type']),
            models.Index(fields=['is_active']),
        ]

class CommunityMember(models.Model):
    ROLE_CHOICES = [
        ('ADMIN', 'Administrator'),
        ('MODERATOR', 'Moderator'),
        ('MEMBER', 'Member'),
    ]
    
    community = models.ForeignKey(Community, on_delete=models.CASCADE, related_name='members')
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='community_memberships')
    role = models.CharField(max_length=10, choices=ROLE_CHOICES, default='MEMBER')
    joined_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='community_members')
    
    def __str__(self):
        return f"{self.user} - {self.community} ({self.role})"
    
    class Meta:
        unique_together = ['community', 'user']
        ordering = ['community', 'role', 'user__first_name']
        indexes = [
            models.Index(fields=['role']),
            models.Index(fields=['is_active']),
        ]

class Post(models.Model):
    community = models.ForeignKey(Community, on_delete=models.CASCADE, related_name='posts')
    author = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='community_posts')
    title = models.CharField(max_length=255)
    content = models.TextField()
    attachment = models.FileField(upload_to='community_attachments/', blank=True, null=True)
    is_announcement = models.BooleanField(default=False)
    is_pinned = models.BooleanField(default=False)
    is_edited = models.BooleanField(default=False)
    edited_at = models.DateTimeField(blank=True, null=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='community_posts')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.title
    
    class Meta:
        ordering = ['-is_pinned', '-created_at']
        indexes = [
            models.Index(fields=['is_announcement']),
            models.Index(fields=['is_pinned']),
        ]
    
    def save(self, *args, **kwargs):
        if self.pk:
            self.is_edited = True
            self.edited_at = timezone.now()
        super().save(*args, **kwargs)

class Comment(models.Model):
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='comments')
    author = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='community_comments')
    content = models.TextField()
    attachment = models.FileField(upload_to='comment_attachments/', blank=True, null=True)
    parent_comment = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='replies')
    is_edited = models.BooleanField(default=False)
    edited_at = models.DateTimeField(blank=True, null=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='community_comments')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Comment by {self.author} on {self.post}"
    
    class Meta:
        ordering = ['created_at']
    
    def save(self, *args, **kwargs):
        if self.pk:
            self.is_edited = True
            self.edited_at = timezone.now()
        super().save(*args, **kwargs)

class ClubMeeting(models.Model):
    MEETING_STATUS_CHOICES = [
        ('SCHEDULED', 'Scheduled'),
        ('ONGOING', 'Ongoing'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
    ]
    
    community = models.ForeignKey(Community, on_delete=models.CASCADE, related_name='meetings')
    title = models.CharField(max_length=255)
    description = models.TextField()
    location = models.CharField(max_length=255)
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    status = models.CharField(max_length=10, choices=MEETING_STATUS_CHOICES, default='SCHEDULED')
    organizer = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='organized_meetings')
    agenda = models.TextField(blank=True, null=True)
    minutes = models.TextField(blank=True, null=True)
    attachment = models.FileField(upload_to='meeting_attachments/', blank=True, null=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='club_meetings')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.title
    
    class Meta:
        ordering = ['-start_time']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['start_time']),
        ]

class MeetingAttendance(models.Model):
    meeting = models.ForeignKey(ClubMeeting, on_delete=models.CASCADE, related_name='attendance')
    member = models.ForeignKey(CommunityMember, on_delete=models.CASCADE, related_name='meeting_attendance')
    is_present = models.BooleanField(default=False)
    check_in_time = models.DateTimeField(blank=True, null=True)
    check_out_time = models.DateTimeField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='meeting_attendance')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.member.user} - {self.meeting}"
    
    class Meta:
        unique_together = ['meeting', 'member']
        ordering = ['meeting', 'member__user__first_name']

class Reaction(models.Model):
    REACTION_CHOICES = [
        ('LIKE', 'Like'),
        ('LOVE', 'Love'),
        ('HAHA', 'Haha'),
        ('WOW', 'Wow'),
        ('SAD', 'Sad'),
        ('ANGRY', 'Angry'),
    ]
    
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='reactions', null=True, blank=True)
    comment = models.ForeignKey(Comment, on_delete=models.CASCADE, related_name='reactions', null=True, blank=True)
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='reactions')
    reaction_type = models.CharField(max_length=5, choices=REACTION_CHOICES)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='reactions')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        target = self.post or self.comment
        return f"{self.user} {self.reaction_type} on {target}"
    
    class Meta:
        unique_together = [
            ('post', 'user'),
            ('comment', 'user'),
        ]
        ordering = ['created_at']
        
    def clean(self):
        from django.core.exceptions import ValidationError
        if not self.post and not self.comment:
            raise ValidationError("Reaction must be associated with either a post or a comment")
        if self.post and self.comment:
            raise ValidationError("Reaction cannot be associated with both a post and a comment")

class Notification(models.Model):
    NOTIFICATION_TYPE_CHOICES = [
        ('POST', 'New Post'),
        ('COMMENT', 'New Comment'),
        ('REPLY', 'Reply to Comment'),
        ('MENTION', 'Mention'),
        ('MEETING', 'Meeting Reminder'),
        ('INVITATION', 'Community Invitation'),
        ('ROLE', 'Role Change'),
    ]
    
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='community_notifications')
    community = models.ForeignKey(Community, on_delete=models.CASCADE, related_name='notifications')
    notification_type = models.CharField(max_length=10, choices=NOTIFICATION_TYPE_CHOICES)
    title = models.CharField(max_length=255)
    message = models.TextField()
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(blank=True, null=True)
    related_post = models.ForeignKey(Post, on_delete=models.SET_NULL, null=True, blank=True, related_name='notifications')
    related_comment = models.ForeignKey(Comment, on_delete=models.SET_NULL, null=True, blank=True, related_name='notifications')
    related_meeting = models.ForeignKey(ClubMeeting, on_delete=models.SET_NULL, null=True, blank=True, related_name='notifications')
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='community_notifications')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.notification_type} for {self.user}"
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['is_read']),
            models.Index(fields=['notification_type']),
        ]
    
    def mark_as_read(self):
        self.is_read = True
        self.read_at = timezone.now()
        self.save()
