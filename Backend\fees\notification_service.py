"""
Notification Service for Payment Processing
Handles email, SMS, and other notifications for payment events
"""

import logging
from datetime import datetime
from django.conf import settings
from django.core.mail import send_mail, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.html import strip_tags

from .school_billing_models import PaymentNotification

logger = logging.getLogger(__name__)


class NotificationService:
    """
    Service for sending payment-related notifications
    """
    
    def __init__(self):
        self.from_email = getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>')
        self.company_name = getattr(settings, 'COMPANY_NAME', 'ShuleXcel')
    
    def send_payment_confirmation(self, payment, receipt=None):
        """
        Send payment confirmation notification to school
        """
        try:
            school = payment.school
            billing_account = school.billing_account
            
            # Prepare context
            context = {
                'school_name': school.name,
                'payment_reference': payment.payment_reference,
                'amount': payment.amount,
                'payment_date': payment.payment_date,
                'payment_method': payment.get_payment_method_display(),
                'receipt_number': receipt.receipt_number if receipt else None,
                'company_name': self.company_name,
                'license_activated': True,  # Since this is called after successful verification
            }
            
            # Send email notification
            self._send_email_notification(
                payment=payment,
                notification_type='PAYMENT_CONFIRMED',
                recipient=billing_account.billing_contact_email,
                subject=f'Payment Confirmed - {payment.payment_reference}',
                template='payment_confirmation',
                context=context,
                receipt=receipt
            )
            
            # Send SMS notification if phone number is available
            if billing_account.billing_contact_phone:
                self._send_sms_notification(
                    payment=payment,
                    notification_type='PAYMENT_CONFIRMED',
                    recipient=billing_account.billing_contact_phone,
                    message=f'Payment confirmed! {payment.payment_reference} - {payment.amount} KES. Your license has been activated. Thank you!',
                    context=context
                )
            
            logger.info(f"Payment confirmation sent for {payment.payment_reference}")
            
        except Exception as e:
            logger.error(f"Error sending payment confirmation: {e}")
    
    def send_receipt_notification(self, payment, receipt):
        """
        Send receipt notification with PDF attachment
        """
        try:
            school = payment.school
            billing_account = school.billing_account
            
            context = {
                'school_name': school.name,
                'receipt_number': receipt.receipt_number,
                'payment_reference': payment.payment_reference,
                'amount': payment.amount,
                'company_name': self.company_name,
            }
            
            self._send_email_notification(
                payment=payment,
                notification_type='RECEIPT_GENERATED',
                recipient=billing_account.billing_contact_email,
                subject=f'Payment Receipt - {receipt.receipt_number}',
                template='receipt_notification',
                context=context,
                receipt=receipt
            )
            
            logger.info(f"Receipt notification sent for {receipt.receipt_number}")
            
        except Exception as e:
            logger.error(f"Error sending receipt notification: {e}")
    
    def send_license_activation_notification(self, payment):
        """
        Send license activation notification
        """
        try:
            school = payment.school
            billing_account = school.billing_account
            license_info = school.license
            
            context = {
                'school_name': school.name,
                'payment_reference': payment.payment_reference,
                'license_package': license_info.package_type.title(),
                'expiry_date': license_info.expiry_date,
                'max_students': license_info.max_students,
                'max_staff': license_info.max_staff,
                'company_name': self.company_name,
            }
            
            self._send_email_notification(
                payment=payment,
                notification_type='LICENSE_ACTIVATED',
                recipient=billing_account.billing_contact_email,
                subject=f'License Activated - {school.name}',
                template='license_activation',
                context=context
            )
            
            logger.info(f"License activation notification sent for {school.name}")
            
        except Exception as e:
            logger.error(f"Error sending license activation notification: {e}")
    
    def send_fraud_alert(self, payment, fraud_score):
        """
        Send fraud alert to administrators
        """
        try:
            admin_emails = getattr(settings, 'FRAUD_ALERT_EMAILS', ['<EMAIL>'])
            
            context = {
                'payment_reference': payment.payment_reference,
                'school_name': payment.school.name,
                'amount': payment.amount,
                'payment_method': payment.get_payment_method_display(),
                'fraud_score': fraud_score,
                'payment_date': payment.payment_date,
                'external_reference': payment.external_reference,
                'company_name': self.company_name,
            }
            
            for admin_email in admin_emails:
                self._send_email_notification(
                    payment=payment,
                    notification_type='FRAUD_ALERT',
                    recipient=admin_email,
                    subject=f'FRAUD ALERT - Payment {payment.payment_reference}',
                    template='fraud_alert',
                    context=context,
                    priority='HIGH'
                )
            
            logger.warning(f"Fraud alert sent for payment {payment.payment_reference}")
            
        except Exception as e:
            logger.error(f"Error sending fraud alert: {e}")
    
    def send_verification_failed_notification(self, payment, reason):
        """
        Send notification when payment verification fails
        """
        try:
            school = payment.school
            billing_account = school.billing_account
            
            context = {
                'school_name': school.name,
                'payment_reference': payment.payment_reference,
                'amount': payment.amount,
                'reason': reason,
                'support_email': getattr(settings, 'SUPPORT_EMAIL', '<EMAIL>'),
                'company_name': self.company_name,
            }
            
            self._send_email_notification(
                payment=payment,
                notification_type='VERIFICATION_FAILED',
                recipient=billing_account.billing_contact_email,
                subject=f'Payment Verification Issue - {payment.payment_reference}',
                template='verification_failed',
                context=context
            )
            
            logger.info(f"Verification failed notification sent for {payment.payment_reference}")
            
        except Exception as e:
            logger.error(f"Error sending verification failed notification: {e}")
    
    def _send_email_notification(self, payment, notification_type, recipient, subject, 
                                template, context, receipt=None, priority='NORMAL'):
        """
        Send email notification with optional receipt attachment
        """
        try:
            # Render email content
            html_content = self._render_email_template(template, context)
            text_content = strip_tags(html_content)
            
            # Create email message
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=self.from_email,
                to=[recipient]
            )
            email.attach_alternative(html_content, "text/html")
            
            # Attach receipt PDF if available
            if receipt and receipt.pdf_file:
                try:
                    email.attach_file(receipt.pdf_file.path)
                except Exception as e:
                    logger.warning(f"Could not attach receipt PDF: {e}")
            
            # Send email
            email.send()
            
            # Log notification
            notification = PaymentNotification.objects.create(
                payment=payment,
                notification_type=notification_type,
                channel='EMAIL',
                recipient=recipient,
                subject=subject,
                message=text_content,
                status='SENT',
                sent_at=timezone.now(),
                metadata={'priority': priority}
            )
            
            # Update receipt email tracking if applicable
            if receipt:
                receipt.emailed_to = recipient
                receipt.emailed_at = timezone.now()
                receipt.email_status = 'SENT'
                receipt.save()
            
            return notification
            
        except Exception as e:
            logger.error(f"Error sending email notification: {e}")
            
            # Log failed notification
            PaymentNotification.objects.create(
                payment=payment,
                notification_type=notification_type,
                channel='EMAIL',
                recipient=recipient,
                subject=subject,
                message=f"Failed to send: {str(e)}",
                status='FAILED',
                error_message=str(e)
            )
            
            return None
    
    def _send_sms_notification(self, payment, notification_type, recipient, message, context):
        """
        Send SMS notification (placeholder for SMS service integration)
        """
        try:
            # This is a placeholder for SMS service integration
            # You would integrate with services like Twilio, Africa's Talking, etc.
            
            # For now, just log the SMS
            logger.info(f"SMS would be sent to {recipient}: {message}")
            
            # Log notification
            notification = PaymentNotification.objects.create(
                payment=payment,
                notification_type=notification_type,
                channel='SMS',
                recipient=recipient,
                message=message,
                status='SENT',
                sent_at=timezone.now(),
                metadata=context
            )
            
            return notification
            
        except Exception as e:
            logger.error(f"Error sending SMS notification: {e}")
            
            # Log failed notification
            PaymentNotification.objects.create(
                payment=payment,
                notification_type=notification_type,
                channel='SMS',
                recipient=recipient,
                message=message,
                status='FAILED',
                error_message=str(e)
            )
            
            return None
    
    def _render_email_template(self, template_name, context):
        """
        Render email template with context
        """
        try:
            template_path = f'emails/{template_name}.html'
            return render_to_string(template_path, context)
        except Exception as e:
            logger.error(f"Error rendering email template {template_name}: {e}")
            return self._get_default_email_template(context)
    
    def _get_default_email_template(self, context):
        """
        Get default email template when specific template is not available
        """
        return f"""
        <html>
        <body style="font-family: Arial, sans-serif; margin: 40px;">
            <div style="text-align: center; margin-bottom: 30px;">
                <h2 style="color: #2563eb;">{context.get('company_name', 'ShuleXcel')}</h2>
            </div>
            
            <div style="margin: 20px 0;">
                <p>Dear {context.get('school_name', 'Valued Customer')},</p>
                
                <p>This is to confirm that we have received and processed your payment.</p>
                
                <div style="background: #f3f4f6; padding: 20px; margin: 20px 0; border-radius: 5px;">
                    <p><strong>Payment Reference:</strong> {context.get('payment_reference', 'N/A')}</p>
                    <p><strong>Amount:</strong> KES {context.get('amount', '0.00')}</p>
                    <p><strong>Date:</strong> {context.get('payment_date', 'N/A')}</p>
                </div>
                
                <p>Thank you for your payment!</p>
                
                <p>Best regards,<br>
                {context.get('company_name', 'ShuleXcel')} Team</p>
            </div>
        </body>
        </html>
        """
