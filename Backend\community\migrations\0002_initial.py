# Generated by Django 5.2.1 on 2025-06-01 13:25

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('academics', '0001_initial'),
        ('community', '0001_initial'),
        ('schools', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='clubmeeting',
            name='organizer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='organized_meetings', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='clubmeeting',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='club_meetings', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='comment',
            name='author',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='community_comments', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='comment',
            name='parent_comment',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='replies', to='community.comment'),
        ),
        migrations.AddField(
            model_name='comment',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='community_comments', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='community',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_communities', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='community',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='communities', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='community',
            name='school_class',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='class_communities', to='academics.classroom'),
        ),
        migrations.AddField(
            model_name='community',
            name='stream',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stream_communities', to='academics.stream'),
        ),
        migrations.AddField(
            model_name='clubmeeting',
            name='community',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='meetings', to='community.community'),
        ),
        migrations.AddField(
            model_name='communitymember',
            name='community',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='members', to='community.community'),
        ),
        migrations.AddField(
            model_name='communitymember',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='community_members', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='communitymember',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='community_memberships', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='meetingattendance',
            name='meeting',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance', to='community.clubmeeting'),
        ),
        migrations.AddField(
            model_name='meetingattendance',
            name='member',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='meeting_attendance', to='community.communitymember'),
        ),
        migrations.AddField(
            model_name='meetingattendance',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='meeting_attendance', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='notification',
            name='community',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='community.community'),
        ),
        migrations.AddField(
            model_name='notification',
            name='related_comment',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='notifications', to='community.comment'),
        ),
        migrations.AddField(
            model_name='notification',
            name='related_meeting',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='notifications', to='community.clubmeeting'),
        ),
        migrations.AddField(
            model_name='notification',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='community_notifications', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='notification',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='community_notifications', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='post',
            name='author',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='community_posts', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='post',
            name='community',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='posts', to='community.community'),
        ),
        migrations.AddField(
            model_name='post',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='community_posts', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='notification',
            name='related_post',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='notifications', to='community.post'),
        ),
        migrations.AddField(
            model_name='comment',
            name='post',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='community.post'),
        ),
        migrations.AddField(
            model_name='reaction',
            name='comment',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='reactions', to='community.comment'),
        ),
        migrations.AddField(
            model_name='reaction',
            name='post',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='reactions', to='community.post'),
        ),
        migrations.AddField(
            model_name='reaction',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reactions', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='reaction',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reactions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='community',
            index=models.Index(fields=['community_type'], name='community_c_communi_e477a9_idx'),
        ),
        migrations.AddIndex(
            model_name='community',
            index=models.Index(fields=['is_active'], name='community_c_is_acti_4d5dc8_idx'),
        ),
        migrations.AddIndex(
            model_name='clubmeeting',
            index=models.Index(fields=['status'], name='community_c_status_ffcbb0_idx'),
        ),
        migrations.AddIndex(
            model_name='clubmeeting',
            index=models.Index(fields=['start_time'], name='community_c_start_t_db18b2_idx'),
        ),
        migrations.AddIndex(
            model_name='communitymember',
            index=models.Index(fields=['role'], name='community_c_role_3a89f2_idx'),
        ),
        migrations.AddIndex(
            model_name='communitymember',
            index=models.Index(fields=['is_active'], name='community_c_is_acti_97cb4f_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='communitymember',
            unique_together={('community', 'user')},
        ),
        migrations.AlterUniqueTogether(
            name='meetingattendance',
            unique_together={('meeting', 'member')},
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['is_announcement'], name='community_p_is_anno_00e690_idx'),
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['is_pinned'], name='community_p_is_pinn_42996c_idx'),
        ),
        migrations.AddIndex(
            model_name='notification',
            index=models.Index(fields=['is_read'], name='community_n_is_read_ee5441_idx'),
        ),
        migrations.AddIndex(
            model_name='notification',
            index=models.Index(fields=['notification_type'], name='community_n_notific_171267_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='reaction',
            unique_together={('comment', 'user'), ('post', 'user')},
        ),
    ]
