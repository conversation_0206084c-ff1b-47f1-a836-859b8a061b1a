from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from .models import Stream
from .serializers.stream_serializer import StreamSerializer

class StreamViewSet(viewsets.ModelViewSet):
    """
    API endpoint for streams
    """
    queryset = Stream.objects.all()
    serializer_class = StreamSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = Stream.objects.all()

        # Debug logging
        print("Streams API - Query params:", self.request.query_params)
        print("Streams API - Initial queryset count:", queryset.count())

        # Filter by class
        class_name = self.request.query_params.get('class_name')
        if class_name:
            queryset = queryset.filter(class_name=class_name)
            print(f"Streams API - After class_name filter: {queryset.count()}")

        # Filter by academic year
        academic_year = self.request.query_params.get('academic_year')
        if academic_year:
            queryset = queryset.filter(academic_year=academic_year)
            print(f"Streams API - After academic_year filter: {queryset.count()}")

        # Filter by school
        school = self.request.query_params.get('school')
        if school:
            queryset = queryset.filter(school=school)
            print(f"Streams API - School filter applied with value: {school}")
            print(f"Streams API - After school filter: {queryset.count()}")

            # List all available streams for debugging
            for stream in queryset[:10]:  # Limit to 10 for brevity
                print(f"Stream: {stream.name}, School: {stream.school_id}")

        return queryset
