from django.core.exceptions import ObjectDoesNotExist
from .modules import AVAILABLE_MODULES, is_module_live

def is_module_enabled(module_code, school_branch):
    """
    Check if a module is enabled for a specific school branch

    Args:
        module_code (str): The module code to check
        school_branch: The SchoolBranch object

    Returns:
        bool: True if the module is enabled, False otherwise
    """
    # Core modules are always enabled
    if module_code in AVAILABLE_MODULES and AVAILABLE_MODULES[module_code]['is_core']:
        return True

    # Check if the module activation exists
    try:
        return school_branch.module_activation.is_module_enabled(module_code)
    except (AttributeError, ObjectDoesNotExist):
        # If no module activation exists, only allow core modules
        return False

def is_module_visible(module_code, school_branch, user=None):
    """
    Check if a module should be visible to a user

    Args:
        module_code (str): The module code to check
        school_branch: The SchoolBranch object
        user: The user to check visibility for

    Returns:
        bool: True if the module should be visible, False otherwise
    """
    # First check if the module is enabled
    if not is_module_enabled(module_code, school_branch):
        return False

    # Check if the module is in development
    if not is_module_live(module_code):
        # Only show development modules to superusers
        return user and user.is_superuser

    # Module is live and enabled, so it's visible
    return True

def get_enabled_modules(school_branch):
    """
    Get all enabled modules for a specific school branch

    Args:
        school_branch: The SchoolBranch object

    Returns:
        list: List of enabled module codes
    """
    # Start with core modules
    enabled_modules = [code for code, info in AVAILABLE_MODULES.items() if info['is_core']]

    # Add enabled modules from module activation
    try:
        enabled_modules.extend([
            code for code in school_branch.module_activation.enabled_modules
            if code not in enabled_modules
        ])
    except (AttributeError, ObjectDoesNotExist):
        pass

    return enabled_modules

def get_module_info(module_code):
    """
    Get information about a specific module

    Args:
        module_code (str): The module code

    Returns:
        dict: Module information or None if not found
    """
    return AVAILABLE_MODULES.get(module_code)
