import random
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from django.contrib.auth import get_user_model

from schools.models import SchoolBranch
from fleet.models import (
    Vehicle, Driver, Route, Schedule, VehicleMaintenance,
    StudentTransport, TransportAttendance, FuelConsumption
)
from users.models import Student

User = get_user_model()

class Command(BaseCommand):
    help = 'Seeds the database with sample fleet data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--branch',
            type=int,
            help='School branch ID to create data for',
        )

    def handle(self, *args, **options):
        branch_id = options.get('branch')

        try:
            if branch_id:
                branches = SchoolBranch.objects.filter(id=branch_id)
                if not branches.exists():
                    self.stdout.write(self.style.ERROR(f'School branch with ID {branch_id} not found'))
                    return
            else:
                branches = SchoolBranch.objects.all()
                if not branches.exists():
                    self.stdout.write(self.style.ERROR('No school branches found. Please create at least one school branch first.'))
                    return

            for branch in branches:
                self.stdout.write(self.style.SUCCESS(f'Creating fleet data for branch: {branch.name}'))
                self._create_fleet_data(branch)

            self.stdout.write(self.style.SUCCESS('Successfully created sample fleet data'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error creating sample data: {str(e)}'))

    @transaction.atomic
    def _create_fleet_data(self, branch):
        # Create vehicles
        vehicles = self._create_vehicles(branch)

        # Create drivers
        drivers = self._create_drivers(branch, vehicles)

        # Create routes
        routes = self._create_routes(branch)

        # Create schedules
        schedules = self._create_schedules(branch, routes, vehicles, drivers)

        # Create maintenance records
        self._create_maintenance_records(branch, vehicles)

        # Create fuel consumption records
        self._create_fuel_records(branch, vehicles)

        # Create student transport records
        student_transports = self._create_student_transports(branch, routes)

        # Create attendance records
        self._create_attendance_records(branch, student_transports, schedules)

    def _create_vehicles(self, branch):
        self.stdout.write('Creating vehicles...')
        vehicles = []

        # Check if vehicles already exist for this branch
        existing_vehicles = Vehicle.objects.filter(school_branch=branch)
        if existing_vehicles.exists():
            self.stdout.write(f"  Using {existing_vehicles.count()} existing vehicles for branch {branch.name}")
            return list(existing_vehicles)

        vehicle_names = [
            "Eagle Bus", "Falcon Van", "Hawk Minibus", "Condor Coach",
            "Swift Bus", "Voyager", "Explorer", "Discovery", "Pioneer",
            "Pathfinder", "Navigator", "Trailblazer"
        ]

        # Use different registration prefixes for each branch to avoid conflicts
        registration_prefixes = {
            'MAKINI SCHOOL - MAIN': 'KCX',
            'Main Campus': 'KCY',
            'Secondary Campus': 'KCZ'
        }

        # Default prefix if branch name not in the dictionary
        registration_prefix = registration_prefixes.get(branch.name, f"KC{branch.id}")

        for i in range(1, 6):
            # Check if registration number already exists
            reg_number = f"{registration_prefix} {100+i}Z"
            if Vehicle.objects.filter(registration_number=reg_number).exists():
                self.stdout.write(f"  Registration number {reg_number} already exists, skipping")
                continue

            year = random.randint(2018, 2023)
            purchase_date = datetime(year, random.randint(1, 12), random.randint(1, 28)).date()

            # Calculate expiry dates (1-3 years from now)
            today = timezone.now().date()
            insurance_expiry = today + timedelta(days=random.randint(30, 1095))
            license_expiry = today + timedelta(days=random.randint(30, 1095))

            vehicle = Vehicle.objects.create(
                name=random.choice(vehicle_names),
                vehicle_type=random.choice(['BUS', 'VAN', 'CAR']),
                registration_number=reg_number,
                model=f"Model {chr(65+i)}",
                manufacturer=random.choice(["Toyota", "Isuzu", "Scania", "Hino", "Mercedes-Benz"]),
                year_of_manufacture=year,
                seating_capacity=random.choice([14, 25, 33, 41, 51]),
                fuel_type=random.choice(['PETROL', 'DIESEL']),
                purchase_date=purchase_date,
                purchase_price=random.randint(2000000, 8000000),
                insurance_expiry=insurance_expiry,
                license_expiry=license_expiry,
                status=random.choice(['ACTIVE', 'ACTIVE', 'ACTIVE', 'MAINTENANCE']),
                current_mileage=random.randint(5000, 100000),
                school_branch=branch,
                notes=f"Sample vehicle {i} for testing purposes."
            )
            vehicles.append(vehicle)
            self.stdout.write(f"  Created vehicle: {vehicle.name} ({vehicle.registration_number})")

        return vehicles

    def _create_drivers(self, branch, vehicles):
        self.stdout.write('Creating drivers...')
        drivers = []

        # Check if drivers already exist for this branch
        existing_drivers = Driver.objects.filter(school_branch=branch)
        if existing_drivers.exists():
            self.stdout.write(f"  Using {existing_drivers.count()} existing drivers for branch {branch.name}")
            return list(existing_drivers)

        # Use any existing users as drivers
        existing_users = User.objects.filter(school_branch=branch)
        if existing_users.exists():
            self.stdout.write(f"  Using {min(5, existing_users.count())} existing users as drivers")
            driver_users = list(existing_users)[:5]
        else:
            # If no users exist, create some basic ones
            driver_users = []
            for i in range(1, 6):
                email = f"driver{i}@example.com"
                user, created = User.objects.get_or_create(
                    email=email,
                    defaults={
                        'first_name': f"Driver{i}",
                        'last_name': f"Test",
                        'username': f"driver{i}",
                        'school_branch': branch
                    }
                )
                if created:
                    user.set_password("password123")
                    user.save()
                    self.stdout.write(f"  Created user for driver: {user.email}")
                driver_users.append(user)

        # Now create the drivers
        for i, user in enumerate(driver_users):
            # Check if user already has a driver profile
            if Driver.objects.filter(user=user).exists():
                self.stdout.write(f"  User {user.email} already has a driver profile, skipping")
                continue

            license_expiry = timezone.now().date() + timedelta(days=random.randint(180, 1095))

            # Generate a unique license number
            license_number = f"DL{100000+i+random.randint(1000, 9999)}"
            while Driver.objects.filter(license_number=license_number).exists():
                license_number = f"DL{100000+i+random.randint(1000, 9999)}"

            driver = Driver.objects.create(
                user=user,
                license_number=license_number,
                license_class=random.choice(["A", "B", "C"]),
                license_expiry=license_expiry,
                experience_years=random.randint(1, 15),
                date_of_birth=datetime(random.randint(1970, 1995), random.randint(1, 12), random.randint(1, 28)).date(),
                address=f"P.O. Box {10000+i}, Nairobi",
                emergency_contact_name=f"Emergency Contact {i}",
                emergency_contact_phone=f"07{random.randint(10000000, 99999999)}",
                status=random.choice(['ACTIVE', 'ACTIVE', 'ACTIVE', 'ON_LEAVE']),
                assigned_vehicle=vehicles[i] if i < len(vehicles) else None,
                school_branch=branch,
                notes=f"Sample driver {i} for testing purposes."
            )
            drivers.append(driver)
            self.stdout.write(f"  Created driver: {driver.user.first_name} {driver.user.last_name}")

        return drivers

    def _create_routes(self, branch):
        self.stdout.write('Creating routes...')
        routes = []

        route_data = [
            {
                "name": "North Route",
                "start": "School Main Gate",
                "end": "Northlands Estate",
                "distance": 12.5,
                "time": 45,
                "stops": ["Thika Road Mall", "Garden City", "Safari Park Hotel", "Roysambu"]
            },
            {
                "name": "South Route",
                "start": "School Main Gate",
                "end": "South B Estate",
                "distance": 8.2,
                "time": 35,
                "stops": ["South C", "Nairobi West", "Mombasa Road Junction", "Bellevue"]
            },
            {
                "name": "East Route",
                "start": "School Main Gate",
                "end": "Buruburu Estate",
                "distance": 10.0,
                "time": 40,
                "stops": ["Donholm", "Pipeline", "Umoja", "Innercore"]
            },
            {
                "name": "West Route",
                "start": "School Main Gate",
                "end": "Westlands",
                "distance": 15.3,
                "time": 55,
                "stops": ["Parklands", "Highridge", "Kangemi", "Mountain View"]
            }
        ]

        for i, data in enumerate(route_data):
            route = Route.objects.create(
                name=data["name"],
                description=f"Route from {data['start']} to {data['end']}",
                start_location=data["start"],
                end_location=data["end"],
                distance=data["distance"],
                estimated_time=data["time"],
                stops=data["stops"],
                is_active=True,
                school_branch=branch
            )
            routes.append(route)
            self.stdout.write(f"  Created route: {route.name}")

        return routes

    def _create_schedules(self, branch, routes, vehicles, drivers):
        self.stdout.write('Creating schedules...')
        schedules = []

        schedule_types = ['REGULAR', 'REGULAR', 'REGULAR', 'SPECIAL', 'FIELD_TRIP']
        days_of_week = [["MON", "TUE", "WED", "THU", "FRI"], ["MON", "WED", "FRI"], ["TUE", "THU"]]

        # Morning schedules
        for i, route in enumerate(routes):
            if i < len(vehicles) and i < len(drivers):
                schedule = Schedule.objects.create(
                    name=f"Morning {route.name}",
                    route=route,
                    vehicle=vehicles[i],
                    driver=drivers[i],
                    schedule_type='REGULAR',
                    days_of_week=random.choice(days_of_week),
                    departure_time=timezone.now().replace(hour=6, minute=30 + min(i*15, 29), second=0, microsecond=0).time(),
                    arrival_time=timezone.now().replace(hour=7, minute=30 + min(i*10, 29), second=0, microsecond=0).time(),
                    start_date=timezone.now().date() - timedelta(days=30),
                    is_active=True,
                    school_branch=branch,
                    notes=f"Morning pickup for {route.name}"
                )
                schedules.append(schedule)
                self.stdout.write(f"  Created schedule: {schedule.name}")

        # Afternoon schedules
        for i, route in enumerate(routes):
            if i < len(vehicles) and i < len(drivers):
                schedule = Schedule.objects.create(
                    name=f"Afternoon {route.name}",
                    route=route,
                    vehicle=vehicles[i],
                    driver=drivers[i],
                    schedule_type='REGULAR',
                    days_of_week=random.choice(days_of_week),
                    departure_time=timezone.now().replace(hour=15, minute=30 + min(i*15, 29), second=0, microsecond=0).time(),
                    arrival_time=timezone.now().replace(hour=16, minute=30 + min(i*10, 29), second=0, microsecond=0).time(),
                    start_date=timezone.now().date() - timedelta(days=30),
                    is_active=True,
                    school_branch=branch,
                    notes=f"Afternoon dropoff for {route.name}"
                )
                schedules.append(schedule)
                self.stdout.write(f"  Created schedule: {schedule.name}")

        # Special schedules
        for i in range(2):
            if i < len(routes) and i < len(vehicles) and i < len(drivers):
                schedule_type = random.choice(schedule_types[3:])
                start_date = timezone.now().date() + timedelta(days=random.randint(5, 30))

                schedule = Schedule.objects.create(
                    name=f"Special {schedule_type.lower().replace('_', ' ')} {i+1}",
                    route=routes[i],
                    vehicle=vehicles[i],
                    driver=drivers[i],
                    schedule_type=schedule_type,
                    departure_time=timezone.now().replace(hour=8+i, minute=0, second=0, microsecond=0).time(),
                    arrival_time=timezone.now().replace(hour=16+i, minute=0, second=0, microsecond=0).time(),
                    start_date=start_date,
                    end_date=start_date,
                    is_active=True,
                    school_branch=branch,
                    notes=f"Special {schedule_type.lower().replace('_', ' ')} schedule"
                )
                schedules.append(schedule)
                self.stdout.write(f"  Created schedule: {schedule.name}")

        return schedules

    def _create_maintenance_records(self, branch, vehicles):
        self.stdout.write('Creating maintenance records...')

        maintenance_types = ['ROUTINE', 'REPAIR', 'INSPECTION', 'EMERGENCY']
        statuses = ['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'COMPLETED']

        for vehicle in vehicles:
            # Past maintenance (completed)
            for i in range(random.randint(1, 3)):
                days_ago = random.randint(30, 365)
                scheduled_date = timezone.now().date() - timedelta(days=days_ago)
                completion_date = scheduled_date + timedelta(days=random.randint(1, 5))

                maintenance = VehicleMaintenance.objects.create(
                    vehicle=vehicle,
                    maintenance_type=random.choice(maintenance_types),
                    description=f"Past maintenance {i+1} for {vehicle.name}",
                    service_provider=random.choice(["AutoCare Services", "Vehicle Maintenance Ltd", "QuickFix Garage"]),
                    scheduled_date=scheduled_date,
                    completion_date=completion_date,
                    cost=random.randint(5000, 50000),
                    mileage_at_service=max(0, vehicle.current_mileage - random.randint(1000, 5000)),
                    status='COMPLETED',
                    notes=f"Sample completed maintenance for {vehicle.name}",
                    school_branch=branch
                )
                self.stdout.write(f"  Created maintenance record: {maintenance.maintenance_type} for {vehicle.name}")

            # Future maintenance (scheduled)
            if random.random() < 0.7:  # 70% chance of having future maintenance
                days_ahead = random.randint(5, 60)
                scheduled_date = timezone.now().date() + timedelta(days=days_ahead)

                maintenance = VehicleMaintenance.objects.create(
                    vehicle=vehicle,
                    maintenance_type=random.choice(maintenance_types),
                    description=f"Upcoming maintenance for {vehicle.name}",
                    service_provider=random.choice(["AutoCare Services", "Vehicle Maintenance Ltd", "QuickFix Garage"]),
                    scheduled_date=scheduled_date,
                    cost=random.randint(5000, 50000),
                    mileage_at_service=vehicle.current_mileage,
                    status='SCHEDULED',
                    notes=f"Sample scheduled maintenance for {vehicle.name}",
                    school_branch=branch
                )
                self.stdout.write(f"  Created maintenance record: {maintenance.maintenance_type} for {vehicle.name}")

    def _create_fuel_records(self, branch, vehicles):
        self.stdout.write('Creating fuel consumption records...')

        # Get a staff user for fuel records
        staff_user = User.objects.filter(user_type='staff', school_branch=branch).first()
        if not staff_user:
            # If no staff user exists, use any user
            staff_user = User.objects.filter(school_branch=branch).first()

        for vehicle in vehicles:
            # Create 3-6 fuel records per vehicle
            for i in range(random.randint(3, 6)):
                days_ago = random.randint(1, 90)
                date = timezone.now().date() - timedelta(days=days_ago)

                # Calculate odometer reading (increasing over time)
                base_mileage = max(0, vehicle.current_mileage - random.randint(1000, 5000))
                odometer = base_mileage + (i * random.randint(300, 800))

                fuel_amount = random.randint(30, 80)
                cost_per_liter = random.randint(120, 150)

                fuel_record = FuelConsumption.objects.create(
                    vehicle=vehicle,
                    date=date,
                    fuel_amount=fuel_amount,
                    cost=fuel_amount * cost_per_liter,
                    odometer_reading=odometer,
                    fuel_station=random.choice(["Shell", "Total", "Rubis", "Kenol", "OilLibya"]),
                    filled_by=staff_user,
                    notes=f"Fuel refill for {vehicle.name}",
                    school_branch=branch
                )
                self.stdout.write(f"  Created fuel record: {fuel_record.fuel_amount}L for {vehicle.name}")

    def _create_student_transports(self, branch, routes):
        self.stdout.write('Creating student transport records...')
        student_transports = []

        # Get students from this branch
        students = Student.objects.filter(school_branch=branch)

        if not students.exists():
            self.stdout.write(self.style.WARNING(f"No students found for branch {branch.name}. Skipping student transport creation."))
            return []

        # Limit to 20 students max
        students = students[:20]

        for i, student in enumerate(students):
            route = routes[i % len(routes)]

            # Create pickup and dropoff points based on route stops
            stops = route.stops
            if stops and len(stops) > 0:
                pickup_point = stops[i % len(stops)]
                dropoff_point = stops[(i + 1) % len(stops)]
            else:
                pickup_point = f"Stop {i+1} on {route.name}"
                dropoff_point = route.end_location

            student_transport = StudentTransport.objects.create(
                student=student,
                route=route,
                pickup_point=pickup_point,
                dropoff_point=dropoff_point,
                is_active=True,
                start_date=timezone.now().date() - timedelta(days=random.randint(30, 180)),
                fee_amount=random.choice([5000, 7500, 10000, 12500]),
                payment_frequency=random.choice(['MONTHLY', 'TERMLY']),
                notes=f"Transport arrangement for {student.user.first_name} {student.user.last_name}",
                school_branch=branch
            )
            student_transports.append(student_transport)
            self.stdout.write(f"  Created student transport: {student.user.first_name} {student.user.last_name} on {route.name}")

        return student_transports

    def _create_attendance_records(self, branch, student_transports, schedules):
        self.stdout.write('Creating transport attendance records...')

        if not student_transports or not schedules:
            self.stdout.write(self.style.WARNING("No student transports or schedules found. Skipping attendance creation."))
            return []

        # Get a staff user for recording attendance
        staff_user = User.objects.filter(user_type='staff', school_branch=branch).first()
        if not staff_user:
            # If no staff user exists, use any user
            staff_user = User.objects.filter(school_branch=branch).first()

        # Create attendance for the past 5 days
        for day_offset in range(1, 6):
            date = timezone.now().date() - timedelta(days=day_offset)

            # Get morning and afternoon schedules
            morning_schedules = [s for s in schedules if 'Morning' in s.name]
            afternoon_schedules = [s for s in schedules if 'Afternoon' in s.name]

            if not morning_schedules or not afternoon_schedules:
                continue

            for student_transport in student_transports:
                # Find appropriate schedule based on route
                morning_schedule = next((s for s in morning_schedules if s.route == student_transport.route),
                                       random.choice(morning_schedules))
                afternoon_schedule = next((s for s in afternoon_schedules if s.route == student_transport.route),
                                         random.choice(afternoon_schedules))

                # Morning attendance (pickup)
                if random.random() < 0.9:  # 90% chance of being present
                    picked_up = True
                    pickup_time = datetime.combine(date, morning_schedule.departure_time) + timedelta(minutes=random.randint(-5, 15))
                else:
                    picked_up = False
                    pickup_time = None

                # Afternoon attendance (dropoff)
                if picked_up and random.random() < 0.95:  # 95% chance of being dropped off if picked up
                    dropped_off = True
                    dropoff_time = datetime.combine(date, afternoon_schedule.departure_time) + timedelta(minutes=random.randint(-5, 15))
                else:
                    dropped_off = False
                    dropoff_time = None

                # Create morning attendance
                try:
                    attendance = TransportAttendance.objects.create(
                        student_transport=student_transport,
                        schedule=morning_schedule,
                        date=date,
                        picked_up=picked_up,
                        dropped_off=dropped_off,
                        pickup_time=pickup_time.time() if pickup_time else None,
                        dropoff_time=dropoff_time.time() if dropoff_time else None,
                        notes=None if picked_up and dropped_off else "Student absent or partially transported",
                        recorded_by=staff_user,
                        school_branch=branch
                    )
                    self.stdout.write(f"  Created attendance record for {student_transport.student.user.first_name} on {date}")
                except Exception as e:
                    self.stdout.write(self.style.WARNING(f"Could not create attendance: {str(e)}"))
