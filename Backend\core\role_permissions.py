from rest_framework import permissions
from django.contrib.auth.models import Group

class RoleBasedPermission(permissions.BasePermission):
    """
    Base permission class for role-based permissions.
    Subclasses should define the allowed_user_types attribute.
    """
    allowed_user_types = []  # Override in subclasses
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
            
        # Superusers always have permission
        if request.user.is_superuser:
            return True
            
        # Check if the user has one of the allowed user types
        return request.user.user_type in self.allowed_user_types

class IsSystemAdmin(RoleBasedPermission):
    """Permission class for system administrators."""
    allowed_user_types = ['system_admin']

class IsSchoolAdmin(RoleBasedPermission):
    """Permission class for school administrators."""
    allowed_user_types = ['school_admin']

class IsDeputyPrincipal(RoleBasedPermission):
    """Permission class for deputy principals."""
    allowed_user_types = ['deputy_principal']

class IsBranchAdmin(RoleBasedPermission):
    """Permission class for branch administrators."""
    allowed_user_types = ['branch_admin']

class IsDepartmentHead(RoleBasedPermission):
    """Permission class for department heads."""
    allowed_user_types = ['department_head']

class IsICTAdmin(RoleBasedPermission):
    """Permission class for ICT administrators."""
    allowed_user_types = ['ict_admin']

class IsTeacher(RoleBasedPermission):
    """Permission class for teachers."""
    allowed_user_types = ['teacher']

class IsLibrarian(RoleBasedPermission):
    """Permission class for librarians."""
    allowed_user_types = ['librarian']

class IsCounselor(RoleBasedPermission):
    """Permission class for counselors."""
    allowed_user_types = ['counselor']

class IsAccountant(RoleBasedPermission):
    """Permission class for accountants."""
    allowed_user_types = ['accountant']

class IsSecretary(RoleBasedPermission):
    """Permission class for secretaries."""
    allowed_user_types = ['secretary']

class IsNurse(RoleBasedPermission):
    """Permission class for nurses."""
    allowed_user_types = ['nurse']

class IsMaintenance(RoleBasedPermission):
    """Permission class for maintenance staff."""
    allowed_user_types = ['maintenance']

class IsSecurity(RoleBasedPermission):
    """Permission class for security staff."""
    allowed_user_types = ['security']

class IsDriver(RoleBasedPermission):
    """Permission class for drivers."""
    allowed_user_types = ['driver']

class IsStudent(RoleBasedPermission):
    """Permission class for students."""
    allowed_user_types = ['student']

class IsParent(RoleBasedPermission):
    """Permission class for parents."""
    allowed_user_types = ['parent']

class IsAnyAdmin(RoleBasedPermission):
    """Permission class for any administrative role."""
    allowed_user_types = [
        'system_admin', 'school_admin', 'deputy_principal', 
        'branch_admin', 'department_head', 'ict_admin'
    ]

class IsAcademicStaff(RoleBasedPermission):
    """Permission class for any academic staff role."""
    allowed_user_types = ['teacher', 'librarian', 'counselor']

class IsSupportStaff(RoleBasedPermission):
    """Permission class for any support staff role."""
    allowed_user_types = [
        'accountant', 'secretary', 'nurse', 
        'maintenance', 'security', 'driver'
    ]

class IsAnyStaff(RoleBasedPermission):
    """Permission class for any staff role (academic or support)."""
    allowed_user_types = [
        'teacher', 'librarian', 'counselor',
        'accountant', 'secretary', 'nurse', 
        'maintenance', 'security', 'driver'
    ]

class IsSchoolMember(permissions.BasePermission):
    """
    Permission class to check if a user belongs to the same school as the object.
    The object must have a school or school_branch attribute.
    """
    def has_object_permission(self, request, view, obj):
        # Superusers always have permission
        if request.user.is_superuser:
            return True
            
        # Get the user's school
        user_school = request.user.school
        if not user_school:
            return False
            
        # Get the object's school
        obj_school = None
        if hasattr(obj, 'school'):
            obj_school = obj.school
        elif hasattr(obj, 'school_branch') and obj.school_branch:
            obj_school = obj.school_branch.school
        elif hasattr(obj, 'user') and hasattr(obj.user, 'school'):
            obj_school = obj.user.school
            
        # Check if the schools match
        return user_school == obj_school

class IsBranchMember(permissions.BasePermission):
    """
    Permission class to check if a user belongs to the same branch as the object.
    The object must have a school_branch attribute.
    """
    def has_object_permission(self, request, view, obj):
        # Superusers always have permission
        if request.user.is_superuser:
            return True
            
        # Get the user's branch
        user_branch = request.user.school_branch
        if not user_branch:
            return False
            
        # Get the object's branch
        obj_branch = None
        if hasattr(obj, 'school_branch'):
            obj_branch = obj.school_branch
        elif hasattr(obj, 'user') and hasattr(obj.user, 'school_branch'):
            obj_branch = obj.user.school_branch
            
        # Check if the branches match
        return user_branch == obj_branch

class HasRequiredGroups(permissions.BasePermission):
    """
    Permission class to check if a user belongs to any of the required groups.
    The view must define a required_groups attribute.
    """
    def has_permission(self, request, view):
        # Superusers always have permission
        if request.user.is_superuser:
            return True
            
        # Check if the view has defined required groups
        if not hasattr(view, 'required_groups') or not view.required_groups:
            return True  # No groups required
            
        # Get the user's groups
        user_groups = request.user.groups.values_list('name', flat=True)
        
        # Check if the user belongs to any of the required groups
        return any(group in user_groups for group in view.required_groups)

class IsParentOfStudent(permissions.BasePermission):
    """
    Permission class to check if a user is a parent of the student.
    The object must be a Student instance or have a student attribute.
    """
    def has_object_permission(self, request, view, obj):
        # Superusers always have permission
        if request.user.is_superuser:
            return True
            
        # Get the student
        student = obj if hasattr(obj, 'user') and hasattr(obj.user, 'user_type') and obj.user.user_type == 'student' else None
        if not student and hasattr(obj, 'student'):
            student = obj.student
            
        if not student:
            return False
            
        # Check if the user is a parent
        if request.user.user_type == 'parent' and hasattr(request.user, 'parent_profile'):
            # Check if the student is in the parent's children
            return student in request.user.parent_profile.children.all()
            
        # Check if the user is a staff member with a link to the student
        from users.models import ParentStaffLink
        return ParentStaffLink.objects.filter(staff_user=request.user, student=student).exists()

class IsTeacherOfStudent(permissions.BasePermission):
    """
    Permission class to check if a user is a teacher of the student.
    The object must be a Student instance or have a student attribute.
    """
    def has_object_permission(self, request, view, obj):
        # Superusers always have permission
        if request.user.is_superuser:
            return True
            
        # Check if the user is a teacher
        if request.user.user_type != 'teacher' or not hasattr(request.user, 'teacher'):
            return False
            
        # Get the student
        student = obj if hasattr(obj, 'user') and hasattr(obj.user, 'user_type') and obj.user.user_type == 'student' else None
        if not student and hasattr(obj, 'student'):
            student = obj.student
            
        if not student:
            return False
            
        # Check if the teacher is assigned to the student's class
        teacher = request.user.teacher
        return (
            student.class_name in teacher.assigned_classes.all() or
            teacher.is_class_teacher and student.class_teacher == teacher
        )
