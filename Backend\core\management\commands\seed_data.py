import random
from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group
from core.models import CustomUser
from schools.models import SchoolBranch

# CustomUser is our user model

class Command(BaseCommand):
    help = 'Seeds the database with initial data for development'

    def add_arguments(self, parser):
        parser.add_argument(
            '--flush',
            action='store_true',
            help='Flush existing data before seeding',
        )

    def handle(self, *args, **options):
        if options['flush']:
            self.stdout.write(self.style.WARNING('Flushing existing data...'))
            # Be careful with this in production!
            # This is only for development purposes
            CustomUser.objects.all().delete()
            Group.objects.all().delete()
            SchoolBranch.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Data flushed successfully'))

        self.create_groups()
        self.create_school_branches()
        self.create_users()

        self.stdout.write(self.style.SUCCESS('Database seeded successfully'))

    def create_groups(self):
        self.stdout.write('Creating user groups...')
        groups = [
            'Administration',
            'Teachers',
            'Students',
            'Parents',
            'Finance',
            'Support',
            'Logistics'
        ]

        created_groups = []
        for group_name in groups:
            group, created = Group.objects.get_or_create(name=group_name)
            created_groups.append(group)
            if created:
                self.stdout.write(f'  Created group: {group_name}')
            else:
                self.stdout.write(f'  Group already exists: {group_name}')

        return created_groups

    def create_school_branches(self):
        self.stdout.write('Creating school branches...')

        # First, create a school
        from datetime import date
        from schools.models import School

        school, created = School.objects.get_or_create(
            name='ShuleXcel Academy',
            defaults={
                'code': 'SXA',
                'address': '123 Education Lane, Nairobi',
                'phone': '+254700000000',
                'email': '<EMAIL>',
                'registration_number': 'REG12345',
                'established_date': date(2020, 1, 1),
            }
        )

        if created:
            self.stdout.write(f'  Created school: {school.name}')
        else:
            self.stdout.write(f'  School already exists: {school.name}')

        branches = [
            {
                'name': 'Main Campus',
                'code': 'MC',
                'address': '123 Main St, Nairobi',
                'phone': '+254700000001',
                'email': '<EMAIL>',
                'registration_number': 'BR001',
                'established_date': date(2020, 1, 1),
                'school': school,
            },
            {
                'name': 'South Campus',
                'code': 'SC',
                'address': '456 South Ave, Nairobi',
                'phone': '+254700000002',
                'email': '<EMAIL>',
                'registration_number': 'BR002',
                'established_date': date(2021, 6, 15),
                'school': school,
            },
            {
                'name': 'East Campus',
                'code': 'EC',
                'address': '789 East Blvd, Nairobi',
                'phone': '+254700000003',
                'email': '<EMAIL>',
                'registration_number': 'BR003',
                'established_date': date(2022, 9, 1),
                'school': school,
            }
        ]

        created_branches = []
        for branch_data in branches:
            branch, created = SchoolBranch.objects.get_or_create(
                name=branch_data['name'],
                defaults=branch_data
            )
            created_branches.append(branch)
            if created:
                self.stdout.write(f'  Created branch: {branch.name}')
            else:
                self.stdout.write(f'  Branch already exists: {branch.name}')

        return created_branches

    def create_users(self):
        self.stdout.write('Creating users...')

        # Get groups and branches
        admin_group = Group.objects.get(name='Administration')
        teacher_group = Group.objects.get(name='Teachers')
        student_group = Group.objects.get(name='Students')
        parent_group = Group.objects.get(name='Parents')
        finance_group = Group.objects.get(name='Finance')

        branches = list(SchoolBranch.objects.all())

        # Create admin user
        admin_user, created = CustomUser.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'username': '<EMAIL>',
                'first_name': 'Admin',
                'last_name': 'User',
                'is_staff': True,
                'is_superuser': True,
                'phone': '+254700000000',
                'user_type': 'admin',
                'school_branch': random.choice(branches)
            }
        )

        if created:
            admin_user.set_password('admin123')
            admin_user.save()
            admin_user.groups.add(admin_group)
            self.stdout.write(f'  Created admin user: {admin_user.email}')
        else:
            self.stdout.write(f'  Admin user already exists: {admin_user.email}')

        # Create teacher users
        teachers = [
            {'email': '<EMAIL>', 'first_name': 'John', 'last_name': 'Doe'},
            {'email': '<EMAIL>', 'first_name': 'Jane', 'last_name': 'Smith'},
            {'email': '<EMAIL>', 'first_name': 'Robert', 'last_name': 'Johnson'},
        ]

        for teacher_data in teachers:
            teacher, created = CustomUser.objects.get_or_create(
                email=teacher_data['email'],
                defaults={
                    'username': teacher_data['email'],
                    'first_name': teacher_data['first_name'],
                    'last_name': teacher_data['last_name'],
                    'phone': f'+2547{random.randint(10000000, 99999999)}',
                    'user_type': 'teacher',
                    'school_branch': random.choice(branches)
                }
            )

            if created:
                teacher.set_password('teacher123')
                teacher.save()
                teacher.groups.add(teacher_group)
                self.stdout.write(f'  Created teacher user: {teacher.email}')
            else:
                self.stdout.write(f'  Teacher user already exists: {teacher.email}')

        # Create student users
        students = [
            {'email': '<EMAIL>', 'first_name': 'Michael', 'last_name': 'Brown'},
            {'email': '<EMAIL>', 'first_name': 'Emily', 'last_name': 'Williams'},
            {'email': '<EMAIL>', 'first_name': 'David', 'last_name': 'Jones'},
        ]

        for student_data in students:
            student, created = CustomUser.objects.get_or_create(
                email=student_data['email'],
                defaults={
                    'username': student_data['email'],
                    'first_name': student_data['first_name'],
                    'last_name': student_data['last_name'],
                    'phone': f'+2547{random.randint(10000000, 99999999)}',
                    'user_type': 'student',
                    'school_branch': random.choice(branches)
                }
            )

            if created:
                student.set_password('student123')
                student.save()
                student.groups.add(student_group)
                self.stdout.write(f'  Created student user: {student.email}')
            else:
                self.stdout.write(f'  Student user already exists: {student.email}')

        # Create parent users
        parents = [
            {'email': '<EMAIL>', 'first_name': 'Sarah', 'last_name': 'Miller'},
            {'email': '<EMAIL>', 'first_name': 'James', 'last_name': 'Wilson'},
        ]

        for parent_data in parents:
            parent, created = CustomUser.objects.get_or_create(
                email=parent_data['email'],
                defaults={
                    'username': parent_data['email'],
                    'first_name': parent_data['first_name'],
                    'last_name': parent_data['last_name'],
                    'phone': f'+2547{random.randint(10000000, 99999999)}',
                    'user_type': 'parent',
                    'school_branch': random.choice(branches)
                }
            )

            if created:
                parent.set_password('parent123')
                parent.save()
                parent.groups.add(parent_group)
                self.stdout.write(f'  Created parent user: {parent.email}')
            else:
                self.stdout.write(f'  Parent user already exists: {parent.email}')

        # Create finance staff
        finance, created = CustomUser.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'username': '<EMAIL>',
                'first_name': 'Finance',
                'last_name': 'Staff',
                'phone': f'+2547{random.randint(10000000, 99999999)}',
                'user_type': 'staff',
                'school_branch': random.choice(branches)
            }
        )

        if created:
            finance.set_password('finance123')
            finance.save()
            finance.groups.add(finance_group)
            self.stdout.write(f'  Created finance user: {finance.email}')
        else:
            self.stdout.write(f'  Finance user already exists: {finance.email}')
