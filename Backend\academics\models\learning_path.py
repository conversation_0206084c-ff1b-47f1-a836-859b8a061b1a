from django.db import models

class LearningPath(models.Model):
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE, related_name='learning_paths')
    current_level = models.CharField(max_length=100)
    target_level = models.CharField(max_length=100)
    milestones = models.JSONField(default=list, blank=True)
    completion_date = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'academics'
        unique_together = ('subject', 'current_level', 'target_level')
        ordering = ['subject', 'current_level']

    def __str__(self):
        return f"{self.subject} ({self.current_level} → {self.target_level})" 