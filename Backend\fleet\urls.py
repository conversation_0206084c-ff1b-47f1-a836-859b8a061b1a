from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    VehicleViewSet, DriverViewSet, RouteViewSet, ScheduleViewSet,
    VehicleMaintenanceViewSet, StudentTransportViewSet, TransportAttendanceViewSet,
    FuelConsumptionViewSet, FleetDashboardViewSet
)
from .transport_fee_views import TransportFeeViewSet, TransportFeeDiscountViewSet

router = DefaultRouter()
router.register('vehicles', VehicleViewSet, basename='vehicle')
router.register('drivers', DriverViewSet, basename='driver')
router.register('routes', RouteViewSet, basename='route')
router.register('schedules', ScheduleViewSet, basename='schedule')
router.register('maintenance', VehicleMaintenanceViewSet, basename='vehicle-maintenance')
router.register('student-transport', StudentTransportViewSet, basename='student-transport')
router.register('attendance', TransportAttendanceViewSet, basename='transport-attendance')
router.register('fuel', FuelConsumptionViewSet, basename='fuel-consumption')
router.register('dashboard', FleetDashboardViewSet, basename='fleet-dashboard')
router.register('transport-fees', TransportFeeViewSet, basename='transport-fee')
router.register('transport-discounts', TransportFeeDiscountViewSet, basename='transport-fee-discount')

urlpatterns = [
    path('', include(router.urls)),
]
