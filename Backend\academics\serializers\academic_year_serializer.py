from rest_framework import serializers
from academics.models import AcademicYear

class AcademicYearSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source='year', read_only=False)
    terms = serializers.SerializerMethodField()
    school_branch_name = serializers.SerializerMethodField()
    terms_data = serializers.ListField(write_only=True, required=False)

    class Meta:
        model = AcademicYear
        fields = ['id', 'year', 'name', 'start_date', 'end_date', 'school_name', 'school_branch',
                 'school_branch_name', 'is_current', 'is_archived', 'terms', 'terms_data']
        validators = [
            serializers.UniqueTogetherValidator(
                queryset=AcademicYear.objects.all(),
                fields=('year', 'school_branch'),
                message="An academic year with this name already exists for this branch."
            )
        ]
    
    def get_terms(self, obj):
        # This is a placeholder - you'll need to implement the actual method
        # based on your Term model and relationship
        return []
    
    def get_school_branch_name(self, obj):
        if obj.school_branch:
            return obj.school_branch.name
        return None
