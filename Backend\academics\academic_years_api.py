from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import AcademicYear
from .serializers.academic_year_serializer import AcademicYearSerializer

class AcademicYearViewSet(viewsets.ModelViewSet):
    """
    API endpoint for academic years
    """
    queryset = AcademicYear.objects.all()
    serializer_class = AcademicYearSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = AcademicYear.objects.all()

        # Debug logging
        print("Academic Years API - Query params:", self.request.query_params)
        print("Academic Years API - Initial queryset count:", queryset.count())

        # Filter by school
        school = self.request.query_params.get('school')
        if school:
            # Handle both 'school' and 'school_name' parameters for backward compatibility
            queryset = queryset.filter(school_name=school)
            print(f"Academic Years API - After school filter (school_name={school}):", queryset.count())

        # Filter by school branch
        school_branch = self.request.query_params.get('school_branch')
        if school_branch:
            queryset = queryset.filter(school_branch=school_branch)
            print(f"Academic Years API - After branch filter (school_branch={school_branch}):", queryset.count())

        # Filter by current status
        is_current = self.request.query_params.get('is_current')
        if is_current is not None:
            is_current_bool = is_current.lower() == 'true'
            queryset = queryset.filter(is_current=is_current_bool)
            print(f"Academic Years API - After current filter (is_current={is_current_bool}):", queryset.count())

        # Filter by archived status
        is_archived = self.request.query_params.get('is_archived')
        if is_archived is not None:
            is_archived_bool = is_archived.lower() == 'true'
            queryset = queryset.filter(is_archived=is_archived_bool)
            print(f"Academic Years API - After archived filter (is_archived={is_archived_bool}):", queryset.count())

        # Filter by template status
        is_template = self.request.query_params.get('is_template')
        if is_template is not None:
            is_template_bool = is_template.lower() == 'true'
            queryset = queryset.filter(is_template=is_template_bool)
            print(f"Academic Years API - After template filter (is_template={is_template_bool}):", queryset.count())

        # Order by start date (descending)
        queryset = queryset.order_by('-start_date')

        # Final debug log
        print("Academic Years API - Final queryset count:", queryset.count())
        if queryset.count() > 0:
            print("Academic Years API - Sample data:", list(queryset.values('id', 'year', 'school_name', 'is_current', 'is_archived')[:3]))

        return queryset

    @action(detail=True, methods=['post'])
    def archive(self, request, pk=None):
        """
        Archive an academic year
        """
        academic_year = self.get_object()

        # Cannot archive current academic year
        if academic_year.is_current:
            return Response(
                {"detail": "Cannot archive the current academic year. Set another academic year as current first."},
                status=status.HTTP_400_BAD_REQUEST
            )

        academic_year.is_archived = True
        academic_year.save()

        serializer = self.get_serializer(academic_year)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def unarchive(self, request, pk=None):
        """
        Unarchive an academic year
        """
        academic_year = self.get_object()
        academic_year.is_archived = False
        academic_year.save()

        serializer = self.get_serializer(academic_year)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def set_current(self, request, pk=None):
        """
        Set an academic year as current
        """
        academic_year = self.get_object()

        # Unset current flag for all other academic years in the same branch
        if academic_year.school_branch:
            AcademicYear.objects.filter(
                school_branch=academic_year.school_branch,
                is_current=True
            ).exclude(pk=academic_year.pk).update(is_current=False)
        else:
            AcademicYear.objects.filter(
                school_name=academic_year.school_name,
                is_current=True
            ).exclude(pk=academic_year.pk).update(is_current=False)

        # Set this academic year as current
        academic_year.is_current = True
        academic_year.save()

        serializer = self.get_serializer(academic_year)
        return Response(serializer.data)
