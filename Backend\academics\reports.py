from django.template.loader import render_to_string
from weasyprint import HTML
import pandas as pd
from .models import (
    TermResult, StreamPerformance, SubjectPerformance,
    TopStudent, PerformanceAnalytics
)

class ReportGenerator:
    def generate_student_report(self, student, term):
        """Generate comprehensive student report"""
        context = {
            'student': student,
            'term': term,
            'academic_performance': self._get_academic_performance(student, term),
            'value_addition': self._get_value_addition(student, term),
            'subject_analysis': self._get_subject_analysis(student, term),
            'recommendations': self._get_recommendations(student, term)
        }
        
        html_string = render_to_string('reports/student_report.html', context)
        return HTML(string=html_string).write_pdf()

    def generate_stream_report(self, stream, term):
        """Generate stream performance report"""
        context = {
            'stream': stream,
            'term': term,
            'performance_metrics': self._get_stream_metrics(stream, term),
            'subject_analysis': self._get_stream_subject_analysis(stream, term),
            'top_performers': self._get_top_performers(stream, term)
        }
        
        html_string = render_to_string('reports/stream_report.html', context)
        return HTML(string=html_string).write_pdf()

class AnalyticsReportGenerator:
    def generate_value_addition_report(self, term):
        """Generate value addition analysis report"""
        context = {
            'term': term,
            'value_metrics': self._calculate_value_metrics(term),
            'improvement_analysis': self._analyze_improvements(term),
            'intervention_impact': self._analyze_intervention_impact(term)
        }
        
        html_string = render_to_string('reports/value_addition_report.html', context)
        return HTML(string=html_string).write_pdf()

class ParentReportGenerator:
    def generate_parent_update(self, student, term):
        """Generate parent progress update"""
        context = {
            'student': student,
            'term': term,
            'progress_summary': self._get_progress_summary(student, term),
            'areas_of_concern': self._identify_concerns(student, term),
            'recommended_actions': self._get_parent_recommendations(student, term)
        }
        
        html_string = render_to_string('reports/parent_update.html', context)
        return HTML(string=html_string).write_pdf()
