import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import PageMeta from "../../components/common/PageMeta";
import { useAuth } from "../../context/AuthContext";
import WelcomeMessage from "../../components/dashboard/WelcomeMessage";

// Import role-specific dashboard components
import AdminDashboard from "../admin/Dashboard";
import TeacherDashboardContent from "../../components/dashboard/TeacherDashboardContent";
import StudentDashboardContent from "../../components/dashboard/StudentDashboardContent";
import ParentDashboardContent from "../../components/dashboard/ParentDashboardContent";

export default function Home() {
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/signin');
    }
  }, [isAuthenticated, navigate]);

  // Determine which dashboard to show based on user role
  const renderDashboardByRole = () => {
    if (!user) return null;

    // Check user role and render appropriate dashboard
    if (user.is_superuser || user.is_staff) {
      return <AdminDashboard />;
    } else if (user.role === 'teacher') {
      return <TeacherDashboardContent />;
    } else if (user.role === 'student') {
      return <StudentDashboardContent />;
    } else if (user.role === 'parent') {
      return <ParentDashboardContent />;
    } else {
      // Default to admin dashboard if role is unknown
      return <AdminDashboard />;
    }
  };

  // Show loading state while checking authentication
  if (!isAuthenticated) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  const location = useLocation();

  return (
    <>
      <PageMeta
        title="Dashboard | ShuleXcel"
        description="ShuleXcel personalized dashboard."
      />
      <div className="container mx-auto px-4 py-6">
        <WelcomeMessage
          userName={user?.first_name || user?.username}
          userType={user?.user_type}
        />
        {renderDashboardByRole()}
      </div>
    </>
  );
}
