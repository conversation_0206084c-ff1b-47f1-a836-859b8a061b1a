from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from datetime import timed<PERSON>ta
from schools.models import SchoolBranch
from fees.models import FeeStructure, FeeType
from fleet.models import Route
from fleet.transport_fees import TransportFee, TransportFeeDiscount

class Command(BaseCommand):
    help = 'Set up test transport fees linking routes with fee structures'

    def add_arguments(self, parser):
        parser.add_argument(
            '--branch',
            type=int,
            help='School branch ID to create data for (optional)',
        )

    @transaction.atomic
    def handle(self, *args, **options):
        branch_id = options.get('branch')
        
        if branch_id:
            branches = SchoolBranch.objects.filter(id=branch_id)
            if not branches.exists():
                self.stdout.write(self.style.ERROR(f'School branch with ID {branch_id} not found'))
                return
        else:
            branches = SchoolBranch.objects.all()
            if not branches.exists():
                self.stdout.write(self.style.ERROR('No school branches found. Please create at least one school branch first.'))
                return
        
        for branch in branches:
            self.stdout.write(f'Setting up transport fees for {branch.name}')
            
            # Get routes for this branch
            routes = Route.objects.filter(school_branch=branch)
            if not routes.exists():
                self.stdout.write(self.style.WARNING(f'No routes found for {branch.name}. Skipping.'))
                continue
                
            # Get fee structures for this branch's school
            fee_structures = FeeStructure.objects.filter(school=branch.school)
            if not fee_structures.exists():
                self.stdout.write(self.style.WARNING(f'No fee structures found for {branch.school.name}. Skipping.'))
                continue
            
            # For each route and fee structure, create a transport fee
            for route in routes:
                for fee_structure in fee_structures:
                    # Check if transport fee already exists
                    existing = TransportFee.objects.filter(
                        route=route,
                        fee_structure=fee_structure
                    )
                    
                    if existing.exists():
                        self.stdout.write(f'  Transport fee already exists for {route.name} in {fee_structure.name}. Skipping.')
                        continue
                    
                    # Create or get a fee type for transport
                    fee_type, created = FeeType.objects.get_or_create(
                        name=f'Transport Fee - {route.name}',
                        school=branch.school,
                        fee_structure=fee_structure,
                        defaults={
                            'description': f'Transport fee for route {route.name}',
                            'amount': 5000.00,  # Default amount
                            'is_mandatory': False
                        }
                    )
                    
                    if created:
                        self.stdout.write(f'  Created fee type: {fee_type.name}')
                    
                    # Calculate fee based on route distance
                    base_fee = 3000.00
                    distance_fee = route.distance * 100.00  # 100 per km
                    total_fee = base_fee + distance_fee
                    
                    # Create transport fee
                    transport_fee = TransportFee.objects.create(
                        route=route,
                        fee_type=fee_type,
                        fee_structure=fee_structure,
                        amount=total_fee,
                        is_active=True,
                        school_branch=branch
                    )
                    
                    self.stdout.write(f'  Created transport fee: {transport_fee}')
                    
                    # Create a discount for this transport fee
                    today = timezone.now().date()
                    
                    discount = TransportFeeDiscount.objects.create(
                        name=f'Early Bird Discount - {route.name}',
                        transport_fee=transport_fee,
                        discount_type='PERCENTAGE',
                        percentage=10.00,  # 10% discount
                        start_date=today,
                        end_date=today + timedelta(days=90),  # Valid for 90 days
                        is_active=True,
                        school_branch=branch
                    )
                    
                    self.stdout.write(f'  Created discount: {discount.name}')
            
        self.stdout.write(self.style.SUCCESS('Transport fees setup complete'))
