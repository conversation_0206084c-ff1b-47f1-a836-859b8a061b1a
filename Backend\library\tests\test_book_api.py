from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from library.models import Book
from schools.models import School, SchoolBranch
from core.models import CustomUser

class BookAPITest(TestCase):
    def setUp(self):
        # Create a test school
        self.school = School.objects.create(
            name="Test School",
            address="123 Test Street",
            phone="1234567890",
            email="<EMAIL>",
            registration_number="TEST-REG-001",
            established_date="2000-01-01"
        )

        # Create a test school branch
        self.school_branch = SchoolBranch.objects.create(
            school=self.school,
            name="Test School Branch",
            address="123 Test Street",
            phone="1234567890",
            email="<EMAIL>",
            registration_number="TEST-BR-REG-001",
            established_date="2000-01-01"
        )

        # Create a test admin user
        self.admin_user = CustomUser.objects.create_user(
            username="testadmin",
            email="<EMAIL>",
            password="testpassword",
            is_staff=True,
            school_branch=self.school_branch
        )

        # Create a test librarian user
        self.librarian_user = CustomUser.objects.create_user(
            username="testlibrarian",
            email="<EMAIL>",
            password="testpassword",
            school_branch=self.school_branch
        )

        # Create a test book
        self.book = Book.objects.create(
            title="Test Book",
            author="Test Author",
            isbn="1234567890123",
            publisher="Test Publisher",
            publication_year=2023,
            category="Fiction",
            pages=200,
            quantity=5,
            available_quantity=5,
            location="Shelf A",
            status="AVAILABLE",
            school_branch=self.school_branch,
            added_by=self.admin_user
        )

        # Initialize the API client
        self.client = APIClient()

    def test_get_all_books(self):
        """Test retrieving all books"""
        # Authenticate as admin
        self.client.force_authenticate(user=self.admin_user)

        # Make the request
        url = reverse('book-list')
        response = self.client.get(url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Handle both paginated and non-paginated responses
        if isinstance(response.data, dict) and 'results' in response.data:
            # Paginated response
            self.assertEqual(len(response.data['results']), 1)
            self.assertEqual(response.data['results'][0]['title'], 'Test Book')
        else:
            # Non-paginated response
            self.assertEqual(len(response.data), 1)
            self.assertEqual(response.data[0]['title'], 'Test Book')

    def test_get_book_detail(self):
        """Test retrieving a specific book"""
        # Authenticate as admin
        self.client.force_authenticate(user=self.admin_user)

        # Make the request
        url = reverse('book-detail', args=[self.book.id])
        response = self.client.get(url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['title'], 'Test Book')
        self.assertEqual(response.data['author'], 'Test Author')

    def test_create_book(self):
        """Test creating a new book"""
        # Authenticate as admin
        self.client.force_authenticate(user=self.admin_user)

        # Prepare the data
        data = {
            'title': 'New Test Book',
            'author': 'New Test Author',
            'isbn': '9876543210123',
            'publisher': 'New Test Publisher',
            'publication_year': 2022,
            'category': 'Non-Fiction',
            'pages': 300,
            'quantity': 3,
            'available_quantity': 3,
            'location': 'Shelf B',
            'status': 'AVAILABLE',
            'school_branch': self.school_branch.id
        }

        # Make the request
        url = reverse('book-list')
        response = self.client.post(url, data, format='json')

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['title'], 'New Test Book')

        # Check that the book was created in the database
        self.assertEqual(Book.objects.count(), 2)

    def test_update_book(self):
        """Test updating a book"""
        # Authenticate as admin
        self.client.force_authenticate(user=self.admin_user)

        # Prepare the data
        data = {
            'title': 'Updated Test Book',
            'quantity': 10,
            'available_quantity': 8
        }

        # Make the request
        url = reverse('book-detail', args=[self.book.id])
        response = self.client.patch(url, data, format='json')

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['title'], 'Updated Test Book')
        self.assertEqual(response.data['quantity'], 10)

        # Refresh the book from the database
        self.book.refresh_from_db()
        self.assertEqual(self.book.title, 'Updated Test Book')

    def test_delete_book(self):
        """Test deleting a book"""
        # Authenticate as admin
        self.client.force_authenticate(user=self.admin_user)

        # Make the request
        url = reverse('book-detail', args=[self.book.id])
        response = self.client.delete(url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # Check that the book was deleted from the database
        self.assertEqual(Book.objects.count(), 0)

    def test_unauthorized_access(self):
        """Test that unauthenticated users cannot access the API"""
        # Make the request without authentication
        url = reverse('book-list')
        response = self.client.get(url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
