from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator, EmailValidator
from django.core.exceptions import ValidationError
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import Generic<PERSON><PERSON>ign<PERSON><PERSON>
from schools.models import SchoolBranch
from core.models import Custom<PERSON>ser
import json
import uuid
from datetime import datetime, timedelta
from cryptography.fernet import <PERSON><PERSON><PERSON>
from django.conf import settings
import os
from django.contrib.auth import get_user_model
from .license_models import LicenseSubscription

User = get_user_model()

class SchoolProfile(models.Model):
    school_branch = models.OneToOneField(SchoolBranch, on_delete=models.CASCADE, related_name='profile')
    logo = models.ImageField(upload_to='school_logos/', blank=True, null=True)
    banner = models.ImageField(upload_to='school_banners/', blank=True, null=True)
    mission = models.TextField(blank=True, null=True)
    vision = models.TextField(blank=True, null=True)
    core_values = models.TextField(blank=True, null=True)
    about = models.TextField(blank=True, null=True)
    history = models.TextField(blank=True, null=True)
    achievements = models.TextField(blank=True, null=True)
    contact_email = models.EmailField(blank=True, null=True)
    contact_phone = models.CharField(max_length=20, blank=True, null=True)
    website = models.URLField(blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    social_media_links = models.JSONField(blank=True, null=True, help_text="JSON object with social media links")
    school_colors = models.JSONField(blank=True, null=True, help_text="JSON object with primary and secondary colors")
    updated_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='updated_school_profiles')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Profile for {self.school_branch.name}"

class SystemConfiguration(models.Model):
    GRADING_SYSTEM_CHOICES = [
        ('PERCENTAGE', 'Percentage'),
        ('LETTER_GRADE', 'Letter Grade'),
        ('GPA', 'GPA'),
        ('CUSTOM', 'Custom'),
    ]

    school_branch = models.OneToOneField(SchoolBranch, on_delete=models.CASCADE, related_name='system_configuration')
    academic_year_start_month = models.PositiveIntegerField(default=1, help_text="Month number (1-12)")
    academic_year_end_month = models.PositiveIntegerField(default=12, help_text="Month number (1-12)")
    current_academic_year = models.CharField(max_length=20, blank=True, null=True)
    current_term = models.CharField(max_length=20, blank=True, null=True)
    grading_system = models.CharField(max_length=20, choices=GRADING_SYSTEM_CHOICES, default='PERCENTAGE')
    grading_scale = models.JSONField(blank=True, null=True, help_text="JSON object with grade boundaries")
    attendance_tracking_method = models.CharField(max_length=50, default='DAILY')
    # Payment and notification settings
    enable_online_payments = models.BooleanField(default=False)
    enable_sms_notifications = models.BooleanField(default=False)
    enable_email_notifications = models.BooleanField(default=True)
    default_language = models.CharField(max_length=20, default='en')
    timezone = models.CharField(max_length=50, default='UTC')
    date_format = models.CharField(max_length=20, default='YYYY-MM-DD')
    time_format = models.CharField(max_length=20, default='HH:mm')
    system_maintenance_mode = models.BooleanField(default=False)
    updated_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='updated_system_configurations')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"System Configuration for {self.school_branch.name}"

class Permission(models.Model):
    """Model for storing system permissions"""
    MODULE_CHOICES = [
        ('USERS', 'Users Management'),
        ('ACADEMICS', 'Academics'),
        ('STUDENTS', 'Students'),
        ('TEACHERS', 'Teachers'),
        ('PARENTS', 'Parents'),
        ('STAFF', 'Staff'),
        ('LIBRARY', 'Library'),
        ('INVENTORY', 'Inventory'),
        ('FINANCE', 'Finance'),
        ('SETTINGS', 'Settings'),
        ('REPORTS', 'Reports'),
        ('COMMUNICATION', 'Communication'),
        ('ATTENDANCE', 'Attendance'),
        ('EXAMS', 'Exams'),
        ('TIMETABLE', 'Timetable'),
    ]

    ACTION_CHOICES = [
        ('VIEW', 'View'),
        ('CREATE', 'Create'),
        ('EDIT', 'Edit'),
        ('DELETE', 'Delete'),
        ('EXPORT', 'Export'),
        ('IMPORT', 'Import'),
        ('APPROVE', 'Approve'),
        ('REJECT', 'Reject'),
    ]

    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=50, unique=True, help_text="Unique code for this permission (e.g., USERS_VIEW)")
    module = models.CharField(max_length=20, choices=MODULE_CHOICES)
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.code})"

    class Meta:
        unique_together = ['module', 'action']
        ordering = ['module', 'action']

class Role(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    permissions = models.ManyToManyField(Permission, related_name='roles', blank=True)
    is_active = models.BooleanField(default=True)
    is_system_role = models.BooleanField(default=False, help_text="System roles cannot be deleted")
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='roles')
    created_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='created_roles')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        unique_together = ['name', 'school_branch']

class UserRole(models.Model):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='settings_user_roles')
    role = models.ForeignKey(Role, on_delete=models.CASCADE, related_name='users')
    is_primary = models.BooleanField(default=False)
    assigned_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='assigned_settings_roles')
    assigned_at = models.DateTimeField(default=timezone.now)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='settings_user_roles')

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.role.name}"

    class Meta:
        unique_together = ['user', 'role', 'school_branch']

class BackupConfiguration(models.Model):
    BACKUP_FREQUENCY_CHOICES = [
        ('DAILY', 'Daily'),
        ('WEEKLY', 'Weekly'),
        ('MONTHLY', 'Monthly'),
        ('MANUAL', 'Manual Only'),
    ]

    BACKUP_STORAGE_CHOICES = [
        ('LOCAL', 'Local Storage'),
        ('CLOUD', 'Cloud Storage'),
        ('BOTH', 'Both Local and Cloud'),
    ]

    school_branch = models.OneToOneField(SchoolBranch, on_delete=models.CASCADE, related_name='backup_configuration')
    is_enabled = models.BooleanField(default=True)
    backup_frequency = models.CharField(max_length=20, choices=BACKUP_FREQUENCY_CHOICES, default='WEEKLY')
    backup_time = models.TimeField(default='00:00')
    backup_day = models.PositiveIntegerField(blank=True, null=True, help_text="Day of week (1-7) or day of month (1-31)")
    backup_storage = models.CharField(max_length=20, choices=BACKUP_STORAGE_CHOICES, default='LOCAL')
    cloud_storage_settings = models.JSONField(blank=True, null=True, help_text="JSON object with cloud storage credentials")
    local_storage_path = models.CharField(max_length=255, blank=True, null=True)
    retention_period_days = models.PositiveIntegerField(default=30)
    include_media_files = models.BooleanField(default=True)
    include_database = models.BooleanField(default=True)
    include_configurations = models.BooleanField(default=True)
    encryption_enabled = models.BooleanField(default=False)
    compression_enabled = models.BooleanField(default=True)
    notify_on_success = models.BooleanField(default=True)
    notify_on_failure = models.BooleanField(default=True)
    notification_email = models.EmailField(blank=True, null=True)
    updated_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='updated_backup_configurations')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Backup Configuration for {self.school_branch.name}"

class Backup(models.Model):
    BACKUP_STATUS_CHOICES = [
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('RESTORED', 'Restored'),
    ]

    BACKUP_TYPE_CHOICES = [
        ('AUTOMATIC', 'Automatic'),
        ('MANUAL', 'Manual'),
        ('PRE_UPDATE', 'Pre-Update'),
    ]

    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='backups')
    backup_name = models.CharField(max_length=255)
    backup_type = models.CharField(max_length=20, choices=BACKUP_TYPE_CHOICES, default='MANUAL')
    status = models.CharField(max_length=20, choices=BACKUP_STATUS_CHOICES, default='IN_PROGRESS')
    file_path = models.CharField(max_length=255, blank=True, null=True)
    file_size = models.PositiveIntegerField(blank=True, null=True, help_text="Size in bytes")
    cloud_storage_url = models.URLField(blank=True, null=True)
    started_at = models.DateTimeField(default=timezone.now)
    completed_at = models.DateTimeField(blank=True, null=True)
    includes_media = models.BooleanField(default=True)
    includes_database = models.BooleanField(default=True)
    includes_configurations = models.BooleanField(default=True)
    is_encrypted = models.BooleanField(default=False)
    is_compressed = models.BooleanField(default=True)
    notes = models.TextField(blank=True, null=True)
    error_message = models.TextField(blank=True, null=True)
    created_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='created_backups')

    def __str__(self):
        return self.backup_name

    class Meta:
        ordering = ['-started_at']

class Integration(models.Model):
    INTEGRATION_TYPE_CHOICES = [
        ('PAYMENT', 'Payment Gateway'),
        ('SMS', 'SMS Service'),
        ('EMAIL', 'Email Service'),
        ('STORAGE', 'Cloud Storage'),
        ('LMS', 'Learning Management System'),
        ('ANALYTICS', 'Analytics Service'),
        ('OTHER', 'Other'),
    ]

    INTEGRATION_STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('INACTIVE', 'Inactive'),
        ('TESTING', 'Testing'),
        ('FAILED', 'Failed'),
    ]

    name = models.CharField(max_length=100)
    integration_type = models.CharField(max_length=20, choices=INTEGRATION_TYPE_CHOICES)
    provider = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    api_key = models.CharField(max_length=255, blank=True, null=True)
    api_secret = models.CharField(max_length=255, blank=True, null=True)
    configuration = models.JSONField(blank=True, null=True, help_text="JSON object with integration settings")
    webhook_url = models.URLField(blank=True, null=True)
    callback_url = models.URLField(blank=True, null=True)
    status = models.CharField(max_length=20, choices=INTEGRATION_STATUS_CHOICES, default='INACTIVE')
    is_enabled = models.BooleanField(default=False)
    last_sync = models.DateTimeField(blank=True, null=True)
    error_message = models.TextField(blank=True, null=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='integrations')
    created_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='created_integrations')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.provider})"

    class Meta:
        unique_together = ['name', 'integration_type', 'school_branch']

class AuditLog(models.Model):
    ACTION_CHOICES = [
        ('CREATE', 'Create'),
        ('UPDATE', 'Update'),
        ('DELETE', 'Delete'),
        ('LOGIN', 'Login'),
        ('LOGOUT', 'Logout'),
        ('EXPORT', 'Export'),
        ('IMPORT', 'Import'),
        ('OTHER', 'Other'),
    ]

    user = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='audit_logs')
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    entity_type = models.CharField(max_length=100, help_text="Model or object type that was affected")
    entity_id = models.CharField(max_length=100, blank=True, null=True, help_text="ID of the affected entity")
    entity_name = models.CharField(max_length=255, blank=True, null=True, help_text="Name or description of the affected entity")
    action_details = models.JSONField(blank=True, null=True, help_text="JSON object with details of the action")
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)
    timestamp = models.DateTimeField(default=timezone.now)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='audit_logs')

    def __str__(self):
        return f"{self.action} {self.entity_type} by {self.user} at {self.timestamp}"

    class Meta:
        ordering = ['-timestamp']