from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from core.permissions import BranchBasedPermission, RoleBasedPermissionMixin, IsSchoolBranchStaff
from rest_framework.permissions import DjangoModelPermissions, IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.db.models import Count, Q
from django.utils import timezone
from .models import School, SchoolBranch
from .serializers import SchoolSerializer, SchoolBranchSerializer

class BaseSchoolViewSet(RoleBasedPermissionMixin, viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated, IsSchoolBranchStaff]
    required_groups = ['Administration', 'School Administrators']

class SchoolViewSet(viewsets.ModelViewSet):
    queryset = School.objects.all()
    serializer_class = SchoolSerializer

    # For development - comment these out for testing
    # permission_classes = [permissions.IsAuthenticated]
    # authentication_classes = [JWTAuthentication]

    # For testing - remove these in production
    permission_classes = []
    authentication_classes = []

    def get_queryset(self):
        queryset = self.queryset

        # For archive/unarchive/retrieve actions, we need to include archived schools
        action = getattr(self, 'action', None)
        if action in ['archive', 'unarchive', 'retrieve']:
            # Don't filter by is_archived for these actions
            pass
        else:
            # Get query parameters
            show_archived_param = self.request.query_params.get('show_archived', 'false')
            show_archived = show_archived_param.lower() == 'true'
            print(f"show_archived_param: {show_archived_param}, show_archived: {show_archived}")

            # Filter archived schools based on the show_archived parameter
            if not show_archived:
                queryset = queryset.filter(is_archived=False)
                print(f"Filtering out archived schools, queryset count: {queryset.count()}")
            else:
                print(f"Including archived schools, queryset count: {queryset.count()}")

        # Get query parameters
        min_branch_count = self.request.query_params.get('min_branch_count')
        max_branch_count = self.request.query_params.get('max_branch_count')

        # Apply branch count filtering if parameters are provided
        if min_branch_count is not None or max_branch_count is not None:
            # Annotate queryset with branch count
            queryset = queryset.annotate(branch_count=Count('branch'))

            # Apply min branch count filter
            if min_branch_count is not None:
                try:
                    min_count = int(min_branch_count)
                    queryset = queryset.filter(branch_count__gte=min_count)
                except ValueError:
                    pass  # Ignore invalid values

            # Apply max branch count filter
            if max_branch_count is not None:
                try:
                    max_count = int(max_branch_count)
                    queryset = queryset.filter(branch_count__lte=max_count)
                except ValueError:
                    pass  # Ignore invalid values

        # For development - return filtered queryset
        return queryset

        # For production - uncomment this code
        # if self.request.user.is_superuser:
        #     return queryset
        # return queryset.filter(id=self.request.user.school_branch.school.id)

    @action(detail=True, methods=['post'])
    def archive(self, request, pk=None):
        school = self.get_object()
        if school.is_archived:
            return Response(
                {"detail": "School is already archived."},
                status=status.HTTP_400_BAD_REQUEST
            )

        school.is_archived = True
        school.archived_at = timezone.now()
        school.is_active = False  # Also deactivate the school
        school.save()

        # Archive all branches of this school
        branches = school.branch.all()
        for branch in branches:
            branch.is_archived = True
            branch.archived_at = timezone.now()
            branch.is_active = False
            branch.save()

        return Response(
            {"detail": f"School '{school.name}' and its {branches.count()} branches have been archived."},
            status=status.HTTP_200_OK
        )

    @action(detail=True, methods=['post'])
    def unarchive(self, request, pk=None):
        school = self.get_object()
        if not school.is_archived:
            return Response(
                {"detail": "School is not archived."},
                status=status.HTTP_400_BAD_REQUEST
            )

        school.is_archived = False
        school.archived_at = None
        school.is_active = True  # Reactivate the school
        school.save()

        # Unarchive all branches of this school if requested
        unarchive_branches = request.data.get('unarchive_branches', False)
        if unarchive_branches:
            branches = school.branch.filter(is_archived=True)
            for branch in branches:
                branch.is_archived = False
                branch.archived_at = None
                branch.is_active = True
                branch.save()

            return Response(
                {"detail": f"School '{school.name}' and its {branches.count()} branches have been unarchived."},
                status=status.HTTP_200_OK
            )

        return Response(
            {"detail": f"School '{school.name}' has been unarchived. Branches remain in their current state."},
            status=status.HTTP_200_OK
        )


class SchoolBranchViewSet(viewsets.ModelViewSet):
    queryset = SchoolBranch.objects.all()
    serializer_class = SchoolBranchSerializer

    # For development - comment these out for testing
    # permission_classes = [permissions.IsAuthenticated]
    # authentication_classes = [JWTAuthentication]

    # For testing - remove these in production
    permission_classes = []
    authentication_classes = []

    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get the current branch for the logged-in user"""
        # In a real implementation, this would use the user's assigned branch
        # For now, we'll return the first branch as a fallback
        try:
            if hasattr(request.user, 'school_branch') and request.user.school_branch:
                branch = request.user.school_branch
                serializer = self.get_serializer(branch)
                return Response(serializer.data)
            else:
                # Fallback to first branch
                branch = SchoolBranch.objects.first()
                if branch:
                    serializer = self.get_serializer(branch)
                    return Response(serializer.data)
                else:
                    return Response(
                        {"detail": "No school branches found."},
                        status=status.HTTP_404_NOT_FOUND
                    )
        except Exception as e:
            return Response(
                {"detail": f"Error retrieving current branch: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get_queryset(self):
        queryset = self.queryset

        # For archive/unarchive actions, we need to include archived branches
        action = getattr(self, 'action', None)
        if action in ['archive', 'unarchive', 'retrieve']:
            # Don't filter by is_archived for these actions
            pass
        else:
            # Get query parameters
            show_archived_param = self.request.query_params.get('show_archived', 'false')
            show_archived = show_archived_param.lower() == 'true'
            print(f"Branch - show_archived_param: {show_archived_param}, show_archived: {show_archived}")

            # Filter archived branches based on the show_archived parameter
            if not show_archived:
                queryset = queryset.filter(is_archived=False)
                print(f"Branch - Filtering out archived branches, queryset count: {queryset.count()}")
            else:
                print(f"Branch - Including archived branches, queryset count: {queryset.count()}")

        # Get query parameters
        school_id = self.request.query_params.get('school')

        # Filter by school ID if provided in query params
        if school_id:
            queryset = queryset.filter(school_id=school_id)

        # For production - uncomment this code
        # if not self.request.user.is_superuser:
        #     # Regular users can only see branches of their school
        #     queryset = queryset.filter(school=self.request.user.school_branch.school)

        return queryset

    @action(detail=True, methods=['post'])
    def archive(self, request, pk=None):
        branch = self.get_object()
        if branch.is_archived:
            return Response(
                {"detail": "Branch is already archived."},
                status=status.HTTP_400_BAD_REQUEST
            )

        branch.is_archived = True
        branch.archived_at = timezone.now()
        branch.is_active = False  # Also deactivate the branch
        branch.save()

        return Response(
            {"detail": f"Branch '{branch.name}' has been archived."},
            status=status.HTTP_200_OK
        )

    @action(detail=True, methods=['post'])
    def unarchive(self, request, pk=None):
        branch = self.get_object()
        if not branch.is_archived:
            return Response(
                {"detail": "Branch is not archived."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if the parent school is archived
        if branch.school.is_archived:
            return Response(
                {"detail": "Cannot unarchive branch because the parent school is archived. Unarchive the school first."},
                status=status.HTTP_400_BAD_REQUEST
            )

        branch.is_archived = False
        branch.archived_at = None
        branch.is_active = True  # Reactivate the branch
        branch.save()

        return Response(
            {"detail": f"Branch '{branch.name}' has been unarchived."},
            status=status.HTTP_200_OK
        )