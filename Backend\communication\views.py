from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from django.db.models import Q

from .models import (
    Announcement, Message, Email, SMS, Newsletter, Event, ParentTeacherMeeting
)
from .serializers import (
    AnnouncementSerializer, MessageSerializer, EmailSerializer, SMSSerializer,
    NewsletterSerializer, EventSerializer, ParentTeacherMeetingSerializer
)
from core.permissions import BranchBasedPermission

class AnnouncementViewSet(viewsets.ModelViewSet):
    serializer_class = AnnouncementSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'content', 'announcement_type', 'priority']
    ordering_fields = ['publish_date', 'priority', 'created_at']
    ordering = ['-publish_date']

    def get_queryset(self):
        queryset = Announcement.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by announcement type
        announcement_type = self.request.query_params.get('announcement_type')
        if announcement_type:
            queryset = queryset.filter(announcement_type=announcement_type)

        # Filter by priority
        priority = self.request.query_params.get('priority')
        if priority:
            queryset = queryset.filter(priority=priority)

        # Filter by target audience
        target_audience = self.request.query_params.get('target_audience')
        if target_audience:
            queryset = queryset.filter(target_audience=target_audience)

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=(is_active.lower() == 'true'))

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(publish_date__range=[start_date, end_date])

        # Filter by target class
        target_class = self.request.query_params.get('target_class')
        if target_class:
            queryset = queryset.filter(target_class_id=target_class)

        # Filter by target stream
        target_stream = self.request.query_params.get('target_stream')
        if target_stream:
            queryset = queryset.filter(target_stream_id=target_stream)

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            created_by=self.request.user,
            school_branch=self.request.user.school_branch
        )

    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get currently active announcements"""
        now = timezone.now()
        announcements = Announcement.objects.filter(
            school_branch=request.user.school_branch,
            is_active=True,
            publish_date__lte=now
        ).filter(
            Q(expiry_date__isnull=True) | Q(expiry_date__gte=now)
        )

        # Filter by user role
        user_role = request.user.role if hasattr(request.user, 'role') else None
        if user_role:
            announcements = announcements.filter(
                Q(target_audience='ALL') |
                Q(target_audience=user_role.upper())
            )

        serializer = self.get_serializer(announcements, many=True)
        return Response(serializer.data)

class MessageViewSet(viewsets.ModelViewSet):
    serializer_class = MessageSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['subject', 'content']
    ordering_fields = ['created_at', 'is_read']
    ordering = ['-created_at']

    def get_queryset(self):
        queryset = Message.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by sender or recipient
        user_id = self.request.user.id
        inbox = self.request.query_params.get('inbox')
        sent = self.request.query_params.get('sent')

        if inbox == 'true':
            queryset = queryset.filter(recipient_id=user_id)
        elif sent == 'true':
            queryset = queryset.filter(sender_id=user_id)
        else:
            queryset = queryset.filter(Q(sender_id=user_id) | Q(recipient_id=user_id))

        # Filter by read status
        is_read = self.request.query_params.get('is_read')
        if is_read is not None:
            queryset = queryset.filter(is_read=(is_read.lower() == 'true'))

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            sender=self.request.user,
            school_branch=self.request.user.school_branch
        )

    @action(detail=True, methods=['post'])
    def mark_as_read(self, request, pk=None):
        message = self.get_object()

        # Only the recipient can mark a message as read
        if message.recipient != request.user:
            return Response(
                {"detail": "Only the recipient can mark a message as read."},
                status=status.HTTP_403_FORBIDDEN
            )

        message.mark_as_read()
        serializer = self.get_serializer(message)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def unread_count(self, request):
        count = Message.objects.filter(
            recipient=request.user,
            is_read=False,
            school_branch=request.user.school_branch
        ).count()

        return Response({"unread_count": count})

class EmailViewSet(viewsets.ModelViewSet):
    serializer_class = EmailSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['subject', 'content', 'recipients']
    ordering_fields = ['created_at', 'status', 'sent_at']
    ordering = ['-created_at']

    def get_queryset(self):
        queryset = Email.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by sender
        sender = self.request.query_params.get('sender')
        if sender:
            queryset = queryset.filter(sender_id=sender)
        else:
            # By default, show only emails sent by the current user
            queryset = queryset.filter(sender=self.request.user)

        # Filter by status
        status_param = self.request.query_params.get('status')
        if status_param:
            queryset = queryset.filter(status=status_param)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(created_at__date__range=[start_date, end_date])

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            sender=self.request.user,
            school_branch=self.request.user.school_branch
        )

    @action(detail=True, methods=['post'])
    def send(self, request, pk=None):
        email = self.get_object()

        if email.status != 'DRAFT':
            return Response(
                {"detail": "Only draft emails can be sent."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Here you would implement the actual email sending logic
        # For now, we'll just update the status

        email.status = 'SENT'
        email.sent_at = timezone.now()
        email.save()

        serializer = self.get_serializer(email)
        return Response(serializer.data)

class SMSViewSet(viewsets.ModelViewSet):
    serializer_class = SMSSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['content', 'recipients']
    ordering_fields = ['created_at', 'status', 'sent_at']
    ordering = ['-created_at']

    def get_queryset(self):
        queryset = SMS.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by sender
        sender = self.request.query_params.get('sender')
        if sender:
            queryset = queryset.filter(sender_id=sender)
        else:
            # By default, show only SMS sent by the current user
            queryset = queryset.filter(sender=self.request.user)

        # Filter by status
        status_param = self.request.query_params.get('status')
        if status_param:
            queryset = queryset.filter(status=status_param)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(created_at__date__range=[start_date, end_date])

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            sender=self.request.user,
            school_branch=self.request.user.school_branch
        )

    @action(detail=True, methods=['post'])
    def send(self, request, pk=None):
        sms = self.get_object()

        if sms.status != 'DRAFT':
            return Response(
                {"detail": "Only draft SMS can be sent."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Here you would implement the actual SMS sending logic
        # For now, we'll just update the status

        sms.status = 'SENT'
        sms.sent_at = timezone.now()
        sms.save()

        serializer = self.get_serializer(sms)
        return Response(serializer.data)

class NewsletterViewSet(viewsets.ModelViewSet):
    serializer_class = NewsletterSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'content']
    ordering_fields = ['publish_date', 'status', 'created_at']
    ordering = ['-publish_date']

    def get_queryset(self):
        queryset = Newsletter.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by status
        status_param = self.request.query_params.get('status')
        if status_param:
            queryset = queryset.filter(status=status_param)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(publish_date__range=[start_date, end_date])

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            created_by=self.request.user,
            school_branch=self.request.user.school_branch
        )

    @action(detail=True, methods=['post'])
    def publish(self, request, pk=None):
        newsletter = self.get_object()

        if newsletter.status != 'DRAFT':
            return Response(
                {"detail": "Only draft newsletters can be published."},
                status=status.HTTP_400_BAD_REQUEST
            )

        newsletter.status = 'PUBLISHED'
        newsletter.publish_date = timezone.now().date()
        newsletter.save()

        serializer = self.get_serializer(newsletter)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def archive(self, request, pk=None):
        newsletter = self.get_object()

        if newsletter.status != 'PUBLISHED':
            return Response(
                {"detail": "Only published newsletters can be archived."},
                status=status.HTTP_400_BAD_REQUEST
            )

        newsletter.status = 'ARCHIVED'
        newsletter.save()

        serializer = self.get_serializer(newsletter)
        return Response(serializer.data)

class EventViewSet(viewsets.ModelViewSet):
    serializer_class = EventSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'description', 'location']
    ordering_fields = ['start_date', 'end_date', 'created_at']
    ordering = ['start_date']

    def get_queryset(self):
        queryset = Event.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by event type
        event_type = self.request.query_params.get('event_type')
        if event_type:
            queryset = queryset.filter(event_type=event_type)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            # Events that overlap with the given date range
            queryset = queryset.filter(
                Q(start_date__lte=end_date) & Q(end_date__gte=start_date)
            )

        # Filter by organizer
        organizer = self.request.query_params.get('organizer')
        if organizer:
            queryset = queryset.filter(organizer_id=organizer)

        # Filter by public/private
        is_public = self.request.query_params.get('is_public')
        if is_public is not None:
            queryset = queryset.filter(is_public=(is_public.lower() == 'true'))

        # Filter by attendance
        attending = self.request.query_params.get('attending')
        if attending == 'true':
            queryset = queryset.filter(attendees=self.request.user)

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            organizer=self.request.user,
            school_branch=self.request.user.school_branch
        )

    @action(detail=True, methods=['post'])
    def attend(self, request, pk=None):
        event = self.get_object()
        event.attendees.add(request.user)
        return Response({"detail": "You are now attending this event."}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def unattend(self, request, pk=None):
        event = self.get_object()
        event.attendees.remove(request.user)
        return Response({"detail": "You are no longer attending this event."}, status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'])
    def upcoming(self, request):
        now = timezone.now()
        upcoming_events = Event.objects.filter(
            school_branch=request.user.school_branch,
            start_date__gte=now
        ).order_by('start_date')

        # Limit to public events or events where the user is an attendee or organizer
        if not request.user.is_staff:
            upcoming_events = upcoming_events.filter(
                Q(is_public=True) |
                Q(attendees=request.user) |
                Q(organizer=request.user)
            )

        serializer = self.get_serializer(upcoming_events, many=True)
        return Response(serializer.data)

class ParentTeacherMeetingViewSet(viewsets.ModelViewSet):
    serializer_class = ParentTeacherMeetingSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'description']
    ordering_fields = ['scheduled_date', 'status', 'created_at']
    ordering = ['scheduled_date']

    def get_queryset(self):
        queryset = ParentTeacherMeeting.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by role (teacher or parent)
        user_id = self.request.user.id
        user_role = getattr(self.request.user, 'role', None)

        if user_role == 'teacher':
            queryset = queryset.filter(teacher_id=user_id)
        elif user_role == 'parent':
            queryset = queryset.filter(parent_id=user_id)

        # Filter by status
        status_param = self.request.query_params.get('status')
        if status_param:
            queryset = queryset.filter(status=status_param)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(scheduled_date__date__range=[start_date, end_date])

        # Filter by student
        student = self.request.query_params.get('student')
        if student:
            queryset = queryset.filter(student_id=student)

        return queryset

    def perform_create(self, serializer):
        # Determine if the current user is a teacher or parent
        user_role = getattr(self.request.user, 'role', None)

        if user_role == 'teacher':
            serializer.save(
                teacher=self.request.user,
                school_branch=self.request.user.school_branch
            )
        elif user_role == 'parent':
            serializer.save(
                parent=self.request.user,
                school_branch=self.request.user.school_branch
            )
        else:
            serializer.save(school_branch=self.request.user.school_branch)

    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        meeting = self.get_object()

        if meeting.status != 'SCHEDULED':
            return Response(
                {"detail": "Only scheduled meetings can be marked as completed."},
                status=status.HTTP_400_BAD_REQUEST
            )

        meeting.status = 'COMPLETED'
        meeting.notes = request.data.get('notes', meeting.notes)
        meeting.save()

        serializer = self.get_serializer(meeting)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        meeting = self.get_object()

        if meeting.status not in ['SCHEDULED', 'RESCHEDULED']:
            return Response(
                {"detail": "Only scheduled or rescheduled meetings can be cancelled."},
                status=status.HTTP_400_BAD_REQUEST
            )

        meeting.status = 'CANCELLED'
        meeting.notes = request.data.get('notes', meeting.notes)
        meeting.save()

        serializer = self.get_serializer(meeting)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def reschedule(self, request, pk=None):
        meeting = self.get_object()

        if meeting.status not in ['SCHEDULED', 'RESCHEDULED']:
            return Response(
                {"detail": "Only scheduled or rescheduled meetings can be rescheduled."},
                status=status.HTTP_400_BAD_REQUEST
            )

        new_date = request.data.get('scheduled_date')
        if not new_date:
            return Response(
                {"detail": "New scheduled date is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        meeting.status = 'RESCHEDULED'
        meeting.scheduled_date = new_date
        meeting.notes = request.data.get('notes', meeting.notes)
        meeting.save()

        serializer = self.get_serializer(meeting)
        return Response(serializer.data)