from rest_framework import serializers
from django.contrib.auth.models import Group, Permission
from .models import CustomUser

class GroupSerializer(serializers.ModelSerializer):
    class Meta:
        model = Group
        fields = ('id', 'name')

class UserSerializer(serializers.ModelSerializer):
    school_branch_name = serializers.SerializerMethodField()
    school_branch = serializers.SerializerMethodField()
    groups = GroupSerializer(many=True, read_only=True)

    class Meta:
        model = CustomUser
        fields = ('id', 'username', 'email', 'first_name', 'last_name', 'phone',
                 'user_type', 'school_branch', 'school_branch_name', 'profile_picture',
                 'is_superuser', 'groups')

    def get_school_branch(self, obj):
        # Get school_branch from the user's profile (safely handle users without profiles)
        try:
            profile = obj.profile
            if profile and hasattr(profile, 'school_branch') and profile.school_branch:
                return profile.school_branch.id
        except:
            # User has no profile (e.g., superuser) - this is okay
            pass
        return None

    def get_school_branch_name(self, obj):
        # Get school_branch name from the user's profile (safely handle users without profiles)
        try:
            profile = obj.profile
            if profile and hasattr(profile, 'school_branch') and profile.school_branch:
                return profile.school_branch.name
        except:
            # User has no profile (e.g., superuser) - this is okay
            pass
        return None

class LoginSerializer(serializers.Serializer):
    email = serializers.EmailField(required=False)
    username = serializers.CharField(required=False)
    password = serializers.CharField(write_only=True)

    def validate(self, attrs):
        # Check that at least one of email or username is provided
        if not attrs.get('email') and not attrs.get('username'):
            raise serializers.ValidationError("Either email or username must be provided")
        return attrs

class RegisterStepOneSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)
    password2 = serializers.CharField(write_only=True, required=True)
    user_type = serializers.ChoiceField(choices=CustomUser.USER_TYPE_CHOICES, required=True)

    # Import here to avoid circular import
    from schools.models import SchoolBranch
    school_branch = serializers.PrimaryKeyRelatedField(queryset=SchoolBranch.objects.all(), required=True)

    class Meta:
        model = CustomUser
        fields = ('id', 'email', 'user_type', 'school_branch', 'password', 'password2')

    def validate(self, attrs):
        if attrs['password'] != attrs['password2']:
            raise serializers.ValidationError({"password": "Password fields didn't match."})
        return attrs

    def create(self, validated_data):
        validated_data.pop('password2', None)
        password = validated_data.pop('password')

        # Create user with minimal fields for step 1
        user = CustomUser.objects.create(
            email=validated_data['email'],
            username=validated_data['email'],  # Use email as username by default
            user_type=validated_data['user_type'],
            school_branch=validated_data['school_branch']
        )

        # Set password separately to ensure proper hashing
        user.set_password(password)
        user.save()

        # The signal will automatically create the appropriate profile
        return user

class RegisterStepTwoSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomUser
        fields = ('id', 'first_name', 'last_name', 'phone', 'profile_picture')

    def update(self, instance, validated_data):
        # Update user with additional fields from step 2
        instance.first_name = validated_data.get('first_name', instance.first_name)
        instance.last_name = validated_data.get('last_name', instance.last_name)
        instance.phone = validated_data.get('phone', instance.phone)
        instance.profile_picture = validated_data.get('profile_picture', instance.profile_picture)
        instance.save()
        return instance

# Keep the original RegisterSerializer for backward compatibility
class RegisterSerializer(RegisterStepOneSerializer):
    class Meta(RegisterStepOneSerializer.Meta):
        fields = ('id', 'username', 'email', 'first_name', 'last_name', 'phone',
                 'user_type', 'school_branch', 'password', 'password2')

    def create(self, validated_data):
        validated_data.pop('password2', None)
        password = validated_data.pop('password')

        # Create user with all fields
        user = CustomUser.objects.create(
            email=validated_data['email'],
            username=validated_data.get('username', '') or validated_data['email'],
            first_name=validated_data.get('first_name', ''),
            last_name=validated_data.get('last_name', ''),
            user_type=validated_data['user_type'],
            school_branch=validated_data['school_branch'],
            phone=validated_data.get('phone', '')
        )

        # Set password separately to ensure proper hashing
        user.set_password(password)
        user.save()

        # The signal will automatically create the appropriate profile
        return user


class GroupSerializer(serializers.ModelSerializer):
    user_count = serializers.SerializerMethodField()

    class Meta:
        model = Group
        fields = ('id', 'name', 'user_count')

    def get_user_count(self, obj):
        return obj.core_users.count()


class PermissionSerializer(serializers.ModelSerializer):
    app_label = serializers.SerializerMethodField()
    model = serializers.SerializerMethodField()

    class Meta:
        model = Permission
        fields = ('id', 'name', 'codename', 'app_label', 'model')

    def get_app_label(self, obj):
        return obj.content_type.app_label

    def get_model(self, obj):
        return obj.content_type.model