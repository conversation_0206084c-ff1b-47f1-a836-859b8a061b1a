from django.db.models.signals import post_save, pre_save, post_delete
from django.dispatch import receiver
from django.utils import timezone
from django.db import models
from .models import FeePayment, Receipt, Invoice, PaymentReminder, StudentFeeAccount
from .analytics import update_payment_analytics

@receiver(post_save, sender=FeePayment)
def create_receipt(sender, instance, created, **kwargs):
    """Create a receipt when a fee payment is verified"""
    if created and instance.verified:
        Receipt.objects.create(
            payment=instance,
            amount=instance.amount_paid,
            receipt_number=f"RCP-{timezone.now().strftime('%Y%m%d')}-{instance.id}",
            issued_by=instance.verified_by
        )

@receiver(post_save, sender=FeePayment)
def update_invoice(sender, instance, created, **kwargs):
    """Update invoice status when payment is made"""
    if created:
        invoice = instance.invoice
        if invoice:
            invoice.amount_paid = invoice.feepayment_set.aggregate(
                total=models.Sum('amount_paid'))['total'] or 0
            invoice.balance = invoice.total_amount - invoice.amount_paid
            
            if invoice.balance <= 0:
                invoice.status = 'PAID'
            elif invoice.balance < invoice.total_amount:
                invoice.status = 'PARTIALLY_PAID'
                
            invoice.save()

@receiver(pre_save, sender=Invoice)
def create_payment_reminder(sender, instance, **kwargs):
    """Create payment reminder for overdue invoices"""
    if instance.pk:  # Only for existing invoices
        old_instance = Invoice.objects.get(pk=instance.pk)
        if old_instance.status != 'OVERDUE' and instance.status == 'OVERDUE':
            PaymentReminder.objects.create(
                student=instance.student,
                due_date=instance.due_date,
                amount_due=instance.balance,
                reminder_type='EMAIL',
                message=f"Payment of {instance.balance} is overdue for {instance.student}"
            )

@receiver(post_save, sender=FeePayment)
def update_student_fee_account(sender, instance, created, **kwargs):
    """Update student fee account when payment is made"""
    if created and instance.verified:
        try:
            fee_account = StudentFeeAccount.objects.get(
                student=instance.student,
                term=instance.term
            )
            fee_account.total_paid += instance.amount_paid
            fee_account.last_payment_date = instance.payment_date
            fee_account.calculate_balance()
            fee_account.save()
        except StudentFeeAccount.DoesNotExist:
            # Create fee account if it doesn't exist
            from .models import FeeStructure
            fee_structure = FeeStructure.objects.filter(
                school=instance.student.school_branch.school,
                term=instance.term,
                is_active=True
            ).first()

            if fee_structure:
                StudentFeeAccount.objects.create(
                    student=instance.student,
                    term=instance.term,
                    fee_structure=fee_structure,
                    total_fees=fee_structure.total_amount,
                    total_paid=instance.amount_paid,
                    last_payment_date=instance.payment_date
                )

@receiver(post_save, sender=FeePayment)
def update_analytics_on_payment(sender, instance, created, **kwargs):
    """Update payment analytics when a payment is made"""
    if created and instance.verified:
        try:
            school = instance.student.school_branch.school
            update_payment_analytics(school)
        except Exception as e:
            # Log error but don't fail the payment
            print(f"Error updating analytics: {e}")

@receiver(post_save, sender=FeePayment)
def notify_finance_office(sender, instance, created, **kwargs):
    """Notify finance office of new payments"""
    if created:
        # Add notification logic here
        pass
