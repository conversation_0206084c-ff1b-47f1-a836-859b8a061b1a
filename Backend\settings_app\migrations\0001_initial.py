# Generated by Django 5.2.1 on 2025-06-01 13:25

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('schools', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('CREATE', 'Create'), ('UPDATE', 'Update'), ('DELETE', 'Delete'), ('LOGIN', 'Login'), ('LOGOUT', 'Logout'), ('EXPORT', 'Export'), ('IMPORT', 'Import'), ('OTHER', 'Other')], max_length=20)),
                ('entity_type', models.CharField(help_text='Model or object type that was affected', max_length=100)),
                ('entity_id', models.CharField(blank=True, help_text='ID of the affected entity', max_length=100, null=True)),
                ('entity_name', models.CharField(blank=True, help_text='Name or description of the affected entity', max_length=255, null=True)),
                ('action_details', models.JSONField(blank=True, help_text='JSON object with details of the action', null=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='audit_logs', to='schools.schoolbranch')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='audit_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='Backup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('backup_name', models.CharField(max_length=255)),
                ('backup_type', models.CharField(choices=[('AUTOMATIC', 'Automatic'), ('MANUAL', 'Manual'), ('PRE_UPDATE', 'Pre-Update')], default='MANUAL', max_length=20)),
                ('status', models.CharField(choices=[('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('RESTORED', 'Restored')], default='IN_PROGRESS', max_length=20)),
                ('file_path', models.CharField(blank=True, max_length=255, null=True)),
                ('file_size', models.PositiveIntegerField(blank=True, help_text='Size in bytes', null=True)),
                ('cloud_storage_url', models.URLField(blank=True, null=True)),
                ('started_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('includes_media', models.BooleanField(default=True)),
                ('includes_database', models.BooleanField(default=True)),
                ('includes_configurations', models.BooleanField(default=True)),
                ('is_encrypted', models.BooleanField(default=False)),
                ('is_compressed', models.BooleanField(default=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_backups', to=settings.AUTH_USER_MODEL)),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='backups', to='schools.schoolbranch')),
            ],
            options={
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='BackupConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_enabled', models.BooleanField(default=True)),
                ('backup_frequency', models.CharField(choices=[('DAILY', 'Daily'), ('WEEKLY', 'Weekly'), ('MONTHLY', 'Monthly'), ('MANUAL', 'Manual Only')], default='WEEKLY', max_length=20)),
                ('backup_time', models.TimeField(default='00:00')),
                ('backup_day', models.PositiveIntegerField(blank=True, help_text='Day of week (1-7) or day of month (1-31)', null=True)),
                ('backup_storage', models.CharField(choices=[('LOCAL', 'Local Storage'), ('CLOUD', 'Cloud Storage'), ('BOTH', 'Both Local and Cloud')], default='LOCAL', max_length=20)),
                ('cloud_storage_settings', models.JSONField(blank=True, help_text='JSON object with cloud storage credentials', null=True)),
                ('local_storage_path', models.CharField(blank=True, max_length=255, null=True)),
                ('retention_period_days', models.PositiveIntegerField(default=30)),
                ('include_media_files', models.BooleanField(default=True)),
                ('include_database', models.BooleanField(default=True)),
                ('include_configurations', models.BooleanField(default=True)),
                ('encryption_enabled', models.BooleanField(default=False)),
                ('compression_enabled', models.BooleanField(default=True)),
                ('notify_on_success', models.BooleanField(default=True)),
                ('notify_on_failure', models.BooleanField(default=True)),
                ('notification_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('school_branch', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='backup_configuration', to='schools.schoolbranch')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_backup_configurations', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='LicenseSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('package_type', models.CharField(choices=[('basic', 'Basic'), ('standard', 'Standard'), ('premium', 'Premium'), ('custom', 'Custom')], default='basic', max_length=20)),
                ('subscription_status', models.CharField(choices=[('ACTIVE', 'Active'), ('TRIAL', 'Trial'), ('EXPIRED', 'Expired'), ('CANCELLED', 'Cancelled')], default='TRIAL', max_length=20)),
                ('start_date', models.DateField(default=django.utils.timezone.now)),
                ('expiry_date', models.DateField()),
                ('max_students', models.PositiveIntegerField(default=500)),
                ('max_staff', models.PositiveIntegerField(default=50)),
                ('max_branches', models.PositiveIntegerField(default=1)),
                ('custom_modules', models.JSONField(blank=True, help_text='List of enabled modules for custom package', null=True)),
                ('license_key', models.CharField(blank=True, max_length=100, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('school', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='license', to='schools.school')),
            ],
        ),
        migrations.CreateModel(
            name='ModuleActivation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('enabled_modules', models.JSONField(default=list, help_text='List of enabled module codes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('school_branch', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='module_activation', to='schools.schoolbranch')),
            ],
        ),
        migrations.CreateModel(
            name='Permission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('code', models.CharField(help_text='Unique code for this permission (e.g., USERS_VIEW)', max_length=50, unique=True)),
                ('module', models.CharField(choices=[('USERS', 'Users Management'), ('ACADEMICS', 'Academics'), ('STUDENTS', 'Students'), ('TEACHERS', 'Teachers'), ('PARENTS', 'Parents'), ('STAFF', 'Staff'), ('LIBRARY', 'Library'), ('INVENTORY', 'Inventory'), ('FINANCE', 'Finance'), ('SETTINGS', 'Settings'), ('REPORTS', 'Reports'), ('COMMUNICATION', 'Communication'), ('ATTENDANCE', 'Attendance'), ('EXAMS', 'Exams'), ('TIMETABLE', 'Timetable')], max_length=20)),
                ('action', models.CharField(choices=[('VIEW', 'View'), ('CREATE', 'Create'), ('EDIT', 'Edit'), ('DELETE', 'Delete'), ('EXPORT', 'Export'), ('IMPORT', 'Import'), ('APPROVE', 'Approve'), ('REJECT', 'Reject')], max_length=20)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['module', 'action'],
                'unique_together': {('module', 'action')},
            },
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_system_role', models.BooleanField(default=False, help_text='System roles cannot be deleted')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_roles', to=settings.AUTH_USER_MODEL)),
                ('permissions', models.ManyToManyField(blank=True, related_name='roles', to='settings_app.permission')),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='roles', to='schools.schoolbranch')),
            ],
            options={
                'unique_together': {('name', 'school_branch')},
            },
        ),
        migrations.CreateModel(
            name='SchoolProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('logo', models.ImageField(blank=True, null=True, upload_to='school_logos/')),
                ('banner', models.ImageField(blank=True, null=True, upload_to='school_banners/')),
                ('mission', models.TextField(blank=True, null=True)),
                ('vision', models.TextField(blank=True, null=True)),
                ('core_values', models.TextField(blank=True, null=True)),
                ('about', models.TextField(blank=True, null=True)),
                ('history', models.TextField(blank=True, null=True)),
                ('achievements', models.TextField(blank=True, null=True)),
                ('contact_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('contact_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('website', models.URLField(blank=True, null=True)),
                ('address', models.TextField(blank=True, null=True)),
                ('social_media_links', models.JSONField(blank=True, help_text='JSON object with social media links', null=True)),
                ('school_colors', models.JSONField(blank=True, help_text='JSON object with primary and secondary colors', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('school_branch', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to='schools.schoolbranch')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_school_profiles', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='SystemConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('academic_year_start_month', models.PositiveIntegerField(default=1, help_text='Month number (1-12)')),
                ('academic_year_end_month', models.PositiveIntegerField(default=12, help_text='Month number (1-12)')),
                ('current_academic_year', models.CharField(blank=True, max_length=20, null=True)),
                ('current_term', models.CharField(blank=True, max_length=20, null=True)),
                ('grading_system', models.CharField(choices=[('PERCENTAGE', 'Percentage'), ('LETTER_GRADE', 'Letter Grade'), ('GPA', 'GPA'), ('CUSTOM', 'Custom')], default='PERCENTAGE', max_length=20)),
                ('grading_scale', models.JSONField(blank=True, help_text='JSON object with grade boundaries', null=True)),
                ('attendance_tracking_method', models.CharField(default='DAILY', max_length=50)),
                ('enable_online_payments', models.BooleanField(default=False)),
                ('enable_sms_notifications', models.BooleanField(default=False)),
                ('enable_email_notifications', models.BooleanField(default=True)),
                ('default_language', models.CharField(default='en', max_length=20)),
                ('timezone', models.CharField(default='UTC', max_length=50)),
                ('date_format', models.CharField(default='YYYY-MM-DD', max_length=20)),
                ('time_format', models.CharField(default='HH:mm', max_length=20)),
                ('system_maintenance_mode', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('school_branch', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='system_configuration', to='schools.schoolbranch')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_system_configurations', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Integration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('integration_type', models.CharField(choices=[('PAYMENT', 'Payment Gateway'), ('SMS', 'SMS Service'), ('EMAIL', 'Email Service'), ('STORAGE', 'Cloud Storage'), ('LMS', 'Learning Management System'), ('ANALYTICS', 'Analytics Service'), ('OTHER', 'Other')], max_length=20)),
                ('provider', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('api_key', models.CharField(blank=True, max_length=255, null=True)),
                ('api_secret', models.CharField(blank=True, max_length=255, null=True)),
                ('configuration', models.JSONField(blank=True, help_text='JSON object with integration settings', null=True)),
                ('webhook_url', models.URLField(blank=True, null=True)),
                ('callback_url', models.URLField(blank=True, null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('INACTIVE', 'Inactive'), ('TESTING', 'Testing'), ('FAILED', 'Failed')], default='INACTIVE', max_length=20)),
                ('is_enabled', models.BooleanField(default=False)),
                ('last_sync', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_integrations', to=settings.AUTH_USER_MODEL)),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='integrations', to='schools.schoolbranch')),
            ],
            options={
                'unique_together': {('name', 'integration_type', 'school_branch')},
            },
        ),
        migrations.CreateModel(
            name='LicenseHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('CREATE', 'Created'), ('UPDATE', 'Updated'), ('RENEW', 'Renewed'), ('DEACTIVATE', 'Deactivated'), ('UPGRADE', 'Upgraded'), ('DOWNGRADE', 'Downgraded')], max_length=20)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('previous_state', models.JSONField(blank=True, help_text='Previous license state before the change', null=True)),
                ('new_state', models.JSONField(help_text='New license state after the change')),
                ('notes', models.TextField(blank=True, help_text='Additional notes about the change')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('license', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='settings_app.licensesubscription')),
            ],
            options={
                'verbose_name': 'License History',
                'verbose_name_plural': 'License History',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['license'], name='settings_ap_license_4b2646_idx'), models.Index(fields=['action'], name='settings_ap_action_f45cfa_idx'), models.Index(fields=['timestamp'], name='settings_ap_timesta_fa3eab_idx')],
            },
        ),
        migrations.CreateModel(
            name='UserRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_primary', models.BooleanField(default=False)),
                ('assigned_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('assigned_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_settings_roles', to=settings.AUTH_USER_MODEL)),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='users', to='settings_app.role')),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='settings_user_roles', to='schools.schoolbranch')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='settings_user_roles', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'role', 'school_branch')},
            },
        ),
    ]
