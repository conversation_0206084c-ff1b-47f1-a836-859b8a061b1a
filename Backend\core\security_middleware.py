"""
Advanced security middleware for ShuleXcel.
"""
import logging
import time
import json
import hashlib
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from django.core.cache import cache
from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
import re

logger = logging.getLogger(__name__)
User = get_user_model()


class AdvancedSecurityMiddleware(MiddlewareMixin):
    """
    Advanced security middleware with multiple protection layers.
    """
    
    # Suspicious patterns in requests
    SUSPICIOUS_PATTERNS = [
        r'<script[^>]*>.*?</script>',  # XSS attempts
        r'union\s+select',  # SQL injection
        r'drop\s+table',  # SQL injection
        r'exec\s*\(',  # Code execution
        r'eval\s*\(',  # Code execution
        r'javascript:',  # XSS
        r'vbscript:',  # XSS
        r'onload\s*=',  # XSS
        r'onerror\s*=',  # XSS
    ]
    
    # File upload restrictions
    ALLOWED_FILE_EXTENSIONS = {
        'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
        'document': ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'],
        'archive': ['.zip', '.rar', '.7z'],
    }
    
    DANGEROUS_FILE_EXTENSIONS = [
        '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar',
        '.php', '.asp', '.aspx', '.jsp', '.py', '.rb', '.pl', '.sh'
    ]

    def process_request(self, request):
        """
        Process incoming requests for security threats.
        """
        # Skip security checks for health endpoints
        if request.path.startswith('/api/health/'):
            return None
            
        # Get client information
        client_ip = self.get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # Check for IP-based blocking
        if self.is_ip_blocked(client_ip):
            logger.warning(f"Blocked IP attempted access: {client_ip}")
            return JsonResponse(
                {'error': 'Access denied'},
                status=403
            )
        
        # Check for suspicious patterns
        if self.detect_suspicious_patterns(request):
            self.block_ip_temporarily(client_ip, 'suspicious_patterns')
            logger.warning(f"Suspicious patterns detected from {client_ip}")
            return JsonResponse(
                {'error': 'Request blocked due to suspicious content'},
                status=400
            )
        
        # Check for bot/crawler patterns
        if self.is_suspicious_bot(user_agent):
            logger.info(f"Suspicious bot detected: {user_agent} from {client_ip}")
            return JsonResponse(
                {'error': 'Bot access not allowed'},
                status=403
            )
        
        # Check request size
        if self.is_request_too_large(request):
            logger.warning(f"Large request detected from {client_ip}")
            return JsonResponse(
                {'error': 'Request too large'},
                status=413
            )
        
        # Check for rapid requests (basic DDoS protection)
        if self.is_rapid_requests(client_ip):
            self.block_ip_temporarily(client_ip, 'rapid_requests')
            logger.warning(f"Rapid requests detected from {client_ip}")
            return JsonResponse(
                {'error': 'Too many requests'},
                status=429
            )
        
        # Store request metadata for analysis
        self.store_request_metadata(request, client_ip)
        
        return None

    def process_response(self, request, response):
        """
        Process responses and add security headers.
        """
        # Add security headers
        self.add_security_headers(response)
        
        # Log security events
        if response.status_code in [401, 403, 429]:
            client_ip = self.get_client_ip(request)
            logger.warning(
                f"Security event: {response.status_code} for {request.path} "
                f"from {client_ip}"
            )
        
        return response

    def get_client_ip(self, request):
        """
        Get the real client IP address.
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    def is_ip_blocked(self, ip):
        """
        Check if IP is in the blocked list.
        """
        blocked_ips = cache.get('blocked_ips', set())
        return ip in blocked_ips

    def block_ip_temporarily(self, ip, reason, duration=3600):
        """
        Temporarily block an IP address.
        """
        cache_key = f'temp_blocked_ip:{ip}'
        cache.set(cache_key, reason, duration)
        
        # Add to blocked IPs list
        blocked_ips = cache.get('blocked_ips', set())
        blocked_ips.add(ip)
        cache.set('blocked_ips', blocked_ips, duration)

    def detect_suspicious_patterns(self, request):
        """
        Detect suspicious patterns in request data.
        """
        # Check URL path
        for pattern in self.SUSPICIOUS_PATTERNS:
            if re.search(pattern, request.path, re.IGNORECASE):
                return True
        
        # Check query parameters
        query_string = request.META.get('QUERY_STRING', '')
        for pattern in self.SUSPICIOUS_PATTERNS:
            if re.search(pattern, query_string, re.IGNORECASE):
                return True
        
        # Check POST data
        if hasattr(request, 'body') and request.body:
            try:
                body_str = request.body.decode('utf-8')
                for pattern in self.SUSPICIOUS_PATTERNS:
                    if re.search(pattern, body_str, re.IGNORECASE):
                        return True
            except UnicodeDecodeError:
                # Binary data, skip pattern checking
                pass
        
        return False

    def is_suspicious_bot(self, user_agent):
        """
        Check if user agent indicates a suspicious bot.
        """
        suspicious_bots = [
            'sqlmap', 'nikto', 'nmap', 'masscan', 'zap', 'burp',
            'acunetix', 'nessus', 'openvas', 'w3af', 'skipfish'
        ]
        
        user_agent_lower = user_agent.lower()
        return any(bot in user_agent_lower for bot in suspicious_bots)

    def is_request_too_large(self, request):
        """
        Check if request is too large.
        """
        max_size = getattr(settings, 'MAX_REQUEST_SIZE', 10 * 1024 * 1024)  # 10MB
        
        content_length = request.META.get('CONTENT_LENGTH')
        if content_length:
            try:
                return int(content_length) > max_size
            except ValueError:
                return False
        
        return False

    def is_rapid_requests(self, ip):
        """
        Check for rapid requests from the same IP.
        """
        cache_key = f'request_count:{ip}'
        current_count = cache.get(cache_key, 0)
        
        # Allow 100 requests per minute
        if current_count > 100:
            return True
        
        cache.set(cache_key, current_count + 1, 60)  # 1 minute window
        return False

    def store_request_metadata(self, request, client_ip):
        """
        Store request metadata for security analysis.
        """
        metadata = {
            'ip': client_ip,
            'path': request.path,
            'method': request.method,
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'timestamp': timezone.now().isoformat(),
            'user_id': request.user.id if request.user.is_authenticated else None
        }
        
        # Store in cache for analysis
        cache_key = f'request_metadata:{client_ip}:{int(time.time())}'
        cache.set(cache_key, metadata, 3600)  # Store for 1 hour

    def add_security_headers(self, response):
        """
        Add comprehensive security headers.
        """
        # Content Security Policy
        csp_policy = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' https:; "
            "connect-src 'self'; "
            "frame-ancestors 'none'; "
            "base-uri 'self'; "
            "form-action 'self';"
        )
        response['Content-Security-Policy'] = csp_policy
        
        # Additional security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response['Permissions-Policy'] = (
            "geolocation=(), microphone=(), camera=(), "
            "payment=(), usb=(), magnetometer=(), gyroscope=(), "
            "accelerometer=(), ambient-light-sensor=()"
        )
        
        # Remove server information
        if 'Server' in response:
            del response['Server']
        
        # Add custom security header
        response['X-Security-Framework'] = 'ShuleXcel-Security-v1.0'


class FileUploadSecurityMiddleware(MiddlewareMixin):
    """
    Middleware to secure file uploads.
    """
    
    def process_request(self, request):
        """
        Check file uploads for security threats.
        """
        if request.method == 'POST' and request.FILES:
            for field_name, uploaded_file in request.FILES.items():
                if not self.is_file_safe(uploaded_file):
                    logger.warning(
                        f"Dangerous file upload attempt: {uploaded_file.name} "
                        f"from {self.get_client_ip(request)}"
                    )
                    return JsonResponse(
                        {'error': f'File type not allowed: {uploaded_file.name}'},
                        status=400
                    )
        
        return None

    def is_file_safe(self, uploaded_file):
        """
        Check if uploaded file is safe.
        """
        filename = uploaded_file.name.lower()
        
        # Check for dangerous extensions
        for ext in AdvancedSecurityMiddleware.DANGEROUS_FILE_EXTENSIONS:
            if filename.endswith(ext):
                return False
        
        # Check file size
        max_size = getattr(settings, 'MAX_UPLOAD_SIZE', 10 * 1024 * 1024)  # 10MB
        if uploaded_file.size > max_size:
            return False
        
        # Check for double extensions
        if filename.count('.') > 1:
            parts = filename.split('.')
            if len(parts) > 2 and any(
                part in AdvancedSecurityMiddleware.DANGEROUS_FILE_EXTENSIONS 
                for part in parts[:-1]
            ):
                return False
        
        return True

    def get_client_ip(self, request):
        """
        Get the real client IP address.
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class SessionSecurityMiddleware(MiddlewareMixin):
    """
    Enhanced session security middleware.
    """
    
    def process_request(self, request):
        """
        Enhance session security.
        """
        if request.user.is_authenticated:
            # Check for session hijacking
            if self.detect_session_hijacking(request):
                logger.warning(
                    f"Potential session hijacking detected for user {request.user.email}"
                )
                # Force logout
                from django.contrib.auth import logout
                logout(request)
                return JsonResponse(
                    {'error': 'Session security violation detected'},
                    status=401
                )
            
            # Update session metadata
            self.update_session_metadata(request)
        
        return None

    def detect_session_hijacking(self, request):
        """
        Detect potential session hijacking.
        """
        session = request.session
        current_ip = self.get_client_ip(request)
        current_ua = request.META.get('HTTP_USER_AGENT', '')
        
        # Check if IP changed
        stored_ip = session.get('security_ip')
        if stored_ip and stored_ip != current_ip:
            return True
        
        # Check if user agent changed significantly
        stored_ua = session.get('security_ua')
        if stored_ua and self.user_agent_changed_significantly(stored_ua, current_ua):
            return True
        
        return False

    def update_session_metadata(self, request):
        """
        Update session security metadata.
        """
        session = request.session
        session['security_ip'] = self.get_client_ip(request)
        session['security_ua'] = request.META.get('HTTP_USER_AGENT', '')
        session['security_last_activity'] = timezone.now().isoformat()

    def user_agent_changed_significantly(self, old_ua, new_ua):
        """
        Check if user agent changed significantly.
        """
        # Simple check - in production, you might want more sophisticated analysis
        old_parts = old_ua.split()[:3]  # First 3 parts
        new_parts = new_ua.split()[:3]
        
        return old_parts != new_parts

    def get_client_ip(self, request):
        """
        Get the real client IP address.
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
