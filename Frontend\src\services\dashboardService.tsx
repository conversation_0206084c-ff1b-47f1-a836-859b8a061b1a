import axios from 'axios';
import { API_BASE_URL } from '../config/constants.ts';
import { authService } from './authService.ts';

// Create a custom axios instance with authentication
const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to include the token in requests
axiosInstance.interceptors.request.use(
  (config) => {
    const token = authService.getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

import { getAnnouncements, getEvents } from './communicationService';

// Define types for dashboard parameters
interface DashboardParams {
  schoolId?: number;
  branchId?: number;
}

// Get real academic data for dashboard metrics
export const getDashboardMetrics = async (params?: DashboardParams) => {
  try {
    console.log('Fetching dashboard metrics with params:', params);

    // Fetch real academic data from our working APIs
    const [classesResponse, streamsResponse, subjectsResponse, departmentsResponse, academicYearsResponse] = await Promise.all([
      axiosInstance.get('/academics/classes/'),
      axiosInstance.get('/academics/streams/'),
      axiosInstance.get('/academics/subjects/'),
      axiosInstance.get('/academics/departments/'),
      axiosInstance.get('/academics/academic-years/')
    ]);

    // Extract data from paginated responses
    const classes = classesResponse.data?.results || classesResponse.data || [];
    const streams = streamsResponse.data?.results || streamsResponse.data || [];
    const subjects = subjectsResponse.data?.results || subjectsResponse.data || [];
    const departments = departmentsResponse.data?.results || departmentsResponse.data || [];
    const academicYears = academicYearsResponse.data?.results || academicYearsResponse.data || [];

    console.log('Academic data fetched:', {
      classes: classes.length,
      streams: streams.length,
      subjects: subjects.length,
      departments: departments.length,
      academicYears: academicYears.length
    });

    return {
      classes: { value: classes.length, data: classes },
      streams: { value: streams.length, data: streams },
      subjects: { value: subjects.length, data: subjects },
      departments: { value: departments.length, data: departments },
      academicYears: { value: academicYears.length, data: academicYears },
      // Find current academic year
      currentAcademicYear: academicYears.find(year => year.is_current) || academicYears[0] || null
    };
  } catch (error) {
    console.error('Error fetching dashboard metrics:', error);
    return null;
  }
};

// Get recent announcements
export const getRecentAnnouncements = async (limit = 3, params?: DashboardParams) => {
  try {
    const queryParams: any = { limit, ordering: '-publish_date' };
    if (params?.schoolId) queryParams.school = params.schoolId;
    if (params?.branchId) queryParams.branch = params.branchId;

    const response = await getAnnouncements(queryParams);
    return response.results || [];
  } catch (error) {
    console.error('Error fetching recent announcements:', error);
    return [];
  }
};

// Get upcoming events
export const getUpcomingEvents = async (limit = 3, params?: DashboardParams) => {
  try {
    const queryParams: any = { limit, ordering: 'start_date' };
    if (params?.schoolId) queryParams.school = params.schoolId;
    if (params?.branchId) queryParams.branch = params.branchId;

    const response = await getEvents(queryParams);
    return response.results || [];
  } catch (error) {
    console.error('Error fetching upcoming events:', error);
    return [];
  }
};

// Get all dashboard data in a single function
export const getAllDashboardData = async (params?: DashboardParams) => {
  try {
    console.log('Fetching all dashboard data...');

    // Fetch all data in parallel
    const [metrics, announcements, events] = await Promise.all([
      getDashboardMetrics(params),
      getRecentAnnouncements(3, params),
      getUpcomingEvents(3, params)
    ]);

    console.log('Dashboard data fetched:', { metrics, announcements: announcements.length, events: events.length });

    // Format the data for the dashboard with real academic data
    const dashboardData = {
      stats: {
        totalStudents: 0, // We'll get this from user API later
        totalTeachers: 0, // We'll get this from user API later
        totalClasses: metrics?.classes?.value || 0,
        totalCourses: metrics?.subjects?.value || 0,
        totalStreams: metrics?.streams?.value || 0,
        totalDepartments: metrics?.departments?.value || 0,
        totalEvents: events.length || 0,
        totalAnnouncements: announcements.length || 0
      },
      academicData: {
        classes: metrics?.classes?.data || [],
        streams: metrics?.streams?.data || [],
        subjects: metrics?.subjects?.data || [],
        departments: metrics?.departments?.data || [],
        academicYears: metrics?.academicYears?.data || [],
        currentAcademicYear: metrics?.currentAcademicYear || null
      },
      recentAnnouncements: announcements.map(announcement => ({
        id: announcement.id,
        title: announcement.title,
        date: announcement.publish_date,
        priority: announcement.priority
      })),
      upcomingEvents: events.map(event => ({
        id: event.id,
        title: event.title,
        date: event.start_date,
        location: event.location || 'N/A'
      })),
      // Empty array for recent activities as we don't have this data yet
      recentActivities: []
    };

    console.log('Formatted dashboard data:', dashboardData);
    return dashboardData;
  } catch (error) {
    console.error('Error fetching all dashboard data:', error);
    return null;
  }
};
