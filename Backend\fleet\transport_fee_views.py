from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q

from .transport_fees import TransportFee, TransportFeeDiscount
from .transport_fee_serializers import TransportFeeSerializer, TransportFeeDiscountSerializer
from .models import Route, StudentTransport
from fees.models import FeeType, FeeStructure, FeePayment
from core.permissions import BranchBasedPermission

class TransportFeeViewSet(viewsets.ModelViewSet):
    """
    API endpoint for transport fees
    """
    serializer_class = TransportFeeSerializer
    permission_classes = [permissions.IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['student__user__first_name', 'student__user__last_name', 'route__name']
    ordering_fields = ['amount', 'due_date', 'created_at']
    ordering = ['due_date']
    
    def get_queryset(self):
        # Allow drf-yasg to inspect the view without full authentication
        if getattr(self, 'swagger_fake_view', False):
            return TransportFee.objects.none()

        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            return TransportFee.objects.none()
            
        queryset = TransportFee.objects.filter(school_branch=profile.school_branch)
        
        # Filter by student
        student_id = self.request.query_params.get('student')
        if student_id:
            queryset = queryset.filter(student_id=student_id)
            
        # Filter by route
        route_id = self.request.query_params.get('route')
        if route_id:
            queryset = queryset.filter(route_id=route_id)
            
        return queryset
    
    def perform_create(self, serializer):
        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            raise PermissionError("User does not have an associated school branch")
            
        serializer.save(school_branch=profile.school_branch)
        
    @action(detail=True, methods=['get'])
    def students(self, request, pk=None):
        """
        Get students using this transport fee route
        """
        transport_fee = self.get_object()
        students = StudentTransport.objects.filter(
            route=transport_fee.route,
            is_active=True,
            school_branch=request.user.school_branch
        )
        
        from .serializers import StudentTransportSerializer
        serializer = StudentTransportSerializer(students, many=True)
        return Response(serializer.data)
        
    @action(detail=True, methods=['get'])
    def discounts(self, request, pk=None):
        """
        Get discounts for this transport fee
        """
        transport_fee = self.get_object()
        discounts = TransportFeeDiscount.objects.filter(
            transport_fee=transport_fee,
            school_branch=request.user.school_branch
        )
        
        serializer = TransportFeeDiscountSerializer(discounts, many=True)
        return Response(serializer.data)
        
class TransportFeeDiscountViewSet(viewsets.ModelViewSet):
    """
    API endpoint for transport fee discounts
    """
    serializer_class = TransportFeeDiscountSerializer
    permission_classes = [permissions.IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['student__user__first_name', 'student__user__last_name', 'reason']
    ordering_fields = ['amount', 'created_at']
    ordering = ['-created_at']
    
    def get_queryset(self):
        # Allow drf-yasg to inspect the view without full authentication
        if getattr(self, 'swagger_fake_view', False):
            return TransportFeeDiscount.objects.none()

        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            return TransportFeeDiscount.objects.none()
            
        queryset = TransportFeeDiscount.objects.filter(school_branch=profile.school_branch)
        
        # Filter by student
        student_id = self.request.query_params.get('student')
        if student_id:
            queryset = queryset.filter(student_id=student_id)
            
        return queryset
    
    def perform_create(self, serializer):
        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            raise PermissionError("User does not have an associated school branch")
            
        serializer.save(school_branch=profile.school_branch)
