from django.db import models
from django.utils import timezone
from fees.models import FeePayment

class MpesaCredential(models.Model):
    """
    Store MPESA API credentials for different schools
    """
    school = models.ForeignKey('schools.School', on_delete=models.CASCADE)
    consumer_key = models.CharField(max_length=255)
    consumer_secret = models.CharField(max_length=255)
    shortcode = models.CharField(max_length=20)
    passkey = models.CharField(max_length=255)
    environment = models.CharField(
        max_length=20,
        choices=[('sandbox', 'Sandbox'), ('production', 'Production')],
        default='sandbox'
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.school.name} - {self.environment}"

class MpesaTransaction(models.Model):
    """
    Store MPESA transaction details
    """
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('CANCELLED', 'Cancelled')
    ]

    transaction_type = models.CharField(
        max_length=20,
        choices=[
            ('STK_PUSH', 'STK Push'),
            ('B2C', 'Business to Customer'),
            ('C2B', 'Customer to Business')
        ]
    )
    transaction_id = models.CharField(max_length=50, unique=True, null=True, blank=True)
    phone_number = models.CharField(max_length=15)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    reference = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    result_code = models.CharField(max_length=10, null=True, blank=True)
    result_description = models.TextField(null=True, blank=True)
    checkout_request_id = models.CharField(max_length=100, null=True, blank=True)
    merchant_request_id = models.CharField(max_length=100, null=True, blank=True)
    mpesa_receipt_number = models.CharField(max_length=50, null=True, blank=True)
    fee_payment = models.ForeignKey(FeePayment, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.transaction_type} - {self.reference} - {self.amount}"

class MpesaCallback(models.Model):
    """
    Store raw MPESA callback data for debugging and auditing
    """
    transaction = models.ForeignKey(MpesaTransaction, on_delete=models.CASCADE, null=True, blank=True)
    callback_data = models.JSONField()
    processed = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Callback at {self.created_at}"