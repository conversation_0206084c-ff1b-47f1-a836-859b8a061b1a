from django.core.management.base import BaseCommand
from academics.models import AcademicYear
from schools.models import School, SchoolBranch
from django.utils import timezone
from datetime import datetime, timedelta

class Command(BaseCommand):
    help = 'Creates a default academic year and sets it as current'

    def handle(self, *args, **options):
        # Check if there are any schools
        schools = School.objects.all()
        if not schools.exists():
            self.stdout.write(self.style.ERROR('No schools found. Please create a school first.'))
            return
        
        # Get the first school
        school = schools.first()
        self.stdout.write(self.style.SUCCESS(f'Using school: {school.name}'))
        
        # Check if there are any branches for this school
        branches = SchoolBranch.objects.filter(school=school)
        branch = None
        if branches.exists():
            branch = branches.first()
            self.stdout.write(self.style.SUCCESS(f'Using branch: {branch.name}'))
        
        # Check if there are any academic years for this school/branch
        academic_years = AcademicYear.objects.filter(school_name=school)
        if branch:
            academic_years = academic_years.filter(school_branch=branch)
        
        # If there are academic years, check if any are current
        if academic_years.exists():
            current_years = academic_years.filter(is_current=True)
            if current_years.exists():
                self.stdout.write(self.style.SUCCESS(f'Current academic year already exists: {current_years.first().year}'))
                return
            
            # If no current years, set the most recent one as current
            most_recent = academic_years.order_by('-start_date').first()
            most_recent.is_current = True
            most_recent.save()
            self.stdout.write(self.style.SUCCESS(f'Set existing academic year as current: {most_recent.year}'))
            return
        
        # If no academic years exist, create a new one
        current_year = datetime.now().year
        next_year = current_year + 1
        
        # Create the academic year
        academic_year = AcademicYear(
            school_name=school,
            school_branch=branch,
            year=f"{current_year}-{next_year}",
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timedelta(days=365),
            is_current=True,
            is_archived=False
        )
        academic_year.save()
        
        self.stdout.write(self.style.SUCCESS(f'Created new academic year: {academic_year.year} and set as current'))
