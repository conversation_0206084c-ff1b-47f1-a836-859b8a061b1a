import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';
import axios from 'axios';
import moduleService, { Module, ModuleActivation } from '../services/moduleService';
import { useAuth } from '../context/AuthContext';

interface ModuleContextType {
  modules: ModuleActivation | null;
  isLoading: boolean;
  error: string | null;
  isModuleVisible: (moduleCode: string) => boolean;
  refreshModules: () => Promise<void>;
}

// Create the context with a default value
const ModuleContext = createContext<ModuleContextType>({
  modules: null,
  isLoading: false,
  error: null,
  isModuleVisible: () => false,
  refreshModules: async () => {},
});

// Custom hook to use the module context
export const useModules = () => useContext(ModuleContext);

interface ModuleProviderProps {
  children: ReactNode;
}

export const ModuleProvider: React.FC<ModuleProviderProps> = ({ children }) => {
  const [modules, setModules] = useState<ModuleActivation | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  const fetchModules = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Check if user is authenticated
      const token = localStorage.getItem('token');
      if (!token) {
        setError('Authentication required');
        setIsLoading(false);
        return;
      }

      const data = await moduleService.getMyModules();
      setModules(data);

      // Clear any previous errors if successful
      setError(null);
    } catch (err) {
      console.error('Error fetching modules:', err);

      // Handle different error types
      if (axios.isAxiosError(err)) {
        // Handle 401 errors specifically
        if (err.response?.status === 401) {
          setError('Your session has expired. Please log in again.');
        }
        // Handle 403 errors specifically
        else if (err.response?.status === 403) {
          console.warn('Permission denied when fetching modules. Using default modules.');

          // Instead of showing an error, try to get a minimal set of modules
          try {
            const fallbackData = await moduleService.getAvailableModules();

            // Create a default module activation with core modules enabled
            const coreModules = fallbackData.filter((m: Module) => m.is_core).map((m: Module) => m.code);

            setModules({
              id: 0,
              school_branch: 0,
              enabled_modules: user?.is_superuser ? fallbackData.map((m: Module) => m.code) : coreModules,
              enabled_modules_details: [],
              available_modules: fallbackData,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

            // Don't set an error message in this case
            setError(null);
            return;
          } catch (fallbackErr) {
            console.error('Error fetching fallback modules:', fallbackErr);
            setError('Unable to load modules. Please contact support.');
          }
        } else {
          setError('Failed to load modules');
        }
      } else {
        setError('Failed to load modules');
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Skip module fetching on auth pages to prevent loops
    const currentPath = window.location.pathname;
    const authPages = ['/signin', '/signup', '/forgot-password', '/register', '/complete-profile'];
    const isAuthPage = authPages.some(page => currentPath.includes(page));

    // Only fetch modules if user exists AND has valid authentication AND not on auth page
    if (user && user.id && isAuthenticated && !isAuthPage) {
      console.log('🧩 ModuleProvider: User authenticated, fetching modules...');
      fetchModules();
    } else {
      // Reset modules when user is not authenticated or on auth pages
      if (!user) {
        console.log('🧩 ModuleProvider: No user data, clearing modules');
      } else if (!isAuthenticated) {
        console.log('🧩 ModuleProvider: Not authenticated, clearing modules');
      } else if (isAuthPage) {
        console.log('🧩 ModuleProvider: On auth page, skipping module fetch');
      }
      setModules(null);
      setIsLoading(false);
    }
  }, [user, isAuthenticated]);

  const isModuleVisible = (moduleCode: string): boolean => {
    // Super users can see all modules
    if (user?.is_superuser) return true;

    // System admins can see all modules
    if (user?.user_type === 'system_admin') return true;

    // Core and academics modules are always visible
    if (moduleCode === 'core' || moduleCode === 'academics') return true;

    // If we're still loading modules, assume the module is visible
    if (isLoading) return true;

    return moduleService.isModuleVisible(modules, moduleCode);
  };

  return (
    <ModuleContext.Provider
      value={{
        modules,
        isLoading,
        error,
        isModuleVisible,
        refreshModules: fetchModules,
      }}
    >
      {children}
    </ModuleContext.Provider>
  );
};
