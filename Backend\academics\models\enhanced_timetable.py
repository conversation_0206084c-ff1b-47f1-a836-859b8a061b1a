from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import time, timedelta
from django.conf import settings
from typing import Any


class TimetableTemplate(models.Model):
    """Templates for timetables that can be reused"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    school = models.ForeignKey('schools.School', on_delete=models.CASCADE)
    
    # Template configuration
    periods_per_day = models.PositiveIntegerField(default=8)
    break_periods = models.JSONField(default=list)  # List of break period numbers
    lunch_period = models.PositiveIntegerField(null=True, blank=True)
    
    # Timing
    school_start_time = models.TimeField(default=time(8, 0))
    school_end_time = models.TimeField(default=time(16, 0))
    period_duration = models.DurationField(default=timedelta(minutes=40))
    break_duration = models.DurationField(default=timedelta(minutes=20))
    lunch_duration = models.DurationField(default=timedelta(minutes=60))
    
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='created_timetable_templates')
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        app_label = 'academics'
        
    def __str__(self):
        return f"{self.name} - {self.school.name}"


class EnhancedTimeSlot(models.Model):
    """Enhanced time slot model"""
    DAY_CHOICES = [
        ('monday', 'Monday'),
        ('tuesday', 'Tuesday'),
        ('wednesday', 'Wednesday'),
        ('thursday', 'Thursday'),
        ('friday', 'Friday'),
        ('saturday', 'Saturday'),
        ('sunday', 'Sunday'),
    ]
    
    PERIOD_TYPES = [
        ('lesson', 'Lesson'),
        ('break', 'Break'),
        ('lunch', 'Lunch'),
        ('assembly', 'Assembly'),
        ('games', 'Games/Sports'),
        ('study', 'Study Period'),
        ('club', 'Club Activity'),
    ]
    
    day_of_week = models.CharField(max_length=10, choices=DAY_CHOICES)
    period_number = models.PositiveIntegerField()
    start_time = models.TimeField()
    end_time = models.TimeField()
    period_type = models.CharField(max_length=10, choices=PERIOD_TYPES, default='lesson')
    
    # Template reference
    template = models.ForeignKey(
        TimetableTemplate, 
        on_delete=models.CASCADE,
        null=True, 
        blank=True
    )
    
    class Meta:
        app_label = 'academics'
        unique_together = ['day_of_week', 'period_number', 'template']
        ordering = ['day_of_week', 'period_number']
        
    def clean(self):
        if self.start_time >= self.end_time:
            raise ValidationError("End time must be after start time")
            
    def get_day_of_week_display(self) -> str:
        return dict(self.DAY_CHOICES).get(self.day_of_week, self.day_of_week)
            
    def __str__(self) -> str:
        return f"{self.get_day_of_week_display()} Period {self.period_number} ({self.start_time}-{self.end_time})"


class EnhancedTimetable(models.Model):
    """Enhanced timetable model"""
    class_name = models.ForeignKey('academics.ClassRoom', on_delete=models.CASCADE)
    academic_year = models.ForeignKey('academics.AcademicYear', on_delete=models.CASCADE)
    term = models.ForeignKey('academics.Term', on_delete=models.CASCADE)
    
    # Template and customization
    template = models.ForeignKey(TimetableTemplate, on_delete=models.SET_NULL, null=True)
    is_customized = models.BooleanField(default=False)
    
    # Status
    is_active = models.BooleanField(default=True)
    is_published = models.BooleanField(default=False)
    
    # Metadata
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='created_timetables')
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='approved_timetables'
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        app_label = 'academics'
        unique_together = ['class_name', 'academic_year', 'term']
        
    def __str__(self):
        return f"Timetable - {self.class_name} ({self.term})"


class TimetableEntry(models.Model):
    """Individual timetable entries"""
    timetable = models.ForeignKey(EnhancedTimetable, on_delete=models.CASCADE)
    time_slot = models.ForeignKey(EnhancedTimeSlot, on_delete=models.CASCADE)
    
    # Subject and teacher
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE)
    teacher = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='timetable_entries')
    
    # Venue
    classroom = models.CharField(max_length=50, blank=True)
    venue_type = models.CharField(
        max_length=20,
        choices=[
            ('classroom', 'Classroom'),
            ('laboratory', 'Laboratory'),
            ('library', 'Library'),
            ('hall', 'Hall'),
            ('field', 'Field'),
            ('computer_lab', 'Computer Lab'),
            ('other', 'Other'),
        ],
        default='classroom'
    )
    
    # Special arrangements
    is_double_period = models.BooleanField(default=False)
    special_notes = models.TextField(blank=True)
    
    # Substitution tracking
    is_substitution = models.BooleanField(default=False)
    original_teacher = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='original_classes'
    )
    substitution_reason = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        app_label = 'academics'
        unique_together = ['timetable', 'time_slot']
        
    def __str__(self):
        return f"{self.subject.name} - {self.teacher} ({self.time_slot})"


class TeacherTimetable(models.Model):
    """Teacher's consolidated timetable view"""
    teacher = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='teacher_timetables')
    academic_year = models.ForeignKey('academics.AcademicYear', on_delete=models.CASCADE)
    term = models.ForeignKey('academics.Term', on_delete=models.CASCADE)
    
    # Workload analysis
    total_periods_per_week = models.PositiveIntegerField(default=0)
    subjects_taught = models.ManyToManyField('academics.Subject', blank=True)
    classes_taught = models.ManyToManyField('academics.ClassRoom', blank=True)
    
    # Free periods
    free_periods = models.JSONField(default=list)
    
    # Status
    is_complete = models.BooleanField(default=False)
    conflicts_detected = models.BooleanField(default=False)
    conflict_details = models.JSONField(default=list)
    
    last_updated = models.DateTimeField(auto_now=True)
    
    class Meta:
        app_label = 'academics'
        unique_together = ['teacher', 'academic_year', 'term']
        
    def __str__(self):
        return f"{self.teacher} Timetable - {self.term}"


class TimetableConflict(models.Model):
    """Track timetable conflicts"""
    CONFLICT_TYPES = [
        ('teacher_double_booking', 'Teacher Double Booking'),
        ('classroom_double_booking', 'Classroom Double Booking'),
        ('student_clash', 'Student Subject Clash'),
        ('resource_unavailable', 'Resource Unavailable'),
        ('teacher_unavailable', 'Teacher Unavailable'),
    ]
    
    timetable = models.ForeignKey(EnhancedTimetable, on_delete=models.CASCADE)
    conflict_type = models.CharField(max_length=25, choices=CONFLICT_TYPES)
    
    # Conflicting entries
    entry1 = models.ForeignKey(
        TimetableEntry, 
        on_delete=models.CASCADE,
        related_name='conflicts_as_entry1'
    )
    entry2 = models.ForeignKey(
        TimetableEntry, 
        on_delete=models.CASCADE,
        related_name='conflicts_as_entry2',
        null=True,
        blank=True
    )
    
    description = models.TextField()
    severity = models.CharField(
        max_length=10,
        choices=[
            ('low', 'Low'),
            ('medium', 'Medium'),
            ('high', 'High'),
            ('critical', 'Critical'),
        ],
        default='medium'
    )
    
    # Resolution
    is_resolved = models.BooleanField(default=False)
    resolution_notes = models.TextField(blank=True)
    resolved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='resolved_conflicts'
    )
    resolved_at = models.DateTimeField(null=True, blank=True)
    
    detected_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        app_label = 'academics'
        
    def get_conflict_type_display(self) -> str:
        return dict(self.CONFLICT_TYPES).get(self.conflict_type, self.conflict_type)
        
    def __str__(self) -> str:
        return f"Conflict: {self.get_conflict_type_display()} - {self.timetable}"


class TimetableSubstitution(models.Model):
    """Track teacher substitutions"""
    original_entry = models.ForeignKey(TimetableEntry, on_delete=models.CASCADE)
    substitute_teacher = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='substitution_assignments')

    # Substitution details
    date = models.DateField()
    reason = models.TextField()

    # Approval
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='approved_substitutions'
    )
    approval_date = models.DateTimeField(null=True, blank=True)
    is_approved = models.BooleanField(default=False)
    
    # Notification
    original_teacher_notified = models.BooleanField(default=False)
    substitute_notified = models.BooleanField(default=False)
    students_notified = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        app_label = 'academics'
        
    def __str__(self):
        return f"Substitution: {self.substitute_teacher} for {self.original_entry}"
