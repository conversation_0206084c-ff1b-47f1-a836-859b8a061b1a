from django.apps import AppConfig


class CommunityConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'community'
    verbose_name = 'Community Management'
    
    def ready(self):
        """
        Import signal handlers when the app is ready.
        This is where you would register any signals for the community app.
        """
        # Import signal handlers
        try:
            import community.signals  # noqa
        except ImportError:
            pass
