import axiosWithAuth from '../utils/axiosInterceptor';

// TypeScript interfaces
/**
 * @typedef {Object} TermTemplate
 * @property {string} id - Term template ID
 * @property {string} name - Term name
 * @property {string} academic_year_template - Academic year template ID
 * @property {string} academic_year_template_name - Academic year template name
 * @property {string} school_name - School name
 * @property {Date} start_date - Term start date
 * @property {Date} end_date - Term end date
 * @property {Date} created_at - Creation timestamp
 * @property {Date} updated_at - Last update timestamp
 */

/**
 * @typedef {Object} AcademicYearTemplate
 * @property {string} id - Academic year template ID
 * @property {string} year - Academic year (e.g., "2023-2024")
 * @property {string} school - School ID
 * @property {string} school_name - School name
 * @property {Date} start_date - Academic year start date
 * @property {Date} end_date - Academic year end date
 * @property {boolean} is_active - Whether the template is active
 * @property {boolean} is_archived - Whether the template is archived
 * @property {boolean} is_global - Whether the template is global (can be used across all schools)
 * @property {string} created_by - ID of the user who created the template
 * @property {string} created_by_name - Name of the user who created the template
 * @property {Array<TermTemplate>} terms - Term templates for this academic year
 * @property {Date} created_at - Creation timestamp
 * @property {Date} updated_at - Last update timestamp
 */

// Direct API endpoints with full paths - Fixed to match backend URLs
const API_ENDPOINTS = {
  classes: '/api/academics/classes/',  // Fixed: backend uses 'classes' not 'classrooms'
  academicYears: '/api/academics/academic-years/',
  streams: '/api/academics/streams/',
  subjects: '/api/academics/subjects/',
  departments: '/api/academics/departments/',
  // Curriculum endpoints
  curriculumSystems: '/api/academics/curriculum/systems/',
  educationLevels: '/api/academics/curriculum/education-levels/',
  schoolCurriculumConfig: '/api/academics/curriculum/school-config/',
  branchCurriculumConfig: '/api/academics/curriculum/branch-config/',
  // Template endpoints
  academicYearTemplates: '/api/academics/academic-years/',  // Keep using the same endpoint with is_template flag
  termTemplates: '/api/academics/terms/',  // Use the terms endpoint with is_template flag
  terms: '/api/academics/terms/',  // Use the terms endpoint directly
  syllabi: '/api/academics/syllabus/syllabi/',
  syllabusUnits: '/api/academics/syllabus/syllabus-units/',
  syllabusTemplates: '/api/academics/syllabus/templates/',
  syllabusTemplateUnits: '/api/academics/syllabus/template-units/'
};

// Error handling helper
const handleApiError = (error, context) => {
  console.error(`Error in ${context}:`, error);
  if (error.response) {
    console.error('Response data:', error.response.data);
    console.error('Response status:', error.response.status);
    console.error('Response headers:', error.response.headers);
  } else if (error.request) {
    console.error('No response received:', error.request);
  }
  throw new Error(`API Error: ${error.response?.status || 'Unknown'} - ${JSON.stringify(error.response?.data || error.message)}`);
};

// Export the API client
export const academicsApi = {
  /**
   * Get all academic years
   *
   * @param {Object} params - Optional query parameters
   * @returns {Promise} Promise with academic years data
   */
  getAcademicYears: async (params = {}) => {
    try {
      // Debug logging
      console.log('Fetching academic years with params:', params);

      const response = await axiosWithAuth.get(API_ENDPOINTS.academicYears, { params });
      const data = response.data;

      // Debug logging
      console.log('Academic years API response:', data);

      // Handle paginated response
      if (data && typeof data === 'object' && 'results' in data && Array.isArray(data.results)) {
        return data.results; // Return empty array if no results
      } else if (Array.isArray(data)) {
        return data;
      } else {
        console.warn('API returned unexpected data format for academic years:', data);
        console.warn('Expected paginated response with results array or direct array, got:', typeof data, data);
        return []; // Return empty array instead of throwing error
      }
    } catch (error) {
      console.error('Error in getAcademicYears:', error);
      console.error('Request params:', params);
      return handleApiError(error, 'getAcademicYears');
    }
  },

  /**
   * Get a specific academic year
   *
   * @param {number} id - Academic year ID
   * @returns {Promise} Promise with academic year data
   */
  getAcademicYear: async (id) => {
    try {
      const response = await axiosWithAuth.get(`${API_ENDPOINTS.academicYears}${id}/`);
      return response.data;
    } catch (error) {
      return handleApiError(error, `getAcademicYear(${id})`);
    }
  },

  /**
   * Create a new academic year
   *
   * @param {Object} data - Academic year data
   * @returns {Promise} Promise with created academic year data
   */
  createAcademicYear: async (data) => {
    try {
      const response = await axiosWithAuth.post(API_ENDPOINTS.academicYears, data);
      return response.data;
    } catch (error) {
      return handleApiError(error, 'createAcademicYear');
    }
  },

  /**
   * Update an academic year
   *
   * @param {number} id - Academic year ID
   * @param {Object} data - Updated academic year data
   * @returns {Promise} Promise with updated academic year data
   */
  updateAcademicYear: async (id, data) => {
    try {
      const response = await axiosWithAuth.put(`${API_ENDPOINTS.academicYears}${id}/`, data);
      return response.data;
    } catch (error) {
      return handleApiError(error, `updateAcademicYear(${id})`);
    }
  },

  /**
   * Get all academic year templates
   *
   * @param {Object} params - Optional query parameters
   * @returns {Promise} Promise with academic year templates data
   */
  getAcademicYearTemplates: async (params = {}) => {
    try {
      // Add is_template=true to the params
      const templateParams = { ...params, is_template: true };
      const response = await axiosWithAuth.get(API_ENDPOINTS.academicYearTemplates, { params: templateParams });
      const data = response.data;

      // Handle paginated response
      if (data && typeof data === 'object' && 'results' in data && Array.isArray(data.results)) {
        return data.results;
      } else if (Array.isArray(data)) {
        return data;
      } else {
        console.warn('API returned unexpected data format for academic year templates:', data);
        console.warn('Expected paginated response with results array or direct array, got:', typeof data, data);
        return []; // Return empty array instead of throwing error
      }
    } catch (error) {
      return handleApiError(error, 'getAcademicYearTemplates');
    }
  },

  /**
   * Get all terms
   *
   * @param {Object} params - Optional query parameters
   * @returns {Promise} Promise with terms data
   */
  getTerms: async (params = {}) => {
    try {
      // Determine if we're fetching template terms or regular terms based on params
      const isTemplate = params.is_template === true;
      const endpoint = isTemplate ? API_ENDPOINTS.termTemplates : API_ENDPOINTS.terms;
      const response = await axiosWithAuth.get(endpoint, { params });
      const data = response.data;

      // Handle paginated response
      if (data && typeof data === 'object' && 'results' in data && Array.isArray(data.results)) {
        return data.results;
      } else if (Array.isArray(data)) {
        return data;
      } else {
        console.warn('API returned unexpected data format for terms:', data);
        console.warn('Expected paginated response with results array or direct array, got:', typeof data, data);
        return [];
      }
    } catch (error) {
      console.error('Error fetching terms:', error);
      return [];
    }
  },

  /**
   * Get a specific term
   *
   * @param {number|string} id - Term ID
   * @returns {Promise} Promise with term data
   */
  getTerm: async (id) => {
    try {
      // Determine if this is a template term based on the ID format or other logic
      // For now, we'll assume it's a regular term
      const endpoint = API_ENDPOINTS.terms;
      const response = await axiosWithAuth.get(`${endpoint}${id}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching term:', error);
      throw error;
    }
  },

  /**
   * Create a new term
   *
   * @param {Object} data - Term data
   * @returns {Promise} Promise with created term
   */
  createTerm: async (data) => {
    try {
      // Use the terms endpoint for regular terms and termTemplates for template terms
      const endpoint = data.is_template ? API_ENDPOINTS.termTemplates : API_ENDPOINTS.terms;
      const response = await axiosWithAuth.post(endpoint, data);
      return response.data;
    } catch (error) {
      console.error('Error creating term:', error);
      throw error;
    }
  },

  /**
   * Update a term
   *
   * @param {number|string} id - Term ID
   * @param {Object} data - Updated term data
   * @returns {Promise} Promise with updated term
   */
  updateTerm: async (id, data) => {
    try {
      // Use the terms endpoint for regular terms and termTemplates for template terms
      const endpoint = data.is_template ? API_ENDPOINTS.termTemplates : API_ENDPOINTS.terms;
      const response = await axiosWithAuth.put(`${endpoint}${id}/`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating term:', error);
      throw error;
    }
  },

  /**
   * Delete a term
   *
   * @param {number|string} id - Term ID
   * @returns {Promise} Promise with deletion status
   */
  deleteTerm: async (id) => {
    try {
      // Determine if this is a template term based on the ID format or other logic
      // For now, we'll assume it's a regular term
      const endpoint = API_ENDPOINTS.terms;
      await axiosWithAuth.delete(`${endpoint}${id}/`);
      return true;
    } catch (error) {
      console.error('Error deleting term:', error);
      throw error;
    }
  },

  /**
   * Get all classes
   *
   * @param {Object} params - Optional query parameters
   * @returns {Promise} Promise with classes data
   */
  getClasses: async (params = {}) => {
    try {
      const response = await axiosWithAuth.get(API_ENDPOINTS.classes, { params });
      const data = response.data;

      // Handle paginated response
      if (data && typeof data === 'object' && 'results' in data && Array.isArray(data.results)) {
        return data.results; // Return empty array if no results
      } else if (Array.isArray(data)) {
        return data;
      } else {
        console.warn('API returned unexpected data format for classes:', data);
        console.warn('Expected paginated response with results array or direct array, got:', typeof data, data);
        return []; // Return empty array instead of throwing error
      }
    } catch (error) {
      return handleApiError(error, 'getClasses');
    }
  },

  /**
   * Get classes by curriculum
   *
   * @param {string} curriculumCode - Curriculum code
   * @returns {Promise} Promise with classes data
   */
  getClassesByCurriculum: async (curriculumCode) => {
    try {
      const response = await axiosWithAuth.get(API_ENDPOINTS.classes, {
        params: { curriculum_system: curriculumCode }
      });
      const data = response.data;

      // Handle paginated response
      if (data && typeof data === 'object' && 'results' in data && Array.isArray(data.results)) {
        return data.results;
      } else if (Array.isArray(data)) {
        return data;
      } else {
        console.warn('API returned unexpected data format for classes by curriculum:', data);
        console.warn('Expected paginated response with results array or direct array, got:', typeof data, data);
        return []; // Return empty array instead of throwing error
      }
    } catch (error) {
      return handleApiError(error, `getClassesByCurriculum(${curriculumCode})`);
    }
  },

  /**
   * Get classes by education level
   *
   * @param {number} educationLevelId - Education level ID
   * @returns {Promise} Promise with classes data
   */
  getClassesByEducationLevel: async (educationLevelId) => {
    try {
      const response = await axiosWithAuth.get(API_ENDPOINTS.classes, {
        params: { education_level: educationLevelId }
      });
      const data = response.data;

      // Handle paginated response
      if (data && typeof data === 'object' && 'results' in data && Array.isArray(data.results)) {
        return data.results;
      } else if (Array.isArray(data)) {
        return data;
      } else {
        console.warn('API returned unexpected data format for classes by education level:', data);
        console.warn('Expected paginated response with results array or direct array, got:', typeof data, data);
        return []; // Return empty array instead of throwing error
      }
    } catch (error) {
      return handleApiError(error, `getClassesByEducationLevel(${educationLevelId})`);
    }
  },

  /**
   * Get a specific class
   *
   * @param {number} id - Class ID
   * @returns {Promise} Promise with class data
   */
  getClass: async (id) => {
    try {
      const response = await axiosWithAuth.get(`${API_ENDPOINTS.classes}${id}/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching class (${id}):`, error);
      throw error;
    }
  },

  /**
   * Create a new class
   *
   * @param {Object} data - Class data
   * @returns {Promise} Promise with created class data
   */
  createClass: async (data) => {
    try {
      const response = await axiosWithAuth.post(API_ENDPOINTS.classes, data);
      return response.data;
    } catch (error) {
      console.error('Error creating class:', error);
      throw error;
    }
  },

  /**
   * Update a class
   *
   * @param {number} id - Class ID
   * @param {Object} data - Updated class data
   * @returns {Promise} Promise with updated class data
   */
  updateClass: async (id, data) => {
    try {
      const response = await axiosWithAuth.put(`${API_ENDPOINTS.classes}${id}/`, data);
      return response.data;
    } catch (error) {
      console.error(`Error updating class (${id}):`, error);
      throw error;
    }
  },

  /**
   * Delete a class
   *
   * @param {number} id - Class ID
   * @returns {Promise} Promise with deletion status
   */
  deleteClass: async (id) => {
    try {
      await axiosWithAuth.delete(`${API_ENDPOINTS.classes}${id}/`);
      return true;
    } catch (error) {
      console.error(`Error deleting class (${id}):`, error);
      throw error;
    }
  },

  /**
   * Get streams
   *
   * @param {Object} params - Optional query parameters
   * @returns {Promise} Promise with streams data
   */
  getStreams: async (params = {}) => {
    try {
      console.log('Fetching streams with params:', params);

      // Make sure we have a school parameter
      if (!params.school) {
        console.warn('No school parameter provided for streams fetch');
        // Try to get from localStorage as fallback
        const schoolId = localStorage.getItem('selectedSchoolId');
        if (schoolId) {
          params.school = schoolId;
          console.log('Using school ID from localStorage:', schoolId);
        } else {
          console.error('No school ID available for streams fetch');
        }
      }

      // Log the full URL being requested
      const fullUrl = `${API_ENDPOINTS.streams}${params.school ? `?school=${params.school}` : ''}`;
      console.log('Requesting streams from URL:', fullUrl);

      // Try fetching without params first to see if any streams exist at all
      console.log('First trying to fetch all streams without filtering...');
      const allStreamsResponse = await axiosWithAuth.get(API_ENDPOINTS.streams);
      const allStreamsData = allStreamsResponse.data;
      console.log('All streams response:', allStreamsData);

      // Now fetch with the school filter
      const response = await axiosWithAuth.get(API_ENDPOINTS.streams, { params });
      const data = response.data;
      console.log('School-filtered streams API response:', data);

      // Handle paginated response
      if (data && typeof data === 'object' && 'results' in data && Array.isArray(data.results)) {
        console.log(`Found ${data.results.length} streams for this school`);
        if (data.results.length === 0) {
          console.log('No streams found for this school, but found streams in the system:',
            allStreamsData.results ? allStreamsData.results.length :
            (Array.isArray(allStreamsData) ? allStreamsData.length : 0));
        }
        return data.results;
      } else if (Array.isArray(data)) {
        console.log(`Found ${data.length} streams for this school`);
        if (data.length === 0) {
          console.log('No streams found for this school, but found streams in the system:',
            allStreamsData.results ? allStreamsData.results.length :
            (Array.isArray(allStreamsData) ? allStreamsData.length : 0));
        }
        return data;
      } else {
        console.warn('API returned unexpected data format for streams:', data);
        console.warn('Expected paginated response with results array or direct array, got:', typeof data, data);
        return []; // Return empty array instead of throwing error
      }
    } catch (error) {
      console.error('Error fetching streams:', error);
      console.error('Error details:', error.response?.data || error.message);
      return []; // Return empty array on error
    }
  },

  /**
   * Get subjects
   *
   * @param {Object} params - Optional query parameters
   * @returns {Promise} Promise with subjects data
   */
  getSubjects: async (params = {}) => {
    try {
      const response = await axiosWithAuth.get(API_ENDPOINTS.subjects, { params });
      const data = response.data;

      // Handle paginated response
      if (data && typeof data === 'object' && 'results' in data && Array.isArray(data.results)) {
        return data.results;
      } else if (Array.isArray(data)) {
        return data;
      } else {
        console.warn('API returned unexpected data format for subjects:', data);
        console.warn('Expected paginated response with results array or direct array, got:', typeof data, data);
        return [];
      }
    } catch (error) {
      console.error('Error fetching subjects:', error);
      return [];
    }
  },

  /**
   * Get departments
   *
   * @param {Object} params - Optional query parameters
   * @returns {Promise} Promise with departments data
   */
  getDepartments: async (params = {}) => {
    try {
      const response = await axiosWithAuth.get(API_ENDPOINTS.departments, { params });
      const data = response.data;

      // Handle paginated response
      if (data && typeof data === 'object' && 'results' in data && Array.isArray(data.results)) {
        return data.results;
      } else if (Array.isArray(data)) {
        return data;
      } else {
        console.warn('API returned unexpected data format for departments:', data);
        console.warn('Expected paginated response with results array or direct array, got:', typeof data, data);
        return [];
      }
    } catch (error) {
      console.error('Error fetching departments:', error);
      return [];
    }
  },

  /**
   * Get a specific department
   *
   * @param {number} id - Department ID
   * @returns {Promise} Promise with department data
   */
  getDepartment: async (id) => {
    try {
      const response = await axiosWithAuth.get(`${API_ENDPOINTS.departments}${id}/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching department (${id}):`, error);
      throw error;
    }
  },

  /**
   * Create a new department
   *
   * @param {Object} data - Department data
   * @returns {Promise} Promise with created department data
   */
  createDepartment: async (data) => {
    try {
      const response = await axiosWithAuth.post(API_ENDPOINTS.departments, data);
      return response.data;
    } catch (error) {
      console.error('Error creating department:', error);
      throw error;
    }
  },

  /**
   * Update a department
   *
   * @param {number} id - Department ID
   * @param {Object} data - Updated department data
   * @returns {Promise} Promise with updated department data
   */
  updateDepartment: async (id, data) => {
    try {
      const response = await axiosWithAuth.put(`${API_ENDPOINTS.departments}${id}/`, data);
      return response.data;
    } catch (error) {
      console.error(`Error updating department (${id}):`, error);
      throw error;
    }
  },

  /**
   * Delete a department
   *
   * @param {number} id - Department ID
   * @returns {Promise} Promise with deletion status
   */
  deleteDepartment: async (id) => {
    try {
      await axiosWithAuth.delete(`${API_ENDPOINTS.departments}${id}/`);
      return true;
    } catch (error) {
      console.error(`Error deleting department (${id}):`, error);
      throw error;
    }
  },

  /**
   * Make an academic year template global (only for superusers)
   *
   * @param {number} id - Template ID
   * @returns {Promise} Promise with updated template data
   */
  makeTemplateGlobal: async (id) => {
    try {
      const response = await axiosWithAuth.post(`${API_ENDPOINTS.academicYearTemplates}${id}/make_global/`);
      return response.data;
    } catch (error) {
      console.error(`Error making template global (${id}):`, error);
      throw error;
    }
  },

  /**
   * Make a global academic year template local (only for superusers)
   *
   * @param {number} id - Template ID
   * @returns {Promise} Promise with updated template data
   */
  makeTemplateLocal: async (id) => {
    try {
      const response = await axiosWithAuth.post(`${API_ENDPOINTS.academicYearTemplates}${id}/make_local/`);
      return response.data;
    } catch (error) {
      console.error(`Error making template local (${id}):`, error);
      throw error;
    }
  },

  /**
   * Get all global academic year templates
   *
   * @returns {Promise} Promise with global templates data
   */
  getGlobalTemplates: async () => {
    try {
      const response = await axiosWithAuth.get(`${API_ENDPOINTS.academicYearTemplates}global_templates/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching global templates:', error);
      throw error;
    }
  },

  /**
   * Get all syllabi
   *
   * @param {Object} params - Optional query parameters
   * @returns {Promise} Promise with syllabi data
   */
  getSyllabi: async (params = {}) => {
    try {
      const response = await axiosWithAuth.get(API_ENDPOINTS.syllabi, { params });
      const data = response.data;

      // Handle paginated response
      if (data && typeof data === 'object' && 'results' in data && Array.isArray(data.results)) {
        return data.results;
      } else if (Array.isArray(data)) {
        return data;
      } else {
        console.warn('API returned unexpected data format for syllabi:', data);
        console.warn('Expected paginated response with results array or direct array, got:', typeof data, data);
        return [];
      }
    } catch (error) {
      console.error('Error fetching syllabi:', error);
      return [];
    }
  },

  /**
   * Get a specific syllabus
   *
   * @param {number} id - Syllabus ID
   * @returns {Promise} Promise with syllabus data
   */
  getSyllabus: async (id) => {
    try {
      const response = await axiosWithAuth.get(`${API_ENDPOINTS.syllabi}${id}/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching syllabus (${id}):`, error);
      throw error;
    }
  },

  /**
   * Create a new syllabus
   *
   * @param {Object} data - Syllabus data
   * @returns {Promise} Promise with created syllabus data
   */
  createSyllabus: async (data) => {
    try {
      const response = await axiosWithAuth.post(API_ENDPOINTS.syllabi, data);
      return response.data;
    } catch (error) {
      console.error('Error creating syllabus:', error);
      throw error;
    }
  },

  /**
   * Update a syllabus
   *
   * @param {number} id - Syllabus ID
   * @param {Object} data - Updated syllabus data
   * @returns {Promise} Promise with updated syllabus data
   */
  updateSyllabus: async (id, data) => {
    try {
      const response = await axiosWithAuth.put(`${API_ENDPOINTS.syllabi}${id}/`, data);
      return response.data;
    } catch (error) {
      console.error(`Error updating syllabus (${id}):`, error);
      throw error;
    }
  },

  /**
   * Delete a syllabus
   *
   * @param {number} id - Syllabus ID
   * @returns {Promise} Promise with deletion status
   */
  deleteSyllabus: async (id) => {
    try {
      await axiosWithAuth.delete(`${API_ENDPOINTS.syllabi}${id}/`);
      return true;
    } catch (error) {
      console.error(`Error deleting syllabus (${id}):`, error);
      throw error;
    }
  },

  /**
   * Import a syllabus
   *
   * @param {File} file - The file to import
   * @param {string} format - The format of the file (json, csv)
   * @returns {Promise} Promise with imported syllabus data
   */
  importSyllabus: async (file, format = 'json') => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('format', format);

      const response = await axiosWithAuth.post(`${API_ENDPOINTS.syllabi}import/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error importing syllabus:', error);
      throw error;
    }
  },

  /**
   * Archive or unarchive an academic year
   *
   * @param {number} id - Academic year ID
   * @param {boolean} archive - Whether to archive (true) or unarchive (false)
   * @returns {Promise} Promise with updated academic year data
   */
  archiveAcademicYear: async (id, archive = true) => {
    try {
      const action = archive ? 'archive' : 'unarchive';
      const response = await axiosWithAuth.post(`${API_ENDPOINTS.academicYears}${id}/${action}/`);
      return response.data;
    } catch (error) {
      console.error(`Error ${archive ? 'archiving' : 'unarchiving'} academic year (${id}):`, error);
      throw error;
    }
  },

  /**
   * Copy an academic year to a template
   *
   * @param {number} id - Academic year ID
   * @param {string} templateName - Name for the new template
   * @returns {Promise} Promise with created template data
   */
  copyAcademicYearToTemplate: async (id, templateName) => {
    try {
      const response = await axiosWithAuth.post(`${API_ENDPOINTS.academicYears}${id}/copy_to_template/`, {
        name: templateName
      });
      return response.data;
    } catch (error) {
      console.error(`Error copying academic year (${id}) to template:`, error);
      throw error;
    }
  },

  /**
   * Get all curriculum systems
   */
  getCurriculumSystems: async (params = {}) => {
    try {
      console.log('Fetching curriculum systems with params:', params);
      const response = await axiosWithAuth.get(API_ENDPOINTS.curriculumSystems, { params });
      console.log('Curriculum systems API response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching curriculum systems:', error);
      throw error;
    }
  },

  /**
   * Get all education levels
   */
  getEducationLevels: async (params = {}) => {
    try {
      console.log('Fetching education levels with params:', params);
      const response = await axiosWithAuth.get(API_ENDPOINTS.educationLevels, { params });
      console.log('Education levels API response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching education levels:', error);
      throw error;
    }
  },

  /**
   * Get school curriculum configuration
   */
  getSchoolCurriculumConfig: async (schoolId, params = {}) => {
    try {
      console.log('Fetching school curriculum config for school:', schoolId);
      const response = await axiosWithAuth.get(`${API_ENDPOINTS.schoolCurriculumConfig}${schoolId}/`, { params });
      console.log('School curriculum config API response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching school curriculum config:', error);
      throw error;
    }
  }
}
