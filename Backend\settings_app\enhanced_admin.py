"""
Enhanced admin interface for the settings module.
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db import models
from django.forms import Textarea
from .enhanced_models import (
    AdvancedSchoolProfile, SystemSettings, NotificationTemplate,
    IntegrationSettings, CustomField, AuditLog
)
import json


@admin.register(AdvancedSchoolProfile)
class AdvancedSchoolProfileAdmin(admin.ModelAdmin):
    """Enhanced admin for school profiles"""
    
    list_display = [
        'school_branch', 'school_type', 'is_active', 'is_verified',
        'current_students', 'max_students', 'capacity_percentage',
        'updated_at'
    ]
    list_filter = [
        'school_type', 'is_active', 'is_verified', 'country',
        'establishment_date', 'created_at'
    ]
    search_fields = [
        'school_branch__name', 'motto', 'vision', 'mission',
        'email', 'phone', 'city', 'registration_number'
    ]
    readonly_fields = [
        'created_at', 'updated_at', 'capacity_utilization_display',
        'full_address_display', 'academic_year_duration_display'
    ]
    
    fieldsets = (
        ('Basic Information', {
            'fields': (
                'school_branch', 'school_type', 'establishment_date',
                'registration_number', 'accreditation_body'
            )
        }),
        ('Branding', {
            'fields': (
                'logo', 'favicon', 'banner_image', 'letterhead',
                'primary_color', 'secondary_color', 'accent_color'
            ),
            'classes': ('collapse',)
        }),
        ('School Information', {
            'fields': (
                'motto', 'vision', 'mission', 'core_values',
                'history', 'achievements'
            ),
            'classes': ('collapse',)
        }),
        ('Contact Information', {
            'fields': (
                'phone', 'mobile', 'fax', 'email', 'admin_email', 'website',
                'address_line_1', 'address_line_2', 'city', 'state_province',
                'postal_code', 'country', 'latitude', 'longitude'
            )
        }),
        ('Academic Configuration', {
            'fields': (
                'academic_year_start_month', 'academic_year_end_month',
                'terms_per_year', 'academic_year_duration_display'
            )
        }),
        ('System Settings', {
            'fields': (
                'timezone', 'language', 'currency', 'date_format', 'time_format'
            )
        }),
        ('Capacity Management', {
            'fields': (
                'max_students', 'current_students', 'max_staff', 'current_staff',
                'capacity_utilization_display'
            )
        }),
        ('Social Media', {
            'fields': (
                'facebook_url', 'twitter_url', 'instagram_url',
                'linkedin_url', 'youtube_url', 'tiktok_url'
            ),
            'classes': ('collapse',)
        }),
        ('Status', {
            'fields': (
                'is_active', 'is_verified', 'verification_date'
            )
        }),
        ('Metadata', {
            'fields': (
                'updated_by', 'created_at', 'updated_at', 'full_address_display'
            ),
            'classes': ('collapse',)
        }),
    )
    
    formfield_overrides = {
        models.JSONField: {'widget': Textarea(attrs={'rows': 4, 'cols': 80})},
    }
    
    def capacity_percentage(self, obj):
        """Display capacity utilization percentage"""
        if obj.max_students and obj.max_students > 0:
            percentage = (obj.current_students / obj.max_students) * 100
            color = 'red' if percentage > 90 else 'orange' if percentage > 80 else 'green'
            return format_html(
                '<span style="color: {};">{:.1f}%</span>',
                color, percentage
            )
        return '-'
    capacity_percentage.short_description = 'Student Capacity'
    
    def capacity_utilization_display(self, obj):
        """Display detailed capacity utilization"""
        utilization = obj.get_capacity_utilization()
        return format_html(
            'Students: {:.1f}% | Staff: {:.1f}%',
            utilization['students'], utilization['staff']
        )
    capacity_utilization_display.short_description = 'Capacity Utilization'
    
    def full_address_display(self, obj):
        """Display formatted full address"""
        return obj.get_full_address() or '-'
    full_address_display.short_description = 'Full Address'
    
    def academic_year_duration_display(self, obj):
        """Display academic year duration"""
        return f"{obj.get_academic_year_duration()} months"
    academic_year_duration_display.short_description = 'Academic Year Duration'
    
    actions = ['verify_schools', 'activate_schools', 'deactivate_schools']
    
    def verify_schools(self, request, queryset):
        """Bulk verify schools"""
        updated = queryset.update(is_verified=True, verification_date=timezone.now())
        self.message_user(request, f'{updated} schools verified successfully.')
    verify_schools.short_description = 'Verify selected schools'
    
    def activate_schools(self, request, queryset):
        """Bulk activate schools"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} schools activated successfully.')
    activate_schools.short_description = 'Activate selected schools'
    
    def deactivate_schools(self, request, queryset):
        """Bulk deactivate schools"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} schools deactivated successfully.')
    deactivate_schools.short_description = 'Deactivate selected schools'


@admin.register(SystemSettings)
class SystemSettingsAdmin(admin.ModelAdmin):
    """Enhanced admin for system settings"""
    
    list_display = [
        'school_branch', 'environment', 'maintenance_mode',
        'debug_mode', 'auto_backup_enabled', 'updated_at'
    ]
    list_filter = [
        'environment', 'maintenance_mode', 'debug_mode',
        'auto_backup_enabled', 'email_notifications_enabled'
    ]
    search_fields = ['school_branch__name']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Environment', {
            'fields': (
                'school_branch', 'environment', 'debug_mode',
                'maintenance_mode', 'maintenance_message'
            )
        }),
        ('Security Settings', {
            'fields': (
                'session_timeout', 'password_expiry_days',
                'max_login_attempts', 'lockout_duration', 'require_mfa'
            )
        }),
        ('Backup Configuration', {
            'fields': (
                'auto_backup_enabled', 'backup_frequency',
                'backup_retention_days', 'backup_location'
            ),
            'classes': ('collapse',)
        }),
        ('Email Configuration', {
            'fields': (
                'email_notifications_enabled', 'smtp_host', 'smtp_port',
                'smtp_use_tls', 'smtp_username', 'smtp_password'
            ),
            'classes': ('collapse',)
        }),
        ('SMS Configuration', {
            'fields': (
                'sms_notifications_enabled', 'sms_provider',
                'sms_api_key', 'sms_sender_id'
            ),
            'classes': ('collapse',)
        }),
        ('File Upload Settings', {
            'fields': (
                'max_file_size', 'allowed_file_types', 'upload_path'
            ),
            'classes': ('collapse',)
        }),
        ('Performance Settings', {
            'fields': (
                'cache_timeout', 'page_size', 'max_export_records'
            ),
            'classes': ('collapse',)
        }),
        ('Integration Settings', {
            'fields': (
                'api_rate_limit', 'webhook_timeout'
            ),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('updated_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    formfield_overrides = {
        models.JSONField: {'widget': Textarea(attrs={'rows': 4, 'cols': 80})},
    }


@admin.register(NotificationTemplate)
class NotificationTemplateAdmin(admin.ModelAdmin):
    """Enhanced admin for notification templates"""
    
    list_display = [
        'name', 'template_type', 'trigger_event', 'is_active',
        'send_immediately', 'created_at'
    ]
    list_filter = [
        'template_type', 'trigger_event', 'is_active',
        'send_immediately', 'created_at'
    ]
    search_fields = ['name', 'subject', 'content']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': (
                'school_branch', 'name', 'template_type', 'trigger_event'
            )
        }),
        ('Template Content', {
            'fields': ('subject', 'content', 'html_content')
        }),
        ('Configuration', {
            'fields': (
                'is_active', 'send_immediately', 'delay_minutes'
            )
        }),
        ('Variables', {
            'fields': ('available_variables',),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': (
                'created_by', 'updated_by', 'created_at', 'updated_at'
            ),
            'classes': ('collapse',)
        }),
    )
    
    formfield_overrides = {
        models.TextField: {'widget': Textarea(attrs={'rows': 6, 'cols': 80})},
        models.JSONField: {'widget': Textarea(attrs={'rows': 4, 'cols': 80})},
    }


@admin.register(IntegrationSettings)
class IntegrationSettingsAdmin(admin.ModelAdmin):
    """Enhanced admin for integration settings"""
    
    list_display = [
        'name', 'integration_type', 'provider', 'status',
        'is_sandbox', 'last_sync', 'created_at'
    ]
    list_filter = [
        'integration_type', 'provider', 'status',
        'is_sandbox', 'created_at'
    ]
    search_fields = ['name', 'provider']
    readonly_fields = ['created_at', 'updated_at', 'last_sync', 'masked_credentials']
    
    fieldsets = (
        ('Basic Information', {
            'fields': (
                'school_branch', 'name', 'integration_type', 'provider'
            )
        }),
        ('Configuration', {
            'fields': (
                'api_endpoint', 'webhook_url', 'is_sandbox'
            )
        }),
        ('Credentials', {
            'fields': ('api_key', 'api_secret', 'masked_credentials'),
            'classes': ('collapse',)
        }),
        ('Settings', {
            'fields': ('configuration',),
            'classes': ('collapse',)
        }),
        ('Status', {
            'fields': ('status', 'last_sync', 'last_error')
        }),
        ('Metadata', {
            'fields': (
                'created_by', 'updated_by', 'created_at', 'updated_at'
            ),
            'classes': ('collapse',)
        }),
    )
    
    def masked_credentials(self, obj):
        """Display masked credentials"""
        html = ""
        if obj.api_key:
            html += f"API Key: ***{obj.api_key[-4:]}<br>"
        if obj.api_secret:
            html += f"API Secret: ***{obj.api_secret[-4:]}<br>"
        return mark_safe(html) if html else "-"
    masked_credentials.short_description = 'Masked Credentials'
    
    formfield_overrides = {
        models.JSONField: {'widget': Textarea(attrs={'rows': 4, 'cols': 80})},
    }


@admin.register(CustomField)
class CustomFieldAdmin(admin.ModelAdmin):
    """Enhanced admin for custom fields"""
    
    list_display = [
        'name', 'target_model', 'field_type', 'is_required',
        'is_active', 'order', 'created_at'
    ]
    list_filter = [
        'target_model', 'field_type', 'is_required',
        'is_active', 'show_in_list', 'created_at'
    ]
    search_fields = ['name', 'label', 'help_text']
    readonly_fields = ['created_at', 'updated_at', 'field_widget_display']
    ordering = ['target_model', 'order', 'name']
    
    fieldsets = (
        ('Basic Information', {
            'fields': (
                'school_branch', 'name', 'field_type', 'target_model'
            )
        }),
        ('Field Configuration', {
            'fields': (
                'label', 'help_text', 'placeholder', 'default_value'
            )
        }),
        ('Validation', {
            'fields': (
                'is_required', 'min_length', 'max_length',
                'min_value', 'max_value'
            ),
            'classes': ('collapse',)
        }),
        ('Choices', {
            'fields': ('choices',),
            'classes': ('collapse',)
        }),
        ('Display', {
            'fields': (
                'order', 'is_active', 'show_in_list', 'show_in_detail'
            )
        }),
        ('Metadata', {
            'fields': (
                'field_widget_display', 'created_by', 'created_at', 'updated_at'
            ),
            'classes': ('collapse',)
        }),
    )
    
    def field_widget_display(self, obj):
        """Display the field widget type"""
        return obj.get_field_widget()
    field_widget_display.short_description = 'Widget Type'
    
    formfield_overrides = {
        models.JSONField: {'widget': Textarea(attrs={'rows': 4, 'cols': 80})},
    }


@admin.register(AuditLog)
class AuditLogAdmin(admin.ModelAdmin):
    """Enhanced admin for audit logs"""
    
    list_display = [
        'timestamp', 'user', 'action', 'severity',
        'success', 'ip_address', 'school_branch'
    ]
    list_filter = [
        'action', 'severity', 'success', 'timestamp',
        'school_branch', 'content_type'
    ]
    search_fields = [
        'user__email', 'description', 'ip_address',
        'user_agent', 'request_path'
    ]
    readonly_fields = [
        'timestamp', 'user', 'action', 'severity', 'description',
        'changes', 'metadata', 'ip_address', 'user_agent',
        'request_path', 'request_method', 'duration_ms',
        'success', 'error_message', 'content_type', 'object_id',
        'object_representation_display'
    ]
    date_hierarchy = 'timestamp'
    ordering = ['-timestamp']
    
    fieldsets = (
        ('Basic Information', {
            'fields': (
                'timestamp', 'user', 'action', 'severity', 'success'
            )
        }),
        ('Target Object', {
            'fields': (
                'content_type', 'object_id', 'object_representation_display'
            ),
            'classes': ('collapse',)
        }),
        ('Details', {
            'fields': ('description', 'changes', 'metadata')
        }),
        ('Request Information', {
            'fields': (
                'ip_address', 'user_agent', 'request_path',
                'request_method', 'duration_ms'
            ),
            'classes': ('collapse',)
        }),
        ('Error Information', {
            'fields': ('error_message',),
            'classes': ('collapse',)
        }),
    )
    
    def object_representation_display(self, obj):
        """Display object representation"""
        return obj.get_object_representation()
    object_representation_display.short_description = 'Target Object'
    
    def has_add_permission(self, request):
        """Disable adding audit logs through admin"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Disable changing audit logs through admin"""
        return False
    
    def has_delete_permission(self, request, obj=None):
        """Disable deleting audit logs through admin"""
        return False
    
    formfield_overrides = {
        models.JSONField: {'widget': Textarea(attrs={'rows': 4, 'cols': 80})},
    }
