from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.forms import UserCreationForm
from django import forms
# from django.apps import apps # Removed, no longer needed in this form
# from django.db import transaction # Removed, no longer needed in this form
from .models import CustomUser
# Import profile inlines from users.admin (will be created in the next step)
from users.admin import AdminProfileInline, StaffInline, TeacherInline, StudentInline, ParentInline # Uncomment after creating inlines

class CustomUserCreationForm(UserCreationForm):
    """Custom form for creating users without profile creation."""
    class Meta:
        model = CustomUser
        fields = ('email', 'username', 'first_name', 'last_name', 'user_type', 'phone')

    def save(self, commit=True):
        # This form only creates the user. Profile creation will be handled by inlines.
        user = super().save(commit=commit)
        # No profile creation logic here anymore
        return user

@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    add_form = CustomUserCreationForm
    model = CustomUser
    
    list_display = ('email', 'username', 'first_name', 'last_name', 'user_type', 'is_active')
    list_filter = ('user_type', 'is_active', 'is_staff')
    search_fields = ('email', 'username', 'first_name', 'last_name')
    ordering = ('email',)
    
    readonly_fields = ('date_joined',)
    
    # Include profile inlines here after creating them in users/admin.py
    inlines = [AdminProfileInline, StaffInline, TeacherInline, StudentInline, ParentInline] # Uncomment after creating inlines

    fieldsets = (
        (None, {'fields': ('email', 'username', 'password')}),
        ('Personal info', {'fields': ('first_name', 'last_name', 'phone', 'profile_picture')}),
        ('User Type', {'fields': ('user_type',)}),
        ('Permissions', {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        ('Important dates', {'fields': ('last_login',)}),
    )
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'username', 'first_name', 'last_name', 'user_type', 'phone', 'password1', 'password2'),
        }),
    )

    # Simplified get_fieldsets and get_readonly_fields
    def get_fieldsets(self, request, obj=None):
        # Use add_fieldsets for the add view, fieldsets for the change view
        if obj is None:
            return self.add_fieldsets
        return self.fieldsets

    def get_readonly_fields(self, request, obj=None):
        # Always use our custom readonly_fields
        return list(self.readonly_fields)
