<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Outstanding Fees Report</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
        }
        th {
            background-color: #f2f2f2;
            text-align: left;
        }
    </style>
</head>
<body>
    <h1>Outstanding Fees Report</h1>
    <h2>School: {{ school.name }}</h2>
    <h3>Reporting Period: {{ reporting_period }}</h3>

    <table>
        <thead>
            <tr>
                <th>Student Name</th>
                <th>Class</th>
                <th>Outstanding Amount</th>
                <th>Due Date</th>
            </tr>
        </thead>
        <tbody>
            {% for balance in outstanding_balances %}
            <tr>
                <td>{{ balance.student.get_full_name }}</td>
                <td>{{ balance.student.class_name }}</td>
                <td>{{ balance.balance }}</td>
                <td>{{ balance.due_date }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</body>
</html>
