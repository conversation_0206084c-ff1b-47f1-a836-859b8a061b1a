{% extends "reports/base_report.html" %}

{% block title %}Teacher Performance Report{% endblock %}
{% block report_title %}Teacher Performance Report{% endblock %}
{% block content %}
<h2>Teacher: {{ teacher.get_full_name }}</h2>

<h2>Performance Metrics</h2>
<table>
    <thead>
        <tr>
            <th>Subject</th>
            <th>Class Average</th>
            <th>Pass Rate</th>
            <th>Completion Rate</th>
            <th>Average Value Addition</th>
            <th>Number of Distinctions</th>
            <th>Number of Failures</th>
        </tr>
    </thead>
    <tbody>
        {% for metric in teacher_metrics %}
        <tr>
            <td>{{ metric.subject.name }}</td>
            <td>{{ metric.class_average }}</td>
            <td>{{ metric.pass_rate }}%</td>
            <td>{{ metric.completion_rate }}%</td>
            <td>{{ metric.avg_value_addition }}</td>
            <td>{{ metric.number_of_distinctions }}</td>
            <td>{{ metric.number_of_failures }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% endblock %}
