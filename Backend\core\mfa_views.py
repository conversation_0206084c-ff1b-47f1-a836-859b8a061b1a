"""
Multi-Factor Authentication views for enhanced security.
"""
import logging
import qrcode
import io
import base64
from django.contrib.auth import get_user_model
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django_otp.models import Device
from django_otp.plugins.otp_totp.models import TOTPDevice
from django_otp.plugins.otp_static.models import StaticDevice, StaticToken
from django_otp.util import random_hex
from rest_framework import status, viewsets, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
import json

logger = logging.getLogger(__name__)
User = get_user_model()


class MFAViewSet(viewsets.ViewSet):
    """
    ViewSet for Multi-Factor Authentication operations.
    """
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['get'])
    def status(self, request):
        """
        Get MFA status for the current user.
        """
        user = request.user
        
        # Check if user has any MFA devices
        totp_devices = TOTPDevice.objects.devices_for_user(user, confirmed=True)
        static_devices = StaticDevice.objects.devices_for_user(user, confirmed=True)
        
        has_mfa = len(totp_devices) > 0 or len(static_devices) > 0
        
        return Response({
            'mfa_enabled': has_mfa,
            'totp_devices': len(totp_devices),
            'backup_tokens': len(static_devices),
            'devices': [
                {
                    'id': device.id,
                    'name': device.name,
                    'type': 'totp',
                    'confirmed': device.confirmed
                } for device in totp_devices
            ]
        })

    @action(detail=False, methods=['post'])
    def setup_totp(self, request):
        """
        Set up TOTP (Time-based One-Time Password) for the user.
        """
        user = request.user
        device_name = request.data.get('device_name', f'{user.username}_totp')
        
        # Check if user already has a TOTP device
        existing_device = TOTPDevice.objects.filter(user=user, name=device_name).first()
        if existing_device and existing_device.confirmed:
            return Response(
                {'error': 'TOTP device already exists and is confirmed'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Create or get unconfirmed device
        if existing_device and not existing_device.confirmed:
            device = existing_device
        else:
            device = TOTPDevice.objects.create(
                user=user,
                name=device_name,
                confirmed=False
            )
        
        # Generate QR code
        qr_code_url = device.config_url
        
        # Create QR code image
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(qr_code_url)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        qr_code_image = base64.b64encode(buffer.getvalue()).decode()
        
        return Response({
            'device_id': device.id,
            'qr_code': f'data:image/png;base64,{qr_code_image}',
            'manual_entry_key': device.key,
            'issuer': 'ShuleXcel',
            'account_name': user.email
        })

    @action(detail=False, methods=['post'])
    def confirm_totp(self, request):
        """
        Confirm TOTP setup by verifying a token.
        """
        device_id = request.data.get('device_id')
        token = request.data.get('token')
        
        if not device_id or not token:
            return Response(
                {'error': 'Device ID and token are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            device = TOTPDevice.objects.get(id=device_id, user=request.user)
        except TOTPDevice.DoesNotExist:
            return Response(
                {'error': 'Device not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Verify the token
        if device.verify_token(token):
            device.confirmed = True
            device.save()
            
            # Generate backup tokens
            backup_tokens = self._generate_backup_tokens(request.user)
            
            logger.info(f"TOTP confirmed for user {request.user.email}")
            
            return Response({
                'success': True,
                'message': 'TOTP device confirmed successfully',
                'backup_tokens': backup_tokens
            })
        else:
            return Response(
                {'error': 'Invalid token'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['post'])
    def verify_totp(self, request):
        """
        Verify TOTP token for authentication.
        """
        token = request.data.get('token')
        
        if not token:
            return Response(
                {'error': 'Token is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        user = request.user
        devices = TOTPDevice.objects.devices_for_user(user, confirmed=True)
        
        for device in devices:
            if device.verify_token(token):
                logger.info(f"TOTP verified for user {user.email}")
                return Response({'verified': True})
        
        # Check backup tokens
        static_devices = StaticDevice.objects.devices_for_user(user, confirmed=True)
        for device in static_devices:
            if device.verify_token(token):
                logger.info(f"Backup token used for user {user.email}")
                return Response({'verified': True, 'backup_token_used': True})
        
        logger.warning(f"Invalid TOTP token for user {user.email}")
        return Response(
            {'error': 'Invalid token'},
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=False, methods=['post'])
    def disable_mfa(self, request):
        """
        Disable MFA for the user (requires password confirmation).
        """
        password = request.data.get('password')
        
        if not password:
            return Response(
                {'error': 'Password is required to disable MFA'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        user = request.user
        if not user.check_password(password):
            return Response(
                {'error': 'Invalid password'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Delete all MFA devices
        TOTPDevice.objects.filter(user=user).delete()
        StaticDevice.objects.filter(user=user).delete()
        
        logger.warning(f"MFA disabled for user {user.email}")
        
        return Response({
            'success': True,
            'message': 'MFA has been disabled'
        })

    @action(detail=False, methods=['get'])
    def backup_tokens(self, request):
        """
        Get remaining backup tokens count.
        """
        user = request.user
        static_devices = StaticDevice.objects.devices_for_user(user, confirmed=True)
        
        total_tokens = 0
        for device in static_devices:
            total_tokens += device.token_set.count()
        
        return Response({
            'remaining_tokens': total_tokens
        })

    @action(detail=False, methods=['post'])
    def regenerate_backup_tokens(self, request):
        """
        Regenerate backup tokens (requires password confirmation).
        """
        password = request.data.get('password')
        
        if not password:
            return Response(
                {'error': 'Password is required to regenerate backup tokens'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        user = request.user
        if not user.check_password(password):
            return Response(
                {'error': 'Invalid password'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Delete existing backup tokens
        StaticDevice.objects.filter(user=user).delete()
        
        # Generate new backup tokens
        backup_tokens = self._generate_backup_tokens(user)
        
        logger.info(f"Backup tokens regenerated for user {user.email}")
        
        return Response({
            'success': True,
            'backup_tokens': backup_tokens
        })

    def _generate_backup_tokens(self, user, count=10):
        """
        Generate backup tokens for the user.
        """
        device = StaticDevice.objects.create(
            user=user,
            name=f'{user.username}_backup',
            confirmed=True
        )
        
        tokens = []
        for _ in range(count):
            token = StaticToken.random_token()
            StaticToken.objects.create(device=device, token=token)
            tokens.append(token)
        
        return tokens


@csrf_exempt
@require_http_methods(["POST"])
def mfa_login_verify(request):
    """
    Verify MFA token during login process.
    """
    try:
        data = json.loads(request.body)
        user_id = data.get('user_id')
        token = data.get('token')
        
        if not user_id or not token:
            return JsonResponse(
                {'error': 'User ID and token are required'},
                status=400
            )
        
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            return JsonResponse(
                {'error': 'User not found'},
                status=404
            )
        
        # Verify TOTP token
        totp_devices = TOTPDevice.objects.devices_for_user(user, confirmed=True)
        for device in totp_devices:
            if device.verify_token(token):
                # Generate JWT tokens
                refresh = RefreshToken.for_user(user)
                
                logger.info(f"MFA login successful for user {user.email}")
                
                return JsonResponse({
                    'success': True,
                    'access': str(refresh.access_token),
                    'refresh': str(refresh),
                    'user_id': user.id
                })
        
        # Check backup tokens
        static_devices = StaticDevice.objects.devices_for_user(user, confirmed=True)
        for device in static_devices:
            if device.verify_token(token):
                refresh = RefreshToken.for_user(user)
                
                logger.info(f"MFA login with backup token for user {user.email}")
                
                return JsonResponse({
                    'success': True,
                    'access': str(refresh.access_token),
                    'refresh': str(refresh),
                    'user_id': user.id,
                    'backup_token_used': True
                })
        
        logger.warning(f"Invalid MFA token during login for user {user.email}")
        return JsonResponse(
            {'error': 'Invalid token'},
            status=400
        )
        
    except json.JSONDecodeError:
        return JsonResponse(
            {'error': 'Invalid JSON'},
            status=400
        )
    except Exception as e:
        logger.error(f"MFA login error: {str(e)}")
        return JsonResponse(
            {'error': 'Internal server error'},
            status=500
        )
