# ShuleXcel Enhanced Settings Module

## 🎯 Overview

The Enhanced Settings Module provides comprehensive configuration and management capabilities for ShuleXcel school management system. This module includes advanced school profile management, system settings, notification templates, integrations, custom fields, and audit logging.

## 🚀 Features

### 📋 Advanced School Profile Management
- **Comprehensive Branding**: Logo, favicon, banner, letterhead management
- **Color Scheme Configuration**: Primary, secondary, and accent colors with hex validation
- **Contact Information**: Multiple phone numbers, emails, and address management
- **Geographic Data**: Latitude/longitude for mapping and location services
- **Academic Configuration**: Terms per year, academic calendar setup
- **Capacity Management**: Student and staff limits with utilization tracking
- **Social Media Integration**: Links to all major social platforms
- **Verification System**: School profile verification and status tracking

### ⚙️ System Settings Configuration
- **Environment Management**: Production, staging, development modes
- **Security Configuration**: Session timeouts, password policies, MFA requirements
- **Backup Management**: Automated backup schedules and retention policies
- **Email Configuration**: SMTP settings and notification preferences
- **SMS Integration**: Provider setup and sender ID configuration
- **File Upload Management**: Size limits and allowed file types
- **Performance Settings**: Cache timeouts and pagination configuration
- **API Management**: Rate limiting and webhook timeout settings

### 📧 Notification Template System
- **Multiple Template Types**: Email, SMS, push notifications, in-app messages
- **Event Triggers**: User registration, password reset, fee payments, grade publishing
- **Variable System**: Dynamic content insertion with template variables
- **Template Testing**: Preview and test templates with sample data
- **HTML Support**: Rich email templates with HTML formatting
- **Scheduling**: Delayed message sending with configurable delays

### 🔗 Integration Management
- **Multiple Integration Types**: Payment gateways, SMS providers, email services, LMS
- **Connection Testing**: Verify third-party service connectivity
- **Credential Security**: Encrypted storage of API keys and secrets
- **Status Monitoring**: Real-time integration health tracking
- **Sandbox Support**: Test environment configuration
- **Error Handling**: Automatic retry mechanisms and error logging

### 🎛️ Custom Fields System
- **Multiple Field Types**: Text, number, email, URL, date, boolean, choice, file upload
- **Target Models**: Students, teachers, parents, classes, subjects
- **Validation Rules**: Required fields, min/max values, length constraints
- **Display Configuration**: List and detail view visibility settings
- **Field Ordering**: Drag-and-drop field reordering
- **Choice Management**: Single and multiple choice field options

### 📊 Audit and Monitoring
- **Comprehensive Logging**: All system activities and user actions
- **Security Monitoring**: Failed logins, permission changes, suspicious activities
- **Performance Tracking**: Response times, error rates, system health
- **Compliance Support**: Audit trails for regulatory requirements
- **Real-time Alerts**: Security incidents and system issues
- **Statistical Analysis**: Usage patterns and activity trends

## 🏗️ Architecture

### Models Structure

```
Enhanced Models:
├── AdvancedSchoolProfile    # Enhanced school profile with comprehensive features
├── SystemSettings          # Advanced system configuration
├── NotificationTemplate     # Email/SMS template management
├── IntegrationSettings     # Third-party integration configuration
├── CustomField            # Dynamic field system
└── AuditLog               # Comprehensive audit logging

Legacy Models (Backward Compatibility):
├── SchoolProfile          # Basic school profile
└── SystemConfiguration    # Basic system settings
```

### API Endpoints

```
Enhanced API (v2):
├── /api/v2/advanced-profiles/     # Advanced school profiles
├── /api/v2/system-settings/       # System settings
├── /api/v2/notification-templates/ # Notification templates
├── /api/v2/integrations/          # Integration settings
├── /api/v2/custom-fields/         # Custom fields
└── /api/v2/audit-logs/            # Audit logs

Legacy API (v1):
├── /api/v1/profiles/              # Basic school profiles
└── /api/v1/configurations/        # Basic system settings
```

## 🛠️ Installation and Setup

### 1. Install Dependencies

```bash
pip install cryptography  # For encryption features
pip install django-filter  # For advanced filtering
pip install pillow  # For image handling
```

### 2. Run Migrations

```bash
python manage.py migrate settings_app
```

### 3. Set Up Default Settings

```bash
# Create default settings for all schools
python manage.py setup_default_settings

# Create enhanced settings for specific school
python manage.py setup_default_settings --school-id 1 --enhanced

# Force overwrite existing settings
python manage.py setup_default_settings --force
```

### 4. Migrate Existing Data (Optional)

```bash
# Preview migration
python manage.py migrate_settings --dry-run

# Perform migration
python manage.py migrate_settings

# Force migration (overwrite existing enhanced models)
python manage.py migrate_settings --force
```

## 📝 Usage Examples

### Creating an Advanced School Profile

```python
from settings_app.enhanced_models import AdvancedSchoolProfile

profile = AdvancedSchoolProfile.objects.create(
    school_branch=school_branch,
    school_type='private',
    motto='Excellence in Education',
    vision='To be a leading educational institution',
    mission='To nurture and develop students',
    primary_color='#1f2937',
    secondary_color='#3b82f6',
    academic_year_start_month=1,
    academic_year_end_month=12,
    terms_per_year=3,
    max_students=1000,
    current_students=750
)

# Get capacity utilization
utilization = profile.get_capacity_utilization()
print(f"Student capacity: {utilization['students']}%")
```

### Creating Notification Templates

```python
from settings_app.enhanced_models import NotificationTemplate

template = NotificationTemplate.objects.create(
    school_branch=school_branch,
    name='Welcome Email',
    template_type='email',
    trigger_event='user_registration',
    subject='Welcome to {{school_name}}',
    content='Dear {{name}}, welcome to our school!',
    available_variables=['name', 'school_name', 'username']
)

# Test template rendering
context = {'name': 'John Doe', 'school_name': 'Test School'}
rendered = template.render_content(context)
```

### Setting Up Integrations

```python
from settings_app.enhanced_models import IntegrationSettings

integration = IntegrationSettings.objects.create(
    school_branch=school_branch,
    name='M-Pesa Payment',
    integration_type='payment_gateway',
    provider='Safaricom',
    api_endpoint='https://api.safaricom.co.ke',
    api_key='your_api_key',
    is_sandbox=True,
    status='testing'
)

# Test connection
result = integration.test_connection()
```

### Creating Custom Fields

```python
from settings_app.enhanced_models import CustomField

field = CustomField.objects.create(
    school_branch=school_branch,
    name='emergency_contact',
    field_type='text',
    target_model='student',
    label='Emergency Contact',
    help_text='Emergency contact person name',
    is_required=True,
    max_length=100
)
```

## 🔧 Configuration

### Environment Variables

```bash
# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USE_TLS=True
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password

# SMS Configuration
SMS_PROVIDER=twilio
SMS_API_KEY=your_sms_api_key
SMS_SENDER_ID=SHULEXCEL

# Security Settings
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=5
REQUIRE_MFA=False

# File Upload Settings
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=.pdf,.doc,.docx,.jpg,.png
```

### Django Settings

```python
# Add to INSTALLED_APPS
INSTALLED_APPS = [
    # ... other apps
    'settings_app',
    'django_filters',
]

# Add to MIDDLEWARE
MIDDLEWARE = [
    # ... other middleware
    'settings_app.middleware.SettingsMiddleware',
]

# Cache configuration for performance
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
    }
}
```

## 🧪 Testing

### Run Tests

```bash
# Run all enhanced settings tests
python manage.py test settings_app.enhanced_tests

# Run specific test class
python manage.py test settings_app.enhanced_tests.AdvancedSchoolProfileModelTest

# Run with coverage
coverage run --source='.' manage.py test settings_app.enhanced_tests
coverage report
```

### Test Coverage

The enhanced settings module includes comprehensive tests covering:
- Model validation and business logic
- API endpoints and serialization
- Template rendering and variable substitution
- Integration connection testing
- Custom field functionality
- Audit logging accuracy

## 📚 API Documentation

### Advanced School Profile API

```http
GET /api/v2/advanced-profiles/
POST /api/v2/advanced-profiles/
GET /api/v2/advanced-profiles/{id}/
PUT /api/v2/advanced-profiles/{id}/
PATCH /api/v2/advanced-profiles/{id}/
DELETE /api/v2/advanced-profiles/{id}/

# Custom endpoints
POST /api/v2/advanced-profiles/{id}/verify_school/
GET /api/v2/advanced-profiles/{id}/capacity_report/
GET /api/v2/advanced-profiles/statistics/
```

### Notification Template API

```http
GET /api/v2/notification-templates/
POST /api/v2/notification-templates/
GET /api/v2/notification-templates/{id}/
PUT /api/v2/notification-templates/{id}/
PATCH /api/v2/notification-templates/{id}/
DELETE /api/v2/notification-templates/{id}/

# Custom endpoints
POST /api/v2/notification-templates/{id}/test_template/
GET /api/v2/notification-templates/available_variables/
```

### Integration Settings API

```http
GET /api/v2/integrations/
POST /api/v2/integrations/
GET /api/v2/integrations/{id}/
PUT /api/v2/integrations/{id}/
PATCH /api/v2/integrations/{id}/
DELETE /api/v2/integrations/{id}/

# Custom endpoints
POST /api/v2/integrations/{id}/test_connection/
POST /api/v2/integrations/{id}/sync_data/
```

## 🔒 Security Features

### Data Protection
- **Encrypted Storage**: API keys and sensitive data encrypted at rest
- **Access Control**: Role-based permissions for all operations
- **Audit Logging**: Comprehensive tracking of all system activities
- **Input Validation**: Strict validation of all user inputs
- **CSRF Protection**: Cross-site request forgery protection

### Security Best Practices
- Regular security audits and vulnerability assessments
- Secure credential management with encryption
- Session management with configurable timeouts
- Multi-factor authentication support
- IP-based access control and monitoring

## 🚀 Performance Optimization

### Caching Strategy
- **Model Caching**: Frequently accessed settings cached
- **Query Optimization**: Efficient database queries with select_related
- **API Response Caching**: Cached responses for read-heavy endpoints
- **Template Caching**: Rendered templates cached for reuse

### Database Optimization
- **Indexes**: Strategic database indexes for performance
- **Query Optimization**: Efficient ORM queries
- **Connection Pooling**: Database connection optimization
- **Bulk Operations**: Efficient bulk create/update operations

## 📈 Monitoring and Analytics

### System Health Monitoring
- **Performance Metrics**: Response times, error rates
- **Resource Usage**: CPU, memory, disk utilization
- **Integration Health**: Third-party service status
- **User Activity**: Login patterns, feature usage

### Audit and Compliance
- **Activity Tracking**: All user actions logged
- **Security Events**: Failed logins, permission changes
- **Data Changes**: Before/after values for updates
- **Compliance Reports**: Regulatory audit trails

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create a feature branch
3. Install development dependencies
4. Run tests to ensure everything works
5. Make your changes
6. Add tests for new functionality
7. Submit a pull request

### Code Standards
- Follow PEP 8 style guidelines
- Write comprehensive tests
- Document all new features
- Use type hints where appropriate
- Maintain backward compatibility

## 📄 License

This module is part of the ShuleXcel School Management System and is subject to the same licensing terms.

## 📞 Support

For technical support and questions:
- **Email**: <EMAIL>
- **Documentation**: [ShuleXcel Docs](https://docs.shulexcel.com)
- **Issues**: [GitHub Issues](https://github.com/shulexcel/issues)

---

**Version**: 2.0.0  
**Last Updated**: 2024  
**Compatibility**: Django 4.2+, Python 3.8+
