from django.db import models
from .curriculum_models import CurriculumSystem, EducationLevel

class SubjectCategory(models.Model):
    """
    Categories for subjects in different curriculum systems
    - CBC: Learning Areas
    - 8-4-4: Subject Groups
    - IGCSE: Subject Groups
    """
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20)
    curriculum_system = models.ForeignKey(CurriculumSystem, on_delete=models.CASCADE, related_name='subject_categories')
    description = models.TextField(blank=True, null=True)
    
    class Meta:
        unique_together = ['code', 'curriculum_system']
        verbose_name_plural = 'Subject Categories'
        
    def __str__(self):
        return f"{self.name} ({self.curriculum_system.code})"

class CurriculumSubject(models.Model):
    """
    Subjects specific to curriculum systems
    """
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20)
    curriculum_system = models.ForeignKey(CurriculumSystem, on_delete=models.CASCADE, related_name='subjects')
    category = models.Foreign<PERSON>ey(SubjectCategory, on_delete=models.CASCADE, related_name='subjects')
    description = models.TextField(blank=True, null=True)
    
    # Applicable education levels
    education_levels = models.ManyToManyField(EducationLevel, related_name='subjects')
    
    # Is this subject compulsory at these levels?
    is_compulsory = models.BooleanField(default=False)
    
    # For CBC-specific attributes
    is_core_competency = models.BooleanField(default=False)
    
    # For 8-4-4 specific attributes
    is_examinable = models.BooleanField(default=True)
    
    # For IGCSE specific attributes
    is_coursework_required = models.BooleanField(default=False)
    coursework_percentage = models.IntegerField(default=0)
    
    # Additional curriculum-specific data
    system_specific_data = models.JSONField(null=True, blank=True)
    
    class Meta:
        unique_together = ['code', 'curriculum_system']
        
    def __str__(self):
        return f"{self.name} ({self.curriculum_system.code})"

class AssessmentMethod(models.Model):
    """
    Assessment methods for different curriculum systems
    """
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20)
    curriculum_system = models.ForeignKey(CurriculumSystem, on_delete=models.CASCADE, related_name='assessment_methods')
    description = models.TextField(blank=True, null=True)
    
    # Assessment type (formative, summative, etc.)
    ASSESSMENT_TYPES = [
        ('formative', 'Formative Assessment'),
        ('summative', 'Summative Assessment'),
        ('diagnostic', 'Diagnostic Assessment'),
        ('performance', 'Performance Assessment'),
        ('portfolio', 'Portfolio Assessment'),
        ('project', 'Project-Based Assessment'),
        ('exam', 'Examination'),
    ]
    assessment_type = models.CharField(max_length=20, choices=ASSESSMENT_TYPES)
    
    # Weighting in final grade (percentage)
    weight_percentage = models.IntegerField(default=100)
    
    # Additional curriculum-specific data
    system_specific_data = models.JSONField(null=True, blank=True)
    
    class Meta:
        unique_together = ['code', 'curriculum_system']
        
    def __str__(self):
        return f"{self.name} ({self.curriculum_system.code})"

class SubjectAssessment(models.Model):
    """
    Links subjects to their assessment methods
    """
    subject = models.ForeignKey(CurriculumSubject, on_delete=models.CASCADE, related_name='assessments')
    assessment_method = models.ForeignKey(AssessmentMethod, on_delete=models.CASCADE, related_name='subject_assessments')
    education_level = models.ForeignKey(EducationLevel, on_delete=models.CASCADE, related_name='subject_assessments')
    
    # Weighting for this specific subject-assessment combination
    weight_percentage = models.IntegerField(default=100)
    
    # Additional details
    details = models.TextField(blank=True, null=True)
    
    class Meta:
        unique_together = ['subject', 'assessment_method', 'education_level']
        
    def __str__(self):
        return f"{self.subject.name} - {self.assessment_method.name} ({self.education_level.name})"
