from rest_framework import serializers
from .transport_fees import Transport<PERSON>ee, TransportFeeDiscount
from .serializers import RouteSerializer
from fees.serializers import FeeTypeSerializer, FeeStructureSerializer

class TransportFeeSerializer(serializers.ModelSerializer):
    route_details = RouteSerializer(source='route', read_only=True)
    fee_type_details = FeeTypeSerializer(source='fee_type', read_only=True)
    fee_structure_details = FeeStructureSerializer(source='fee_structure', read_only=True)
    
    class Meta:
        model = TransportFee
        fields = '__all__'
        
class TransportFeeDiscountSerializer(serializers.ModelSerializer):
    transport_fee_details = TransportFeeSerializer(source='transport_fee', read_only=True)
    
    class Meta:
        model = TransportFeeDiscount
        fields = '__all__'
