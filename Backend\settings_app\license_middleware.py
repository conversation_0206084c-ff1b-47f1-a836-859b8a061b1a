from django.http import JsonResponse
from .license_models import LicenseSubscription

class LicenseStatusMiddleware:
    """
    Middleware to automatically update license status based on expiry date
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
    def __call__(self, request):
        # Only check for authenticated users with a school branch
        if hasattr(request, 'user') and request.user.is_authenticated and hasattr(request.user, 'school_branch') and request.user.school_branch:
            try:
                # Get the license for the user's school
                license = request.user.school_branch.school.license
                
                # Update the license status if needed
                license.update_status()
                
            except (AttributeError, LicenseSubscription.DoesNotExist):
                # No license exists, nothing to update
                pass
                
        # Continue with the request
        return self.get_response(request)
