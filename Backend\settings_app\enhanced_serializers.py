"""
Enhanced serializers for the settings module with comprehensive validation and features.
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils import timezone
from .enhanced_models import (
    AdvancedSchoolProfile, SystemSettings, NotificationTemplate,
    IntegrationSettings, CustomField, AuditLog
)
from .models import SchoolProfile, SystemConfiguration
import re

User = get_user_model()


class AdvancedSchoolProfileSerializer(serializers.ModelSerializer):
    """Enhanced serializer for school profile with validation"""
    
    # Read-only computed fields
    academic_year_duration = serializers.ReadOnlyField(source='get_academic_year_duration')
    full_address = serializers.ReadOnlyField(source='get_full_address')
    capacity_utilization = serializers.ReadOnlyField(source='get_capacity_utilization')
    
    # Custom fields for better API response
    school_branch_name = serializers.CharField(source='school_branch.name', read_only=True)
    updated_by_name = serializers.Char<PERSON><PERSON>(source='updated_by.get_full_name', read_only=True)
    
    class Meta:
        model = AdvancedSchoolProfile
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'updated_by')
    
    def validate_primary_color(self, value):
        """Validate hex color format"""
        if not re.match(r'^#[0-9A-Fa-f]{6}$', value):
            raise serializers.ValidationError("Enter a valid hex color code (e.g., #FF0000)")
        return value
    
    def validate_secondary_color(self, value):
        """Validate hex color format"""
        if not re.match(r'^#[0-9A-Fa-f]{6}$', value):
            raise serializers.ValidationError("Enter a valid hex color code (e.g., #FF0000)")
        return value
    
    def validate_accent_color(self, value):
        """Validate hex color format"""
        if not re.match(r'^#[0-9A-Fa-f]{6}$', value):
            raise serializers.ValidationError("Enter a valid hex color code (e.g., #FF0000)")
        return value
    
    def validate_phone(self, value):
        """Validate phone number format"""
        if value and not re.match(r'^\+?1?\d{9,15}$', value):
            raise serializers.ValidationError("Enter a valid phone number")
        return value
    
    def validate_registration_number(self, value):
        """Validate registration number uniqueness"""
        if value:
            instance = self.instance
            if AdvancedSchoolProfile.objects.filter(
                registration_number=value
            ).exclude(pk=instance.pk if instance else None).exists():
                raise serializers.ValidationError("Registration number already exists")
        return value
    
    def validate(self, data):
        """Cross-field validation"""
        # Validate academic year months
        start_month = data.get('academic_year_start_month')
        end_month = data.get('academic_year_end_month')
        
        if start_month and end_month and start_month == end_month:
            raise serializers.ValidationError({
                'academic_year_end_month': 'Academic year start and end months cannot be the same'
            })
        
        # Validate capacity
        max_students = data.get('max_students')
        current_students = data.get('current_students')
        
        if max_students and current_students and current_students > max_students:
            raise serializers.ValidationError({
                'current_students': 'Current students cannot exceed maximum capacity'
            })
        
        max_staff = data.get('max_staff')
        current_staff = data.get('current_staff')
        
        if max_staff and current_staff and current_staff > max_staff:
            raise serializers.ValidationError({
                'current_staff': 'Current staff cannot exceed maximum capacity'
            })
        
        return data
    
    def create(self, validated_data):
        """Create with current user as updated_by"""
        request = self.context.get('request')
        if request and request.user:
            validated_data['updated_by'] = request.user
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        """Update with current user as updated_by"""
        request = self.context.get('request')
        if request and request.user:
            validated_data['updated_by'] = request.user
        return super().update(instance, validated_data)


class SystemSettingsSerializer(serializers.ModelSerializer):
    """Enhanced serializer for system settings"""
    
    school_branch_name = serializers.CharField(source='school_branch.name', read_only=True)
    updated_by_name = serializers.CharField(source='updated_by.get_full_name', read_only=True)
    allowed_file_types_display = serializers.ReadOnlyField(source='get_allowed_file_types_display')
    
    class Meta:
        model = SystemSettings
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'updated_by')
    
    def validate_session_timeout(self, value):
        """Validate session timeout minimum"""
        if value < 300:  # 5 minutes minimum
            raise serializers.ValidationError("Session timeout cannot be less than 5 minutes (300 seconds)")
        return value
    
    def validate_max_login_attempts(self, value):
        """Validate maximum login attempts"""
        if value < 3:
            raise serializers.ValidationError("Maximum login attempts cannot be less than 3")
        return value
    
    def validate_max_file_size(self, value):
        """Validate file size limit"""
        if value > 100 * 1024 * 1024:  # 100MB max
            raise serializers.ValidationError("Maximum file size cannot exceed 100MB")
        return value
    
    def validate_allowed_file_types(self, value):
        """Validate file type extensions"""
        if value:
            for file_type in value:
                if not file_type.startswith('.'):
                    raise serializers.ValidationError(f"File type '{file_type}' must start with a dot")
                if len(file_type) < 2:
                    raise serializers.ValidationError(f"File type '{file_type}' is too short")
        return value
    
    def update(self, instance, validated_data):
        """Update with current user as updated_by"""
        request = self.context.get('request')
        if request and request.user:
            validated_data['updated_by'] = request.user
        return super().update(instance, validated_data)


class NotificationTemplateSerializer(serializers.ModelSerializer):
    """Serializer for notification templates"""
    
    school_branch_name = serializers.CharField(source='school_branch.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    updated_by_name = serializers.CharField(source='updated_by.get_full_name', read_only=True)
    
    class Meta:
        model = NotificationTemplate
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'created_by', 'updated_by')
    
    def validate_content(self, value):
        """Validate template content for variables"""
        # Check for balanced braces
        open_braces = value.count('{{')
        close_braces = value.count('}}')
        
        if open_braces != close_braces:
            raise serializers.ValidationError("Template variables must have balanced braces {{ }}")
        
        return value
    
    def validate_subject(self, value):
        """Validate email subject"""
        template_type = self.initial_data.get('template_type')
        if template_type == 'email' and not value:
            raise serializers.ValidationError("Subject is required for email templates")
        return value
    
    def validate_delay_minutes(self, value):
        """Validate delay minutes"""
        if value > 1440:  # 24 hours max
            raise serializers.ValidationError("Delay cannot exceed 24 hours (1440 minutes)")
        return value
    
    def create(self, validated_data):
        """Create with current user"""
        request = self.context.get('request')
        if request and request.user:
            validated_data['created_by'] = request.user
            validated_data['updated_by'] = request.user
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        """Update with current user"""
        request = self.context.get('request')
        if request and request.user:
            validated_data['updated_by'] = request.user
        return super().update(instance, validated_data)


class IntegrationSettingsSerializer(serializers.ModelSerializer):
    """Serializer for integration settings"""
    
    school_branch_name = serializers.CharField(source='school_branch.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    updated_by_name = serializers.CharField(source='updated_by.get_full_name', read_only=True)
    
    # Mask sensitive fields in responses
    api_key = serializers.CharField(write_only=True, required=False, allow_blank=True)
    api_secret = serializers.CharField(write_only=True, required=False, allow_blank=True)
    
    class Meta:
        model = IntegrationSettings
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'created_by', 'updated_by', 'last_sync')
    
    def validate_api_endpoint(self, value):
        """Validate API endpoint URL"""
        if value and not value.startswith(('http://', 'https://')):
            raise serializers.ValidationError("API endpoint must be a valid URL")
        return value
    
    def validate_webhook_url(self, value):
        """Validate webhook URL"""
        if value and not value.startswith(('http://', 'https://')):
            raise serializers.ValidationError("Webhook URL must be a valid URL")
        return value
    
    def to_representation(self, instance):
        """Mask sensitive data in response"""
        data = super().to_representation(instance)
        
        # Mask API key and secret
        if instance.api_key:
            data['api_key_masked'] = f"***{instance.api_key[-4:]}" if len(instance.api_key) > 4 else "***"
        if instance.api_secret:
            data['api_secret_masked'] = f"***{instance.api_secret[-4:]}" if len(instance.api_secret) > 4 else "***"
        
        return data
    
    def create(self, validated_data):
        """Create with current user"""
        request = self.context.get('request')
        if request and request.user:
            validated_data['created_by'] = request.user
            validated_data['updated_by'] = request.user
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        """Update with current user"""
        request = self.context.get('request')
        if request and request.user:
            validated_data['updated_by'] = request.user
        return super().update(instance, validated_data)


class CustomFieldSerializer(serializers.ModelSerializer):
    """Serializer for custom fields"""
    
    school_branch_name = serializers.CharField(source='school_branch.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    field_widget = serializers.ReadOnlyField(source='get_field_widget')
    
    class Meta:
        model = CustomField
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'created_by')
    
    def validate_choices(self, value):
        """Validate choices for choice fields"""
        field_type = self.initial_data.get('field_type')
        
        if field_type in ['choice', 'multiple_choice']:
            if not value or len(value) < 2:
                raise serializers.ValidationError("Choice fields must have at least 2 options")
            
            # Check for duplicate choices
            if len(value) != len(set(value)):
                raise serializers.ValidationError("Choices cannot have duplicate values")
        
        return value
    
    def validate_min_length(self, value):
        """Validate minimum length"""
        max_length = self.initial_data.get('max_length')
        if value and max_length and value > max_length:
            raise serializers.ValidationError("Minimum length cannot be greater than maximum length")
        return value
    
    def validate_min_value(self, value):
        """Validate minimum value"""
        max_value = self.initial_data.get('max_value')
        if value and max_value and value > max_value:
            raise serializers.ValidationError("Minimum value cannot be greater than maximum value")
        return value
    
    def create(self, validated_data):
        """Create with current user"""
        request = self.context.get('request')
        if request and request.user:
            validated_data['created_by'] = request.user
        return super().create(validated_data)


class AuditLogSerializer(serializers.ModelSerializer):
    """Serializer for audit logs"""
    
    school_branch_name = serializers.CharField(source='school_branch.name', read_only=True)
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    content_type_name = serializers.CharField(source='content_type.name', read_only=True)
    object_representation = serializers.ReadOnlyField(source='get_object_representation')
    
    class Meta:
        model = AuditLog
        fields = '__all__'
        read_only_fields = ('timestamp',)


# Backward compatibility serializers for existing models
class SchoolProfileSerializer(serializers.ModelSerializer):
    """Backward compatible serializer for existing school profile"""
    
    school_branch_name = serializers.CharField(source='school_branch.name', read_only=True)
    updated_by_name = serializers.CharField(source='updated_by.get_full_name', read_only=True)
    
    class Meta:
        model = SchoolProfile
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'updated_by')
    
    def update(self, instance, validated_data):
        """Update with current user as updated_by"""
        request = self.context.get('request')
        if request and request.user:
            validated_data['updated_by'] = request.user
        return super().update(instance, validated_data)


class SystemConfigurationSerializer(serializers.ModelSerializer):
    """Backward compatible serializer for existing system configuration"""
    
    school_branch_name = serializers.CharField(source='school_branch.name', read_only=True)
    updated_by_name = serializers.CharField(source='updated_by.get_full_name', read_only=True)
    
    class Meta:
        model = SystemConfiguration
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'updated_by')
    
    def update(self, instance, validated_data):
        """Update with current user as updated_by"""
        request = self.context.get('request')
        if request and request.user:
            validated_data['updated_by'] = request.user
        return super().update(instance, validated_data)
