from rest_framework import serializers
from academics.models import (
    ClassProgressionRule, StudentProgression, CareerPath,
    CareerPathSubjectRequirement, StudentCareerGuidance, SubjectCombination,
    ExamType, ExamSession, EnhancedExam, ExamRegistration,
    EnhancedExamResult, ExamStatistics, ExamMalpractice,
    TimetableTemplate, EnhancedTimeSlot, EnhancedTimetable,
    TimetableEntry, TeacherTimetable, TimetableConflict,
    TimetableSubstitution, LiveStreamingPlatform, LiveSession,
    SessionParticipant, SessionAttendance, SessionRecording,
    SessionChat, ResultPublication, PublicationAccess,
    LessonPlan, LessonDelivery, LessonResource,
    StudentLessonFeedback, LessonObservation, LessonSeries,
    LessonSeriesLesson
)


# Class Progression Serializers
class ClassProgressionRuleSerializer(serializers.ModelSerializer):
    current_level_name = serializers.CharField(source='current_level.name', read_only=True)
    next_level_name = serializers.Char<PERSON><PERSON>(source='next_level.name', read_only=True)
    school_name = serializers.CharField(source='school.name', read_only=True)
    
    class Meta:
        model = ClassProgressionRule
        fields = '__all__'


class StudentProgressionSerializer(serializers.ModelSerializer):
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)
    from_class_name = serializers.CharField(source='from_class.name', read_only=True)
    to_class_name = serializers.CharField(source='to_class.name', read_only=True)
    decided_by_name = serializers.CharField(source='decided_by.get_full_name', read_only=True)
    
    class Meta:
        model = StudentProgression
        fields = '__all__'


class CareerPathSubjectRequirementSerializer(serializers.ModelSerializer):
    subject_name = serializers.CharField(source='subject.name', read_only=True)
    
    class Meta:
        model = CareerPathSubjectRequirement
        fields = '__all__'


class CareerPathSerializer(serializers.ModelSerializer):
    subject_requirements = CareerPathSubjectRequirementSerializer(
        source='careerpathsubjectrequirement_set', 
        many=True, 
        read_only=True
    )
    
    class Meta:
        model = CareerPath
        fields = '__all__'


class StudentCareerGuidanceSerializer(serializers.ModelSerializer):
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)
    counselor_name = serializers.CharField(source='counselor.get_full_name', read_only=True)
    recommended_career_names = serializers.StringRelatedField(
        source='recommended_careers', 
        many=True, 
        read_only=True
    )
    
    class Meta:
        model = StudentCareerGuidance
        fields = '__all__'


class SubjectCombinationSerializer(serializers.ModelSerializer):
    subject_names = serializers.StringRelatedField(source='subjects', many=True, read_only=True)
    career_path_names = serializers.StringRelatedField(source='career_paths', many=True, read_only=True)
    education_level_names = serializers.StringRelatedField(source='education_levels', many=True, read_only=True)
    
    class Meta:
        model = SubjectCombination
        fields = '__all__'


# Enhanced Exam Serializers
class ExamTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ExamType
        fields = '__all__'


class ExamSessionSerializer(serializers.ModelSerializer):
    academic_year_name = serializers.CharField(source='academic_year.year', read_only=True)
    term_name = serializers.CharField(source='term.name', read_only=True)
    exam_type_name = serializers.CharField(source='exam_type.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = ExamSession
        fields = '__all__'


class EnhancedExamSerializer(serializers.ModelSerializer):
    exam_session_name = serializers.CharField(source='exam_session.name', read_only=True)
    subject_name = serializers.CharField(source='subject.name', read_only=True)
    class_name_display = serializers.CharField(source='class_name.name', read_only=True)
    invigilator_name = serializers.CharField(source='invigilator.get_full_name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = EnhancedExam
        fields = '__all__'


class ExamRegistrationSerializer(serializers.ModelSerializer):
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)
    exam_name = serializers.CharField(source='exam.subject.name', read_only=True)
    
    class Meta:
        model = ExamRegistration
        fields = '__all__'


class EnhancedExamResultSerializer(serializers.ModelSerializer):
    exam_name = serializers.CharField(source='exam.subject.name', read_only=True)
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)
    marked_by_name = serializers.CharField(source='marked_by.get_full_name', read_only=True)
    verified_by_name = serializers.CharField(source='verified_by.get_full_name', read_only=True)
    
    class Meta:
        model = EnhancedExamResult
        fields = '__all__'


class ExamStatisticsSerializer(serializers.ModelSerializer):
    exam_name = serializers.CharField(source='exam.subject.name', read_only=True)
    
    class Meta:
        model = ExamStatistics
        fields = '__all__'


class ExamMalpracticeSerializer(serializers.ModelSerializer):
    exam_name = serializers.CharField(source='exam.subject.name', read_only=True)
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)
    reported_by_name = serializers.CharField(source='reported_by.get_full_name', read_only=True)
    
    class Meta:
        model = ExamMalpractice
        fields = '__all__'


# Enhanced Timetable Serializers
class TimetableTemplateSerializer(serializers.ModelSerializer):
    school_name = serializers.CharField(source='school.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = TimetableTemplate
        fields = '__all__'


class EnhancedTimeSlotSerializer(serializers.ModelSerializer):
    template_name = serializers.CharField(source='template.name', read_only=True)
    
    class Meta:
        model = EnhancedTimeSlot
        fields = '__all__'


class EnhancedTimetableSerializer(serializers.ModelSerializer):
    class_name_display = serializers.CharField(source='class_name.name', read_only=True)
    academic_year_display = serializers.CharField(source='academic_year.year', read_only=True)
    term_name = serializers.CharField(source='term.name', read_only=True)
    template_name = serializers.CharField(source='template.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.get_full_name', read_only=True)
    
    class Meta:
        model = EnhancedTimetable
        fields = '__all__'


class TimetableEntrySerializer(serializers.ModelSerializer):
    subject_name = serializers.CharField(source='subject.name', read_only=True)
    teacher_name = serializers.CharField(source='teacher.get_full_name', read_only=True)
    time_slot_display = serializers.CharField(source='time_slot.__str__', read_only=True)
    original_teacher_name = serializers.CharField(source='original_teacher.get_full_name', read_only=True)
    
    class Meta:
        model = TimetableEntry
        fields = '__all__'


class TeacherTimetableSerializer(serializers.ModelSerializer):
    teacher_name = serializers.CharField(source='teacher.get_full_name', read_only=True)
    academic_year_display = serializers.CharField(source='academic_year.year', read_only=True)
    term_name = serializers.CharField(source='term.name', read_only=True)
    
    class Meta:
        model = TeacherTimetable
        fields = '__all__'


class TimetableConflictSerializer(serializers.ModelSerializer):
    timetable_display = serializers.CharField(source='timetable.__str__', read_only=True)
    entry1_display = serializers.CharField(source='entry1.__str__', read_only=True)
    entry2_display = serializers.CharField(source='entry2.__str__', read_only=True)
    resolved_by_name = serializers.CharField(source='resolved_by.get_full_name', read_only=True)
    
    class Meta:
        model = TimetableConflict
        fields = '__all__'


class TimetableSubstitutionSerializer(serializers.ModelSerializer):
    original_entry_display = serializers.CharField(source='original_entry.__str__', read_only=True)
    substitute_teacher_name = serializers.CharField(source='substitute_teacher.get_full_name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.get_full_name', read_only=True)
    
    class Meta:
        model = TimetableSubstitution
        fields = '__all__'


# Live Streaming Serializers
class LiveStreamingPlatformSerializer(serializers.ModelSerializer):
    class Meta:
        model = LiveStreamingPlatform
        fields = '__all__'


class LiveSessionSerializer(serializers.ModelSerializer):
    class_name_display = serializers.CharField(source='class_name.name', read_only=True)
    subject_name = serializers.CharField(source='subject.name', read_only=True)
    teacher_name = serializers.CharField(source='teacher.get_full_name', read_only=True)
    term_name = serializers.CharField(source='term.name', read_only=True)
    platform_name = serializers.CharField(source='platform.name', read_only=True)
    
    class Meta:
        model = LiveSession
        fields = '__all__'


class SessionParticipantSerializer(serializers.ModelSerializer):
    session_title = serializers.CharField(source='session.title', read_only=True)
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    
    class Meta:
        model = SessionParticipant
        fields = '__all__'


class SessionAttendanceSerializer(serializers.ModelSerializer):
    session_title = serializers.CharField(source='session.title', read_only=True)
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)
    
    class Meta:
        model = SessionAttendance
        fields = '__all__'


class SessionRecordingSerializer(serializers.ModelSerializer):
    session_title = serializers.CharField(source='session.title', read_only=True)
    
    class Meta:
        model = SessionRecording
        fields = '__all__'


class SessionChatSerializer(serializers.ModelSerializer):
    session_title = serializers.CharField(source='session.title', read_only=True)
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    
    class Meta:
        model = SessionChat
        fields = '__all__'


class ResultPublicationSerializer(serializers.ModelSerializer):
    academic_year_display = serializers.CharField(source='academic_year.year', read_only=True)
    term_name = serializers.CharField(source='term.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    reviewed_by_name = serializers.CharField(source='reviewed_by.get_full_name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.get_full_name', read_only=True)
    
    class Meta:
        model = ResultPublication
        fields = '__all__'


class PublicationAccessSerializer(serializers.ModelSerializer):
    publication_title = serializers.CharField(source='publication.title', read_only=True)
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)

    class Meta:
        model = PublicationAccess
        fields = '__all__'


# Lesson Serializers
class LessonPlanSerializer(serializers.ModelSerializer):
    subject_name = serializers.CharField(source='subject.name', read_only=True)
    class_name_display = serializers.CharField(source='class_name.name', read_only=True)
    teacher_name = serializers.CharField(source='teacher.get_full_name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.get_full_name', read_only=True)

    class Meta:
        model = LessonPlan
        fields = '__all__'


class LessonDeliverySerializer(serializers.ModelSerializer):
    lesson_plan_title = serializers.CharField(source='lesson_plan.title', read_only=True)

    class Meta:
        model = LessonDelivery
        fields = '__all__'


class LessonResourceSerializer(serializers.ModelSerializer):
    lesson_plan_title = serializers.CharField(source='lesson_plan.title', read_only=True)
    uploaded_by_name = serializers.CharField(source='uploaded_by.get_full_name', read_only=True)

    class Meta:
        model = LessonResource
        fields = '__all__'


class StudentLessonFeedbackSerializer(serializers.ModelSerializer):
    lesson_delivery_display = serializers.CharField(source='lesson_delivery.__str__', read_only=True)
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)

    class Meta:
        model = StudentLessonFeedback
        fields = '__all__'


class LessonObservationSerializer(serializers.ModelSerializer):
    lesson_delivery_display = serializers.CharField(source='lesson_delivery.__str__', read_only=True)
    observer_name = serializers.CharField(source='observer.get_full_name', read_only=True)

    class Meta:
        model = LessonObservation
        fields = '__all__'


class LessonSeriesSerializer(serializers.ModelSerializer):
    subject_name = serializers.CharField(source='subject.name', read_only=True)
    class_name_display = serializers.CharField(source='class_name.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)

    class Meta:
        model = LessonSeries
        fields = '__all__'


class LessonSeriesLessonSerializer(serializers.ModelSerializer):
    series_title = serializers.CharField(source='series.title', read_only=True)
    lesson_plan_title = serializers.CharField(source='lesson_plan.title', read_only=True)

    class Meta:
        model = LessonSeriesLesson
        fields = '__all__'
