from rest_framework import serializers
from .models import (
    AssetCategory, Asset, SupplyCategory, Supply, SupplyTransaction,
    Maintenance, Supplier, PurchaseOrder, PurchaseOrderItem, InventoryReport
)
from core.Serializers import UserSerializer

class AssetCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = AssetCategory
        fields = '__all__'
        extra_kwargs = {
            'school_branch': {'required': True}
        }

class AssetSerializer(serializers.ModelSerializer):
    category_details = AssetCategorySerializer(source='category', read_only=True)
    added_by_details = UserSerializer(source='added_by', read_only=True)
    assigned_to_details = UserSerializer(source='assigned_to', read_only=True)
    
    class Meta:
        model = Asset
        fields = '__all__'
        extra_kwargs = {
            'added_by': {'write_only': True},
            'category': {'write_only': True},
            'assigned_to': {'write_only': True},
            'school_branch': {'required': True}
        }

class SupplyCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = SupplyCategory
        fields = '__all__'
        extra_kwargs = {
            'school_branch': {'required': True}
        }

class SupplySerializer(serializers.ModelSerializer):
    category_details = SupplyCategorySerializer(source='category', read_only=True)
    added_by_details = UserSerializer(source='added_by', read_only=True)
    total_value = serializers.DecimalField(max_digits=12, decimal_places=2, read_only=True)
    needs_reordering = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Supply
        fields = '__all__'
        extra_kwargs = {
            'added_by': {'write_only': True},
            'category': {'write_only': True},
            'school_branch': {'required': True}
        }
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['total_value'] = instance.total_value
        representation['needs_reordering'] = instance.needs_reordering
        return representation

class SupplierSerializer(serializers.ModelSerializer):
    class Meta:
        model = Supplier
        fields = '__all__'
        extra_kwargs = {
            'school_branch': {'required': True}
        }

class SupplyTransactionSerializer(serializers.ModelSerializer):
    supply_details = SupplySerializer(source='supply', read_only=True)
    performed_by_details = UserSerializer(source='performed_by', read_only=True)
    
    class Meta:
        model = SupplyTransaction
        fields = '__all__'
        extra_kwargs = {
            'supply': {'write_only': True},
            'performed_by': {'write_only': True},
            'school_branch': {'required': True}
        }

class MaintenanceSerializer(serializers.ModelSerializer):
    asset_details = AssetSerializer(source='asset', read_only=True)
    created_by_details = UserSerializer(source='created_by', read_only=True)
    vendor_details = SupplierSerializer(source='vendor', read_only=True)
    
    class Meta:
        model = Maintenance
        fields = '__all__'
        extra_kwargs = {
            'asset': {'write_only': True},
            'created_by': {'write_only': True},
            'vendor': {'write_only': True},
            'school_branch': {'required': True}
        }

class PurchaseOrderItemSerializer(serializers.ModelSerializer):
    total_price = serializers.DecimalField(max_digits=12, decimal_places=2, read_only=True)
    is_fully_received = serializers.BooleanField(read_only=True)
    supply_details = SupplySerializer(source='supply', read_only=True)
    asset_category_details = AssetCategorySerializer(source='asset_category', read_only=True)
    
    class Meta:
        model = PurchaseOrderItem
        fields = '__all__'
        extra_kwargs = {
            'purchase_order': {'required': True},
            'supply': {'required': False},
            'asset_category': {'required': False}
        }
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['total_price'] = instance.total_price
        representation['is_fully_received'] = instance.is_fully_received
        return representation

class PurchaseOrderSerializer(serializers.ModelSerializer):
    items = PurchaseOrderItemSerializer(many=True, read_only=True)
    supplier_details = SupplierSerializer(source='supplier', read_only=True)
    created_by_details = UserSerializer(source='created_by', read_only=True)
    approved_by_details = UserSerializer(source='approved_by', read_only=True)
    grand_total = serializers.DecimalField(max_digits=12, decimal_places=2, read_only=True)
    
    class Meta:
        model = PurchaseOrder
        fields = '__all__'
        extra_kwargs = {
            'supplier': {'write_only': True},
            'created_by': {'write_only': True},
            'approved_by': {'write_only': True},
            'school_branch': {'required': True}
        }
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['grand_total'] = instance.grand_total
        return representation
    
    def create(self, validated_data):
        items_data = self.context.get('items', [])
        purchase_order = PurchaseOrder.objects.create(**validated_data)
        
        for item_data in items_data:
            PurchaseOrderItem.objects.create(purchase_order=purchase_order, **item_data)
        
        return purchase_order

class InventoryReportSerializer(serializers.ModelSerializer):
    generated_by_details = UserSerializer(source='generated_by', read_only=True)
    
    class Meta:
        model = InventoryReport
        fields = '__all__'
        extra_kwargs = {
            'generated_by': {'write_only': True},
            'school_branch': {'required': True}
        }
