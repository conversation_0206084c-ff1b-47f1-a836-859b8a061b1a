# Generated by Django 5.2.1 on 2025-06-01 13:25

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('schools', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TransportAttendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('picked_up', models.BooleanField(default=False)),
                ('dropped_off', models.BooleanField(default=False)),
                ('pickup_time', models.TimeField(blank=True, null=True)),
                ('dropoff_time', models.TimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-date', 'student_transport__student'],
            },
        ),
        migrations.CreateModel(
            name='TransportFee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='TransportFeeDiscount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('discount_type', models.CharField(choices=[('FIXED', 'Fixed Amount'), ('PERCENTAGE', 'Percentage')], max_length=10)),
                ('amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Vehicle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('vehicle_type', models.CharField(choices=[('BUS', 'Bus'), ('VAN', 'Van'), ('CAR', 'Car'), ('TRUCK', 'Truck'), ('OTHER', 'Other')], max_length=10)),
                ('registration_number', models.CharField(max_length=20, unique=True)),
                ('model', models.CharField(max_length=100)),
                ('manufacturer', models.CharField(max_length=100)),
                ('year_of_manufacture', models.PositiveIntegerField()),
                ('seating_capacity', models.PositiveIntegerField()),
                ('fuel_type', models.CharField(choices=[('PETROL', 'Petrol'), ('DIESEL', 'Diesel'), ('ELECTRIC', 'Electric'), ('HYBRID', 'Hybrid'), ('OTHER', 'Other')], max_length=10)),
                ('purchase_date', models.DateField()),
                ('purchase_price', models.DecimalField(decimal_places=2, max_digits=12)),
                ('insurance_expiry', models.DateField()),
                ('license_expiry', models.DateField()),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('MAINTENANCE', 'Under Maintenance'), ('INACTIVE', 'Inactive'), ('RETIRED', 'Retired')], default='ACTIVE', max_length=15)),
                ('current_mileage', models.PositiveIntegerField(default=0)),
                ('image', models.ImageField(blank=True, null=True, upload_to='vehicle_images/')),
                ('documents', models.FileField(blank=True, null=True, upload_to='vehicle_documents/')),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='VehicleMaintenance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('maintenance_type', models.CharField(choices=[('ROUTINE', 'Routine Service'), ('REPAIR', 'Repair'), ('INSPECTION', 'Inspection'), ('EMERGENCY', 'Emergency'), ('OTHER', 'Other')], max_length=15)),
                ('description', models.TextField()),
                ('service_provider', models.CharField(blank=True, max_length=100, null=True)),
                ('scheduled_date', models.DateField()),
                ('completion_date', models.DateField(blank=True, null=True)),
                ('cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('mileage_at_service', models.PositiveIntegerField(blank=True, null=True)),
                ('status', models.CharField(choices=[('SCHEDULED', 'Scheduled'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled')], default='SCHEDULED', max_length=15)),
                ('notes', models.TextField(blank=True, null=True)),
                ('documents', models.FileField(blank=True, null=True, upload_to='maintenance_documents/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-scheduled_date'],
            },
        ),
        migrations.CreateModel(
            name='Driver',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('license_number', models.CharField(max_length=50, unique=True)),
                ('license_class', models.CharField(max_length=20)),
                ('license_expiry', models.DateField()),
                ('experience_years', models.PositiveIntegerField()),
                ('date_of_birth', models.DateField()),
                ('address', models.TextField()),
                ('emergency_contact_name', models.CharField(max_length=100)),
                ('emergency_contact_phone', models.CharField(max_length=20)),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('ON_LEAVE', 'On Leave'), ('INACTIVE', 'Inactive'), ('TERMINATED', 'Terminated')], default='ACTIVE', max_length=15)),
                ('documents', models.FileField(blank=True, null=True, upload_to='driver_documents/')),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='drivers', to='schools.schoolbranch')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='driver_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['user__first_name', 'user__last_name'],
            },
        ),
        migrations.CreateModel(
            name='FuelConsumption',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('fuel_amount', models.DecimalField(decimal_places=2, help_text='Amount in liters', max_digits=8)),
                ('cost', models.DecimalField(decimal_places=2, max_digits=10)),
                ('odometer_reading', models.PositiveIntegerField()),
                ('fuel_station', models.CharField(blank=True, max_length=100, null=True)),
                ('receipt', models.FileField(blank=True, null=True, upload_to='fuel_receipts/')),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('filled_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='fuel_records', to=settings.AUTH_USER_MODEL)),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fuel_records', to='schools.schoolbranch')),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='Route',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('start_location', models.CharField(max_length=255)),
                ('end_location', models.CharField(max_length=255)),
                ('distance', models.DecimalField(decimal_places=2, help_text='Distance in kilometers', max_digits=8)),
                ('estimated_time', models.PositiveIntegerField(help_text='Estimated time in minutes')),
                ('stops', models.JSONField(blank=True, help_text='JSON array of stop locations', null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='routes', to='schools.schoolbranch')),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Schedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('schedule_type', models.CharField(choices=[('REGULAR', 'Regular'), ('SPECIAL', 'Special Event'), ('FIELD_TRIP', 'Field Trip')], default='REGULAR', max_length=15)),
                ('days_of_week', models.JSONField(blank=True, help_text='JSON array of days (for regular schedules)', null=True)),
                ('departure_time', models.TimeField()),
                ('arrival_time', models.TimeField()),
                ('start_date', models.DateField(default=django.utils.timezone.now)),
                ('end_date', models.DateField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('driver', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='fleet.driver')),
                ('route', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='fleet.route')),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='schools.schoolbranch')),
            ],
            options={
                'ordering': ['departure_time'],
            },
        ),
        migrations.CreateModel(
            name='StudentTransport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pickup_point', models.CharField(max_length=255)),
                ('dropoff_point', models.CharField(max_length=255)),
                ('is_active', models.BooleanField(default=True)),
                ('start_date', models.DateField(default=django.utils.timezone.now)),
                ('end_date', models.DateField(blank=True, null=True)),
                ('usage_type', models.CharField(choices=[('FULL', 'Full (Both Ways, All Days)'), ('MORNING_ONLY', 'Morning Only (Pickup Only)'), ('AFTERNOON_ONLY', 'Afternoon Only (Dropoff Only)'), ('PARTIAL_DAYS', 'Partial Days (Selected Days Only)'), ('CUSTOM', 'Custom Schedule')], default='FULL', max_length=20)),
                ('days_per_week', models.PositiveSmallIntegerField(default=5, help_text='Number of days per week the student uses transport')),
                ('selected_days', models.JSONField(blank=True, help_text="JSON array of weekdays (e.g., ['MON', 'WED', 'FRI'])", null=True)),
                ('fee_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('payment_frequency', models.CharField(choices=[('TERMLY', 'Once Per Term'), ('MONTHLY', 'Monthly'), ('WEEKLY', 'Weekly'), ('DAILY', 'Daily (Pay As You Go)')], default='MONTHLY', max_length=20)),
                ('apply_transport_fee', models.BooleanField(default=True, help_text="Whether to include transport fee in student's fee structure")),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('route', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='students', to='fleet.route')),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='student_transport', to='schools.schoolbranch')),
            ],
            options={
                'ordering': ['student'],
            },
        ),
    ]
