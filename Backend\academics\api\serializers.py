from rest_framework import serializers
from ..models import (
    Student, Subject, Assessment, AssessmentResult,
    LearningPath, SmartIntervention, PerformanceAnalytics, TeacherPerformanceMetrics,
    ClassProgressionRule, StudentProgression, CareerPath, CareerPathSubjectRequirement,
    StudentCareerGuidance, SubjectCombination, ExamType, ExamSession, EnhancedExam,
    ExamRegistration, EnhancedExamResult, ExamMalpractice, ExamStatistics
)

class PerformanceAnalyticsSerializer(serializers.ModelSerializer):
    subject_name = serializers.CharField(source='subject.name', read_only=True)
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)
    term_name = serializers.CharField(source='term.name', read_only=True)
    
    class Meta:
        model = PerformanceAnalytics
        fields = [
            'id', 'subject_name', 'student_name', 'term_name',
            'initial_assessment_score', 'mid_term_score', 'final_score',
            'value_addition', 'improvement_percentage', 'strengths',
            'areas_for_improvement', 'teacher_remarks', 'intervention_needed'
        ]
        read_only_fields = [
            'value_addition', 'improvement_percentage', 'intervention_needed'
        ]

    def validate(self, data):
        if data.get('final_score', 0) < data.get('initial_assessment_score', 0):
            raise serializers.ValidationError(
                "Final score cannot be less than initial assessment score"
            )
        return data

class LearningPathSerializer(serializers.ModelSerializer):
    milestones = serializers.JSONField()
    progress_percentage = serializers.SerializerMethodField()
    
    class Meta:
        model = LearningPath
        fields = [
            'subject', 'current_level', 'target_level',
            'milestones', 'progress_percentage', 'completion_date'
        ]
    
    def get_progress_percentage(self, obj):
        completed = sum(1 for m in obj.milestones if m.get('completed', False))
        total = len(obj.milestones)
        return (completed / total * 100) if total > 0 else 0

class InterventionAnalyticsSerializer(serializers.ModelSerializer):
    effectiveness_score = serializers.SerializerMethodField()
    
    class Meta:
        model = SmartIntervention
        fields = [
            'trigger_type', 'risk_level', 'automated_suggestions',
            'effectiveness_score', 'is_active'
        ]
    
    def get_effectiveness_score(self, obj):
        # Calculate intervention effectiveness
        return obj.calculate_effectiveness()

class TeacherPerformanceMetricsSerializer(serializers.ModelSerializer):
    teacher_name = serializers.CharField(source='teacher.get_full_name', read_only=True)
    subject_name = serializers.CharField(source='subject.name', read_only=True)

    class Meta:
        model = TeacherPerformanceMetrics
        fields = '__all__'
        read_only_fields = ('class_average', 'pass_rate', 'completion_rate',
                          'avg_value_addition', 'number_of_distinctions', 'number_of_failures')

class ClassProgressionRuleSerializer(serializers.ModelSerializer):
    current_level_name = serializers.CharField(source='current_level.name', read_only=True)
    next_level_name = serializers.CharField(source='next_level.name', read_only=True)
    school_name = serializers.CharField(source='school.name', read_only=True)

    class Meta:
        model = ClassProgressionRule
        fields = [
            'id', 'current_level', 'current_level_name',
            'next_level', 'next_level_name', 'school', 'school_name',
            'is_default_progression', 'requirements', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class StudentProgressionSerializer(serializers.ModelSerializer):
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)
    current_class_name = serializers.CharField(source='current_class.name', read_only=True)
    next_class_name = serializers.CharField(source='next_class.name', read_only=True)
    term_name = serializers.CharField(source='term.name', read_only=True)
    decision_by_name = serializers.CharField(source='decision_by.get_full_name', read_only=True)

    class Meta:
        model = StudentProgression
        fields = [
            'id', 'student', 'student_name', 'current_class', 'current_class_name',
            'next_class', 'next_class_name', 'progression_rule', 'term', 'term_name',
            'status', 'average_score', 'total_points', 'position_in_class',
            'decision_date', 'decision_by', 'decision_by_name', 'decision_notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class CareerPathSubjectRequirementSerializer(serializers.ModelSerializer):
    subject_name = serializers.CharField(source='subject.name', read_only=True)

    class Meta:
        model = CareerPathSubjectRequirement
        fields = ['id', 'career_path', 'subject', 'subject_name', 'minimum_grade', 'is_mandatory']

class CareerPathSerializer(serializers.ModelSerializer):
    subject_requirements = CareerPathSubjectRequirementSerializer(
        source='careerpathsubjectrequirement_set',
        many=True,
        read_only=True
    )

    class Meta:
        model = CareerPath
        fields = [
            'id', 'name', 'description', 'category', 'required_subjects',
            'minimum_grade', 'job_prospects', 'salary_range', 'growth_outlook',
            'further_education_options', 'recommended_universities',
            'is_active', 'subject_requirements', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class StudentCareerGuidanceSerializer(serializers.ModelSerializer):
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)
    counselor_name = serializers.CharField(source='counselor.get_full_name', read_only=True)
    recommended_careers = CareerPathSerializer(many=True, read_only=True)

    class Meta:
        model = StudentCareerGuidance
        fields = [
            'id', 'student', 'student_name', 'recommended_careers',
            'strengths', 'areas_for_improvement', 'counselor', 'counselor_name',
            'session_date', 'session_notes', 'next_session_date',
            'action_items', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class SubjectCombinationSerializer(serializers.ModelSerializer):
    subjects = serializers.StringRelatedField(many=True)
    career_paths = CareerPathSerializer(many=True, read_only=True)
    education_levels = serializers.StringRelatedField(many=True)

    class Meta:
        model = SubjectCombination
        fields = [
            'id', 'name', 'description', 'subjects', 'career_paths',
            'education_levels', 'is_active', 'created_at'
        ]
        read_only_fields = ['created_at']

class ExamTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ExamType
        fields = [
            'id', 'name', 'code', 'description', 'weight_percentage', 'is_active'
        ]

class ExamSessionSerializer(serializers.ModelSerializer):
    exam_type_name = serializers.CharField(source='exam_type.name', read_only=True)
    academic_year_name = serializers.CharField(source='academic_year.name', read_only=True)
    term_name = serializers.CharField(source='term.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)

    class Meta:
        model = ExamSession
        fields = [
            'id', 'name', 'academic_year', 'academic_year_name',
            'term', 'term_name', 'exam_type', 'exam_type_name',
            'start_date', 'end_date', 'registration_deadline',
            'instructions', 'rules', 'is_active', 'is_published',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class EnhancedExamSerializer(serializers.ModelSerializer):
    subject_name = serializers.CharField(source='subject.name', read_only=True)
    class_name = serializers.CharField(source='class_name.name', read_only=True)
    invigilator_name = serializers.CharField(source='invigilator.get_full_name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)

    class Meta:
        model = EnhancedExam
        fields = [
            'id', 'exam_session', 'subject', 'subject_name',
            'class_name', 'class_name', 'exam_date', 'start_time',
            'end_time', 'duration', 'venue', 'invigilator',
            'invigilator_name', 'total_marks', 'pass_mark',
            'question_paper_file', 'marking_scheme_file',
            'is_published', 'results_published',
            'special_instructions', 'materials_allowed',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class ExamRegistrationSerializer(serializers.ModelSerializer):
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)
    exam_details = EnhancedExamSerializer(source='exam', read_only=True)

    class Meta:
        model = ExamRegistration
        fields = [
            'id', 'student', 'student_name', 'exam', 'exam_details',
            'registration_date', 'special_needs', 'extra_time_granted',
            'is_registered', 'is_present'
        ]
        read_only_fields = ['registration_date']

class EnhancedExamResultSerializer(serializers.ModelSerializer):
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)
    exam_details = EnhancedExamSerializer(source='exam', read_only=True)
    marked_by_name = serializers.CharField(source='marked_by.get_full_name', read_only=True)
    verified_by_name = serializers.CharField(source='verified_by.get_full_name', read_only=True)

    class Meta:
        model = EnhancedExamResult
        fields = [
            'id', 'exam', 'exam_details', 'student', 'student_name',
            'raw_score', 'percentage', 'grade', 'points',
            'position_in_class', 'position_in_stream', 'position_in_subject',
            'strengths', 'weaknesses', 'recommendations',
            'marked_by', 'marked_by_name', 'marking_date',
            'verified_by', 'verified_by_name', 'verification_date',
            'is_verified', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class ExamMalpracticeSerializer(serializers.ModelSerializer):
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)
    exam_details = EnhancedExamSerializer(source='exam', read_only=True)
    reported_by_name = serializers.CharField(source='reported_by.get_full_name', read_only=True)

    class Meta:
        model = ExamMalpractice
        fields = [
            'id', 'exam', 'exam_details', 'student', 'student_name',
            'malpractice_type', 'description', 'evidence',
            'reported_by', 'reported_by_name', 'report_date',
            'action_taken', 'penalty_applied',
            'is_resolved', 'resolution_date'
        ]
        read_only_fields = ['report_date']

class ExamStatisticsSerializer(serializers.ModelSerializer):
    exam_details = EnhancedExamSerializer(source='exam', read_only=True)

    class Meta:
        model = ExamStatistics
        fields = [
            'id', 'exam', 'exam_details', 'total_students',
            'students_present', 'students_absent',
            'highest_score', 'lowest_score', 'average_score',
            'median_score', 'pass_rate', 'distinction_rate',
            'failure_rate', 'grade_distribution', 'calculated_at'
        ]
        read_only_fields = ['calculated_at']
