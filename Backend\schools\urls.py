from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.response import Response
from .views import SchoolViewSet, SchoolBranchViewSet
from .models import School, SchoolBranch
from .serializers import SchoolSerializer, SchoolBranchSerializer

router = DefaultRouter()
router.register('schools', SchoolViewSet)
router.register('branch', SchoolBranchViewSet)

# Custom view for current-school endpoint
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny

@api_view(['GET'])
@permission_classes([AllowAny])  # For development, change to appropriate permissions in production
def current_school(request):
    """Get the current school for the logged-in user"""
    try:
        # Check if user has an assigned school branch
        if hasattr(request.user, 'school_branch') and request.user.school_branch and request.user.school_branch.school:
            school = request.user.school_branch.school
            serializer = SchoolSerializer(school)
            # Add the branch information
            data = serializer.data
            data['current_branch'] = SchoolBranchSerializer(request.user.school_branch).data
            return Response(data)
        else:
            # Return a specific error code for unassigned users
            return Response(
                {
                    "detail": "User is not assigned to any school or branch.",
                    "error_code": "NO_SCHOOL_ASSIGNED",
                    "requires_selection": True
                },
                status=400  # Using 400 Bad Request instead of 404 Not Found
            )
    except Exception as e:
        return Response(
            {
                "detail": f"Error retrieving current school: {str(e)}",
                "error_code": "SERVER_ERROR"
            },
            status=500
        )

# API endpoint to assign a school and branch to a user
@api_view(['POST'])
@permission_classes([AllowAny])  # For development, change to appropriate permissions in production
def assign_school_branch(request):
    """Assign a school and branch to the current user"""
    try:
        # Get the school and branch IDs from the request data
        school_id = request.data.get('school_id')
        branch_id = request.data.get('branch_id')

        if not school_id:
            return Response(
                {"detail": "School ID is required."},
                status=400
            )

        # Validate the school exists
        try:
            school = School.objects.get(id=school_id)
        except School.DoesNotExist:
            return Response(
                {"detail": "School not found."},
                status=404
            )

        # Validate the branch exists and belongs to the school
        if branch_id:
            try:
                branch = SchoolBranch.objects.get(id=branch_id)
                if branch.school.id != school.id:
                    return Response(
                        {"detail": "Branch does not belong to the selected school."},
                        status=400
                    )
            except SchoolBranch.DoesNotExist:
                return Response(
                    {"detail": "Branch not found."},
                    status=404
                )
        else:
            # If no branch is specified, use the first branch of the school
            branch = school.branch.first()
            if not branch:
                return Response(
                    {"detail": "School has no branches. Please create a branch first."},
                    status=400
                )

        # Assign the school and branch to the user
        user = request.user
        if not user.is_authenticated:
            return Response(
                {"detail": "Authentication required."},
                status=401
            )

        # Update the user's school and branch
        user.school = school
        user.school_branch = branch
        user.save()

        # Return the updated school and branch information
        school_data = SchoolSerializer(school).data
        school_data['current_branch'] = SchoolBranchSerializer(branch).data

        return Response({
            "detail": "School and branch assigned successfully.",
            "school": school_data
        })
    except Exception as e:
        return Response(
            {"detail": f"Error assigning school and branch: {str(e)}"},
            status=500
        )

urlpatterns = [
    path('', include(router.urls)),
    path('current-school/', current_school, name='current-school'),
    path('assign-school-branch/', assign_school_branch, name='assign-school-branch'),
]
