# Generated by Django 5.2.1 on 2025-06-01 13:25

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('academics', '0001_initial'),
        ('analytics', '0001_initial'),
        ('schools', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='branchanalytics',
            name='school_branch',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='enrollmentmetrics',
            name='academic_year',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.academicyear'),
        ),
        migrations.AddField(
            model_name='enrollmentmetrics',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='performancemetrics',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='performancemetrics',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.term'),
        ),
    ]
