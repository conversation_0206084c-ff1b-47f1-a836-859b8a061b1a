"""
School Admin Billing Views
Allows school administrators to view their billing information and make payments
"""

from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db.models import Sum, Q
from decimal import Decimal
import logging

from .school_billing_models import (
    SchoolBillingAccount, SchoolInvoice, SchoolPayment
)
from .school_billing_serializers import (
    SchoolBillingAccountSerializer, SchoolInvoiceSerializer, 
    SchoolPaymentSerializer
)
from schools.models import School
from settings_app.license_models import LicenseSubscription
from .exceptions import BillingError
from users.models import CustomUser

logger = logging.getLogger(__name__)


class SchoolAdminPermission(permissions.BasePermission):
    """
    Custom permission for school administrators
    """
    # from typing import Literal  # Removed as not needed

    def has_permission(self, request, view) -> bool:
        """
        Return True if permission is granted, False otherwise.
        """
        if not request.user or not request.user.is_authenticated:
            return False

        # Allow if user is superuser
        if request.user.is_superuser:
            return True

        # Check if user is a school admin
        try:
            # Assuming school admins have a relationship to schools
            # This might need adjustment based on your user model structure
            user_schools = getattr(request.user, 'schools', None)
            if user_schools and user_schools.exists():
                return True

            # Alternative: check if user has school admin role
            if hasattr(request.user, 'role') and request.user.role in ['school_admin', 'principal']:
                return True

        except Exception as e:
            logger.error(f"Error checking school admin permission: {e}")

        return False

    def has_object_permission(self, request, view, obj):
        """
        Return True if permission is granted, False otherwise.
        """
        if request.user.is_superuser:
            return True

        try:
            if hasattr(obj, 'school'):
                user_schools = getattr(request.user, 'schools', None)
                if user_schools:
                    return obj.school in user_schools.all()
        except Exception as e:
            logger.error(f"Error checking object permission: {e}")

        return False


class SchoolAdminBillingViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for school administrators to view their billing information
    """
    permission_classes = [SchoolAdminPermission]
    
    def get_user_school(self):
        """Get the school associated with the current user"""
        try:
            if self.request.user.is_superuser:
                # For superuser, get school from query params
                school_id = self.request.query_params.get('school_id')
                if school_id:
                    return get_object_or_404(School, id=school_id)
                return None
            
            # For school admin, get their associated school
            user_schools = getattr(self.request.user, 'schools', None)
            if user_schools and user_schools.exists():
                return user_schools.first()
            
            # Alternative: if user has a direct school relationship
            if self.request.user.is_authenticated:
                user_school = getattr(self.request.user, 'school', None)
                if user_school:
                    return user_school
                
        except Exception as e:
            logger.error(f"Error getting user school: {e}")
            
        return None

    @action(detail=False, methods=['get'])
    def billing_overview(self, request):
        """
        Get comprehensive billing overview for the school
        """
        try:
            school = self.get_user_school()
            if not school:
                return Response({
                    'error': 'No school associated with your account'
                }, status=status.HTTP_404_NOT_FOUND)

            # Get billing account
            try:
                billing_account = SchoolBillingAccount.objects.get(school=school)
            except SchoolBillingAccount.DoesNotExist:
                return Response({
                    'error': 'No billing account found for your school'
                }, status=status.HTTP_404_NOT_FOUND)

            # Get license information
            license_info = None
            try:
                license_sub = school.license
                license_info = {
                    'package_type': license_sub.package_type,
                    'subscription_status': license_sub.subscription_status,
                    'start_date': license_sub.start_date,
                    'expiry_date': license_sub.expiry_date,
                    'max_students': license_sub.max_students,
                    'max_staff': license_sub.max_staff,
                    'max_branches': license_sub.max_branches,
                    'days_until_expiry': (license_sub.expiry_date - timezone.now().date()).days if license_sub.expiry_date else None,
                    'is_expired': license_sub.expiry_date < timezone.now().date() if license_sub.expiry_date else False,
                    'grace_period_active': billing_account.is_overdue() and license_sub.subscription_status == 'ACTIVE'
                }
            except LicenseSubscription.DoesNotExist:
                pass

            # Get current outstanding invoices
            outstanding_invoices = SchoolInvoice.objects.filter(
                school=school,
                status__in=['SENT', 'OVERDUE']
            ).order_by('-invoice_date')

            # Get recent payments
            recent_payments = SchoolPayment.objects.filter(
                school=school
            ).order_by('-payment_date')[:5]

            # Calculate totals
            total_outstanding = outstanding_invoices.aggregate(
                total=Sum('total_amount')
            )['total'] or Decimal('0.00')

            total_paid_this_year = SchoolPayment.objects.filter(
                school=school,
                payment_date__year=timezone.now().year,
                status='COMPLETED'
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

            # Serialize data
            billing_account_data = SchoolBillingAccountSerializer(billing_account).data
            outstanding_invoices_data = SchoolInvoiceSerializer(outstanding_invoices, many=True).data
            recent_payments_data = SchoolPaymentSerializer(recent_payments, many=True).data

            return Response({
                'school': {
                    'id': school.id,
                    'name': school.name,
                    'email': school.email,
                    'phone': school.phone
                },
                'billing_account': billing_account_data,
                'license_info': license_info,
                'outstanding_invoices': outstanding_invoices_data,
                'recent_payments': recent_payments_data,
                'summary': {
                    'total_outstanding': float(total_outstanding),
                    'total_paid_this_year': float(total_paid_this_year),
                    'can_make_payment': billing_account.billing_status == 'ACTIVE' and not billing_account.is_overdue(),
                    'grace_period_active': billing_account.is_overdue() and license_info and license_info.get('grace_period_active', False),
                    'account_status': billing_account.billing_status,
                    'next_billing_date': billing_account.next_billing_date
                }
            })

        except Exception as e:
            logger.error(f"Error fetching billing overview: {e}")
            return Response({
                'error': 'Failed to fetch billing information'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def invoices(self, request):
        """
        Get all invoices for the school
        """
        try:
            school = self.get_user_school()
            if not school:
                return Response({
                    'error': 'No school associated with your account'
                }, status=status.HTTP_404_NOT_FOUND)

            # Filter parameters
            status_filter = request.query_params.get('status')
            start_date = request.query_params.get('start_date')
            end_date = request.query_params.get('end_date')

            queryset = SchoolInvoice.objects.filter(school=school)

            if status_filter:
                queryset = queryset.filter(status=status_filter)
            if start_date:
                queryset = queryset.filter(invoice_date__gte=start_date)
            if end_date:
                queryset = queryset.filter(invoice_date__lte=end_date)

            invoices = queryset.order_by('-invoice_date')
            serializer = SchoolInvoiceSerializer(invoices, many=True)

            return Response({
                'invoices': serializer.data,
                'count': invoices.count()
            })

        except Exception as e:
            logger.error(f"Error fetching invoices: {e}")
            return Response({
                'error': 'Failed to fetch invoices'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def payments(self, request):
        """
        Get all payments for the school
        """
        try:
            school = self.get_user_school()
            if not school:
                return Response({
                    'error': 'No school associated with your account'
                }, status=status.HTTP_404_NOT_FOUND)

            # Filter parameters
            status_filter = request.query_params.get('status')
            start_date = request.query_params.get('start_date')
            end_date = request.query_params.get('end_date')

            queryset = SchoolPayment.objects.filter(school=school)

            if status_filter:
                queryset = queryset.filter(status=status_filter)
            if start_date:
                queryset = queryset.filter(payment_date__gte=start_date)
            if end_date:
                queryset = queryset.filter(payment_date__lte=end_date)

            payments = queryset.order_by('-payment_date')
            serializer = SchoolPaymentSerializer(payments, many=True)

            return Response({
                'payments': serializer.data,
                'count': payments.count()
            })

        except Exception as e:
            logger.error(f"Error fetching payments: {e}")
            return Response({
                'error': 'Failed to fetch payments'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def initiate_payment(self, request):
        """
        Initiate a payment for outstanding invoices
        """
        try:
            school = self.get_user_school()
            if not school:
                return Response({
                    'error': 'No school associated with your account'
                }, status=status.HTTP_404_NOT_FOUND)

            # Get billing account
            try:
                billing_account = SchoolBillingAccount.objects.get(school=school)
            except SchoolBillingAccount.DoesNotExist:
                return Response({
                    'error': 'No billing account found for your school'
                }, status=status.HTTP_404_NOT_FOUND)

            # Check if school can make payments (active account or within grace period)
            if billing_account.billing_status not in ['ACTIVE', 'DELINQUENT']:
                return Response({
                    'error': 'Your account is suspended. Please contact support.'
                }, status=status.HTTP_403_FORBIDDEN)

            # Get payment data
            invoice_id = request.data.get('invoice_id')
            amount = request.data.get('amount')
            payment_method = request.data.get('payment_method')
            external_reference = request.data.get('external_reference', '')
            notes = request.data.get('notes', '')

            # Validate required fields
            if not all([invoice_id, amount, payment_method]):
                return Response({
                    'error': 'Invoice ID, amount, and payment method are required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get the invoice
            try:
                invoice = SchoolInvoice.objects.get(id=invoice_id, school=school)
            except SchoolInvoice.DoesNotExist:
                return Response({
                    'error': 'Invoice not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # Validate amount
            try:
                amount = Decimal(str(amount))
                if amount <= 0:
                    raise ValueError("Amount must be positive")
                if amount > invoice.total_amount:
                    raise ValueError("Amount cannot exceed invoice total")
            except (ValueError, TypeError) as e:
                return Response({
                    'error': f'Invalid amount: {str(e)}'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create payment record
            payment = SchoolPayment.objects.create(
                school=school,
                invoice=invoice,
                amount=amount,
                payment_method=payment_method,
                external_reference=external_reference,
                notes=notes,
                status='PENDING'
            )

            # Serialize the payment
            payment_data = SchoolPaymentSerializer(payment).data

            return Response({
                'message': 'Payment initiated successfully',
                'payment': payment_data,
                'next_steps': 'Your payment has been recorded and is pending verification. You will receive confirmation once it has been processed.'
            })

        except Exception as e:
            logger.error(f"Error initiating payment: {e}")
            return Response({
                'error': 'Failed to initiate payment'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def download_invoice(self, request):
        """
        Download invoice PDF
        """
        try:
            school = self.get_user_school()
            if not school:
                return Response({
                    'error': 'No school associated with your account'
                }, status=status.HTTP_404_NOT_FOUND)

            invoice_id = request.query_params.get('invoice_id')
            if not invoice_id:
                return Response({
                    'error': 'Invoice ID is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get the invoice
            try:
                invoice = SchoolInvoice.objects.get(id=invoice_id, school=school)
            except SchoolInvoice.DoesNotExist:
                return Response({
                    'error': 'Invoice not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # For now, return invoice data (PDF generation can be implemented later)
            invoice_data = SchoolInvoiceSerializer(invoice).data
            
            return Response({
                'invoice': invoice_data,
                'download_url': f'/api/fees/school-admin-billing/download_invoice/?invoice_id={invoice_id}',
                'message': 'PDF generation will be implemented in the next version'
            })

        except Exception as e:
            logger.error(f"Error downloading invoice: {e}")
            return Response({
                'error': 'Failed to download invoice'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
