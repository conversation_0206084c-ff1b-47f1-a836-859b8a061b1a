from django.core.management.base import BaseCommand
from django.db import transaction
from schools.models import School
from mpesa_integration.models import MpesaCredential

class Command(BaseCommand):
    help = 'Set up test MPESA credentials for schools'

    def add_arguments(self, parser):
        parser.add_argument(
            '--school',
            type=int,
            help='School ID to create credentials for (optional)',
        )

    @transaction.atomic
    def handle(self, *args, **options):
        school_id = options.get('school')
        
        if school_id:
            schools = School.objects.filter(id=school_id)
            if not schools.exists():
                self.stdout.write(self.style.ERROR(f'School with ID {school_id} not found'))
                return
        else:
            schools = School.objects.all()
            if not schools.exists():
                self.stdout.write(self.style.ERROR('No schools found. Please create at least one school first.'))
                return
        
        for school in schools:
            # Check if credentials already exist
            existing = MpesaCredential.objects.filter(school=school, environment='sandbox')
            if existing.exists():
                self.stdout.write(self.style.WARNING(f'MPESA credentials already exist for {school.name}. Skipping.'))
                continue
            
            # Create sandbox credentials
            credential = MpesaCredential.objects.create(
                school=school,
                consumer_key='your_test_consumer_key',
                consumer_secret='your_test_consumer_secret',
                shortcode='174379',  # Safaricom test shortcode
                passkey='bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919',  # Safaricom test passkey
                environment='sandbox',
                is_active=True
            )
            
            self.stdout.write(self.style.SUCCESS(f'Created MPESA test credentials for {school.name}'))
            
        self.stdout.write(self.style.SUCCESS('MPESA test setup complete'))
