import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { authService } from '../../services/authService.ts';
import type { UserType, SchoolBranch } from '../../config/apiEndpoints'; // Import SchoolBranch from the appropriate module

interface AuthState {
  isAuthenticated: boolean;
  user: {
    id?: number;
    email?: string;
    user_type?: UserType;
    first_name?: string;
    last_name?: string;
    school_branch?: SchoolBranch; // Ensure SchoolBranch is imported or defined
    groups?: Array<{ id: number, name: string }>; // User groups
    is_superuser?: boolean; // Whether the user is a superuser
  } | null;
  token: string | null;
  loading: boolean;
  error: string | null;
  schoolBranch: SchoolBranch | null;
}

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  token: null,
  loading: false,
  error: null,
  schoolBranch: null
};

export const login = createAsyncThunk(
  'auth/login',
  async (credentials: { email: string; password: string }, { rejectWithValue }) => {
    try {
      const response = await authService.login(credentials);

      if (!response.user) {
        return rejectWithValue('Invalid response: missing user data');
      }

      // At this point, user_type should be set by authService
      const userType = response.user.user_type?.toLowerCase();
      if (!userType) {
        return rejectWithValue('Invalid response: missing user type');
      }

      // Validate user type
      const validTypes = ['admin', 'teacher', 'student', 'parent', 'system_admin'];
      if (!validTypes.includes(userType)) {
        return rejectWithValue(`Unsupported user type: ${userType}`);
      }

      return {
        ...response,
        user: {
          ...response.user,
          user_type: userType
        }
      };
    } catch (error: any) {
      // Handle license errors specifically
      if (error.response?.status === 403 && error.response?.data?.error) {
        return rejectWithValue(error.response.data.error);
      }

      return rejectWithValue(
        error.response?.data?.error ||
        error.response?.data?.detail ||
        error.message ||
        'Login failed'
      );
    }
  }
);

export const logout = createAsyncThunk('auth/logout', async (_, { rejectWithValue }) => {
  try {
    await authService.logout();
    return true;
  } catch (error) {
    // Still return success even if server logout fails
    // We want the user to be logged out locally
    return true;
  }
});

export const refreshToken = createAsyncThunk(
  'auth/refreshToken',
  async (_, { rejectWithValue, getState }) => {
    try {
      console.log('Attempting to refresh token in Redux action');
      const response = await authService.refreshToken();

      // Get the current user data from localStorage (no API call)
      const userData = await authService.getCurrentUser();

      return {
        access: response.access,
        user: userData
      };
    } catch (error: any) {
      console.error('Token refresh failed in Redux action:', error);
      return rejectWithValue(
        error.response?.data?.detail ||
        error.message ||
        'Token refresh failed'
      );
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setUser: (state, action) => {
      state.user = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(login.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isAuthenticated = true;
        state.user = action.payload.user;
        state.token = action.payload.access;
        state.loading = false;
        state.error = null;
      })
      .addCase(login.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || 'Login failed';
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
      })
      .addCase(refreshToken.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.isAuthenticated = true;
        state.user = action.payload.user;
        state.token = action.payload.access;
        state.loading = false;
        state.error = null;
      })
      .addCase(refreshToken.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || 'Token refresh failed';
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
      })
      .addCase(logout.fulfilled, (state) => {
        return initialState;
      })
      .addCase(logout.rejected, (state) => {
        // Still clear the state even if server logout fails
        return initialState;
      });
  },
});

export const { clearError, setUser } = authSlice.actions;
export default authSlice.reducer;
