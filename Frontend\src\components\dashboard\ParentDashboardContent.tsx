import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { useSchool } from '../../contexts/SchoolContext';
import { getAllDashboardData } from '../../services/dashboardService';

// Import Heroicons
import {
  UserGroupIcon,
  CalendarIcon,
  ClipboardDocumentCheckIcon,
  ArrowRightIcon,
  ChartBarIcon,
  AcademicCapIcon,
  BellIcon,
  CreditCardIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';

// Define types for parent dashboard data
interface ParentDashboardStats {
  totalChildren: number;
  pendingPayments: number;
  upcomingEvents: number;
  unreadMessages: number;
}

interface ChildInfo {
  id: number;
  name: string;
  grade: string;
  attendancePercentage: number;
  performanceStatus: 'excellent' | 'good' | 'average' | 'needs-improvement';
}

interface PaymentInfo {
  id: number;
  description: string;
  amount: number;
  dueDate: string;
  status: 'paid' | 'pending' | 'overdue';
}

interface AnnouncementInfo {
  id: number;
  title: string;
  date: string;
  from: string;
}

interface ParentDashboardData {
  stats: ParentDashboardStats;
  children: ChildInfo[];
  payments: PaymentInfo[];
  announcements: AnnouncementInfo[];
}

// Default dashboard data for when API calls fail
const defaultDashboardData: ParentDashboardData = {
  stats: {
    totalChildren: 0,
    pendingPayments: 0,
    upcomingEvents: 0,
    unreadMessages: 0
  },
  children: [],
  payments: [],
  announcements: []
};

const ParentDashboardContent: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<ParentDashboardData>(defaultDashboardData);
  const [loading, setLoading] = useState<boolean>(true);
  const navigate = useNavigate();

  // Get the selected school and branch from context
  const { selectedSchool, selectedBranch } = useSchool();

  useEffect(() => {
    const fetchData = async () => {
      try {
        // In a real implementation, this would call a parent-specific dashboard API
        // For now, we'll adapt the admin dashboard data
        const data = await getAllDashboardData({
          schoolId: selectedSchool?.id,
          branchId: selectedBranch?.id
        });

        if (data) {
          // Use real academic data from the API
          const realClasses = data.academicData?.classes || [];

          setDashboardData({
            stats: {
              totalChildren: 2, // Mock data - would come from student enrollment API
              pendingPayments: 1, // Mock data - would come from payments API
              upcomingEvents: data.upcomingEvents.length,
              unreadMessages: 3 // Mock data - would come from messages API
            },
            children: [
              // Mock data using real class names - in a real implementation, this would come from the API
              {
                id: 1,
                name: 'John Smith',
                grade: realClasses[0]?.name || 'Grade 10A',
                attendancePercentage: 95,
                performanceStatus: 'excellent'
              },
              {
                id: 2,
                name: 'Sarah Smith',
                grade: realClasses[1]?.name || 'Grade 8B',
                attendancePercentage: 88,
                performanceStatus: 'good'
              },
            ],
            payments: [
              // Mock data - in a real implementation, this would come from the API
              {
                id: 1,
                description: 'Term 2 Tuition Fee',
                amount: 25000,
                dueDate: '2025-05-15',
                status: 'pending'
              },
              {
                id: 2,
                description: 'School Trip Fee',
                amount: 5000,
                dueDate: '2025-04-30',
                status: 'paid'
              },
              {
                id: 3,
                description: 'Term 1 Tuition Fee',
                amount: 25000,
                dueDate: '2025-01-15',
                status: 'paid'
              },
            ],
            announcements: data.recentAnnouncements.map(announcement => ({
              id: announcement.id,
              title: announcement.title,
              date: announcement.date,
              from: 'School Administration'
            }))
          });
        } else {
          // If no data is returned, use default data
          setDashboardData(defaultDashboardData);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // If there's an error, use default data
        setDashboardData(defaultDashboardData);
      } finally {
        setLoading(false);
      }
    };

    // Only fetch data if we have a selected school and branch
    if (selectedSchool && selectedBranch) {
      setLoading(true);
      fetchData();
    } else {
      // If no school or branch is selected, use default data and stop loading
      setDashboardData(defaultDashboardData);
      setLoading(false);
    }
  }, [selectedSchool, selectedBranch]); // Re-fetch when school or branch changes

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 dark:border-blue-400"></div>
        <span className="sr-only">Loading dashboard data...</span>
      </div>
    );
  }

  const getPerformanceColor = (status: string) => {
    switch (status) {
      case 'excellent':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30';
      case 'good':
        return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30';
      case 'average':
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30';
      case 'needs-improvement':
        return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30';
      case 'pending':
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30';
      case 'overdue':
        return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white">Parent Dashboard</h1>
        <div className="mt-4 md:mt-0 flex items-center space-x-3">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {selectedSchool && selectedBranch && (
              <span>
                <span className="font-medium text-gray-700 dark:text-gray-300">{selectedSchool.name}</span>
                {selectedBranch && ` - ${selectedBranch.name}`}
              </span>
            )}
          </div>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {format(new Date(), 'EEEE, MMMM d, yyyy')}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Summary Cards */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-blue-50 dark:bg-blue-900/20">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-blue-100 dark:bg-blue-800/50 rounded-lg">
                <UserGroupIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white ml-3">Children</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-800 dark:text-white">{dashboardData.stats.totalChildren}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Enrolled children</p>
              </div>
            </div>
          </div>
          <div className="px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700">
            <button
              type="button"
              onClick={() => navigate('/children')}
              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium flex items-center"
            >
              View Children
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-amber-50 dark:bg-amber-900/20">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-amber-100 dark:bg-amber-800/50 rounded-lg">
                <CreditCardIcon className="h-6 w-6 text-amber-600 dark:text-amber-400" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white ml-3">Payments</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-800 dark:text-white">{dashboardData.stats.pendingPayments}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Pending payments</p>
              </div>
            </div>
          </div>
          <div className="px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700">
            <button
              type="button"
              onClick={() => navigate('/payments')}
              className="text-amber-600 dark:text-amber-400 hover:text-amber-800 dark:hover:text-amber-300 text-sm font-medium flex items-center"
            >
              View Payments
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-purple-50 dark:bg-purple-900/20">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-purple-100 dark:bg-purple-800/50 rounded-lg">
                <CalendarIcon className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white ml-3">Events</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-800 dark:text-white">{dashboardData.stats.upcomingEvents}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Upcoming events</p>
              </div>
            </div>
          </div>
          <div className="px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700">
            <button
              type="button"
              onClick={() => navigate('/calendar')}
              className="text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 text-sm font-medium flex items-center"
            >
              View Calendar
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-green-50 dark:bg-green-900/20">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-green-100 dark:bg-green-800/50 rounded-lg">
                <ChatBubbleLeftRightIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white ml-3">Messages</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-800 dark:text-white">{dashboardData.stats.unreadMessages}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Unread messages</p>
              </div>
            </div>
          </div>
          <div className="px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700">
            <button
              type="button"
              onClick={() => navigate('/messages')}
              className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 text-sm font-medium flex items-center"
            >
              View Messages
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>
      </div>

      {/* Children and Payments */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* My Children */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white">My Children</h2>
              <button
                type="button"
                onClick={() => navigate('/children')}
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium flex items-center"
              >
                View All
                <ArrowRightIcon className="h-4 w-4 ml-1" />
              </button>
            </div>
            <div className="border-t border-gray-100 dark:border-gray-700 -mx-6 px-6 py-2"></div>
            <div className="mt-4">
              {dashboardData.children.length > 0 ? (
                <div className="space-y-4">
                  {dashboardData.children.map((child) => (
                    <div key={child.id} className="flex items-center justify-between py-4 border-b border-gray-100 dark:border-gray-700 last:border-0">
                      <div className="flex items-start space-x-3">
                        <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg mt-1">
                          <UserGroupIcon className="h-5 w-5 text-blue-500 dark:text-blue-400" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-800 dark:text-white">{child.name}</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">{child.grade}</p>
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        <div className={`px-3 py-1 rounded-full text-xs font-medium ${getPerformanceColor(child.performanceStatus)}`}>
                          {child.performanceStatus.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          Attendance: {child.attendancePercentage}%
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-full mb-3">
                    <UserGroupIcon className="h-8 w-8 text-blue-500 dark:text-blue-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-1">No children enrolled</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 max-w-xs">You don't have any children enrolled in this school.</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Recent Payments */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white">Recent Payments</h2>
              <button
                type="button"
                onClick={() => navigate('/payments')}
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium flex items-center"
              >
                View All
                <ArrowRightIcon className="h-4 w-4 ml-1" />
              </button>
            </div>
            <div className="border-t border-gray-100 dark:border-gray-700 -mx-6 px-6 py-2"></div>
            <div className="mt-4">
              {dashboardData.payments.length > 0 ? (
                <div className="space-y-4">
                  {dashboardData.payments.map((payment) => (
                    <div key={payment.id} className="flex items-center justify-between py-4 border-b border-gray-100 dark:border-gray-700 last:border-0">
                      <div className="flex items-start space-x-3">
                        <div className="p-2 bg-amber-50 dark:bg-amber-900/20 rounded-lg mt-1">
                          <CreditCardIcon className="h-5 w-5 text-amber-500 dark:text-amber-400" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-800 dark:text-white">{payment.description}</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Due: {format(new Date(payment.dueDate), 'MMM dd, yyyy')}</p>
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">KES {payment.amount.toLocaleString()}</div>
                        <div className={`mt-1 px-3 py-1 rounded-full text-xs font-medium ${getPaymentStatusColor(payment.status)}`}>
                          {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <div className="p-3 bg-amber-50 dark:bg-amber-900/20 rounded-full mb-3">
                    <CreditCardIcon className="h-8 w-8 text-amber-500 dark:text-amber-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-1">No payment history</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 max-w-xs">You don't have any payment records yet.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Announcements */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-gray-800 dark:text-white">Announcements</h2>
            <button
              type="button"
              onClick={() => navigate('/announcements')}
              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium flex items-center"
            >
              View All
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
          <div className="border-t border-gray-100 dark:border-gray-700 -mx-6 px-6 py-2"></div>
          <div className="mt-4">
            {dashboardData.announcements.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {dashboardData.announcements.map((announcement) => (
                  <div key={announcement.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200">
                    <div className="flex items-center mb-3">
                      <BellIcon className="h-5 w-5 text-indigo-500 dark:text-indigo-400 mr-2" />
                      <span className="text-xs text-gray-500 dark:text-gray-400">{format(new Date(announcement.date), 'MMM dd, yyyy')}</span>
                    </div>
                    <h3 className="font-medium text-gray-800 dark:text-white mb-1">{announcement.title}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">From: {announcement.from}</p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <div className="p-3 bg-indigo-50 dark:bg-indigo-900/20 rounded-full mb-3">
                  <BellIcon className="h-8 w-8 text-indigo-500 dark:text-indigo-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-1">No announcements</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 max-w-xs">There are no announcements at this time.</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
        <div className="p-6">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            <button
              type="button"
              onClick={() => navigate('/payments/make-payment')}
              className="flex items-center justify-center px-4 py-3 bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300 rounded-lg hover:bg-amber-100 dark:hover:bg-amber-800/30 transition-colors duration-200"
            >
              <CreditCardIcon className="h-5 w-5 mr-2" />
              Make Payment
            </button>
            <button
              type="button"
              onClick={() => navigate('/messages/new')}
              className="flex items-center justify-center px-4 py-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-100 dark:hover:bg-green-800/30 transition-colors duration-200"
            >
              <ChatBubbleLeftRightIcon className="h-5 w-5 mr-2" />
              Contact Teacher
            </button>
            <button
              type="button"
              onClick={() => navigate('/reports')}
              className="flex items-center justify-center px-4 py-3 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-800/30 transition-colors duration-200"
            >
              <DocumentTextIcon className="h-5 w-5 mr-2" />
              View Reports
            </button>
            <button
              type="button"
              onClick={() => navigate('/attendance')}
              className="flex items-center justify-center px-4 py-3 bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-800/30 transition-colors duration-200"
            >
              <UserGroupIcon className="h-5 w-5 mr-2" />
              Check Attendance
            </button>
            <button
              type="button"
              onClick={() => navigate('/performance')}
              className="flex items-center justify-center px-4 py-3 bg-indigo-50 dark:bg-indigo-900/20 text-indigo-700 dark:text-indigo-300 rounded-lg hover:bg-indigo-100 dark:hover:bg-indigo-800/30 transition-colors duration-200"
            >
              <ChartBarIcon className="h-5 w-5 mr-2" />
              Performance Tracker
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ParentDashboardContent;
