from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from communication.models import Announcement
from schools.models import School, SchoolBranch
from core.models import CustomUser
from datetime import datetime, timedelta

class AnnouncementAPITest(TestCase):
    def setUp(self):
        # Create a test school
        self.school = School.objects.create(
            name="Test School",
            address="123 Test Street",
            phone="1234567890",
            email="<EMAIL>",
            registration_number="TEST-REG-001",
            established_date="2000-01-01"
        )

        # Create a test school branch
        self.school_branch = SchoolBranch.objects.create(
            school=self.school,
            name="Test School Branch",
            address="123 Test Street",
            phone="1234567890",
            email="<EMAIL>",
            registration_number="TEST-BR-REG-001",
            established_date="2000-01-01"
        )

        # Create a test admin user
        self.admin_user = CustomUser.objects.create_user(
            username="testadmin",
            email="<EMAIL>",
            password="testpassword",
            is_staff=True,
            school_branch=self.school_branch
        )

        # Create a test teacher user
        self.teacher_user = CustomUser.objects.create_user(
            username="testteacher",
            email="<EMAIL>",
            password="testpassword",
            school_branch=self.school_branch
        )

        # Create a test announcement
        self.announcement = Announcement.objects.create(
            title="Test Announcement",
            content="This is a test announcement content.",
            announcement_type="GENERAL",
            priority="MEDIUM",
            target_audience="ALL",
            publish_date=datetime.now().date(),
            is_active=True,
            school_branch=self.school_branch,
            created_by=self.admin_user
        )

        # Initialize the API client
        self.client = APIClient()

    def test_get_all_announcements(self):
        """Test retrieving all announcements"""
        # Authenticate as admin
        self.client.force_authenticate(user=self.admin_user)

        # Make the request
        url = reverse('announcement-list')
        response = self.client.get(url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Handle both paginated and non-paginated responses
        if isinstance(response.data, dict) and 'results' in response.data:
            # Paginated response
            self.assertEqual(len(response.data['results']), 1)
            self.assertEqual(response.data['results'][0]['title'], 'Test Announcement')
        else:
            # Non-paginated response
            self.assertEqual(len(response.data), 1)
            self.assertEqual(response.data[0]['title'], 'Test Announcement')

    def test_get_announcement_detail(self):
        """Test retrieving a specific announcement"""
        # Authenticate as admin
        self.client.force_authenticate(user=self.admin_user)

        # Make the request
        url = reverse('announcement-detail', args=[self.announcement.id])
        response = self.client.get(url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['title'], 'Test Announcement')
        self.assertEqual(response.data['announcement_type'], 'GENERAL')

    def test_create_announcement(self):
        """Test creating a new announcement"""
        # Authenticate as admin
        self.client.force_authenticate(user=self.admin_user)

        # Prepare the data
        tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        data = {
            'title': 'New Test Announcement',
            'content': 'This is a new test announcement content.',
            'announcement_type': 'ACADEMIC',
            'priority': 'HIGH',
            'target_audience': 'TEACHERS',
            'publish_date': tomorrow,
            'is_active': True,
            'school_branch': self.school_branch.id,
            'created_by': self.admin_user.id  # Add the created_by field
        }

        # Make the request
        url = reverse('announcement-list')
        response = self.client.post(url, data, format='json')

        # If the test fails, print the response data for debugging
        if response.status_code != status.HTTP_201_CREATED:
            print(f"Response data: {response.data}")

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['title'], 'New Test Announcement')

        # Check that the announcement was created in the database
        self.assertEqual(Announcement.objects.count(), 2)

    def test_update_announcement(self):
        """Test updating an announcement"""
        # Authenticate as admin
        self.client.force_authenticate(user=self.admin_user)

        # Prepare the data
        data = {
            'title': 'Updated Test Announcement',
            'priority': 'HIGH'
        }

        # Make the request
        url = reverse('announcement-detail', args=[self.announcement.id])
        response = self.client.patch(url, data, format='json')

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['title'], 'Updated Test Announcement')
        self.assertEqual(response.data['priority'], 'HIGH')

        # Refresh the announcement from the database
        self.announcement.refresh_from_db()
        self.assertEqual(self.announcement.title, 'Updated Test Announcement')

    def test_delete_announcement(self):
        """Test deleting an announcement"""
        # Authenticate as admin
        self.client.force_authenticate(user=self.admin_user)

        # Make the request
        url = reverse('announcement-detail', args=[self.announcement.id])
        response = self.client.delete(url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # Check that the announcement was deleted from the database
        self.assertEqual(Announcement.objects.count(), 0)

    def test_current_announcements(self):
        """Test retrieving current announcements"""
        # Authenticate as teacher
        self.client.force_authenticate(user=self.teacher_user)

        # Make the request
        url = reverse('announcement-current')
        response = self.client.get(url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['title'], 'Test Announcement')

    def test_unauthorized_access(self):
        """Test that unauthenticated users cannot access the API"""
        # Make the request without authentication
        url = reverse('announcement-list')
        response = self.client.get(url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
