from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from core.permissions import IsAdminOrSuperUser
from academics.services.curriculum_propagation import CurriculumPropagationService
from schools.models import School, SchoolBranch

class CurriculumPropagationViewSet(viewsets.ViewSet):
    """
    API endpoint for propagating curriculum configurations from schools to branches
    """
    permission_classes = [IsAuthenticated, IsAdminOrSuperUser]
    
    @action(detail=True, methods=['post'])
    def propagate_to_all_branches(self, request, pk=None):
        """
        Propagate curriculum configuration from a school to all its branches
        """
        results = CurriculumPropagationService.propagate_to_all_branches(pk)
        
        if 'error' in results:
            return Response({'error': results['error']}, status=status.HTTP_404_NOT_FOUND)
        
        return Response(results)
    
    @action(detail=True, methods=['post'])
    def propagate_to_specific_branches(self, request, pk=None):
        """
        Propagate curriculum configuration from a school to specific branches
        """
        branch_ids = request.data.get('branch_ids', [])
        
        if not branch_ids:
            return Response(
                {'error': 'No branch IDs provided'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        results = CurriculumPropagationService.propagate_to_specific_branches(pk, branch_ids)
        
        if 'error' in results:
            return Response({'error': results['error']}, status=status.HTTP_404_NOT_FOUND)
        
        return Response(results)
    
    @action(detail=True, methods=['get'])
    def get_branches(self, request, pk=None):
        """
        Get all branches for a school with their curriculum configuration status
        """
        try:
            school = School.objects.get(pk=pk)
            branches = SchoolBranch.objects.filter(school=school)
            
            branch_data = []
            for branch in branches:
                branch_info = {
                    'id': branch.id,
                    'name': branch.name,
                    'code': branch.code,
                    'has_curriculum_config': hasattr(branch, 'curriculum_config'),
                    'is_inherited_from_school': False
                }
                
                if hasattr(branch, 'curriculum_config'):
                    branch_info['is_inherited_from_school'] = branch.curriculum_config.is_inherited_from_school
                
                branch_data.append(branch_info)
            
            return Response(branch_data)
        except School.DoesNotExist:
            return Response(
                {'error': f'School with ID {pk} not found'},
                status=status.HTTP_404_NOT_FOUND
            )
