import axiosWithAuth from '../utils/axiosInterceptor';

export interface User {
  id?: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  phone: string;
  user_type: 'student' | 'teacher' | 'admin';
  school: number;
  school_branch: number;
  is_active: boolean;
}

export interface SchoolBranch {
  id?: number;
  name: string;
  code: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  registration_number: string;
  established_date: string;
  is_active: boolean;
  school: number;
  users: number[];
}

export interface Student {
  id: number;
  user: User;
  profile_picture?: string;
  date_of_birth?: string;
  gender?: 'M' | 'F';
  school_branch?: SchoolBranch;
  school_branch_id: number;
  class_teacher?: number;
  class_name?: number | string;
  stream?: number | string;
  stream_name?: string;
  admission_number: string;
  current_class?: number;
  subjects?: number[];
  grade?: string;
  admission_date?: string;
  // These fields are for convenience in the UI
  first_name?: string;
  last_name?: string;
}

interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

export interface StudentFormData {
  // User fields
  username?: string;
  email?: string;
  first_name: string;
  last_name: string;
  phone?: string;
  is_active?: boolean;

  // Student fields
  gender: 'M' | 'F';
  date_of_birth?: string;
  admission_number?: string;
  class_name?: string | number;
  grade?: string;
  stream?: string | number;
  stream_name?: string;
  school_branch_id?: number;
  class_teacher?: number;
  current_class?: number;
  subjects?: number[];
  profile_picture?: File | null;
}

export const studentService = {
  async getAllStudents(params?: {
    class?: string;
    grade?: string;
    search?: string;
    page?: number;
    page_size?: number;
    school_branch?: number;
    is_active?: boolean;
  }): Promise<PaginatedResponse<Student>> {
    const response = await axiosWithAuth.get('/api/users/students/', { params });
    

    if (!response.data.results) {
      console.warn('API response missing results array:', response.data);
      return {
        count: 0,
        next: null,
        previous: null,
        results: []
      };
    }

    // Map the response to match our Student interface
    const mappedResults = response.data.results.map((student: any) => {
      // Get stream name if available
      let streamName = '-';
      if (student.stream_name) {
        streamName = student.stream_name;
      } else if (typeof student.stream === 'object' && student.stream?.name) {
        streamName = student.stream.name;
      }

      return {
        ...student,
        // Add convenience fields for UI
        first_name: student.user?.first_name || student.first_name || '',
        last_name: student.user?.last_name || student.last_name || '',
        // Add stream_name
        stream_name: streamName,
      };
    });

    return {
      ...response.data,
      results: mappedResults
    };
  },

  async getStudentById(id: number): Promise<Student> {
    const response = await axiosWithAuth.get(`/api/users/students/${id}/`);
    
    // Map the response to match our Student interface
    const student = response.data;

    // Get stream name if available
    let streamName = '-';
    if (student.stream_name) {
      streamName = student.stream_name;
    } else if (typeof student.stream === 'object' && student.stream?.name) {
      streamName = student.stream.name;
    }

    return {
      ...student,
      // Add convenience fields for UI
      first_name: student.user?.first_name || student.first_name || '',
      last_name: student.user?.last_name || student.last_name || '',
      // Add stream_name
      stream_name: streamName,
    };
  },

  async createStudent(data: StudentFormData): Promise<Student> {
    // Create FormData for file upload
    const formData = new FormData();

    // Prepare user data
    const userData = {
      first_name: data.first_name,
      last_name: data.last_name,
      email: data.email || '',
      phone: data.phone || '',
      username: data.username || '',
      user_type: 'student',
      is_active: data.is_active !== undefined ? data.is_active : true,
    };

    formData.append('user', JSON.stringify(userData));

    // Add all student fields
    const studentFields = ['gender', 'date_of_birth', 'admission_number', 'class_name', 'stream',
                          'school_branch_id', 'class_teacher', 'current_class', 'subjects'];

    studentFields.forEach(field => {
      const value = data[field as keyof StudentFormData];
      if (value !== undefined && field !== 'stream_name') {
        if (Array.isArray(value)) {
          // Handle arrays like subjects
          formData.append(field, JSON.stringify(value));
        } else {
          formData.append(field, String(value));
        }
      }
    });

    // Add profile picture if it exists
    if (data.profile_picture) {
      formData.append('profile_picture', data.profile_picture);
    }

    const response = await axiosWithAuth.post('/api/users/students/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    // Map the response to include stream_name
    const student = response.data;
    let streamName = '-';
    if (student.stream_name) {
      streamName = student.stream_name;
    } else if (typeof student.stream === 'object' && student.stream?.name) {
      streamName = student.stream.name;
    }

    return {
      ...student,
      first_name: student.user?.first_name || student.first_name || '',
      last_name: student.user?.last_name || student.last_name || '',
      stream_name: streamName,
    };
  },

  async updateStudent(id: number, data: Partial<StudentFormData>): Promise<Student> {
    // Create FormData for file upload
    const formData = new FormData();

    // Prepare user data if it exists
    if (data.first_name || data.last_name || data.email || data.phone || data.is_active !== undefined) {
      const userData = {
        first_name: data.first_name,
        last_name: data.last_name,
        email: data.email,
        phone: data.phone,
        is_active: data.is_active,
      };

      // Remove undefined values
      Object.keys(userData).forEach(key => {
        if (userData[key as keyof typeof userData] === undefined) {
          delete userData[key as keyof typeof userData];
        }
      });

      formData.append('user', JSON.stringify(userData));
    }

    // Add all student fields
    const studentFields = ['gender', 'date_of_birth', 'admission_number', 'class_name', 'stream',
                          'school_branch_id', 'class_teacher', 'current_class', 'subjects'];

    studentFields.forEach(field => {
      const value = data[field as keyof StudentFormData];
      if (value !== undefined && field !== 'stream_name') {
        if (Array.isArray(value)) {
          // Handle arrays like subjects
          formData.append(field, JSON.stringify(value));
        } else {
          formData.append(field, String(value));
        }
      }
    });

    // Add profile picture if it exists
    if (data.profile_picture) {
      formData.append('profile_picture', data.profile_picture);
    }

    const response = await axiosWithAuth.patch(`/api/users/students/${id}/`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    // Map the response to include stream_name
    const student = response.data;
    let streamName = '-';
    if (student.stream_name) {
      streamName = student.stream_name;
    } else if (typeof student.stream === 'object' && student.stream?.name) {
      streamName = student.stream.name;
    }

    return {
      ...student,
      first_name: student.user?.first_name || student.first_name || '',
      last_name: student.user?.last_name || student.last_name || '',
      stream_name: streamName,
    };
  },

  async deleteStudent(id: number): Promise<void> {
    await axiosWithAuth.delete(`/api/users/students/${id}/`);
  },

  async toggleStudentStatus(id: number, isActive: boolean): Promise<Student> {
    // Create a partial update with just the user status
    const data = {
      user: {
        is_active: isActive
      }
    };

    const response = await axiosWithAuth.patch(`/api/users/students/${id}/`, data);
    return response.data;
  },

  async exportToExcel() {
    const response = await axiosWithAuth.get('/api/users/students/export/excel/', {
      responseType: 'blob'
    });
    return response.data;
  },

  async exportToPDF() {
    const response = await axiosWithAuth.get('/api/users/students/export/pdf/', {
      responseType: 'blob'
    });
    return response.data;
  }
};
