# Generated by Django 5.2.1 on 2025-06-01 13:25

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('academics', '0003_initial'),
        ('fees', '0001_initial'),
        ('schools', '0001_initial'),
        ('users', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='feearrears',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='users.student'),
        ),
        migrations.AddField(
            model_name='feearrears',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.term'),
        ),
        migrations.AddField(
            model_name='feebalance',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='users.student'),
        ),
        migrations.AddField(
            model_name='feebalance',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.term'),
        ),
        migrations.AddField(
            model_name='feediscount',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='schools.school'),
        ),
        migrations.AddField(
            model_name='feepayment',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='users.student'),
        ),
        migrations.AddField(
            model_name='feepayment',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.term'),
        ),
        migrations.AddField(
            model_name='feepayment',
            name='verified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='users.staff'),
        ),
        migrations.AddField(
            model_name='feestatement',
            name='generated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='users.staff'),
        ),
        migrations.AddField(
            model_name='feestatement',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='users.student'),
        ),
        migrations.AddField(
            model_name='feestatement',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.term'),
        ),
        migrations.AddField(
            model_name='feestructure',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='schools.school'),
        ),
        migrations.AddField(
            model_name='feestructure',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.term'),
        ),
        migrations.AddField(
            model_name='feetype',
            name='fee_structure',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fee_types', to='fees.feestructure'),
        ),
        migrations.AddField(
            model_name='feetype',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='schools.school'),
        ),
        migrations.AddField(
            model_name='feepayment',
            name='fee_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fees.feetype'),
        ),
        migrations.AddField(
            model_name='invoice',
            name='fee_structure',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fees.feestructure'),
        ),
        migrations.AddField(
            model_name='invoice',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='users.student'),
        ),
        migrations.AddField(
            model_name='lease',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='schools.school'),
        ),
        migrations.AddField(
            model_name='leasepayment',
            name='lease',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='fees.lease'),
        ),
        migrations.AddField(
            model_name='paymentanalytics',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='schools.school'),
        ),
        migrations.AddField(
            model_name='paymentanalytics',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.term'),
        ),
        migrations.AddField(
            model_name='paymentauditlog',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='paymentplan',
            name='approved_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='users.staff'),
        ),
        migrations.AddField(
            model_name='paymentplan',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='users.student'),
        ),
        migrations.AddField(
            model_name='paymentplan',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.term'),
        ),
        migrations.AddField(
            model_name='paymentreceipt',
            name='generated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='paymentreminder',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='users.student'),
        ),
        migrations.AddField(
            model_name='paymentschedule',
            name='payment_plan',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='fees.paymentplan'),
        ),
        migrations.AddField(
            model_name='paymentverificationrule',
            name='schools',
            field=models.ManyToManyField(blank=True, to='schools.school'),
        ),
        migrations.AddField(
            model_name='receipt',
            name='issued_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='users.staff'),
        ),
        migrations.AddField(
            model_name='receipt',
            name='payment',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='fees.feepayment'),
        ),
        migrations.AddField(
            model_name='schoolbillingaccount',
            name='school',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='billing_account', to='schools.school'),
        ),
        migrations.AddField(
            model_name='schoolinvoice',
            name='billing_account',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invoices', to='fees.schoolbillingaccount'),
        ),
        migrations.AddField(
            model_name='schoolinvoice',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_invoices', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='schoolinvoice',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invoices', to='schools.school'),
        ),
        migrations.AddField(
            model_name='schoolinvoicelineitem',
            name='invoice',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_items', to='fees.schoolinvoice'),
        ),
        migrations.AddField(
            model_name='schoolpayment',
            name='invoice',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='fees.schoolinvoice'),
        ),
        migrations.AddField(
            model_name='schoolpayment',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='schools.school'),
        ),
        migrations.AddField(
            model_name='schoolpayment',
            name='verified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_payments', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='paymentreceipt',
            name='payment',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='receipt', to='fees.schoolpayment'),
        ),
        migrations.AddField(
            model_name='paymentnotification',
            name='payment',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='fees.schoolpayment'),
        ),
        migrations.AddField(
            model_name='paymentauditlog',
            name='payment',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='audit_logs', to='fees.schoolpayment'),
        ),
        migrations.AddField(
            model_name='schoolinvoicelineitem',
            name='subscription_plan',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='fees.schoolsubscriptionplan'),
        ),
        migrations.AddField(
            model_name='studentfeeaccount',
            name='fee_structure',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fees.feestructure'),
        ),
        migrations.AddField(
            model_name='studentfeeaccount',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fee_accounts', to='users.student'),
        ),
        migrations.AddField(
            model_name='studentfeeaccount',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.term'),
        ),
        migrations.AlterUniqueTogether(
            name='feestructure',
            unique_together={('school', 'academic_year', 'term')},
        ),
        migrations.AlterUniqueTogether(
            name='paymentanalytics',
            unique_together={('school', 'term', 'date')},
        ),
        migrations.AlterUniqueTogether(
            name='studentfeeaccount',
            unique_together={('student', 'term')},
        ),
    ]
