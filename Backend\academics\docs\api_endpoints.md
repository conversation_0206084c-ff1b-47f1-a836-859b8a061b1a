# Academic Analytics API Documentation

## Authentication
All endpoints require authentication using JWT tokens. Include the token in the Authorization header:
```http
Authorization: Bearer <your_token>
```

## Endpoints

### Student Performance Analytics
GET `/api/analytics/student/{id}/performance/`

**Permissions:** Teacher or Admin only

**Response:**
```json
{
    "performance_prediction": {
        "predicted_score": 85.5,
        "confidence": 0.87,
        "risk_level": 1,
        "recommendations": [
            "Focus on practical exercises",
            "Increase participation in discussions"
        ]
    },
    "learning_style": {
        "primary_style": "VISUAL",
        "secondary_style": "KINESTHETIC"
    },
    "risk_assessment": {
        "risk_level": "LOW",
        "factors": []
    }
}
```

### Learning Path Generation
POST `/api/learning-paths/generate/{student_id}/`

**Permissions:** Teacher or Admin only

**Request:**
```json
{
    "subject_id": 123,
    "target_level": "ADVANCED",
    "preferences": {
        "learning_style": "VISUAL",
        "pace": "MODERATE"
    }
}
```

**Response:**
```json
{
    "learning_path": {
        "id": 1,
        "milestones": [],
        "estimated_completion": "2024-06-30",
        "current_level": "INTERMEDIATE"
    }
}
```

### Progress Tracking
GET `/api/performance/metrics/{student_id}/`

**Permissions:** Authenticated Student/Parent or Teacher

**Response:**
```json
{
    "progress_summary": {
        "overall_progress": 75.5,
        "subjects_improved": 3,
        "areas_needing_attention": 1
    },
    "competency_tracking": {
        "mastered_topics": [],
        "in_progress": [],
        "not_started": []
    }
}
```

## Error Responses

### Authentication Errors
```json
{
    "detail": "Authentication credentials were not provided."
}
```

### Permission Errors
```json
{
    "detail": "You do not have permission to perform this action."
}
```

### Resource Not Found
```json
{
    "detail": "Not found."
}
```
