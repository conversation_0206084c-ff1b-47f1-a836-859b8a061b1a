from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from .models import Department
from .serializers.department_serializer import DepartmentSerializer

class DepartmentViewSet(viewsets.ModelViewSet):
    """
    API endpoint for departments
    """
    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = Department.objects.all()

        # Filter by school
        school = self.request.query_params.get('school')
        if school:
            queryset = queryset.filter(school=school)

        # Filter by school branch
        school_branch = self.request.query_params.get('school_branch')
        if school_branch:
            queryset = queryset.filter(school_branch=school_branch)

        return queryset

    def create(self, request, *args, **kwargs):
        # Print the request data for debugging
        print(f"Request data for department creation: {request.data}")

        # Try to create the department and print any validation errors
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            print(f"Validation errors: {serializer.errors}")
            return Response(serializer.errors, status=400)

        return super().create(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        # Print the request data for debugging
        print(f"Request data for department update: {request.data}")

        # Try to update the department and print any validation errors
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=kwargs.get('partial', False))
        if not serializer.is_valid():
            print(f"Validation errors: {serializer.errors}")
            return Response(serializer.errors, status=400)

        return super().update(request, *args, **kwargs)
