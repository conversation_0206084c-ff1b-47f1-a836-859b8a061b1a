from django.db.models import Sum, Count, Avg, Q
from django.utils import timezone
from datetime import datetime, timedelta
from .models import (
    FeePayment, FeeBalance, StudentFeeAccount, 
    PaymentAnalytics, FeeStructure, Invoice
)
from users.models import Student
from schools.models import School
from academics.models import Term


def calculate_collection_rate(school, term=None):
    """
    Calculate fee collection rate for a school and term
    """
    if not term:
        term = Term.objects.filter(school=school, is_current=True).first()
    
    if not term:
        return 0
    
    # Total expected fees
    total_expected = FeeStructure.objects.filter(
        school=school,
        term=term,
        is_active=True
    ).aggregate(total=Sum('total_amount'))['total'] or 0
    
    # Total collected
    total_collected = FeePayment.objects.filter(
        student__school_branch__school=school,
        term=term,
        verified=True
    ).aggregate(total=Sum('amount_paid'))['total'] or 0
    
    if total_expected > 0:
        return (total_collected / total_expected) * 100
    return 0


def get_payment_analytics(school, term=None, start_date=None, end_date=None):
    """
    Get comprehensive payment analytics for a school
    """
    if not term:
        term = Term.objects.filter(school=school, is_current=True).first()
    
    if not start_date:
        start_date = term.start_date if term else timezone.now().date() - timedelta(days=30)
    
    if not end_date:
        end_date = timezone.now().date()
    
    # Base queryset
    payments = FeePayment.objects.filter(
        student__school_branch__school=school,
        payment_date__date__range=[start_date, end_date],
        verified=True
    )
    
    if term:
        payments = payments.filter(term=term)
    
    # Calculate metrics
    total_payments = payments.count()
    total_amount = payments.aggregate(total=Sum('amount_paid'))['total'] or 0
    average_payment = payments.aggregate(avg=Avg('amount_paid'))['avg'] or 0
    
    # Payment methods breakdown
    payment_methods = payments.values('payment_method').annotate(
        count=Count('id'),
        total=Sum('amount_paid')
    ).order_by('-total')
    
    # Daily payment trends
    daily_payments = payments.extra(
        select={'day': 'date(payment_date)'}
    ).values('day').annotate(
        count=Count('id'),
        total=Sum('amount_paid')
    ).order_by('day')
    
    # Outstanding balances
    outstanding = StudentFeeAccount.objects.filter(
        student__school_branch__school=school,
        balance__gt=0
    )
    
    if term:
        outstanding = outstanding.filter(term=term)
    
    total_outstanding = outstanding.aggregate(total=Sum('balance'))['total'] or 0
    students_with_balance = outstanding.count()
    
    return {
        'total_payments': total_payments,
        'total_amount': float(total_amount),
        'average_payment': float(average_payment),
        'payment_methods': list(payment_methods),
        'daily_trends': list(daily_payments),
        'total_outstanding': float(total_outstanding),
        'students_with_balance': students_with_balance,
        'collection_rate': calculate_collection_rate(school, term)
    }


def get_student_fee_summary(student, term=None):
    """
    Get comprehensive fee summary for a student
    """
    if not term:
        term = Term.objects.filter(
            school=student.school_branch.school,
            is_current=True
        ).first()
    
    if not term:
        return None
    
    # Get or create fee account
    fee_account, created = StudentFeeAccount.objects.get_or_create(
        student=student,
        term=term,
        defaults={
            'fee_structure': FeeStructure.objects.filter(
                school=student.school_branch.school,
                term=term,
                is_active=True
            ).first(),
            'total_fees': 0
        }
    )
    
    # Calculate current balance
    fee_account.calculate_balance()
    fee_account.save()
    
    # Get payment history
    payments = FeePayment.objects.filter(
        student=student,
        term=term
    ).order_by('-payment_date')
    
    # Get invoices
    invoices = Invoice.objects.filter(
        student=student,
        fee_structure__term=term
    ).order_by('-created_at')
    
    return {
        'student': student,
        'term': term,
        'fee_account': fee_account,
        'payments': payments,
        'invoices': invoices,
        'payment_history': [
            {
                'date': payment.payment_date,
                'amount': payment.amount_paid,
                'method': payment.get_payment_method_display(),
                'reference': payment.reference_number,
                'verified': payment.verified
            }
            for payment in payments
        ]
    }


def get_parent_children_fees(parent):
    """
    Get fee information for all children of a parent
    """
    children_fees = []
    
    for child in parent.children.all():
        # Get current term
        current_term = Term.objects.filter(
            school=child.school_branch.school,
            is_current=True
        ).first()
        
        if current_term:
            fee_summary = get_student_fee_summary(child, current_term)
            if fee_summary:
                children_fees.append(fee_summary)
    
    return children_fees


def update_payment_analytics(school, date=None):
    """
    Update daily payment analytics for a school
    """
    if not date:
        date = timezone.now().date()
    
    # Get current term
    current_term = Term.objects.filter(
        school=school,
        is_current=True
    ).first()
    
    if not current_term:
        return
    
    # Calculate daily metrics
    payments_today = FeePayment.objects.filter(
        student__school_branch__school=school,
        term=current_term,
        payment_date__date=date,
        verified=True
    )
    
    total_collected = payments_today.aggregate(total=Sum('amount_paid'))['total'] or 0
    number_of_payments = payments_today.count()
    average_payment = payments_today.aggregate(avg=Avg('amount_paid'))['avg'] or 0
    
    # Total expected for the term
    total_expected = FeeStructure.objects.filter(
        school=school,
        term=current_term,
        is_active=True
    ).aggregate(total=Sum('total_amount'))['total'] or 0
    
    # Total collected so far
    total_collected_term = FeePayment.objects.filter(
        student__school_branch__school=school,
        term=current_term,
        verified=True
    ).aggregate(total=Sum('amount_paid'))['total'] or 0
    
    collection_rate = (total_collected_term / total_expected * 100) if total_expected > 0 else 0
    outstanding_amount = total_expected - total_collected_term
    
    # Update or create analytics record
    analytics, created = PaymentAnalytics.objects.update_or_create(
        school=school,
        term=current_term,
        date=date,
        defaults={
            'total_expected': total_expected,
            'total_collected': total_collected_term,
            'collection_rate': collection_rate,
            'outstanding_amount': outstanding_amount,
            'number_of_payments': number_of_payments,
            'average_payment_amount': average_payment
        }
    )
    
    return analytics


def get_fee_defaulters(school, term=None, days_overdue=30):
    """
    Get list of students with overdue fees
    """
    if not term:
        term = Term.objects.filter(school=school, is_current=True).first()
    
    if not term:
        return []
    
    cutoff_date = timezone.now().date() - timedelta(days=days_overdue)
    
    defaulters = StudentFeeAccount.objects.filter(
        student__school_branch__school=school,
        term=term,
        balance__gt=0,
        created_at__date__lte=cutoff_date
    ).select_related('student', 'term').order_by('-balance')
    
    return defaulters


def generate_collection_report(school, term=None):
    """
    Generate comprehensive collection report
    """
    if not term:
        term = Term.objects.filter(school=school, is_current=True).first()
    
    analytics = get_payment_analytics(school, term)
    defaulters = get_fee_defaulters(school, term)
    
    # Class-wise collection
    class_collection = {}
    for student_class in school.classes.all():
        students = Student.objects.filter(
            school_branch__school=school,
            current_class=student_class
        )
        
        class_payments = FeePayment.objects.filter(
            student__in=students,
            term=term,
            verified=True
        ).aggregate(total=Sum('amount_paid'))['total'] or 0
        
        class_collection[student_class.name] = class_payments
    
    return {
        'school': school,
        'term': term,
        'analytics': analytics,
        'defaulters': defaulters,
        'class_collection': class_collection,
        'generated_at': timezone.now()
    }
