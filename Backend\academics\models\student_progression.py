from django.db import models
from django.utils import timezone
from users.models import Student

class StudentProgression(models.Model):
    """
    Tracks student progression through education levels
    """
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='progressions')
    current_class = models.ForeignKey('academics.ClassRoom', on_delete=models.CASCADE, related_name='progression_students')
    next_class = models.ForeignKey('academics.ClassRoom', on_delete=models.CASCADE, related_name='incoming_students', null=True, blank=True)
    progression_rule = models.ForeignKey('academics.ClassProgressionRule', on_delete=models.SET_NULL, null=True, blank=True)
    term = models.ForeignKey('academics.Term', on_delete=models.CASCADE)
    
    # Progression status
    STATUS_CHOICES = [
        ('PENDING', 'Pending Review'),
        ('APPROVED', 'Approved'),
        ('REPEAT', 'Repeat Class'),
        ('TRANSFER', 'Transfer'),
        ('GRADUATED', 'Graduated'),
        ('WITHDRAWN', 'Withdrawn')
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    
    # Performance metrics
    average_score = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    total_points = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    position_in_class = models.IntegerField(null=True, blank=True)
    
    # Decision details
    decision_date = models.DateField(null=True, blank=True)
    decision_by = models.ForeignKey('users.Teacher', on_delete=models.SET_NULL, null=True, blank=True)
    decision_notes = models.TextField(blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'academics'
        unique_together = ['student', 'term']
        ordering = ['-term', 'student']

    def __str__(self):
        return f"{self.student} - {self.current_class} to {self.next_class or 'No Next Class'}"

    def save(self, *args, **kwargs):
        # If status is changed to APPROVED, set the decision date
        if self.status == 'APPROVED' and not self.decision_date:
            self.decision_date = timezone.now().date()
        super().save(*args, **kwargs)

    def get_progression_requirements(self):
        """Get the requirements for progression"""
        if self.progression_rule:
            return self.progression_rule.requirements
        return None

    def check_progression_eligibility(self):
        """Check if student meets progression requirements"""
        if not self.progression_rule:
            return False
            
        requirements = self.get_progression_requirements()
        if not requirements:
            return True
            
        # Check minimum score requirement
        if 'minimum_score' in requirements and self.average_score:
            if self.average_score < requirements['minimum_score']:
                return False
                
        # Add more eligibility checks as needed
        
        return True 