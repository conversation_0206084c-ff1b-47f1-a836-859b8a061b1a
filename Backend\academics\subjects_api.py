from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from .models import Subject
# Import directly from the subject_serializer.py file to avoid circular imports
from .subject_serializer import SubjectSerializer

class SubjectViewSet(viewsets.ModelViewSet):
    queryset = Subject.objects.all()
    serializer_class = SubjectSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = Subject.objects.all()

        # Filter by class
        class_name = self.request.query_params.get('class_name')
        if class_name:
            queryset = queryset.filter(class_name=class_name)

        # Filter by stream
        stream = self.request.query_params.get('stream')
        if stream:
            queryset = queryset.filter(stream=stream)

        # Filter by department
        department = self.request.query_params.get('department')
        if department:
            queryset = queryset.filter(department=department)

        # Filter by school branch
        school_branch = self.request.query_params.get('school_branch')
        if school_branch:
            queryset = queryset.filter(school_branch=school_branch)

        return queryset
