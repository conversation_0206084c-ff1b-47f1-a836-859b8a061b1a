from rest_framework import serializers
from .curriculum_subjects import SubjectCategory, CurriculumSubject, AssessmentMethod, SubjectAssessment
from .curriculum_serializers import CurriculumSystemSerializer, EducationLevelSerializer

class SubjectCategorySerializer(serializers.ModelSerializer):
    curriculum_system_details = CurriculumSystemSerializer(source='curriculum_system', read_only=True)
    
    class Meta:
        model = SubjectCategory
        fields = ['id', 'name', 'code', 'curriculum_system', 'curriculum_system_details', 'description']
        extra_kwargs = {
            'curriculum_system': {'write_only': True}
        }

class CurriculumSubjectSerializer(serializers.ModelSerializer):
    curriculum_system_details = CurriculumSystemSerializer(source='curriculum_system', read_only=True)
    category_details = SubjectCategorySerializer(source='category', read_only=True)
    education_level_details = EducationLevelSerializer(source='education_levels', many=True, read_only=True)
    
    class Meta:
        model = CurriculumSubject
        fields = [
            'id', 'name', 'code', 'curriculum_system', 'curriculum_system_details',
            'category', 'category_details', 'description', 'education_levels',
            'education_level_details', 'is_compulsory', 'is_core_competency',
            'is_examinable', 'is_coursework_required', 'coursework_percentage',
            'system_specific_data'
        ]
        extra_kwargs = {
            'curriculum_system': {'write_only': True},
            'category': {'write_only': True},
            'education_levels': {'write_only': True}
        }

class AssessmentMethodSerializer(serializers.ModelSerializer):
    curriculum_system_details = CurriculumSystemSerializer(source='curriculum_system', read_only=True)
    
    class Meta:
        model = AssessmentMethod
        fields = [
            'id', 'name', 'code', 'curriculum_system', 'curriculum_system_details',
            'description', 'assessment_type', 'weight_percentage', 'system_specific_data'
        ]
        extra_kwargs = {
            'curriculum_system': {'write_only': True}
        }

class SubjectAssessmentSerializer(serializers.ModelSerializer):
    subject_details = CurriculumSubjectSerializer(source='subject', read_only=True)
    assessment_method_details = AssessmentMethodSerializer(source='assessment_method', read_only=True)
    education_level_details = EducationLevelSerializer(source='education_level', read_only=True)
    
    class Meta:
        model = SubjectAssessment
        fields = [
            'id', 'subject', 'subject_details', 'assessment_method', 'assessment_method_details',
            'education_level', 'education_level_details', 'weight_percentage', 'details'
        ]
        extra_kwargs = {
            'subject': {'write_only': True},
            'assessment_method': {'write_only': True},
            'education_level': {'write_only': True}
        }
