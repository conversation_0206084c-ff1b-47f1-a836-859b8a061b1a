from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth.models import Group
from .models import SchoolBranch

@receiver(post_save, sender=SchoolBranch)
def create_branch_groups(sender, instance, created, **kwargs):
    if created:
        default_groups = [
            'BranchAdmin',
            'Teachers',
            'Students',
            'Parents',
            'Finance',
            'Logistics',
            'IT Support',
            'Administrative Assistants',
            'Maintenance',
            'Accountants',
            'Bursars',
            'Transport Managers',
            'Inventory Managers',
            'Facility Managers',
        ]
        for group_name in default_groups:
            Group.objects.create(
                name=f"{instance.code}_{group_name}"
            )