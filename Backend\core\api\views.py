from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from .serializers import CustomUserSerializer, UserProfileSerializer
from profiles.models import UserProfile
from .schemas import (
    user_create_schema, user_update_schema, change_password_schema,
    reset_password_schema, activate_user_schema, deactivate_user_schema
)

CustomUser = get_user_model()

class CustomUserViewSet(viewsets.ModelViewSet):
    queryset = CustomUser.objects.all()
    serializer_class = CustomUserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_permissions(self):
        if self.action in ['create', 'reset_password']:
            return [permissions.AllowAny()]
        return super().get_permissions()

    def get_queryset(self):
        user = self.request.user
        if user.is_superuser:
            return CustomUser.objects.all()
        elif user.user_type == 'school_admin':
            return CustomUser.objects.filter(profile__school=user.profile.school)
        elif user.user_type == 'branch_admin':
            return CustomUser.objects.filter(profile__school_branch=user.profile.school_branch)
        return CustomUser.objects.filter(id=user.id)

    @user_create_schema
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @user_update_schema
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @user_update_schema
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    @action(detail=False, methods=['get'])
    def me(self, request):
        serializer = self.get_serializer(request.user)
        return Response(serializer.data)

    @reset_password_schema
    @action(detail=False, methods=['post'])
    def reset_password(self, request):
        email = request.data.get('email')
        try:
            user = CustomUser.objects.get(email=email)
            # Send password reset email
            user.send_password_reset_email()
            return Response({'detail': 'Password reset email sent.'})
        except CustomUser.DoesNotExist:
            return Response(
                {'detail': 'User not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

    @change_password_schema
    @action(detail=True, methods=['post'])
    def change_password(self, request, pk=None):
        user = self.get_object()
        old_password = request.data.get('old_password')
        new_password = request.data.get('new_password')
        
        if not user.check_password(old_password):
            return Response(
                {'detail': 'Invalid old password.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        user.set_password(new_password)
        user.save()
        return Response({'detail': 'Password changed successfully.'})

    @deactivate_user_schema
    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        user = self.get_object()
        user.is_active = False
        user.save()
        return Response({'detail': 'User deactivated successfully.'})

    @activate_user_schema
    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        user = self.get_object()
        user.is_active = True
        user.save()
        return Response({'detail': 'User activated successfully.'}) 