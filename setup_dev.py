#!/usr/bin/env python
"""
ShuleXcel Development Setup Script

This script sets up the development environment for ShuleXcel.
"""

import os
import sys
import subprocess
import platform

def run_command(command, cwd=None):
    """Run a command and return success status"""
    try:
        print(f"Running: {command}")
        result = subprocess.run(command, shell=True, cwd=cwd, check=True)
        print(f"✅ Success: {command}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running {command}: {e}")
        return False

def setup_backend():
    """Setup backend development environment"""
    print("🔧 Setting up Backend...")
    
    backend_dir = "Backend"
    
    # Check if virtual environment exists
    venv_path = os.path.join(backend_dir, "venv")
    if not os.path.exists(venv_path):
        print("Creating virtual environment...")
        if not run_command("python -m venv venv", cwd=backend_dir):
            return False
    
    # Activate virtual environment and install dependencies
    if platform.system() == "Windows":
        pip_cmd = os.path.join(venv_path, "Scripts", "pip")
        python_cmd = os.path.join(venv_path, "Scripts", "python")
    else:
        pip_cmd = os.path.join(venv_path, "bin", "pip")
        python_cmd = os.path.join(venv_path, "bin", "python")
    
    # Install dependencies
    if not run_command(f"{pip_cmd} install -r requirements.txt", cwd=backend_dir):
        return False
    
    # Run migrations
    if not run_command(f"{python_cmd} manage.py migrate", cwd=backend_dir):
        return False
    
    # Collect static files
    if not run_command(f"{python_cmd} manage.py collectstatic --noinput", cwd=backend_dir):
        return False
    
    # Setup MVP data
    if not run_command(f"{python_cmd} setup_mvp.py", cwd=backend_dir):
        return False
    
    print("✅ Backend setup completed!")
    return True

def setup_frontend():
    """Setup frontend development environment"""
    print("🎨 Setting up Frontend...")
    
    frontend_dir = "Frontend"
    
    # Install dependencies
    if not run_command("npm install", cwd=frontend_dir):
        return False
    
    print("✅ Frontend setup completed!")
    return True

def create_dev_scripts():
    """Create development scripts"""
    print("📝 Creating development scripts...")
    
    # Create start_dev.py script
    start_dev_content = '''#!/usr/bin/env python
"""
Start ShuleXcel development servers
"""

import subprocess
import threading
import time
import os

def start_backend():
    """Start Django development server"""
    os.chdir("Backend")
    if os.name == 'nt':  # Windows
        subprocess.run(["venv\\Scripts\\python", "manage.py", "runserver", "8000"])
    else:  # Unix/Linux/Mac
        subprocess.run(["venv/bin/python", "manage.py", "runserver", "8000"])

def start_frontend():
    """Start React development server"""
    os.chdir("Frontend")
    subprocess.run(["npm", "run", "dev"])

if __name__ == "__main__":
    print("🚀 Starting ShuleXcel Development Servers...")
    print("Backend will be available at: http://localhost:8000")
    print("Frontend will be available at: http://localhost:5173")
    print("Press Ctrl+C to stop both servers")
    
    # Start backend in a separate thread
    backend_thread = threading.Thread(target=start_backend)
    backend_thread.daemon = True
    backend_thread.start()
    
    # Give backend time to start
    time.sleep(3)
    
    # Start frontend (this will block)
    try:
        start_frontend()
    except KeyboardInterrupt:
        print("\\n👋 Shutting down development servers...")
'''
    
    with open("start_dev.py", "w") as f:
        f.write(start_dev_content)
    
    # Make executable on Unix systems
    if platform.system() != "Windows":
        os.chmod("start_dev.py", 0o755)
    
    print("✅ Development scripts created!")
    return True

def main():
    """Main setup function"""
    print("🚀 ShuleXcel Development Environment Setup")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("Backend") or not os.path.exists("Frontend"):
        print("❌ Error: Please run this script from the ShuleXcelApp root directory")
        sys.exit(1)
    
    success = True
    
    # Setup backend
    if not setup_backend():
        success = False
    
    # Setup frontend
    if not setup_frontend():
        success = False
    
    # Create development scripts
    if not create_dev_scripts():
        success = False
    
    if success:
        print("\n" + "=" * 50)
        print("🎉 Development environment setup completed!")
        print("\nTo start development servers:")
        print("  python start_dev.py")
        print("\nDefault admin credentials:")
        print("  Email: <EMAIL>")
        print("  Password: admin123")
        print("\nHappy coding! 🚀")
    else:
        print("\n❌ Setup failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
