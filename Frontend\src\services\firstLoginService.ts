import axiosInstance from '../components/utils/AxiosInstance';
import { authService } from './authService.ts';

export const firstLoginService = {
  /**
   * Check if this is the user's first login
   * @returns {Promise<boolean>} True if this is the first login, false otherwise
   */
  isFirstLogin: async (): Promise<boolean> => {
    try {
      // Get current user data from localStorage first (faster)
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        const user = JSON.parse(storedUser);

        // Check if the user has completed their profile
        // Multiple conditions that indicate a first login
        const isFirstLogin = user && (
          !user.first_name ||
          !user.last_name ||
          user.is_first_login === true ||
          user.is_first_login === 'true' // Handle string values
        );

        if (isFirstLogin) {
          return true;
        }
      }

      // If not found in localStorage or unclear, avoid API calls during initial load
      // to prevent authentication loops. Return false to be safe.
      console.warn('User data not found in localStorage, assuming not first login to avoid loops');
      return false;
    } catch (error) {
      console.error('Error checking first login status:', error);
      return false;
    }
  },

  /**
   * Mark the user's first login as complete
   * @returns {Promise<boolean>} True if successful, false otherwise
   */
  completeFirstLogin: async (): Promise<boolean> => {
    try {
      // Use the correct API endpoint
      const response = await axiosInstance.post('users/me/complete-first-login/');

      // If the API call succeeds, return true
      if (response.status === 200) {
        return true;
      }

      // No mock data - we want to work with real data only
      console.error('Failed to complete first login. Please ensure the backend is running.');
      return false;
    } catch (error: unknown) {
      console.error('Error completing first login:', error);

      // No mock data - we want to work with real data only
      console.error('Failed to complete first login. Please ensure the backend is running.');
      return false;
    }
  }
};

export default firstLoginService;
