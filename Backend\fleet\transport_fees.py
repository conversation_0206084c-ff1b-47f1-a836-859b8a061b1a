from django.db import models
from django.utils import timezone
from schools.models import SchoolBranch
from fees.models import FeeType, FeeStructure
from .models import Route, StudentTransport

class TransportFee(models.Model):
    """
    Links transport routes with fee types to manage transport fees
    """
    route = models.ForeignKey(Route, on_delete=models.CASCADE, related_name='transport_fees')
    fee_type = models.ForeignKey(FeeType, on_delete=models.CASCADE, related_name='transport_routes')
    fee_structure = models.ForeignKey(FeeStructure, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    is_active = models.BooleanField(default=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['route', 'fee_structure']
        
    def __str__(self):
        return f"{self.route.name} - {self.fee_structure.name} - {self.amount}"

class TransportFeeDiscount(models.Model):
    """
    Discounts for transport fees
    """
    DISCOUNT_TYPE_CHOICES = [
        ('FIXED', 'Fixed Amount'),
        ('PERCENTAGE', 'Percentage'),
    ]
    
    name = models.CharField(max_length=100)
    transport_fee = models.ForeignKey(TransportFee, on_delete=models.CASCADE, related_name='discounts')
    discount_type = models.CharField(max_length=10, choices=DISCOUNT_TYPE_CHOICES)
    amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    percentage = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    start_date = models.DateField()
    end_date = models.DateField()
    is_active = models.BooleanField(default=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} - {self.transport_fee.route.name}"
    
    def calculate_discount(self, original_amount):
        """
        Calculate the discount amount
        """
        if not self.is_active:
            return 0
            
        today = timezone.now().date()
        if today < self.start_date or today > self.end_date:
            return 0
            
        if self.discount_type == 'FIXED':
            return min(self.amount, original_amount)
        else:  # PERCENTAGE
            return (self.percentage / 100) * original_amount
