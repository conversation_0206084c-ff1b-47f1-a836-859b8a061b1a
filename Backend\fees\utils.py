from decimal import Decimal
from django.db.models import Sum
from .models import FeeStructure, FeeType, FeeBalance

def calculate_student_fees(student, term):
    """
    Calculate all applicable fees for a student for a specific term
    
    Args:
        student: The student object
        term: The term object
        
    Returns:
        dict: A dictionary containing fee details
    """
    # Get the fee structure for this term
    try:
        fee_structure = FeeStructure.objects.get(
            school=student.school_branch.school,
            term=term,
            is_active=True
        )
    except FeeStructure.DoesNotExist:
        return {
            'total': 0,
            'items': []
        }
    
    # Get all fee types for this structure
    fee_types = FeeType.objects.filter(
        fee_structure=fee_structure
    )
    
    # Build the fee items list
    fee_items = []
    for fee_type in fee_types:
        fee_items.append({
            'type': 'standard',
            'name': fee_type.name,
            'description': fee_type.description,
            'amount': float(fee_type.amount),
            'mandatory': fee_type.is_mandatory
        })
    
    # Add transport fee if applicable
    try:
        # Import here to avoid circular imports
        from fleet.utils import calculate_transport_fee, should_include_transport_fee
        
        if should_include_transport_fee(student, student.school_branch):
            transport_fee = calculate_transport_fee(student, term)
            if transport_fee:
                fee_items.append(transport_fee)
    except ImportError:
        # Fleet module might not be installed
        pass
    
    # Calculate total
    total = sum(item['amount'] if 'final_amount' not in item else item['final_amount'] for item in fee_items)
    
    return {
        'total': total,
        'items': fee_items
    }

def get_student_fee_balance(student, term):
    """
    Get the current fee balance for a student for a specific term
    
    Args:
        student: The student object
        term: The term object
        
    Returns:
        dict: A dictionary containing balance details
    """
    # Calculate total fees
    fees = calculate_student_fees(student, term)
    total_fees = fees['total']
    
    # Get total payments
    try:
        balance = FeeBalance.objects.get(
            student=student,
            term=term
        )
        amount_paid = balance.amount_paid
    except FeeBalance.DoesNotExist:
        amount_paid = 0
        
        # Create a new balance record
        balance = FeeBalance.objects.create(
            student=student,
            term=term,
            total_amount=total_fees,
            amount_paid=0,
            balance=total_fees
        )
    
    # Calculate remaining balance
    remaining = total_fees - amount_paid
    
    return {
        'total_fees': total_fees,
        'amount_paid': amount_paid,
        'balance': remaining,
        'fee_items': fees['items']
    }
