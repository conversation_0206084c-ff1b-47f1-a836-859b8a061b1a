import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
from datetime import datetime, timedelta
from .models import FeePayment, FeeBalance, FeeStructure

class FeeForecastingSystem:
    def forecast_collections(self, school, term, forecast_period=30):
        """Forecast fee collections for the next period"""
        historical_data = self._get_historical_collections(school)
        
        forecast = {
            'expected_collections': self._predict_collections(historical_data, forecast_period),
            'collection_trends': self._analyze_collection_trends(historical_data),
            'risk_factors': self._identify_risk_factors(school, term),
            'recommendations': self._generate_collection_recommendations(school, term)
        }
        
        return forecast

    def predict_default_risk(self, school, term):
        """Predict students at risk of defaulting"""
        current_balances = FeeBalance.objects.filter(
            student__school=school,
            term=term
        ).select_related('student')
        
        risk_analysis = []
        for balance in current_balances:
            risk_score = self._calculate_default_risk_score(balance)
            if risk_score > 0.3:  # Risk threshold
                risk_analysis.append({
                    'student': balance.student.get_full_name(),
                    'risk_score': risk_score,
                    'factors': self._get_risk_factors(balance),
                    'recommended_actions': self._suggest_risk_mitigation(risk_score)
                })
        
        return risk_analysis

    def generate_budget_forecast(self, school, upcoming_term):
        """Generate budget forecasts for upcoming term"""
        return {
            'expected_income': self._forecast_fee_income(school, upcoming_term),
            'collection_schedule': self._predict_collection_schedule(school, upcoming_term),
            'cash_flow_projection': self._project_cash_flow(school, upcoming_term),
            'risk_adjusted_forecast': self._calculate_risk_adjusted_forecast(school, upcoming_term)
        }

    def _predict_collections(self, historical_data, forecast_period):
        """Use time series analysis to predict future collections"""
        model = LinearRegression()
        X = historical_data['time_features']
        y = historical_data['collection_amounts']
        
        model.fit(X, y)
        future_features = self._generate_future_features(forecast_period)
        predictions = model.predict(future_features)
        
        return {
            'daily_predictions': predictions.tolist(),
            'total_expected': float(predictions.sum()),
            'confidence_interval': self._calculate_confidence_interval(predictions)
        }

    def _analyze_collection_trends(self, historical_data):
        """Analyze patterns in fee collection"""
        return {
            'peak_collection_periods': self._identify_peak_periods(historical_data),
            'seasonal_patterns': self._analyze_seasonality(historical_data),
            'payment_method_trends': self._analyze_payment_methods(historical_data)
        }

    def _calculate_default_risk_score(self, balance):
        """Calculate risk score based on multiple factors"""
        factors = {
            'payment_history': self._analyze_payment_history(balance.student),
            'current_balance_ratio': balance.balance / balance.total_amount,
            'payment_consistency': self._calculate_payment_consistency(balance.student),
            'previous_defaults': self._get_previous_defaults(balance.student)
        }
        
        weights = {
            'payment_history': 0.3,
            'current_balance_ratio': 0.25,
            'payment_consistency': 0.25,
            'previous_defaults': 0.2
        }
        
        return sum(score * weights[factor] for factor, score in factors.items())

    def _forecast_fee_income(self, school, term):
        """Forecast expected fee income for upcoming term"""
        # Get fee structure
        fee_structure = FeeStructure.objects.filter(school=school, term=term)
        total_expected = sum(fee.amount for fee in fee_structure)
        
        # Adjust for historical collection rate
        historical_collection_rate = self._get_historical_collection_rate(school)
        
        # Consider enrolled students
        enrolled_students = self._get_enrolled_students(school, term)
        
        # Calculate expected income
        base_forecast = total_expected * enrolled_students * historical_collection_rate
        
        # Adjust for concessions and scholarships
        concession_adjustment = self._calculate_concession_impact(school, term)
        
        return {
            'base_forecast': base_forecast,
            'adjusted_forecast': base_forecast - concession_adjustment,
            'collection_rate': historical_collection_rate,
            'student_count': enrolled_students
        }

    def _project_cash_flow(self, school, term):
        """Project detailed cash flow for the term"""
        weekly_projections = []
        term_weeks = self._get_term_weeks(term)
        
        for week in range(term_weeks):
            projection = {
                'week': week + 1,
                'expected_income': self._predict_weekly_income(school, term, week),
                'collection_probability': self._calculate_collection_probability(week),
                'cumulative_projection': self._calculate_cumulative_projection(weekly_projections)
            }
            weekly_projections.append(projection)
        
        return weekly_projections

    def _calculate_risk_adjusted_forecast(self, school, term):
        """Calculate risk-adjusted fee forecasts"""
        base_forecast = self._forecast_fee_income(school, term)
        risk_factors = self._assess_forecast_risks(school, term)
        
        scenarios = {
            'optimistic': self._adjust_forecast(base_forecast, risk_factors, 'optimistic'),
            'realistic': self._adjust_forecast(base_forecast, risk_factors, 'realistic'),
            'conservative': self._adjust_forecast(base_forecast, risk_factors, 'conservative')
        }
        
        return {
            'scenarios': scenarios,
            'risk_factors': risk_factors,
            'recommended_scenario': self._determine_recommended_scenario(scenarios, risk_factors)
        }
