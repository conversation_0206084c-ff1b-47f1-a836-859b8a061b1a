import { Navigate, useLocation } from 'react-router-dom';
import { useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import type { UserType } from '../config/apiEndpoints';
import { hasAnyRole } from '../utils/roleUtils.ts';
import { authService } from '../services/authService.ts';
import { getRoleBasedHomePath } from '../utils/routeUtils';

interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles?: UserType[];
  requiredGroups?: string[];
}

export const ProtectedRoute = ({ children, allowedRoles, requiredGroups }: ProtectedRouteProps) => {
  const { isAuthenticated, user, refreshToken, logout } = useAuth();
  const location = useLocation();

  // Token expiration is now handled by AuthContext and axios interceptors
  // Removed redundant token refresh logic to prevent conflicts

  if (!isAuthenticated) {
    // Save the current location to redirect back after login
    return <Navigate to="/signin" state={{ from: location }} replace />;
  }

  // Check if user is trying to access the root path and redirect to role-specific dashboard
  if (location.pathname === '/' && user?.user_type) {
    const homePath = getRoleBasedHomePath(user);
    if (homePath !== '/') {
      return <Navigate to={homePath} replace />;
    }
  }

  // Check user type if allowedRoles is provided
  if (allowedRoles && user?.user_type) {
    // System admins and superusers have access to all routes
    if (user.is_superuser || user.user_type === 'system_admin') {
      // Allow access
    } else if (!allowedRoles.includes(user.user_type as UserType)) {
      // Redirect to the user's role-specific dashboard instead of unauthorized page
      const homePath = getRoleBasedHomePath(user);
      return <Navigate to={homePath} replace />;
    }
  }

  // Check user groups if requiredGroups is provided
  if (requiredGroups && requiredGroups.length > 0 && !hasAnyRole(user, requiredGroups)) {
    // Redirect to the user's role-specific dashboard instead of unauthorized page
    const homePath = getRoleBasedHomePath(user);
    return <Navigate to={homePath} replace />;
  }

  return <>{children}</>;
};
