from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from django.db.models import Sum, Avg, Count, Q
from datetime import datetime, timedelta

from .models import (
    Vehicle, Driver, Route, Schedule, VehicleMaintenance,
    StudentTransport, TransportAttendance, FuelConsumption
)
from .serializers import (
    VehicleSerializer, DriverSerializer, RouteSerializer, ScheduleSerializer,
    VehicleMaintenanceSerializer, StudentTransportSerializer, TransportAttendanceSerializer,
    FuelConsumptionSerializer, FleetDashboardSerializer, VehicleReportSerializer
)
from core.permissions import BranchBasedPermission

class VehicleViewSet(viewsets.ModelViewSet):
    serializer_class = VehicleSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'registration_number', 'model', 'manufacturer']
    ordering_fields = ['name', 'status', 'purchase_date', 'created_at']
    ordering = ['name']

    def get_queryset(self):
        # Get school_branch from the user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            return Vehicle.objects.none()
            
        queryset = Vehicle.objects.filter(school_branch=profile.school_branch)

        # Filter by status
        status = self.request.query_params.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Filter by vehicle type
        vehicle_type = self.request.query_params.get('vehicle_type')
        if vehicle_type:
            queryset = queryset.filter(vehicle_type=vehicle_type)

        return queryset

    def perform_create(self, serializer):
        # Get school_branch from the user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            raise PermissionError("User does not have an associated school branch")
        serializer.save(school_branch=profile.school_branch)

    @action(detail=True, methods=['get'])
    def maintenance_history(self, request, pk=None):
        vehicle = self.get_object()
        maintenance = VehicleMaintenance.objects.filter(vehicle=vehicle)
        serializer = VehicleMaintenanceSerializer(maintenance, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def fuel_history(self, request, pk=None):
        vehicle = self.get_object()
        fuel_records = FuelConsumption.objects.filter(vehicle=vehicle)
        serializer = FuelConsumptionSerializer(fuel_records, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def schedules(self, request, pk=None):
        vehicle = self.get_object()
        schedules = Schedule.objects.filter(vehicle=vehicle)
        serializer = ScheduleSerializer(schedules, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def report(self, request, pk=None):
        vehicle = self.get_object()

        # Get maintenance costs
        maintenance_cost = VehicleMaintenance.objects.filter(
            vehicle=vehicle,
            status='COMPLETED'
        ).aggregate(total=Sum('cost'))['total'] or 0

        # Get fuel costs
        fuel_cost = FuelConsumption.objects.filter(
            vehicle=vehicle
        ).aggregate(total=Sum('cost'))['total'] or 0

        # Calculate distance traveled (based on odometer readings)
        fuel_records = FuelConsumption.objects.filter(vehicle=vehicle).order_by('date')
        if fuel_records.exists():
            first_record = fuel_records.first()
            last_record = fuel_records.last()
            distance_traveled = last_record.odometer_reading - first_record.odometer_reading
        else:
            distance_traveled = 0

        # Calculate fuel efficiency
        total_fuel = FuelConsumption.objects.filter(vehicle=vehicle).aggregate(
            total=Sum('fuel_amount'))['total'] or 0
        fuel_efficiency = distance_traveled / total_fuel if total_fuel > 0 else 0

        # Get maintenance history
        maintenance_history = VehicleMaintenance.objects.filter(
            vehicle=vehicle
        ).order_by('-scheduled_date')[:10]

        # Get fuel history
        fuel_history = FuelConsumption.objects.filter(
            vehicle=vehicle
        ).order_by('-date')[:10]

        # Calculate utilization rate (based on schedules)
        total_days = (timezone.now().date() - vehicle.purchase_date).days
        scheduled_days = Schedule.objects.filter(
            vehicle=vehicle,
            is_active=True
        ).count()
        utilization_rate = scheduled_days / total_days if total_days > 0 else 0

        # Get upcoming maintenance
        scheduled_maintenance = VehicleMaintenance.objects.filter(
            vehicle=vehicle,
            status__in=['SCHEDULED', 'IN_PROGRESS']
        ).order_by('scheduled_date')

        data = {
            'vehicle': vehicle,
            'total_maintenance_cost': maintenance_cost,
            'total_fuel_cost': fuel_cost,
            'total_distance_traveled': distance_traveled,
            'fuel_efficiency': fuel_efficiency,
            'maintenance_history': maintenance_history,
            'fuel_history': fuel_history,
            'utilization_rate': utilization_rate,
            'scheduled_maintenance': scheduled_maintenance
        }

        serializer = VehicleReportSerializer(data)
        return Response(serializer.data)

class DriverViewSet(viewsets.ModelViewSet):
    serializer_class = DriverSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['user__first_name', 'user__last_name', 'license_number']
    ordering_fields = ['user__first_name', 'user__last_name', 'status', 'created_at']
    ordering = ['user__first_name', 'user__last_name']

    def get_queryset(self):
        # Get school_branch from the user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            return Driver.objects.none()
            
        queryset = Driver.objects.filter(school_branch=profile.school_branch)

        # Filter by status
        status = self.request.query_params.get('status')
        if status:
            queryset = queryset.filter(status=status)

        return queryset

    def perform_create(self, serializer):
        # Get school_branch from the user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            raise PermissionError("User does not have an associated school branch")
        serializer.save(school_branch=profile.school_branch)

    @action(detail=True, methods=['get'])
    def schedules(self, request, pk=None):
        driver = self.get_object()
        schedules = Schedule.objects.filter(driver=driver)
        serializer = ScheduleSerializer(schedules, many=True)
        return Response(serializer.data)

class RouteViewSet(viewsets.ModelViewSet):
    serializer_class = RouteSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'start_location', 'end_location']
    ordering_fields = ['name', 'is_active', 'created_at']
    ordering = ['name']

    def get_queryset(self):
        # Get school_branch from the user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            return Route.objects.none()
            
        queryset = Route.objects.filter(school_branch=profile.school_branch)

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=(is_active.lower() == 'true'))

        return queryset

    def perform_create(self, serializer):
        # Get school_branch from the user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            raise PermissionError("User does not have an associated school branch")
        serializer.save(school_branch=profile.school_branch)

    @action(detail=True, methods=['get'])
    def schedules(self, request, pk=None):
        route = self.get_object()
        schedules = Schedule.objects.filter(route=route)
        serializer = ScheduleSerializer(schedules, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def students(self, request, pk=None):
        route = self.get_object()
        students = StudentTransport.objects.filter(route=route, is_active=True)
        serializer = StudentTransportSerializer(students, many=True)
        return Response(serializer.data)

class ScheduleViewSet(viewsets.ModelViewSet):
    serializer_class = ScheduleSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'route__name', 'vehicle__name', 'driver__user__first_name', 'driver__user__last_name']
    ordering_fields = ['name', 'departure_time', 'is_active', 'created_at']
    ordering = ['departure_time']

    def get_queryset(self):
        # Get school_branch from the user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            return Schedule.objects.none()
            
        queryset = Schedule.objects.filter(school_branch=profile.school_branch)

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=(is_active.lower() == 'true'))

        # Filter by schedule type
        schedule_type = self.request.query_params.get('schedule_type')
        if schedule_type:
            queryset = queryset.filter(schedule_type=schedule_type)

        # Filter by route
        route_id = self.request.query_params.get('route')
        if route_id:
            queryset = queryset.filter(route_id=route_id)

        # Filter by vehicle
        vehicle_id = self.request.query_params.get('vehicle')
        if vehicle_id:
            queryset = queryset.filter(vehicle_id=vehicle_id)

        # Filter by driver
        driver_id = self.request.query_params.get('driver')
        if driver_id:
            queryset = queryset.filter(driver_id=driver_id)

        return queryset

    def perform_create(self, serializer):
        # Get school_branch from the user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            raise PermissionError("User does not have an associated school branch")
        serializer.save(school_branch=profile.school_branch)

    @action(detail=True, methods=['get'])
    def attendance(self, request, pk=None):
        schedule = self.get_object()

        # Get date range from query params
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        queryset = TransportAttendance.objects.filter(schedule=schedule)

        if start_date:
            queryset = queryset.filter(date__gte=start_date)

        if end_date:
            queryset = queryset.filter(date__lte=end_date)

        serializer = TransportAttendanceSerializer(queryset, many=True)
        return Response(serializer.data)

class VehicleMaintenanceViewSet(viewsets.ModelViewSet):
    serializer_class = VehicleMaintenanceSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['vehicle__name', 'vehicle__registration_number', 'maintenance_type', 'service_provider']
    ordering_fields = ['scheduled_date', 'status', 'created_at']
    ordering = ['-scheduled_date']

    def get_queryset(self):
        # Get school_branch from the user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            return VehicleMaintenance.objects.none()
            
        queryset = VehicleMaintenance.objects.filter(school_branch=profile.school_branch)

        # Filter by status
        status = self.request.query_params.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Filter by maintenance type
        maintenance_type = self.request.query_params.get('maintenance_type')
        if maintenance_type:
            queryset = queryset.filter(maintenance_type=maintenance_type)

        # Filter by vehicle
        vehicle_id = self.request.query_params.get('vehicle')
        if vehicle_id:
            queryset = queryset.filter(vehicle_id=vehicle_id)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        if start_date:
            queryset = queryset.filter(scheduled_date__gte=start_date)

        end_date = self.request.query_params.get('end_date')
        if end_date:
            queryset = queryset.filter(scheduled_date__lte=end_date)

        return queryset

    def perform_create(self, serializer):
        # Get school_branch from the user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            raise PermissionError("User does not have an associated school branch")
        serializer.save(school_branch=profile.school_branch)

    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        maintenance = self.get_object()

        if maintenance.status != 'IN_PROGRESS':
            return Response(
                {"detail": "Only maintenance in progress can be completed."},
                status=status.HTTP_400_BAD_REQUEST
            )

        completion_date = request.data.get('completion_date', timezone.now().date())
        cost = request.data.get('cost')
        notes = request.data.get('notes')

        maintenance.status = 'COMPLETED'
        maintenance.completion_date = completion_date

        if cost:
            maintenance.cost = cost

        if notes:
            maintenance.notes = notes

        maintenance.save()

        serializer = self.get_serializer(maintenance)
        return Response(serializer.data)

class StudentTransportViewSet(viewsets.ModelViewSet):
    serializer_class = StudentTransportSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['student__user__first_name', 'student__user__last_name', 'route__name']
    ordering_fields = ['student__user__last_name', 'student__user__first_name', 'is_active', 'created_at']
    ordering = ['student__user__last_name', 'student__user__first_name']

    def get_queryset(self):
        # Get school_branch from the user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            return StudentTransport.objects.none()
            
        queryset = StudentTransport.objects.filter(school_branch=profile.school_branch)

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=(is_active.lower() == 'true'))

        # Filter by route
        route_id = self.request.query_params.get('route')
        if route_id:
            queryset = queryset.filter(route_id=route_id)

        # Filter by student
        student_id = self.request.query_params.get('student')
        if student_id:
            queryset = queryset.filter(student_id=student_id)

        # Filter by usage type
        usage_type = self.request.query_params.get('usage_type')
        if usage_type:
            queryset = queryset.filter(usage_type=usage_type)

        # Filter by payment frequency
        payment_frequency = self.request.query_params.get('payment_frequency')
        if payment_frequency:
            queryset = queryset.filter(payment_frequency=payment_frequency)

        # Filter by apply_transport_fee
        apply_fee = self.request.query_params.get('apply_transport_fee')
        if apply_fee is not None:
            queryset = queryset.filter(apply_transport_fee=(apply_fee.lower() == 'true'))

        return queryset

    def perform_create(self, serializer):
        # Get school_branch from the user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            raise PermissionError("User does not have an associated school branch")
        serializer.save(school_branch=profile.school_branch)

    @action(detail=True, methods=['get'])
    def attendance(self, request, pk=None):
        student_transport = self.get_object()

        # Get date range from query params
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        queryset = TransportAttendance.objects.filter(student_transport=student_transport)

        if start_date:
            queryset = queryset.filter(date__gte=start_date)

        if end_date:
            queryset = queryset.filter(date__lte=end_date)

        serializer = TransportAttendanceSerializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def calculate_fee(self, request, pk=None):
        """Calculate transport fee for this student based on usage pattern"""
        student_transport = self.get_object()

        # Get term from query params
        term_id = request.query_params.get('term')
        term = None

        if term_id:
            from academics.models import Term
            try:
                term = Term.objects.get(id=term_id)
            except Term.DoesNotExist:
                pass

        # Import the utility function
        from .utils import calculate_transport_fee

        # Calculate the fee
        fee_details = calculate_transport_fee(student_transport.student, term)

        if not fee_details:
            return Response(
                {"detail": "No transport fee applicable for this student."},
                status=status.HTTP_404_NOT_FOUND
            )

        return Response(fee_details)

class TransportAttendanceViewSet(viewsets.ModelViewSet):
    serializer_class = TransportAttendanceSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['student_route_assignment__student__user__first_name', 'student_route_assignment__student__user__last_name']
    ordering_fields = ['date', 'is_present']
    ordering = ['date']

    def get_queryset(self):
        # Allow drf-yasg to inspect the view without full authentication
        if getattr(self, 'swagger_fake_view', False):
            return TransportAttendance.objects.none()

        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            return TransportAttendance.objects.none()
            
        queryset = TransportAttendance.objects.filter(school_branch=profile.school_branch)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(date__range=[start_date, end_date])

        # Filter by route
        route_id = self.request.query_params.get('route')
        if route_id:
            queryset = queryset.filter(student_route_assignment__route_id=route_id)

        # Filter by student
        student_id = self.request.query_params.get('student')
        if student_id:
            queryset = queryset.filter(student_route_assignment__student_id=student_id)

        return queryset

    def perform_create(self, serializer):
        # Get school_branch from user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            raise PermissionError("User does not have an associated school branch")
            
        serializer.save(
            school_branch=profile.school_branch,
            recorded_by=self.request.user
        )

    @action(detail=False, methods=['post'])
    def bulk_create(self, request):
        schedule_id = request.data.get('schedule')
        date = request.data.get('date')
        student_transports = request.data.get('student_transports', [])

        if not schedule_id or not date:
            return Response(
                {"detail": "Schedule ID and date are required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            schedule = Schedule.objects.get(id=schedule_id, school_branch=request.user.school_branch)
        except Schedule.DoesNotExist:
            return Response(
                {"detail": "Schedule not found."},
                status=status.HTTP_404_NOT_FOUND
            )

        created_records = []

        for st_data in student_transports:
            student_transport_id = st_data.get('student_transport')
            picked_up = st_data.get('picked_up', False)
            dropped_off = st_data.get('dropped_off', False)
            pickup_time = st_data.get('pickup_time')
            dropoff_time = st_data.get('dropoff_time')
            notes = st_data.get('notes')

            try:
                student_transport = StudentTransport.objects.get(
                    id=student_transport_id,
                    school_branch=request.user.school_branch
                )

                attendance, created = TransportAttendance.objects.update_or_create(
                    student_transport=student_transport,
                    schedule=schedule,
                    date=date,
                    defaults={
                        'picked_up': picked_up,
                        'dropped_off': dropped_off,
                        'pickup_time': pickup_time,
                        'dropoff_time': dropoff_time,
                        'notes': notes,
                        'recorded_by': request.user,
                        'school_branch': request.user.school_branch
                    }
                )

                created_records.append(attendance)

            except StudentTransport.DoesNotExist:
                continue

        serializer = self.get_serializer(created_records, many=True)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

class FuelConsumptionViewSet(viewsets.ModelViewSet):
    serializer_class = FuelConsumptionSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['vehicle__name', 'vehicle__registration_number', 'fuel_station']
    ordering_fields = ['date', 'created_at']
    ordering = ['-date']

    def get_queryset(self):
        # Get school_branch from the user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            return FuelConsumption.objects.none()
            
        queryset = FuelConsumption.objects.filter(school_branch=profile.school_branch)

        # Filter by vehicle
        vehicle_id = self.request.query_params.get('vehicle')
        if vehicle_id:
            queryset = queryset.filter(vehicle_id=vehicle_id)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        if start_date:
            queryset = queryset.filter(date__gte=start_date)

        end_date = self.request.query_params.get('end_date')
        if end_date:
            queryset = queryset.filter(date__lte=end_date)

        return queryset

    def perform_create(self, serializer):
        # Get school_branch from the user's profile
        profile = self.request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            raise PermissionError("User does not have an associated school branch")
        serializer.save(
            school_branch=profile.school_branch,
            filled_by=self.request.user
        )

class FleetDashboardViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated, BranchBasedPermission]

    def list(self, request):
        # Get school_branch from the user's profile
        profile = request.user.profile
        if not profile or not hasattr(profile, 'school_branch'):
            return Response({"detail": "User does not have an associated school branch"}, status=400)
            
        school_branch = profile.school_branch

        # Get vehicle statistics
        total_vehicles = Vehicle.objects.filter(school_branch=school_branch).count()
        active_vehicles = Vehicle.objects.filter(school_branch=school_branch, status='ACTIVE').count()
        maintenance_vehicles = Vehicle.objects.filter(school_branch=school_branch, status='MAINTENANCE').count()

        # Get driver statistics
        total_drivers = Driver.objects.filter(school_branch=school_branch).count()
        active_drivers = Driver.objects.filter(school_branch=school_branch, status='ACTIVE').count()

        # Get route statistics
        total_routes = Route.objects.filter(school_branch=school_branch).count()
        active_routes = Route.objects.filter(school_branch=school_branch, is_active=True).count()

        # Get student transport statistics
        total_students_using_transport = StudentTransport.objects.filter(
            school_branch=school_branch,
            is_active=True
        ).count()

        # Get upcoming maintenance
        upcoming_maintenance = VehicleMaintenance.objects.filter(
            school_branch=school_branch,
            status__in=['SCHEDULED', 'IN_PROGRESS'],
            scheduled_date__gte=timezone.now().date()
        ).order_by('scheduled_date')[:5]

        # Get recent fuel consumption
        recent_fuel_consumption = FuelConsumption.objects.filter(
            school_branch=school_branch
        ).order_by('-date')[:5]

        # Calculate vehicle utilization
        vehicle_utilization = {}
        vehicles = Vehicle.objects.filter(school_branch=school_branch, status='ACTIVE')

        for vehicle in vehicles:
            schedules_count = Schedule.objects.filter(
                vehicle=vehicle,
                is_active=True
            ).count()

            # Calculate utilization as percentage of days with schedules
            total_days = (timezone.now().date() - vehicle.purchase_date).days
            utilization = (schedules_count / total_days * 100) if total_days > 0 else 0
            vehicle_utilization[vehicle.name] = round(utilization, 2)

        data = {
            'total_vehicles': total_vehicles,
            'active_vehicles': active_vehicles,
            'maintenance_vehicles': maintenance_vehicles,
            'total_drivers': total_drivers,
            'active_drivers': active_drivers,
            'total_routes': total_routes,
            'active_routes': active_routes,
            'total_students_using_transport': total_students_using_transport,
            'upcoming_maintenance': upcoming_maintenance,
            'recent_fuel_consumption': recent_fuel_consumption,
            'vehicle_utilization': vehicle_utilization
        }

        serializer = FleetDashboardSerializer(data)
        return Response(serializer.data)
