from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.db.models import Q, Count, Avg
from django.utils import timezone

from academics.models import (
    LiveStreamingPlatform, LiveSession, SessionParticipant,
    SessionAttendance, SessionRecording, SessionChat,
    ResultPublication, PublicationAccess
)
from .enhanced_serializers import (
    LiveStreamingPlatformSerializer, LiveSessionSerializer,
    SessionParticipantSerializer, SessionAttendanceSerializer,
    SessionRecordingSerializer, SessionChatSerializer,
    ResultPublicationSerializer, PublicationAccessSerializer
)


class LiveStreamingPlatformViewSet(viewsets.ModelViewSet):
    """ViewSet for managing live streaming platforms"""
    queryset = LiveStreamingPlatform.objects.filter(is_active=True)
    serializer_class = LiveStreamingPlatformSerializer
    permission_classes = [IsAuthenticated]


class LiveSessionViewSet(viewsets.ModelViewSet):
    """ViewSet for managing live sessions"""
    serializer_class = LiveSessionSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = LiveSession.objects.all()
        class_name = self.request.query_params.get('class_name', None)
        subject = self.request.query_params.get('subject', None)
        teacher = self.request.query_params.get('teacher', None)
        status_filter = self.request.query_params.get('status', None)
        date_from = self.request.query_params.get('date_from', None)
        date_to = self.request.query_params.get('date_to', None)
        
        if class_name:
            queryset = queryset.filter(class_name_id=class_name)
        if subject:
            queryset = queryset.filter(subject_id=subject)
        if teacher:
            queryset = queryset.filter(teacher_id=teacher)
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        if date_from:
            queryset = queryset.filter(scheduled_start__gte=date_from)
        if date_to:
            queryset = queryset.filter(scheduled_start__lte=date_to)
            
        return queryset.select_related(
            'class_name', 'subject', 'teacher', 'term', 'platform'
        ).order_by('-scheduled_start')
    
    @action(detail=True, methods=['post'])
    def start_session(self, request, pk=None):
        """Start a live session"""
        session = self.get_object()
        session.status = 'live'
        session.actual_start = timezone.now()
        session.save()
        
        return Response({'message': 'Session started successfully'})
    
    @action(detail=True, methods=['post'])
    def end_session(self, request, pk=None):
        """End a live session"""
        session = self.get_object()
        session.status = 'ended'
        session.actual_end = timezone.now()
        session.save()
        
        return Response({'message': 'Session ended successfully'})
    
    @action(detail=True, methods=['post'])
    def pause_session(self, request, pk=None):
        """Pause a live session"""
        session = self.get_object()
        session.status = 'paused'
        session.save()
        
        return Response({'message': 'Session paused successfully'})
    
    @action(detail=True, methods=['get'])
    def participants(self, request, pk=None):
        """Get session participants"""
        session = self.get_object()
        participants = SessionParticipant.objects.filter(session=session)
        serializer = SessionParticipantSerializer(participants, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def attendance(self, request, pk=None):
        """Get session attendance"""
        session = self.get_object()
        attendance = SessionAttendance.objects.filter(session=session)
        serializer = SessionAttendanceSerializer(attendance, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def chat_messages(self, request, pk=None):
        """Get session chat messages"""
        session = self.get_object()
        messages = SessionChat.objects.filter(
            session=session,
            is_deleted=False
        ).order_by('timestamp')
        serializer = SessionChatSerializer(messages, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def join_session(self, request, pk=None):
        """Join a live session"""
        session = self.get_object()
        user = request.user
        
        participant, created = SessionParticipant.objects.get_or_create(
            session=session,
            user=user,
            defaults={
                'participant_type': user.user_type,
                'joined_at': timezone.now(),
                'is_present': True
            }
        )
        
        if not created:
            participant.joined_at = timezone.now()
            participant.is_present = True
            participant.save()
        
        return Response({'message': 'Joined session successfully'})
    
    @action(detail=True, methods=['post'])
    def leave_session(self, request, pk=None):
        """Leave a live session"""
        session = self.get_object()
        user = request.user
        
        try:
            participant = SessionParticipant.objects.get(
                session=session,
                user=user
            )
            participant.left_at = timezone.now()
            participant.is_present = False
            participant.save()
            
            return Response({'message': 'Left session successfully'})
        except SessionParticipant.DoesNotExist:
            return Response(
                {'error': 'Not a participant of this session'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=False, methods=['get'])
    def upcoming_sessions(self, request):
        """Get upcoming sessions for the current user"""
        user = request.user
        upcoming = LiveSession.objects.filter(
            scheduled_start__gte=timezone.now(),
            status='scheduled'
        )
        
        # Filter based on user type
        if hasattr(user, 'teacher'):
            upcoming = upcoming.filter(teacher=user.teacher)
        elif hasattr(user, 'student'):
            upcoming = upcoming.filter(class_name=user.student.class_name)
        
        serializer = self.get_serializer(upcoming, many=True)
        return Response(serializer.data)


class SessionParticipantViewSet(viewsets.ModelViewSet):
    """ViewSet for managing session participants"""
    serializer_class = SessionParticipantSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = SessionParticipant.objects.all()
        session = self.request.query_params.get('session', None)
        user = self.request.query_params.get('user', None)
        participant_type = self.request.query_params.get('participant_type', None)
        
        if session:
            queryset = queryset.filter(session_id=session)
        if user:
            queryset = queryset.filter(user_id=user)
        if participant_type:
            queryset = queryset.filter(participant_type=participant_type)
            
        return queryset.select_related('session', 'user')


class SessionAttendanceViewSet(viewsets.ModelViewSet):
    """ViewSet for managing session attendance"""
    serializer_class = SessionAttendanceSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = SessionAttendance.objects.all()
        session = self.request.query_params.get('session', None)
        student = self.request.query_params.get('student', None)
        
        if session:
            queryset = queryset.filter(session_id=session)
        if student:
            queryset = queryset.filter(student_id=student)
            
        return queryset.select_related('session', 'student')
    
    @action(detail=False, methods=['post'])
    def bulk_mark_attendance(self, request):
        """Bulk mark attendance for a session"""
        session_id = request.data.get('session_id')
        attendance_data = request.data.get('attendance_data', [])
        
        if not session_id or not attendance_data:
            return Response(
                {'error': 'session_id and attendance_data are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        session = get_object_or_404(LiveSession, id=session_id)
        updated_count = 0
        
        for attendance in attendance_data:
            student_id = attendance.get('student_id')
            is_present = attendance.get('is_present', False)
            attendance_percentage = attendance.get('attendance_percentage', 0)
            
            attendance_obj, created = SessionAttendance.objects.update_or_create(
                session=session,
                student_id=student_id,
                defaults={
                    'is_present': is_present,
                    'attendance_percentage': attendance_percentage
                }
            )
            updated_count += 1
        
        return Response({
            'message': f'Attendance marked for {updated_count} students'
        })


class SessionRecordingViewSet(viewsets.ModelViewSet):
    """ViewSet for managing session recordings"""
    serializer_class = SessionRecordingSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = SessionRecording.objects.all()
        session = self.request.query_params.get('session', None)
        is_processed = self.request.query_params.get('is_processed', None)
        
        if session:
            queryset = queryset.filter(session_id=session)
        if is_processed is not None:
            queryset = queryset.filter(is_processed=is_processed.lower() == 'true')
            
        return queryset.select_related('session')
    
    @action(detail=True, methods=['post'])
    def process_recording(self, request, pk=None):
        """Mark recording as processed"""
        recording = self.get_object()
        recording.is_processed = True
        recording.processing_status = 'completed'
        recording.save()
        
        return Response({'message': 'Recording processed successfully'})


class SessionChatViewSet(viewsets.ModelViewSet):
    """ViewSet for managing session chat"""
    serializer_class = SessionChatSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = SessionChat.objects.filter(is_deleted=False)
        session = self.request.query_params.get('session', None)
        user = self.request.query_params.get('user', None)
        message_type = self.request.query_params.get('message_type', None)
        
        if session:
            queryset = queryset.filter(session_id=session)
        if user:
            queryset = queryset.filter(user_id=user)
        if message_type:
            queryset = queryset.filter(message_type=message_type)
            
        return queryset.select_related('session', 'user').order_by('timestamp')
    
    @action(detail=True, methods=['post'])
    def flag_message(self, request, pk=None):
        """Flag a chat message"""
        message = self.get_object()
        message.is_flagged = True
        message.save()
        
        return Response({'message': 'Message flagged successfully'})
    
    @action(detail=True, methods=['post'])
    def delete_message(self, request, pk=None):
        """Delete a chat message"""
        message = self.get_object()
        message.is_deleted = True
        message.save()
        
        return Response({'message': 'Message deleted successfully'})


class ResultPublicationViewSet(viewsets.ModelViewSet):
    """ViewSet for managing result publications"""
    serializer_class = ResultPublicationSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = ResultPublication.objects.all()
        academic_year = self.request.query_params.get('academic_year', None)
        term = self.request.query_params.get('term', None)
        publication_type = self.request.query_params.get('publication_type', None)
        status_filter = self.request.query_params.get('status', None)
        
        if academic_year:
            queryset = queryset.filter(academic_year_id=academic_year)
        if term:
            queryset = queryset.filter(term_id=term)
        if publication_type:
            queryset = queryset.filter(publication_type=publication_type)
        if status_filter:
            queryset = queryset.filter(status=status_filter)
            
        return queryset.select_related(
            'academic_year', 'term', 'created_by', 'reviewed_by', 'approved_by'
        ).order_by('-created_at')
    
    @action(detail=True, methods=['post'])
    def publish(self, request, pk=None):
        """Publish results"""
        publication = self.get_object()
        publication.status = 'published'
        publication.published_at = timezone.now()
        publication.save()
        
        return Response({'message': 'Results published successfully'})
    
    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve result publication"""
        publication = self.get_object()
        publication.status = 'approved'
        publication.approved_by = request.user.teacher
        publication.save()
        
        return Response({'message': 'Publication approved successfully'})
    
    @action(detail=True, methods=['get'])
    def access_log(self, request, pk=None):
        """Get publication access log"""
        publication = self.get_object()
        access_log = PublicationAccess.objects.filter(publication=publication)
        serializer = PublicationAccessSerializer(access_log, many=True)
        return Response(serializer.data)


class PublicationAccessViewSet(viewsets.ModelViewSet):
    """ViewSet for managing publication access"""
    serializer_class = PublicationAccessSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = PublicationAccess.objects.all()
        publication = self.request.query_params.get('publication', None)
        user = self.request.query_params.get('user', None)
        
        if publication:
            queryset = queryset.filter(publication_id=publication)
        if user:
            queryset = queryset.filter(user_id=user)
            
        return queryset.select_related('publication', 'user')
