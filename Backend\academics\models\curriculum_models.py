from django.db import models
from schools.models import School, SchoolBranch

class CurriculumSystem(models.Model):
    """Curriculum system model (e.g., CBC, 8-4-4)"""
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20, unique=True)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Curriculum System"
        verbose_name_plural = "Curriculum Systems"


class SchoolCurriculumConfig(models.Model):
    """School curriculum configuration model"""
    school = models.ForeignKey(School, on_delete=models.CASCADE, related_name='curriculum_config')
    primary_curriculum = models.ForeignKey(
        CurriculumSystem,
        on_delete=models.PROTECT,
        related_name='primary_schools'
    )
    secondary_curriculum = models.ForeignKey(
        CurriculumSystem,
        on_delete=models.PROTECT,
        related_name='secondary_schools'
    )

    # For schools that might be transitioning between systems
    is_transition_period = models.BooleanField(default=False)
    transition_details = models.TextField(blank=True, null=True)

    # Flag to indicate if this configuration was auto-created and needs review
    is_provisional = models.BooleanField(default=False, help_text="Indicates if this configuration was auto-created and needs review")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Curriculum Config for {self.school.name}"

    class Meta:
        verbose_name = "School Curriculum Configuration"
        verbose_name_plural = "School Curriculum Configurations"


class BranchCurriculumConfig(models.Model):
    """Branch-specific curriculum configuration"""
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='curriculum_config')
    primary_curriculum = models.ForeignKey(
        CurriculumSystem,
        on_delete=models.PROTECT,
        related_name='primary_branches'
    )
    secondary_curriculum = models.ForeignKey(
        CurriculumSystem,
        on_delete=models.PROTECT,
        related_name='secondary_branches'
    )

    # For branches that might be transitioning between systems
    is_transition_period = models.BooleanField(default=False)
    transition_details = models.TextField(blank=True, null=True)

    # Track if this config is inherited from the school or customized for the branch
    is_inherited_from_school = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Curriculum Config for {self.school_branch.name}"

    class Meta:
        verbose_name = "Branch Curriculum Configuration"
        verbose_name_plural = "Branch Curriculum Configurations"


class CurriculumPropagationHistory(models.Model):
    """
    Tracks history of curriculum propagation from schools to branches
    """
    school = models.ForeignKey(School, on_delete=models.CASCADE, related_name='propagation_history')
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='propagation_history')
    user = models.ForeignKey('core.CustomUser', on_delete=models.SET_NULL, null=True, related_name='propagation_history')
    propagated_at = models.DateTimeField(auto_now_add=True)

    # Store the configuration details at the time of propagation
    primary_curriculum_id = models.IntegerField()
    primary_curriculum_name = models.CharField(max_length=100)
    secondary_curriculum_id = models.IntegerField(null=True, blank=True)
    secondary_curriculum_name = models.CharField(max_length=100, null=True, blank=True)
    is_transition_period = models.BooleanField(default=False)
    transition_details = models.TextField(null=True, blank=True)

    # Status of the propagation
    status = models.CharField(max_length=20, choices=[
        ('success', 'Success'),
        ('failed', 'Failed')
    ])
    error_message = models.TextField(null=True, blank=True)

    def __str__(self):
        return f"Propagation: {self.school.name} to {self.school_branch.name} on {self.propagated_at}"

    class Meta:
        verbose_name = "Curriculum Propagation History"
        verbose_name_plural = "Curriculum Propagation History"
        ordering = ['-propagated_at']
