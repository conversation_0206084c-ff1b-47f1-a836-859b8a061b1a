# This file is intentionally left blank to avoid circular imports.

# Import all models
from .academic_models import (
    AcademicYear, Department, Stream, ClassRoom, Term, TimeSlot,
    GradingSystem, GradeScale, StreamGradingSystem, Subject
)
from .enhanced_timetable import (
    TimetableTemplate, EnhancedTimeSlot, EnhancedTimetable, TimetableEntry,
    TeacherTimetable, TimetableConflict, TimetableSubstitution
)
from .lessons import (
    LessonPlan, LessonDelivery, LessonResource, StudentLessonFeedback,
    LessonObservation, LessonSeries, LessonSeriesLesson
)
from .enhanced_exams import (
    ExamType, ExamSession, EnhancedExam, ExamRegistration, EnhancedExamResult,
    ExamMalpractice, ExamStatistics
)
from .live_streaming import (
    LiveStreamingPlatform, LiveSession, SessionParticipant, SessionAttendance,
    SessionRecording, SessionChat, ResultPublication, PublicationAccess
)
from .class_progression import (
    ClassProgressionRule, CareerPath, CareerPathSubjectRequirement,
    StudentCareerGuidance, SubjectCombination
)
from .student_progression import StudentProgression
from users.models import Student
from .assessment import Assessment, AssessmentResult
from .learning_path import LearningPath
from .smart_intervention import SmartIntervention
from .performance_analytics import PerformanceAnalytics
from .term_result import TermResult
from .student_attendance import StudentAttendance
from .teacher_performance_metrics import TeacherPerformanceMetrics

__all__ = [
    # Academic Models
    'AcademicYear', 'Department', 'Stream', 'ClassRoom', 'Term', 'TimeSlot',
    'GradingSystem', 'GradeScale', 'StreamGradingSystem', 'Subject',
    
    # Enhanced Timetable
    'TimetableTemplate', 'EnhancedTimeSlot', 'EnhancedTimetable', 'TimetableEntry',
    'TeacherTimetable', 'TimetableConflict', 'TimetableSubstitution',
    
    # Lessons
    'LessonPlan', 'LessonDelivery', 'LessonResource', 'StudentLessonFeedback',
    'LessonObservation', 'LessonSeries', 'LessonSeriesLesson',
    
    # Enhanced Exams
    'ExamType', 'ExamSession', 'EnhancedExam', 'ExamRegistration', 'EnhancedExamResult',
    'ExamMalpractice', 'ExamStatistics',
    
    # Live Streaming
    'LiveStreamingPlatform', 'LiveSession', 'SessionParticipant', 'SessionAttendance',
    'SessionRecording', 'SessionChat', 'ResultPublication', 'PublicationAccess',
    
    # Progression
    'ClassProgressionRule', 'StudentProgression', 'CareerPath', 'CareerPathSubjectRequirement',
    'StudentCareerGuidance', 'SubjectCombination',
    
    # Assessments
    'Assessment', 'AssessmentResult',
    
    # Learning Path
    'LearningPath',
    
    # Smart Intervention
    'SmartIntervention',
    
    # Performance Analytics
    'PerformanceAnalytics',
    
    # Term Result
    'TermResult',
    
    # Student Attendance
    'StudentAttendance',
    
    # Teacher Performance Metrics
    'TeacherPerformanceMetrics'
] 