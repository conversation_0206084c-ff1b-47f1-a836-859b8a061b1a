from rest_framework import serializers
from schools.models import School, SchoolBranch
from schools.serializers import SchoolSerializer, SchoolBranchSerializer
from academics.curriculum_models import (
    CurriculumSystem,
    SchoolCurriculumConfig,
    BranchCurriculumConfig,
    CurriculumConfigTemplate,
    CurriculumConfigHistory,
    CurriculumComplianceCheck,
    CurriculumComplianceNotification,
    CurriculumPropagationHistory
)
from core.Serializers import UserSerializer
from core.models import CustomUser

class CurriculumSystemSerializer(serializers.ModelSerializer):
    """Serializer for CurriculumSystem model"""
    class Meta:
        model = CurriculumSystem
        fields = '__all__'


class SchoolCurriculumConfigSerializer(serializers.ModelSerializer):
    """Serializer for SchoolCurriculumConfig model"""
    school = SchoolSerializer(read_only=True)
    school_id = serializers.PrimaryKeyRelatedField(
        source='school',
        queryset=School.objects.all(),
        write_only=True
    )
    primary_curriculum = CurriculumSystemSerializer(read_only=True)
    primary_curriculum_id = serializers.PrimaryKeyRelatedField(
        source='primary_curriculum',
        queryset=CurriculumSystem.objects.all(),
        write_only=True
    )
    secondary_curriculum = CurriculumSystemSerializer(read_only=True)
    secondary_curriculum_id = serializers.PrimaryKeyRelatedField(
        source='secondary_curriculum',
        queryset=CurriculumSystem.objects.all(),
        write_only=True
    )

    class Meta:
        model = SchoolCurriculumConfig
        fields = [
            'id', 'school', 'school_id',
            'primary_curriculum', 'primary_curriculum_id',
            'secondary_curriculum', 'secondary_curriculum_id',
            'is_transition_period', 'transition_details',
            'is_provisional', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class CurriculumConfigTemplateSerializer(serializers.ModelSerializer):
    """Serializer for CurriculumConfigTemplate model"""
    created_by = UserSerializer(read_only=True)
    created_by_id = serializers.PrimaryKeyRelatedField(
        source='created_by',
        queryset=CustomUser.objects.all(),
        write_only=True,
        required=False
    )
    school = SchoolSerializer(read_only=True)
    school_id = serializers.PrimaryKeyRelatedField(
        source='school',
        queryset=School.objects.all(),
        write_only=True,
        required=False
    )
    primary_curriculum = CurriculumSystemSerializer(read_only=True)
    primary_curriculum_id = serializers.PrimaryKeyRelatedField(
        source='primary_curriculum',
        queryset=CurriculumSystem.objects.all(),
        write_only=True
    )
    secondary_curriculum = CurriculumSystemSerializer(read_only=True)
    secondary_curriculum_id = serializers.PrimaryKeyRelatedField(
        source='secondary_curriculum',
        queryset=CurriculumSystem.objects.all(),
        write_only=True
    )

    class Meta:
        model = CurriculumConfigTemplate
        fields = [
            'id', 'name', 'description',
            'primary_curriculum', 'primary_curriculum_id',
            'secondary_curriculum', 'secondary_curriculum_id',
            'is_transition_period', 'transition_details',
            'curriculum_modifications',
            'created_by', 'created_by_id',
            'is_system_template', 'school', 'school_id',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class CurriculumConfigHistorySerializer(serializers.ModelSerializer):
    """Serializer for CurriculumConfigHistory model"""
    changed_by = UserSerializer(read_only=True)
    primary_curriculum = CurriculumSystemSerializer(read_only=True)
    secondary_curriculum = CurriculumSystemSerializer(read_only=True)
    template_used = serializers.SerializerMethodField()

    class Meta:
        model = CurriculumConfigHistory
        fields = [
            'id', 'school_config', 'changed_by',
            'primary_curriculum', 'secondary_curriculum',
            'is_transition_period', 'transition_details',
            'curriculum_modifications', 'is_provisional',
            'change_type', 'template_used', 'change_comment',
            'created_at'
        ]
        read_only_fields = ['id', 'created_at']

    def get_template_used(self, obj):
        if obj.template_used:
            return {
                'id': obj.template_used.id,
                'name': obj.template_used.name
            }
        return None


class BranchCurriculumConfigSerializer(serializers.ModelSerializer):
    """Serializer for BranchCurriculumConfig model"""
    school_branch = SchoolBranchSerializer(read_only=True)
    school_branch_id = serializers.PrimaryKeyRelatedField(
        source='school_branch',
        queryset=SchoolBranch.objects.all(),
        write_only=True
    )
    primary_curriculum = CurriculumSystemSerializer(read_only=True)
    primary_curriculum_id = serializers.PrimaryKeyRelatedField(
        source='primary_curriculum',
        queryset=CurriculumSystem.objects.all(),
        write_only=True
    )
    secondary_curriculum = CurriculumSystemSerializer(read_only=True)
    secondary_curriculum_id = serializers.PrimaryKeyRelatedField(
        source='secondary_curriculum',
        queryset=CurriculumSystem.objects.all(),
        write_only=True
    )

    class Meta:
        model = BranchCurriculumConfig
        fields = [
            'id', 'school_branch', 'school_branch_id',
            'primary_curriculum', 'primary_curriculum_id',
            'secondary_curriculum', 'secondary_curriculum_id',
            'is_transition_period', 'transition_details',
            'is_inherited_from_school',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class CurriculumComplianceCheckSerializer(serializers.ModelSerializer):
    """Serializer for CurriculumComplianceCheck model"""
    created_by = UserSerializer(read_only=True)
    created_by_id = serializers.PrimaryKeyRelatedField(
        source='created_by',
        queryset=CustomUser.objects.all(),
        write_only=True,
        required=False
    )
    template = CurriculumConfigTemplateSerializer(read_only=True)
    template_id = serializers.PrimaryKeyRelatedField(
        source='template',
        queryset=CurriculumConfigTemplate.objects.all(),
        write_only=True
    )
    school = SchoolSerializer(read_only=True)
    school_id = serializers.PrimaryKeyRelatedField(
        source='school',
        queryset=School.objects.all(),
        write_only=True,
        required=False,
        allow_null=True
    )

    class Meta:
        model = CurriculumComplianceCheck
        fields = [
            'id', 'name', 'description',
            'template', 'template_id',
            'compliance_threshold', 'frequency',
            'is_active', 'last_check_at', 'next_check_at',
            'school', 'school_id', 'school_name_filter',
            'notify_missing_configs', 'notify_school_admins', 'send_email_notifications',
            'created_by', 'created_by_id',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'last_check_at', 'created_at', 'updated_at']

    # This method is no longer needed since we're using SchoolSerializer
    # def get_school(self, obj):
    #     if obj.school:
    #         return {
    #             'id': obj.school.id,
    #             'name': obj.school.name
    #         }
    #     return None


class CurriculumComplianceNotificationSerializer(serializers.ModelSerializer):
    """Serializer for CurriculumComplianceNotification model"""
    school = SchoolSerializer(read_only=True)
    template = CurriculumConfigTemplateSerializer(read_only=True)
    scheduled_check = serializers.SerializerMethodField()
    created_by = UserSerializer(read_only=True)
    resolved_by = UserSerializer(read_only=True)

    class Meta:
        model = CurriculumComplianceNotification
        fields = [
            'id', 'school', 'template', 'scheduled_check',
            'compliance_score', 'compliance_status', 'differences',
            'is_resolved', 'resolved_at', 'resolved_by', 'resolution_comment',
            'created_by', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_scheduled_check(self, obj):
        if obj.scheduled_check:
            return {
                'id': obj.scheduled_check.id,
                'name': obj.scheduled_check.name
            }
        return None


class CurriculumPropagationHistorySerializer(serializers.ModelSerializer):
    """Serializer for CurriculumPropagationHistory model"""
    school = SchoolSerializer(read_only=True)
    school_branch = SchoolBranchSerializer(read_only=True)
    user = UserSerializer(read_only=True)

    class Meta:
        model = CurriculumPropagationHistory
        fields = '__all__'
