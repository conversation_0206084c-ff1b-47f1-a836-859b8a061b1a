from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404

from .curriculum_subjects import SubjectCategory, CurriculumSubject, AssessmentMethod, SubjectAssessment
from .curriculum_subject_serializers import (
    SubjectCategorySerializer, CurriculumSubjectSerializer,
    AssessmentMethodSerializer, SubjectAssessmentSerializer
)
from .curriculum_models import CurriculumSystem, EducationLevel
from core.permissions import RoleBasedPermissionMixin

class SubjectCategoryViewSet(RoleBasedPermissionMixin, viewsets.ModelViewSet):
    """
    API endpoint for subject categories
    """
    queryset = SubjectCategory.objects.all()
    serializer_class = SubjectCategorySerializer
    permission_classes = [IsAuthenticated]
    required_groups = ['Administration', 'School Administrators', 'Academic Staff']
    
    def get_queryset(self):
        queryset = SubjectCategory.objects.all()
        
        # Filter by curriculum system
        curriculum_system = self.request.query_params.get('curriculum_system', None)
        if curriculum_system:
            queryset = queryset.filter(curriculum_system__code=curriculum_system)
            
        return queryset
    
    @action(detail=False, methods=['get'])
    def by_curriculum(self, request):
        """Get subject categories grouped by curriculum system"""
        curriculum_systems = CurriculumSystem.objects.filter(is_active=True)
        result = {}
        
        for system in curriculum_systems:
            categories = SubjectCategory.objects.filter(curriculum_system=system)
            result[system.code] = SubjectCategorySerializer(categories, many=True).data
            
        return Response(result)

class CurriculumSubjectViewSet(RoleBasedPermissionMixin, viewsets.ModelViewSet):
    """
    API endpoint for curriculum subjects
    """
    queryset = CurriculumSubject.objects.all()
    serializer_class = CurriculumSubjectSerializer
    permission_classes = [IsAuthenticated]
    required_groups = ['Administration', 'School Administrators', 'Academic Staff']
    
    def get_queryset(self):
        queryset = CurriculumSubject.objects.all()
        
        # Filter by curriculum system
        curriculum_system = self.request.query_params.get('curriculum_system', None)
        if curriculum_system:
            queryset = queryset.filter(curriculum_system__code=curriculum_system)
            
        # Filter by category
        category = self.request.query_params.get('category', None)
        if category:
            queryset = queryset.filter(category__code=category)
            
        # Filter by education level
        education_level = self.request.query_params.get('education_level', None)
        if education_level:
            queryset = queryset.filter(education_levels__code=education_level)
            
        # Filter by compulsory status
        is_compulsory = self.request.query_params.get('is_compulsory', None)
        if is_compulsory is not None:
            is_compulsory = is_compulsory.lower() == 'true'
            queryset = queryset.filter(is_compulsory=is_compulsory)
            
        return queryset
    
    @action(detail=False, methods=['get'])
    def by_curriculum_level(self, request):
        """Get subjects for a specific curriculum and education level"""
        curriculum_code = request.query_params.get('curriculum', None)
        level_code = request.query_params.get('level', None)
        
        if not curriculum_code or not level_code:
            return Response(
                {"error": "Both curriculum and level parameters are required"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        try:
            curriculum = CurriculumSystem.objects.get(code=curriculum_code)
            level = EducationLevel.objects.get(curriculum_system=curriculum, code=level_code)
            
            subjects = CurriculumSubject.objects.filter(
                curriculum_system=curriculum,
                education_levels=level
            )
            
            serializer = self.get_serializer(subjects, many=True)
            return Response(serializer.data)
        except (CurriculumSystem.DoesNotExist, EducationLevel.DoesNotExist):
            return Response(
                {"error": "Curriculum system or education level not found"},
                status=status.HTTP_404_NOT_FOUND
            )

class AssessmentMethodViewSet(RoleBasedPermissionMixin, viewsets.ModelViewSet):
    """
    API endpoint for assessment methods
    """
    queryset = AssessmentMethod.objects.all()
    serializer_class = AssessmentMethodSerializer
    permission_classes = [IsAuthenticated]
    required_groups = ['Administration', 'School Administrators', 'Academic Staff']
    
    def get_queryset(self):
        queryset = AssessmentMethod.objects.all()
        
        # Filter by curriculum system
        curriculum_system = self.request.query_params.get('curriculum_system', None)
        if curriculum_system:
            queryset = queryset.filter(curriculum_system__code=curriculum_system)
            
        # Filter by assessment type
        assessment_type = self.request.query_params.get('assessment_type', None)
        if assessment_type:
            queryset = queryset.filter(assessment_type=assessment_type)
            
        return queryset
    
    @action(detail=False, methods=['get'])
    def by_curriculum(self, request):
        """Get assessment methods grouped by curriculum system"""
        curriculum_systems = CurriculumSystem.objects.filter(is_active=True)
        result = {}
        
        for system in curriculum_systems:
            methods = AssessmentMethod.objects.filter(curriculum_system=system)
            result[system.code] = AssessmentMethodSerializer(methods, many=True).data
            
        return Response(result)

class SubjectAssessmentViewSet(RoleBasedPermissionMixin, viewsets.ModelViewSet):
    """
    API endpoint for subject assessments
    """
    queryset = SubjectAssessment.objects.all()
    serializer_class = SubjectAssessmentSerializer
    permission_classes = [IsAuthenticated]
    required_groups = ['Administration', 'School Administrators', 'Academic Staff']
    
    def get_queryset(self):
        queryset = SubjectAssessment.objects.all()
        
        # Filter by subject
        subject_id = self.request.query_params.get('subject', None)
        if subject_id:
            queryset = queryset.filter(subject_id=subject_id)
            
        # Filter by education level
        education_level_id = self.request.query_params.get('education_level', None)
        if education_level_id:
            queryset = queryset.filter(education_level_id=education_level_id)
            
        return queryset
    
    @action(detail=False, methods=['get'])
    def by_subject(self, request, subject_pk=None):
        """Get assessments for a specific subject"""
        subject = get_object_or_404(CurriculumSubject, pk=subject_pk)
        assessments = SubjectAssessment.objects.filter(subject=subject)
        serializer = self.get_serializer(assessments, many=True)
        return Response(serializer.data)
