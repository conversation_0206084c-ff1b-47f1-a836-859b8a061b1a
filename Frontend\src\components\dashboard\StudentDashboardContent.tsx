import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { useSchool } from '../../contexts/SchoolContext';
import { getAllDashboardData } from '../../services/dashboardService';

// Import Heroicons
import {
  BookOpenIcon,
  CalendarIcon,
  ClipboardDocumentCheckIcon,
  ArrowRightIcon,
  ChartBarIcon,
  AcademicCapIcon,
  ClockIcon,
  BellIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline';

// Define types for student dashboard data
interface StudentDashboardStats {
  totalSubjects: number;
  pendingAssignments: number;
  upcomingExams: number;
  attendancePercentage: number;
}

interface SubjectInfo {
  id: number;
  name: string;
  teacher: string;
  grade: string | null;
}

interface AssignmentInfo {
  id: number;
  title: string;
  dueDate: string;
  subject: string;
  status: 'pending' | 'submitted' | 'graded';
  grade?: string;
}

interface ExamInfo {
  id: number;
  title: string;
  date: string;
  subject: string;
  duration: string;
}

interface AnnouncementInfo {
  id: number;
  title: string;
  date: string;
  from: string;
}

interface StudentDashboardData {
  stats: StudentDashboardStats;
  subjects: SubjectInfo[];
  assignments: AssignmentInfo[];
  exams: ExamInfo[];
  announcements: AnnouncementInfo[];
}

// Default dashboard data for when API calls fail
const defaultDashboardData: StudentDashboardData = {
  stats: {
    totalSubjects: 0,
    pendingAssignments: 0,
    upcomingExams: 0,
    attendancePercentage: 0
  },
  subjects: [],
  assignments: [],
  exams: [],
  announcements: []
};

const StudentDashboardContent: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<StudentDashboardData>(defaultDashboardData);
  const [loading, setLoading] = useState<boolean>(true);
  const navigate = useNavigate();

  // Get the selected school and branch from context
  const { selectedSchool, selectedBranch } = useSchool();

  useEffect(() => {
    const fetchData = async () => {
      try {
        // In a real implementation, this would call a student-specific dashboard API
        // For now, we'll adapt the admin dashboard data
        const data = await getAllDashboardData({
          schoolId: selectedSchool?.id,
          branchId: selectedBranch?.id
        });

        if (data) {
          // Use real academic data from the API
          const realSubjects = data.academicData?.subjects || [];

          setDashboardData({
            stats: {
              totalSubjects: realSubjects.length,
              pendingAssignments: 3, // Mock data - would come from assignments API
              upcomingExams: 2, // Mock data - would come from exams API
              attendancePercentage: 95 // Mock data - would come from attendance API
            },
            subjects: realSubjects.slice(0, 5).map((subject: any, index: number) => ({
              id: subject.id || index + 1,
              name: subject.name,
              teacher: 'Teacher TBD', // Would come from teacher assignment API
              grade: index % 3 === 0 ? 'A' : index % 3 === 1 ? 'B+' : null // Mock grades
            })),
            assignments: [
              // Mock data - in a real implementation, this would come from the API
              { id: 1, title: 'Algebra Problem Set', dueDate: '2025-05-15', subject: 'Mathematics', status: 'pending' },
              { id: 2, title: 'Physics Lab Report', dueDate: '2025-05-10', subject: 'Physics', status: 'submitted' },
              { id: 3, title: 'Chemistry Equations', dueDate: '2025-05-20', subject: 'Chemistry', status: 'graded', grade: 'A' },
            ],
            exams: [
              // Mock data - in a real implementation, this would come from the API
              { id: 1, title: 'Mid-Term Exam', date: '2025-06-10', subject: 'Mathematics', duration: '2 hours' },
              { id: 2, title: 'Physics Practical', date: '2025-06-15', subject: 'Physics', duration: '3 hours' },
            ],
            announcements: data.recentAnnouncements.map(announcement => ({
              id: announcement.id,
              title: announcement.title,
              date: announcement.date,
              from: 'School Administration'
            }))
          });
        } else {
          // If no data is returned, use default data
          setDashboardData(defaultDashboardData);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // If there's an error, use default data
        setDashboardData(defaultDashboardData);
      } finally {
        setLoading(false);
      }
    };

    // Only fetch data if we have a selected school and branch
    if (selectedSchool && selectedBranch) {
      setLoading(true);
      fetchData();
    } else {
      // If no school or branch is selected, use default data and stop loading
      setDashboardData(defaultDashboardData);
      setLoading(false);
    }
  }, [selectedSchool, selectedBranch]); // Re-fetch when school or branch changes

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 dark:border-blue-400"></div>
        <span className="sr-only">Loading dashboard data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white">Student Dashboard</h1>
        <div className="mt-4 md:mt-0 flex items-center space-x-3">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {selectedSchool && selectedBranch && (
              <span>
                <span className="font-medium text-gray-700 dark:text-gray-300">{selectedSchool.name}</span>
                {selectedBranch && ` - ${selectedBranch.name}`}
              </span>
            )}
          </div>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {format(new Date(), 'EEEE, MMMM d, yyyy')}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Summary Cards */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-blue-50 dark:bg-blue-900/20">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-blue-100 dark:bg-blue-800/50 rounded-lg">
                <BookOpenIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white ml-3">Subjects</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-800 dark:text-white">{dashboardData.stats.totalSubjects}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Enrolled subjects</p>
              </div>
            </div>
          </div>
          <div className="px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700">
            <button
              type="button"
              onClick={() => navigate('/academics/subjects')}
              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium flex items-center"
            >
              View Subjects
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-amber-50 dark:bg-amber-900/20">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-amber-100 dark:bg-amber-800/50 rounded-lg">
                <ClipboardDocumentCheckIcon className="h-6 w-6 text-amber-600 dark:text-amber-400" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white ml-3">Assignments</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-800 dark:text-white">{dashboardData.stats.pendingAssignments}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Pending assignments</p>
              </div>
            </div>
          </div>
          <div className="px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700">
            <button
              type="button"
              onClick={() => navigate('/assignments')}
              className="text-amber-600 dark:text-amber-400 hover:text-amber-800 dark:hover:text-amber-300 text-sm font-medium flex items-center"
            >
              View Assignments
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-purple-50 dark:bg-purple-900/20">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-purple-100 dark:bg-purple-800/50 rounded-lg">
                <AcademicCapIcon className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white ml-3">Exams</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-800 dark:text-white">{dashboardData.stats.upcomingExams}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Upcoming exams</p>
              </div>
            </div>
          </div>
          <div className="px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700">
            <button
              type="button"
              onClick={() => navigate('/exams')}
              className="text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 text-sm font-medium flex items-center"
            >
              View Exam Schedule
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-green-50 dark:bg-green-900/20">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-green-100 dark:bg-green-800/50 rounded-lg">
                <UserGroupIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white ml-3">Attendance</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-800 dark:text-white">{dashboardData.stats.attendancePercentage}%</p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">This term</p>
              </div>
            </div>
          </div>
          <div className="px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700">
            <button
              type="button"
              onClick={() => navigate('/attendance')}
              className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 text-sm font-medium flex items-center"
            >
              View Attendance
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>
      </div>

      {/* Assignments and Exams */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Pending Assignments */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white">Pending Assignments</h2>
              <button
                type="button"
                onClick={() => navigate('/assignments')}
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium flex items-center"
              >
                View All
                <ArrowRightIcon className="h-4 w-4 ml-1" />
              </button>
            </div>
            <div className="border-t border-gray-100 dark:border-gray-700 -mx-6 px-6 py-2"></div>
            <div className="mt-4">
              {dashboardData.assignments.length > 0 ? (
                <div className="space-y-4">
                  {dashboardData.assignments.filter(a => a.status === 'pending').map((assignment) => (
                    <div key={assignment.id} className="flex items-center justify-between py-3 border-b border-gray-100 dark:border-gray-700 last:border-0">
                      <div className="flex items-start space-x-3">
                        <div className="p-2 bg-amber-50 dark:bg-amber-900/20 rounded-lg mt-1">
                          <ClipboardDocumentCheckIcon className="h-5 w-5 text-amber-500 dark:text-amber-400" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-800 dark:text-white">{assignment.title}</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">{assignment.subject}</p>
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        <div className="flex items-center text-sm font-medium text-amber-600 dark:text-amber-400">
                          <ClockIcon className="h-4 w-4 mr-1" />
                          {format(new Date(assignment.dueDate), 'MMM dd')}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          Due date
                        </div>
                      </div>
                    </div>
                  ))}

                  {dashboardData.assignments.filter(a => a.status === 'pending').length === 0 && (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-full mb-3">
                        <ClipboardDocumentCheckIcon className="h-8 w-8 text-green-500 dark:text-green-400" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-1">All caught up!</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400 max-w-xs">You don't have any pending assignments.</p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <div className="p-3 bg-amber-50 dark:bg-amber-900/20 rounded-full mb-3">
                    <ClipboardDocumentCheckIcon className="h-8 w-8 text-amber-500 dark:text-amber-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-1">No assignments</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 max-w-xs">You don't have any assignments yet.</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Upcoming Exams */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white">Upcoming Exams</h2>
              <button
                type="button"
                onClick={() => navigate('/exams')}
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium flex items-center"
              >
                View All
                <ArrowRightIcon className="h-4 w-4 ml-1" />
              </button>
            </div>
            <div className="border-t border-gray-100 dark:border-gray-700 -mx-6 px-6 py-2"></div>
            <div className="mt-4">
              {dashboardData.exams.length > 0 ? (
                <div className="space-y-4">
                  {dashboardData.exams.map((exam) => (
                    <div key={exam.id} className="flex items-center justify-between py-3 border-b border-gray-100 dark:border-gray-700 last:border-0">
                      <div className="flex items-start space-x-3">
                        <div className="p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg mt-1">
                          <AcademicCapIcon className="h-5 w-5 text-purple-500 dark:text-purple-400" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-800 dark:text-white">{exam.title}</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">{exam.subject}</p>
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        <div className="flex items-center text-sm font-medium text-purple-600 dark:text-purple-400">
                          <CalendarIcon className="h-4 w-4 mr-1" />
                          {format(new Date(exam.date), 'MMM dd')}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {exam.duration}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <div className="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-full mb-3">
                    <AcademicCapIcon className="h-8 w-8 text-purple-500 dark:text-purple-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-1">No upcoming exams</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 max-w-xs">You don't have any exams scheduled yet.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Subjects and Announcements */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* My Subjects */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white">My Subjects</h2>
              <button
                type="button"
                onClick={() => navigate('/academics/subjects')}
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium flex items-center"
              >
                View All
                <ArrowRightIcon className="h-4 w-4 ml-1" />
              </button>
            </div>
            <div className="border-t border-gray-100 dark:border-gray-700 -mx-6 px-6 py-2"></div>
            <div className="mt-4">
              {dashboardData.subjects.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Subject
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Teacher
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Grade
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {dashboardData.subjects.map((subject) => (
                        <tr key={subject.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">{subject.name}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500 dark:text-gray-400">{subject.teacher}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {subject.grade ? (
                              <div className="text-sm font-medium text-gray-900 dark:text-white">{subject.grade}</div>
                            ) : (
                              <div className="text-sm text-gray-400 dark:text-gray-500">-</div>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-full mb-3">
                    <BookOpenIcon className="h-8 w-8 text-blue-500 dark:text-blue-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-1">No subjects enrolled</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 max-w-xs">You are not enrolled in any subjects yet.</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Announcements */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white">Announcements</h2>
              <button
                type="button"
                onClick={() => navigate('/announcements')}
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium flex items-center"
              >
                View All
                <ArrowRightIcon className="h-4 w-4 ml-1" />
              </button>
            </div>
            <div className="border-t border-gray-100 dark:border-gray-700 -mx-6 px-6 py-2"></div>
            <div className="mt-4">
              {dashboardData.announcements.length > 0 ? (
                <div className="space-y-4">
                  {dashboardData.announcements.map((announcement) => (
                    <div key={announcement.id} className="flex items-center justify-between py-3 border-b border-gray-100 dark:border-gray-700 last:border-0">
                      <div className="flex items-start space-x-3">
                        <div className="p-2 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg mt-1">
                          <BellIcon className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-800 dark:text-white">{announcement.title}</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">From: {announcement.from}</p>
                        </div>
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {format(new Date(announcement.date), 'MMM dd')}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <div className="p-3 bg-indigo-50 dark:bg-indigo-900/20 rounded-full mb-3">
                    <BellIcon className="h-8 w-8 text-indigo-500 dark:text-indigo-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-1">No announcements</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 max-w-xs">There are no announcements at this time.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentDashboardContent;
