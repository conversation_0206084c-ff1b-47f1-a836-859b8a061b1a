from django.db import models
from django.conf import settings

class StudentAttendance(models.Model):
    student = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='attendances')
    class_room = models.ForeignKey('academics.ClassRoom', on_delete=models.CASCADE, related_name='attendances')
    date = models.DateField()
    status = models.CharField(max_length=10, choices=[('present', 'Present'), ('absent', 'Absent'), ('late', 'Late'), ('excused', 'Excused')], default='present')
    remarks = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'academics'
        unique_together = ['student', 'date', 'class_room']
        ordering = ['-date', 'student']

    def __str__(self):
        return f"{self.student} - {self.class_room} - {self.date} ({self.status})" 