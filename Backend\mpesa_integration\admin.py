from django.contrib import admin
from .models import MpesaCredential, MpesaTransaction, MpesaCallback

@admin.register(MpesaCredential)
class MpesaCredentialAdmin(admin.ModelAdmin):
    list_display = ('school', 'shortcode', 'environment', 'is_active', 'created_at')
    list_filter = ('environment', 'is_active', 'school')
    search_fields = ('school__name', 'shortcode')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('School Information', {
            'fields': ('school',)
        }),
        ('API Credentials', {
            'fields': ('consumer_key', 'consumer_secret', 'shortcode', 'passkey', 'environment')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

@admin.register(MpesaTransaction)
class MpesaTransactionAdmin(admin.ModelAdmin):
    list_display = ('transaction_type', 'phone_number', 'amount', 'status', 'mpesa_receipt_number', 'created_at')
    list_filter = ('transaction_type', 'status', 'created_at')
    search_fields = ('phone_number', 'reference', 'mpesa_receipt_number', 'transaction_id')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Transaction Details', {
            'fields': ('transaction_type', 'transaction_id', 'phone_number', 'amount', 'reference', 'description')
        }),
        ('Status Information', {
            'fields': ('status', 'result_code', 'result_description')
        }),
        ('MPESA Response', {
            'fields': ('checkout_request_id', 'merchant_request_id', 'mpesa_receipt_number')
        }),
        ('Related Records', {
            'fields': ('fee_payment',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

@admin.register(MpesaCallback)
class MpesaCallbackAdmin(admin.ModelAdmin):
    list_display = ('transaction', 'processed', 'created_at')
    list_filter = ('processed', 'created_at')
    readonly_fields = ('transaction', 'callback_data', 'processed', 'created_at')
    fieldsets = (
        ('Callback Information', {
            'fields': ('transaction', 'callback_data', 'processed', 'created_at')
        }),
    )