from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import timedelta
from django.conf import settings


class ExamType(models.Model):
    """Types of exams (CAT, Mid-term, End-term, etc.)"""
    name = models.CharField(max_length=50)
    code = models.CharField(max_length=10, unique=True)
    description = models.TextField(blank=True)
    weight_percentage = models.DecimalField(
        max_digits=5, 
        decimal_places=2,
        help_text="Weight of this exam type in final grade calculation"
    )
    is_active = models.BooleanField(default=True)
    
    class Meta:
        app_label = 'academics'
        
    def __str__(self):
        return self.name


class ExamSession(models.Model):
    """Exam sessions/periods"""
    name = models.CharField(max_length=100)
    academic_year = models.ForeignKey('academics.AcademicYear', on_delete=models.CASCADE)
    term = models.ForeignKey('academics.Term', on_delete=models.CASCADE)
    exam_type = models.Foreign<PERSON>ey(ExamType, on_delete=models.CASCADE)
    
    start_date = models.DateField()
    end_date = models.DateField()
    registration_deadline = models.DateField()
    
    # Instructions and rules
    instructions = models.TextField(blank=True)
    rules = models.TextField(blank=True)
    
    # Status
    is_active = models.BooleanField(default=True)
    is_published = models.BooleanField(default=False)
    
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='created_exam_sessions')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        app_label = 'academics'
        unique_together = ['academic_year', 'term', 'exam_type']
        
    def clean(self):
        if self.start_date >= self.end_date:
            raise ValidationError("End date must be after start date")
        if self.registration_deadline > self.start_date:
            raise ValidationError("Registration deadline must be before exam start date")
            
    def __str__(self):
        return f"{self.name} - {self.term}"


class EnhancedExam(models.Model):
    """Enhanced exam model with more features"""
    exam_session = models.ForeignKey(ExamSession, on_delete=models.CASCADE)
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE)
    class_name = models.ForeignKey('academics.ClassRoom', on_delete=models.CASCADE)
    
    # Exam details
    exam_date = models.DateField()
    start_time = models.TimeField()
    end_time = models.TimeField()
    duration = models.DurationField()
    
    # Venue and logistics
    venue = models.CharField(max_length=100)
    invigilator = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='invigilated_exams'
    )
    
    # Exam configuration
    total_marks = models.PositiveIntegerField(default=100)
    pass_mark = models.PositiveIntegerField(default=40)
    
    # Question paper details
    question_paper_file = models.FileField(
        upload_to='exam_papers/', 
        blank=True, 
        null=True
    )
    marking_scheme_file = models.FileField(
        upload_to='marking_schemes/', 
        blank=True, 
        null=True
    )
    
    # Status
    is_published = models.BooleanField(default=False)
    results_published = models.BooleanField(default=False)
    
    # Special instructions
    special_instructions = models.TextField(blank=True)
    materials_allowed = models.TextField(blank=True)
    
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='created_exams')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        app_label = 'academics'
        unique_together = ['exam_session', 'subject', 'class_name']
        
    def clean(self):
        if self.start_time >= self.end_time:
            raise ValidationError("End time must be after start time")
        if self.pass_mark > self.total_marks:
            raise ValidationError("Pass mark cannot exceed total marks")
            
    def __str__(self):
        return f"{self.subject.name} - {self.class_name} ({self.exam_date})"


class ExamRegistration(models.Model):
    """Student registration for exams"""
    student = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='exam_registrations')
    exam = models.ForeignKey(EnhancedExam, on_delete=models.CASCADE)
    registration_date = models.DateTimeField(auto_now_add=True)
    
    # Special accommodations
    special_needs = models.TextField(blank=True)
    extra_time_granted = models.DurationField(null=True, blank=True)
    
    # Status
    is_registered = models.BooleanField(default=True)
    is_present = models.BooleanField(default=False)
    
    class Meta:
        app_label = 'academics'
        unique_together = ['student', 'exam']
        
    def __str__(self):
        return f"{self.student} - {self.exam}"


class EnhancedExamResult(models.Model):
    """Enhanced exam results with detailed analysis"""
    exam = models.ForeignKey(EnhancedExam, on_delete=models.CASCADE)
    student = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='exam_results')
    
    # Scores
    raw_score = models.DecimalField(max_digits=5, decimal_places=2)
    percentage = models.DecimalField(max_digits=5, decimal_places=2)
    grade = models.CharField(max_length=2)
    points = models.DecimalField(max_digits=4, decimal_places=1)
    
    # Position/Ranking
    position_in_class = models.PositiveIntegerField(null=True, blank=True)
    position_in_stream = models.PositiveIntegerField(null=True, blank=True)
    position_in_subject = models.PositiveIntegerField(null=True, blank=True)
    
    # Analysis
    strengths = models.TextField(blank=True)
    weaknesses = models.TextField(blank=True)
    recommendations = models.TextField(blank=True)
    
    # Marking details
    marked_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='marked_results')
    marking_date = models.DateTimeField(null=True, blank=True)

    # Verification
    verified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='verified_results'
    )
    verification_date = models.DateTimeField(null=True, blank=True)
    is_verified = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        app_label = 'academics'
        unique_together = ['exam', 'student']
        
    def save(self, *args, **kwargs):
        # Calculate percentage
        if self.exam.total_marks > 0:
            self.percentage = (self.raw_score / self.exam.total_marks) * 100
        super().save(*args, **kwargs)
        
    def __str__(self):
        return f"{self.student} - {self.exam}: {self.raw_score}/{self.exam.total_marks}"


class ExamStatistics(models.Model):
    """Statistical analysis for exams"""
    exam = models.OneToOneField(EnhancedExam, on_delete=models.CASCADE)
    
    # Basic statistics
    total_students = models.PositiveIntegerField()
    students_present = models.PositiveIntegerField()
    students_absent = models.PositiveIntegerField()
    
    # Score statistics
    highest_score = models.DecimalField(max_digits=5, decimal_places=2)
    lowest_score = models.DecimalField(max_digits=5, decimal_places=2)
    average_score = models.DecimalField(max_digits=5, decimal_places=2)
    median_score = models.DecimalField(max_digits=5, decimal_places=2)
    
    # Performance analysis
    pass_rate = models.DecimalField(max_digits=5, decimal_places=2)
    distinction_rate = models.DecimalField(max_digits=5, decimal_places=2)
    failure_rate = models.DecimalField(max_digits=5, decimal_places=2)
    
    # Grade distribution
    grade_distribution = models.JSONField(default=dict)
    
    calculated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        app_label = 'academics'
        
    def __str__(self):
        return f"Statistics for {self.exam}"


class ExamMalpractice(models.Model):
    """Record exam malpractice incidents"""
    MALPRACTICE_TYPES = [
        ('cheating', 'Cheating'),
        ('impersonation', 'Impersonation'),
        ('unauthorized_material', 'Unauthorized Material'),
        ('collusion', 'Collusion'),
        ('disruption', 'Disruption'),
        ('other', 'Other'),
    ]
    
    exam = models.ForeignKey(EnhancedExam, on_delete=models.CASCADE)
    student = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='malpractice_incidents')
    malpractice_type = models.CharField(max_length=100, choices=MALPRACTICE_TYPES)

    description = models.TextField()
    evidence = models.FileField(upload_to='malpractice_evidence/', blank=True, null=True)

    reported_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='reported_malpractices')
    report_date = models.DateTimeField(auto_now_add=True)
    
    # Action taken
    action_taken = models.TextField(blank=True)
    penalty_applied = models.TextField(blank=True)
    
    # Status
    is_resolved = models.BooleanField(default=False)
    resolution_date = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        app_label = 'academics'
        
    def __str__(self):
        return f"Malpractice: {self.student} - {self.exam}"
