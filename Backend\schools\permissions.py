from rest_framework import permissions

class BranchBasedPermission(permissions.BasePermission):
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        branch_id = view.kwargs.get('branch_id')
        if branch_id:
            return request.user.school_branch_id == int(branch_id)
        return True

    def has_object_permission(self, request, view, obj):
        return hasattr(obj, 'school_branch') and obj.school_branch == request.user.school_branch