from django.dispatch import receiver
from django_rest_passwordreset.signals import reset_password_token_created
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from datetime import datetime
import logging
import traceback

logger = logging.getLogger(__name__)

@receiver(reset_password_token_created)
def password_reset_token_created(sender, instance, reset_password_token, *args, **kwargs):
    """
    Handles password reset tokens
    When a token is created, an e-mail needs to be sent to the user
    
    This handler is connected to the django_rest_passwordreset package's signal
    """
    # Get the frontend URL from settings with a trailing slash
    frontend_url = settings.FRONTEND_URL
    if not frontend_url.endswith('/'):
        frontend_url += '/'
    
    # Create the reset URL that the user will receive
    reset_url = f"{frontend_url}password-reset/{reset_password_token.key}"
    
    context = {
        'full_link': reset_url,
        'email_address': reset_password_token.user.email,
        'user_name': reset_password_token.user.get_full_name() or reset_password_token.user.email,
        'site_name': 'ShuleXcel',
        'valid_hours': 24,  # Token validity in hours
        'reset_token': reset_password_token.key
    }
    
    logger.info(f"Password reset token created for user: {reset_password_token.user.email}")
    
    # Render HTML message
    try:
        html_message = render_to_string("ShuleXcel/password_reset_email.html", context)
        logger.debug("Template rendered successfully")
    except Exception as template_error:
        logger.error(f"Template rendering error: {template_error}")
        
        # Fallback to a simple HTML message
        html_message = f"""
        <html>
            <body>
                <h1>Password Reset Request</h1>
                <p>Dear {context['user_name']},</p>
                <p>We received a request to reset your password for your {context['site_name']} account.</p>
                <p>To reset your password, please click on the link below or copy and paste it into your browser:</p>
                <p><a href="{context['full_link']}">{context['full_link']}</a></p>
                <p><strong>IMPORTANT:</strong> This link will expire in {context['valid_hours']} hours.</p>
                <p>If you didn't request a password reset, please ignore this email or contact your school administrator if you have concerns.</p>
                <p>Best regards,<br>The {context['site_name']} Team</p>
            </body>
        </html>
        """
    
    # Create a plain text version
    plain_message = f"""
Password Reset - ShuleXcel
==========================

Dear {context['user_name']},

We received a request to reset your password for your {context['site_name']} account.

To reset your password, please click on the link below or copy and paste it into your browser:

{context['full_link']}

IMPORTANT: This link will expire in {context['valid_hours']} hours.

If you didn't request a password reset, please ignore this email or contact your school administrator if you have concerns.

Best regards,
The {context['site_name']} Team

---
© {datetime.now().year} {context['site_name']}. All rights reserved.
This is an automated message, please do not reply to this email.
"""
    
    try:
        # Get a connection with debug output
        from django.core.mail import get_connection
        connection = get_connection(
            backend=settings.EMAIL_BACKEND,
            fail_silently=False,
        )
        
        # Create the email message
        msg = EmailMultiAlternatives(
            subject="Password Reset Request for ShuleXcel Account",
            body=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[reset_password_token.user.email],
            connection=connection,
        )
        
        # Attach the HTML version
        msg.attach_alternative(html_message, "text/html")
        
        logger.info(f"Sending password reset email to: {reset_password_token.user.email}")
        
        # Send the email
        result = msg.send()
        
        logger.info(f"Password reset email sent to {reset_password_token.user.email} (Result: {result})")
        
    except Exception as email_error:
        logger.error(f"Error sending password reset email: {email_error}")
        
        # Fall back to console backend if SMTP fails
        try:
            from django.core.mail.backends.console import EmailBackend
            console_backend = EmailBackend()
            
            # Create a new message for the console backend
            console_msg = EmailMultiAlternatives(
                subject="Password Reset Request for ShuleXcel Account",
                body=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[reset_password_token.user.email],
                connection=console_backend,
            )
            
            # Attach the HTML version
            console_msg.attach_alternative(html_message, "text/html")
            
            # Send the email
            console_msg.send()
            
            logger.info(f"Fallback email sent to console for: {reset_password_token.user.email}")
            
        except Exception as console_error:
            logger.error(f"Console backend error: {console_error}")
            traceback.print_exc()
