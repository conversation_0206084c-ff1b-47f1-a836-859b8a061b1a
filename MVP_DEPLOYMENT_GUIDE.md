# ShuleXcel MVP Deployment Guide

This guide provides step-by-step instructions for deploying the ShuleXcel MVP (Minimum Viable Product) to production.

## 🎯 MVP Features

The ShuleXcel MVP includes the following core features:

### ✅ Core Modules
- **Authentication & User Management**: Secure login, user registration, role-based access
- **School & Branch Management**: Multi-school and multi-branch support
- **License Management**: Package-based licensing with trial support
- **Academic Foundation**: Classes, subjects, students, teachers, academic years
- **Dashboard**: Basic reporting and overview
- **Settings**: School profiles and basic configuration

### 🚀 Ready for Production
- JWT-based authentication with MFA support
- Role-based permissions (Admin, Teacher, Student, Parent)
- Docker containerization
- Database migrations
- Static file handling
- Health check endpoints
- Comprehensive API documentation

## 📋 Prerequisites

Before deploying, ensure you have:

1. **Docker & Docker Compose** installed
2. **Domain name** (for production)
3. **SSL certificate** (recommended)
4. **PostgreSQL database** (recommended for production)
5. **Email service** (for notifications)

## 🚀 Quick Deployment

### Option 1: Automated Deployment (Recommended)

#### For Linux/Mac:
```bash
chmod +x deploy_mvp.sh
./deploy_mvp.sh
```

#### For Windows:
```powershell
.\deploy_mvp.ps1
```

### Option 2: Manual Deployment

1. **Clone the repository**
   ```bash
   git clone https://github.com/makaulucky/ShuleXcelApp.git
   cd ShuleXcelApp
   ```

2. **Configure environment**
   ```bash
   cp Backend/.env.example Backend/.env
   # Edit Backend/.env with your production settings
   ```

3. **Deploy with Docker**
   ```bash
   docker-compose up -d
   ```

4. **Setup initial data**
   ```bash
   docker-compose exec backend python setup_mvp.py
   ```

## ⚙️ Configuration

### Environment Variables

Key environment variables to configure in `Backend/.env`:

```env
# Security
SECRET_KEY=your-super-secret-key-here
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# Database (PostgreSQL recommended)
DATABASE_URL=postgresql://username:password@localhost:5432/shulexcel_db

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Frontend URL
FRONTEND_URL=https://your-domain.com

# Security Settings (for HTTPS)
SECURE_SSL_REDIRECT=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
```

### Database Setup

#### SQLite (Development/Testing)
```env
DATABASE_URL=sqlite:///db.sqlite3
```

#### PostgreSQL (Production)
```env
DATABASE_URL=postgresql://username:password@localhost:5432/shulexcel_db
```

## 🔐 Security Configuration

### SSL/HTTPS Setup

1. **Obtain SSL certificate** (Let's Encrypt recommended)
2. **Configure reverse proxy** (Nginx recommended)
3. **Update environment variables**:
   ```env
   SECURE_SSL_REDIRECT=True
   SESSION_COOKIE_SECURE=True
   CSRF_COOKIE_SECURE=True
   ```

### Default Credentials

After deployment, change the default admin credentials:

- **Email**: <EMAIL>
- **Password**: admin123

**⚠️ IMPORTANT**: Change these credentials immediately after first login!

## 📊 Post-Deployment

### 1. Access the Application

- **Frontend**: http://your-domain.com
- **Backend API**: http://your-domain.com:8000
- **Admin Panel**: http://your-domain.com:8000/admin
- **API Documentation**: http://your-domain.com:8000/swagger/

### 2. Initial Setup

1. **Login as admin** using default credentials
2. **Change admin password** immediately
3. **Create your school** and configure basic settings
4. **Add users** (teachers, students, parents)
5. **Configure academic structure** (classes, subjects)
6. **Test core functionality**

### 3. License Activation

The MVP includes a trial license by default. For production use:

1. Contact support for license activation
2. Configure license settings in admin panel
3. Enable required modules for your school

## 🔧 Maintenance

### Backup

```bash
# Database backup
docker-compose exec db pg_dump -U shulexcel_user shulexcel_db > backup.sql

# Media files backup
docker-compose exec backend tar -czf /tmp/media_backup.tar.gz /app/media
docker cp $(docker-compose ps -q backend):/tmp/media_backup.tar.gz ./media_backup.tar.gz
```

### Updates

```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
docker-compose down
docker-compose build
docker-compose up -d

# Run migrations
docker-compose exec backend python manage.py migrate
```

### Monitoring

Check application health:
```bash
# Check service status
docker-compose ps

# View logs
docker-compose logs backend
docker-compose logs frontend

# Health check
curl http://your-domain.com:8000/api/health/
```

## 🆘 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `.env`
   - Ensure database server is running
   - Verify network connectivity

2. **Static Files Not Loading**
   - Run: `docker-compose exec backend python manage.py collectstatic`
   - Check STATIC_ROOT and STATIC_URL settings

3. **Permission Denied Errors**
   - Check file permissions
   - Ensure Docker has proper access

4. **Email Not Working**
   - Verify email credentials in `.env`
   - Check firewall settings
   - Test with a simple email service first

### Getting Help

- **Documentation**: Check the `docs/` directory
- **Issues**: Create an issue on GitHub
- **Support**: Contact <EMAIL>

## 📈 Scaling Considerations

For scaling beyond MVP:

1. **Database**: Use managed PostgreSQL service
2. **File Storage**: Implement cloud storage (AWS S3, etc.)
3. **Caching**: Add Redis for caching
4. **Load Balancing**: Use multiple application instances
5. **Monitoring**: Implement comprehensive monitoring
6. **Backup**: Automated backup solutions

## 🎉 Success!

Your ShuleXcel MVP is now deployed and ready for use! 

Remember to:
- ✅ Change default passwords
- ✅ Configure SSL/HTTPS
- ✅ Set up regular backups
- ✅ Monitor application health
- ✅ Plan for scaling as you grow

Happy school management! 🏫📚
