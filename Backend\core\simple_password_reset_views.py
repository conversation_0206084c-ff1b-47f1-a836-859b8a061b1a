from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from django.contrib.auth import get_user_model
from django.core.mail import EmailMultiAlternatives
from django.conf import settings
from django.template.loader import render_to_string
from datetime import datetime, timedelta
import time
import hmac
import hashlib
import logging
import traceback
import secrets
import string

User = get_user_model()
logger = logging.getLogger(__name__)

class SimplePasswordResetView(APIView):
    """
    A simplified password reset view that provides a secure method to request a password reset.
    This is the recommended endpoint to use for password resets.
    
    Features:
    - Secure token generation with HMAC signature
    - Token expiration (24 hours by default)
    - Email template with branded content
    - Protection against email enumeration
    - Detailed logging for troubleshooting
    """
    permission_classes = [permissions.AllowAny]  # Allow any user to access this endpoint
    throttle_scope = 'password_reset'
    
    def post(self, request, *args, **kwargs):
        """
        Request a password reset email
        
        Expects:
        {
            "email": "<EMAIL>"
        }
        
        Returns:
        {
            "detail": "Password reset email has been sent."
        }
        """
        email = request.data.get('email', '')
        
        if not email:
            return Response({'detail': 'Email is required.'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            # Always return success to prevent email enumeration
            logger.info(f"Password reset requested for non-existent email: {email}")
            return Response({'detail': 'Password reset email has been sent.'}, status=status.HTTP_200_OK)
        
        # Log the password reset request
        logger.info(f"Password reset requested for user: {user.email}")
        
        try:
            # Get the frontend URL from settings with a trailing slash
            frontend_url = settings.FRONTEND_URL
            if not frontend_url.endswith('/'):
                frontend_url += '/'
            
            # Create a secure token
            user_id = user.id
            # Generate a random token
            token = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(20))
            
            # Create a timestamp for the request
            timestamp = int(time.time())
            
            # Create a signature to verify the link hasn't been tampered with
            # Format: user_id:token:timestamp
            payload = f"{user_id}:{token}:{timestamp}"
            
            # Create HMAC signature using Django's SECRET_KEY
            signature = hmac.new(
                key=settings.SECRET_KEY.encode(),
                msg=payload.encode(),
                digestmod=hashlib.sha256
            ).hexdigest()
            
            # Combine all parts into a secure token
            # Format: user_id.token.timestamp.signature
            secure_token = f"{user_id}.{token}.{timestamp}.{signature}"
            
            # Update to match the route in App.tsx which is /password-reset/:token
            full_link = f"{frontend_url}password-reset/{secure_token}"
            
            # Calculate expiration time (24 hours from now)
            expiry_time = datetime.now() + timedelta(hours=24)
            expiry_time_str = expiry_time.strftime("%Y-%m-%d %H:%M:%S")
            
            context = {
                'full_link': full_link,
                'email_address': user.email,
                'user_name': user.get_full_name() or user.email,
                'site_name': 'ShuleXcel',
                'valid_hours': 24,  # Token validity in hours
                'expiry_time': expiry_time_str
            }
            
            logger.debug(f"Preparing password reset email for user: {user.email}")
            
            # Render HTML message
            try:
                html_message = render_to_string("ShuleXcel/password_reset_email.html", context)
                logger.debug("Template rendered successfully")
            except Exception as template_error:
                logger.error(f"Template rendering error: {template_error}")
                
                # Fallback to a simple HTML message
                html_message = f"""
                <html>
                    <body>
                        <h1>Password Reset Request</h1>
                        <p>Dear {context['user_name']},</p>
                        <p>We received a request to reset your password for your {context['site_name']} account.</p>
                        <p>To reset your password, please click on the link below or copy and paste it into your browser:</p>
                        <p><a href="{context['full_link']}">{context['full_link']}</a></p>
                        <p><strong>IMPORTANT:</strong> This link will expire in {context['valid_hours']} hours (at {context['expiry_time']}).</p>
                        <p>If you didn't request a password reset, please ignore this email or contact your school administrator if you have concerns.</p>
                        <p>Best regards,<br>The {context['site_name']} Team</p>
                    </body>
                </html>
                """
            
            # Create a plain text version
            plain_message = f"""
Password Reset - ShuleXcel
==========================

Dear {context['user_name']},

We received a request to reset your password for your {context['site_name']} account.

To reset your password, please click on the link below or copy and paste it into your browser:

{context['full_link']}

IMPORTANT: This link will expire in {context['valid_hours']} hours (at {context['expiry_time']}).

If you didn't request a password reset, please ignore this email or contact your school administrator if you have concerns.

Best regards,
The {context['site_name']} Team

---
© {datetime.now().year} {context['site_name']}. All rights reserved.
This is an automated message, please do not reply to this email.
"""
            
            try:
                # Get a connection with debug output
                from django.core.mail import get_connection
                connection = get_connection(
                    backend=settings.EMAIL_BACKEND,
                    fail_silently=False,
                )
                
                # Create the email message
                msg = EmailMultiAlternatives(
                    subject="Password Reset Request for ShuleXcel Account",
                    body=plain_message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    to=[user.email],
                    connection=connection,
                )
                
                # Attach the HTML version
                msg.attach_alternative(html_message, "text/html")
                
                logger.info(f"Sending password reset email to: {user.email}")
                
                # Send the email
                result = msg.send()
                
                logger.info(f"Password reset email sent to {user.email} (Result: {result})")
                
            except Exception as email_error:
                logger.error(f"Error sending password reset email: {email_error}")
                
                # Fall back to console backend if SMTP fails
                try:
                    from django.core.mail.backends.console import EmailBackend
                    console_backend = EmailBackend()
                    
                    # Create a new message for the console backend
                    console_msg = EmailMultiAlternatives(
                        subject="Password Reset Request for ShuleXcel Account",
                        body=plain_message,
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        to=[user.email],
                        connection=console_backend,
                    )
                    
                    # Attach the HTML version
                    console_msg.attach_alternative(html_message, "text/html")
                    
                    # Send the email
                    console_msg.send()
                    
                    logger.info(f"Fallback email sent to console for: {user.email}")
                    
                except Exception as console_error:
                    logger.error(f"Console backend error: {console_error}")
            
        except Exception as e:
            # Log the error but don't re-raise to prevent 500 errors
            logger.error(f"Password reset error: {e}", exc_info=True)
        
        # Always return success to prevent email enumeration
        return Response({'detail': 'Password reset email has been sent.'}, status=status.HTTP_200_OK)
