from django.db import models
from django.utils import timezone
from schools.models import School

class AcademicYearTemplate(models.Model):
    """
    Template for academic years that can be applied to multiple branches
    """
    school = models.ForeignKey('schools.School', on_delete=models.CASCADE, related_name='academic_year_templates', null=True, blank=True, help_text='Optional for global templates')
    year = models.CharField(max_length=9)  # e.g., "2023-2024"
    start_date = models.DateField()
    end_date = models.DateField()
    is_active = models.BooleanField(default=True)
    is_archived = models.BooleanField(default=False)
    is_global = models.BooleanField(default=False, help_text="If true, this template can be used across all schools")
    created_by = models.ForeignKey('core.CustomUser', on_delete=models.SET_NULL, null=True, related_name='created_templates')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['year', 'school']
        ordering = ['-start_date']

    def __str__(self):
        school_name = self.school.name if self.school else 'Global'
        return f"{self.year} - {school_name} (Template){' (Global)' if self.is_global else ''}"

class TermTemplate(models.Model):
    """
    Template for terms that can be applied to multiple branches
    """
    academic_year_template = models.ForeignKey(AcademicYearTemplate, on_delete=models.CASCADE, related_name='term_templates')
    name = models.CharField(max_length=100)
    start_date = models.DateField()
    end_date = models.DateField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['name', 'academic_year_template']
        ordering = ['start_date']

    def __str__(self):
        return f"{self.name} - {self.academic_year_template.year} (Template)"
