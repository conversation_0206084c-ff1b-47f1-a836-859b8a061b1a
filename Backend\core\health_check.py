"""
Health check views for monitoring application status.
"""
import logging
from django.http import JsonResponse
from django.db import connection
from django.core.cache import cache
from django.conf import settings
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import time

logger = logging.getLogger(__name__)


@csrf_exempt
@require_http_methods(["GET"])
def health_check(request):
    """
    Basic health check endpoint.
    Returns 200 if the application is running.
    """
    return JsonResponse({
        'status': 'healthy',
        'timestamp': time.time(),
        'version': '1.0.0'
    })


@csrf_exempt
@require_http_methods(["GET"])
def health_check_detailed(request):
    """
    Detailed health check that verifies database and cache connectivity.
    """
    health_status = {
        'status': 'healthy',
        'timestamp': time.time(),
        'version': '1.0.0',
        'checks': {}
    }
    
    overall_status = True
    
    # Check database connectivity
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
        health_status['checks']['database'] = {
            'status': 'healthy',
            'message': 'Database connection successful'
        }
    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")
        health_status['checks']['database'] = {
            'status': 'unhealthy',
            'message': f'Database connection failed: {str(e)}'
        }
        overall_status = False
    
    # Check cache connectivity
    try:
        cache_key = 'health_check_test'
        cache_value = 'test_value'
        cache.set(cache_key, cache_value, 30)
        retrieved_value = cache.get(cache_key)
        
        if retrieved_value == cache_value:
            health_status['checks']['cache'] = {
                'status': 'healthy',
                'message': 'Cache connection successful'
            }
        else:
            raise Exception('Cache value mismatch')
            
        cache.delete(cache_key)
    except Exception as e:
        logger.error(f"Cache health check failed: {str(e)}")
        health_status['checks']['cache'] = {
            'status': 'unhealthy',
            'message': f'Cache connection failed: {str(e)}'
        }
        overall_status = False
    
    # Check disk space (basic check)
    try:
        import shutil
        total, used, free = shutil.disk_usage('/')
        free_percentage = (free / total) * 100
        
        if free_percentage > 10:  # More than 10% free space
            health_status['checks']['disk_space'] = {
                'status': 'healthy',
                'message': f'Disk space: {free_percentage:.1f}% free'
            }
        else:
            health_status['checks']['disk_space'] = {
                'status': 'warning',
                'message': f'Low disk space: {free_percentage:.1f}% free'
            }
    except Exception as e:
        logger.error(f"Disk space check failed: {str(e)}")
        health_status['checks']['disk_space'] = {
            'status': 'unknown',
            'message': f'Disk space check failed: {str(e)}'
        }
    
    # Set overall status
    health_status['status'] = 'healthy' if overall_status else 'unhealthy'
    
    # Return appropriate HTTP status code
    status_code = 200 if overall_status else 503
    
    return JsonResponse(health_status, status=status_code)


@csrf_exempt
@require_http_methods(["GET"])
def readiness_check(request):
    """
    Readiness check for Kubernetes/container orchestration.
    Checks if the application is ready to serve traffic.
    """
    readiness_status = {
        'status': 'ready',
        'timestamp': time.time(),
        'checks': {}
    }
    
    overall_ready = True
    
    # Check if migrations are up to date
    try:
        from django.core.management import execute_from_command_line
        from django.core.management.base import CommandError
        from io import StringIO
        import sys
        
        # Capture output
        old_stdout = sys.stdout
        sys.stdout = StringIO()
        
        try:
            execute_from_command_line(['manage.py', 'showmigrations', '--plan'])
            output = sys.stdout.getvalue()
            
            # Check if there are unapplied migrations
            if '[ ]' in output:
                readiness_status['checks']['migrations'] = {
                    'status': 'not_ready',
                    'message': 'Unapplied migrations found'
                }
                overall_ready = False
            else:
                readiness_status['checks']['migrations'] = {
                    'status': 'ready',
                    'message': 'All migrations applied'
                }
        finally:
            sys.stdout = old_stdout
            
    except Exception as e:
        logger.error(f"Migration check failed: {str(e)}")
        readiness_status['checks']['migrations'] = {
            'status': 'unknown',
            'message': f'Migration check failed: {str(e)}'
        }
    
    # Check database connectivity
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
        readiness_status['checks']['database'] = {
            'status': 'ready',
            'message': 'Database accessible'
        }
    except Exception as e:
        logger.error(f"Database readiness check failed: {str(e)}")
        readiness_status['checks']['database'] = {
            'status': 'not_ready',
            'message': f'Database not accessible: {str(e)}'
        }
        overall_ready = False
    
    # Set overall status
    readiness_status['status'] = 'ready' if overall_ready else 'not_ready'
    
    # Return appropriate HTTP status code
    status_code = 200 if overall_ready else 503
    
    return JsonResponse(readiness_status, status=status_code)


@csrf_exempt
@require_http_methods(["GET"])
def liveness_check(request):
    """
    Liveness check for Kubernetes/container orchestration.
    Simple check to verify the application process is alive.
    """
    return JsonResponse({
        'status': 'alive',
        'timestamp': time.time(),
        'pid': os.getpid() if 'os' in globals() else 'unknown'
    })


# Import os for liveness check
import os
