from django.db import models
from django.conf import settings

class TermResult(models.Model):
    student = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='term_results')
    term = models.ForeignKey('academics.Term', on_delete=models.CASCADE, related_name='term_results')
    class_room = models.ForeignKey('academics.ClassRoom', on_delete=models.CASCADE, related_name='term_results')
    total_score = models.DecimalField(max_digits=6, decimal_places=2)
    average_score = models.DecimalField(max_digits=5, decimal_places=2)
    position_in_class = models.PositiveIntegerField(null=True, blank=True)
    grade = models.Char<PERSON>ield(max_length=5, blank=True)
    remarks = models.TextField(blank=True)
    is_published = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'academics'
        unique_together = ['student', 'term', 'class_room']
        ordering = ['-term', 'student']

    def __str__(self):
        return f"{self.student} - {self.term} - {self.class_room}" 