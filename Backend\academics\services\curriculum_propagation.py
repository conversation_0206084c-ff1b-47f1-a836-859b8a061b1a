from django.db import transaction
from academics.curriculum_models import SchoolCurriculumConfig, BranchCurriculumConfig
from schools.models import School, SchoolBranch

class CurriculumPropagationService:
    """
    Service for propagating curriculum configurations from schools to branches
    """

    @staticmethod
    def propagate_to_all_branches(school_id):
        """
        Propagate curriculum configuration from a school to all its branches

        Args:
            school_id: ID of the school whose configuration should be propagated

        Returns:
            dict: Summary of the propagation results
        """
        try:
            school = School.objects.get(id=school_id)
            school_config = SchoolCurriculumConfig.objects.get(school=school)
            branches = SchoolBranch.objects.filter(school=school)

            results = {
                'total_branches': branches.count(),
                'updated': 0,
                'created': 0,
                'failed': 0,
                'skipped': 0,
                'details': []
            }

            for branch in branches:
                try:
                    with transaction.atomic():
                        branch_config, created = BranchCurriculumConfig.objects.update_or_create(
                            school_branch=branch,
                            defaults={
                                'primary_curriculum': school_config.primary_curriculum,
                                'secondary_curriculum': school_config.secondary_curriculum,
                                'is_transition_period': school_config.is_transition_period,
                                'transition_details': school_config.transition_details,
                                'curriculum_modifications': school_config.curriculum_modifications,
                                'is_inherited_from_school': True
                            }
                        )

                        if created:
                            results['created'] += 1
                            results['details'].append({
                                'branch_id': branch.id,
                                'branch_name': branch.name,
                                'status': 'created'
                            })
                        else:
                            # Skip update if the branch has customized its config
                            if not branch_config.is_inherited_from_school:
                                results['skipped'] += 1
                                results['details'].append({
                                    'branch_id': branch.id,
                                    'branch_name': branch.name,
                                    'status': 'skipped',
                                    'reason': 'Branch has customized configuration'
                                })
                                continue

                            results['updated'] += 1
                            results['details'].append({
                                'branch_id': branch.id,
                                'branch_name': branch.name,
                                'status': 'updated'
                            })
                except Exception as e:
                    results['failed'] += 1
                    results['details'].append({
                        'branch_id': branch.id,
                        'branch_name': branch.name,
                        'status': 'failed',
                        'error': str(e)
                    })

            return results
        except School.DoesNotExist:
            return {'error': f'School with ID {school_id} not found'}
        except SchoolCurriculumConfig.DoesNotExist:
            return {'error': f'Curriculum configuration for school with ID {school_id} not found'}

    @staticmethod
    def propagate_to_specific_branches(school_id, branch_ids):
        """
        Propagate curriculum configuration from a school to specific branches

        Args:
            school_id: ID of the school whose configuration should be propagated
            branch_ids: List of branch IDs to propagate to

        Returns:
            dict: Summary of the propagation results
        """
        try:
            school = School.objects.get(id=school_id)
            school_config = SchoolCurriculumConfig.objects.get(school=school)
            branches = SchoolBranch.objects.filter(school=school, id__in=branch_ids)

            results = {
                'total_branches': branches.count(),
                'updated': 0,
                'created': 0,
                'failed': 0,
                'skipped': 0,
                'details': []
            }

            for branch in branches:
                try:
                    with transaction.atomic():
                        branch_config, created = BranchCurriculumConfig.objects.update_or_create(
                            school_branch=branch,
                            defaults={
                                'primary_curriculum': school_config.primary_curriculum,
                                'secondary_curriculum': school_config.secondary_curriculum,
                                'is_transition_period': school_config.is_transition_period,
                                'transition_details': school_config.transition_details,
                                'curriculum_modifications': school_config.curriculum_modifications,
                                'is_inherited_from_school': True
                            }
                        )

                        if created:
                            results['created'] += 1
                            results['details'].append({
                                'branch_id': branch.id,
                                'branch_name': branch.name,
                                'status': 'created'
                            })
                        else:
                            results['updated'] += 1
                            results['details'].append({
                                'branch_id': branch.id,
                                'branch_name': branch.name,
                                'status': 'updated'
                            })
                except Exception as e:
                    results['failed'] += 1
                    results['details'].append({
                        'branch_id': branch.id,
                        'branch_name': branch.name,
                        'status': 'failed',
                        'error': str(e)
                    })

            return results
        except School.DoesNotExist:
            return {'error': f'School with ID {school_id} not found'}
        except SchoolCurriculumConfig.DoesNotExist:
            return {'error': f'Curriculum configuration for school with ID {school_id} not found'}
