from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from django.db.models import Q, Count, Sum
from django.shortcuts import get_object_or_404

from .models import Book, EResource, Borrowing, Return, LibraryReport
from .serializers import (
    BookSerializer, EResourceSerializer, BorrowingSerializer,
    ReturnSerializer, OverdueBookSerializer, LibraryReportSerializer
)
from core.permissions import BranchBasedPermission

class BookViewSet(viewsets.ModelViewSet):
    serializer_class = BookSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'author', 'isbn', 'category', 'subject']
    ordering_fields = ['title', 'author', 'publication_year', 'created_at']
    ordering = ['title']

    def get_queryset(self):
        queryset = Book.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by status
        status = self.request.query_params.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Filter by category
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category=category)

        # Filter by subject
        subject = self.request.query_params.get('subject')
        if subject:
            queryset = queryset.filter(subject=subject)

        # Filter by availability
        available = self.request.query_params.get('available')
        if available == 'true':
            queryset = queryset.filter(available_quantity__gt=0)
        elif available == 'false':
            queryset = queryset.filter(available_quantity=0)

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            added_by=self.request.user,
            school_branch=self.request.user.school_branch
        )

    @action(detail=True, methods=['get'])
    def borrowing_history(self, request, pk=None):
        book = self.get_object()
        borrowings = Borrowing.objects.filter(book=book)
        serializer = BorrowingSerializer(borrowings, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def categories(self, request):
        categories = Book.objects.values_list('category', flat=True).distinct()
        return Response(list(categories))

    @action(detail=False, methods=['get'])
    def subjects(self, request):
        subjects = Book.objects.values_list('subject', flat=True).distinct()
        return Response(list(subjects))

class EResourceViewSet(viewsets.ModelViewSet):
    serializer_class = EResourceSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'author', 'category', 'subject']
    ordering_fields = ['title', 'author', 'resource_type', 'created_at']
    ordering = ['title']

    def get_queryset(self):
        queryset = EResource.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by resource type
        resource_type = self.request.query_params.get('resource_type')
        if resource_type:
            queryset = queryset.filter(resource_type=resource_type)

        # Filter by category
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category=category)

        # Filter by subject
        subject = self.request.query_params.get('subject')
        if subject:
            queryset = queryset.filter(subject=subject)

        # Filter by access level
        access_level = self.request.query_params.get('access_level')
        if access_level:
            queryset = queryset.filter(access_level=access_level)

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            added_by=self.request.user,
            school_branch=self.request.user.school_branch
        )

    @action(detail=False, methods=['get'])
    def categories(self, request):
        categories = EResource.objects.values_list('category', flat=True).distinct()
        return Response(list(categories))

    @action(detail=False, methods=['get'])
    def subjects(self, request):
        subjects = EResource.objects.values_list('subject', flat=True).distinct()
        return Response(list(subjects))

    @action(detail=False, methods=['get'])
    def resource_types(self, request):
        resource_types = dict(EResource.RESOURCE_TYPE_CHOICES)
        return Response(resource_types)

class BorrowingViewSet(viewsets.ModelViewSet):
    serializer_class = BorrowingSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['book__title', 'borrower__first_name', 'borrower__last_name', 'borrower__email']
    ordering_fields = ['borrow_date', 'due_date', 'is_returned', 'is_renewed']
    ordering = ['-borrow_date']

    def get_queryset(self):
        queryset = Borrowing.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by borrower
        borrower_id = self.request.query_params.get('borrower')
        if borrower_id:
            queryset = queryset.filter(borrower_id=borrower_id)

        # Filter by book
        book_id = self.request.query_params.get('book')
        if book_id:
            queryset = queryset.filter(book_id=book_id)

        # Filter by return status
        is_returned = self.request.query_params.get('is_returned')
        if is_returned == 'true':
            queryset = queryset.filter(is_returned=True)
        elif is_returned == 'false':
            queryset = queryset.filter(is_returned=False)

        # Filter by overdue status
        is_overdue = self.request.query_params.get('is_overdue')
        if is_overdue == 'true':
            queryset = queryset.filter(
                is_returned=False,
                due_date__lt=timezone.now().date()
            )

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            issued_by=self.request.user,
            school_branch=self.request.user.school_branch
        )

    @action(detail=True, methods=['post'])
    def renew(self, request, pk=None):
        borrowing = self.get_object()

        # Check if already returned
        if borrowing.is_returned:
            return Response(
                {"detail": "Cannot renew a returned book."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check renewal count limit
        if borrowing.renewal_count >= 3:
            return Response(
                {"detail": "Maximum renewal limit reached."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Calculate new due date (typically 2 weeks from now)
        new_due_date = timezone.now().date() + timezone.timedelta(days=14)

        # Update borrowing record
        borrowing.due_date = new_due_date
        borrowing.is_renewed = True
        borrowing.renewal_count += 1
        borrowing.save()

        serializer = self.get_serializer(borrowing)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def return_book(self, request, pk=None):
        borrowing = self.get_object()

        # Check if already returned
        if borrowing.is_returned:
            return Response(
                {"detail": "This book has already been returned."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create return record
        return_data = {
            'borrowing': borrowing.id,
            'return_date': request.data.get('return_date', timezone.now().date()),
            'condition': request.data.get('condition', 'GOOD'),
            'fine_amount': request.data.get('fine_amount', borrowing.calculate_fine()),
            'fine_paid': request.data.get('fine_paid', False),
            'payment_method': request.data.get('payment_method'),
            'notes': request.data.get('notes'),
            'received_by': request.user.id
        }

        serializer = ReturnSerializer(data=return_data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class ReturnViewSet(viewsets.ModelViewSet):
    serializer_class = ReturnSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['borrowing__book__title', 'borrowing__borrower__first_name', 'borrowing__borrower__last_name']
    ordering_fields = ['return_date', 'fine_paid', 'created_at']
    ordering = ['-return_date']

    def get_queryset(self):
        queryset = Return.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(borrowing__school_branch=self.request.user.school_branch)

        # Filter by fine payment status
        fine_paid = self.request.query_params.get('fine_paid')
        if fine_paid == 'true':
            queryset = queryset.filter(fine_paid=True)
        elif fine_paid == 'false':
            queryset = queryset.filter(fine_paid=False)

        # Filter by return date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(return_date__range=[start_date, end_date])

        return queryset

    def perform_create(self, serializer):
        serializer.save(received_by=self.request.user)

    @action(detail=True, methods=['post'])
    def pay_fine(self, request, pk=None):
        return_record = self.get_object()

        # Check if fine already paid
        if return_record.fine_paid:
            return Response(
                {"detail": "Fine has already been paid."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update payment details
        return_record.fine_paid = True
        return_record.payment_date = request.data.get('payment_date', timezone.now().date())
        return_record.payment_method = request.data.get('payment_method', 'CASH')
        return_record.save()

        # Update borrowing record
        borrowing = return_record.borrowing
        borrowing.fine_paid = True
        borrowing.save()

        serializer = self.get_serializer(return_record)
        return Response(serializer.data)

class OverdueBookViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = OverdueBookSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['book__title', 'borrower__first_name', 'borrower__last_name', 'borrower__email']
    ordering_fields = ['due_date', 'borrow_date']
    ordering = ['due_date']

    def get_queryset(self):
        queryset = Borrowing.objects.filter(
            is_returned=False,
            due_date__lt=timezone.now().date()
        )

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by days overdue
        min_days = self.request.query_params.get('min_days')
        if min_days:
            min_date = timezone.now().date() - timezone.timedelta(days=int(min_days))
            queryset = queryset.filter(due_date__lte=min_date)

        return queryset

    @action(detail=False, methods=['post'])
    def send_reminders(self, request):
        borrowings = self.get_queryset()

        # Group by borrower to avoid sending multiple emails
        borrower_ids = borrowings.values_list('borrower_id', flat=True).distinct()

        # Here you would implement the actual email sending logic
        # For now, we'll just return the count of reminders that would be sent

        return Response({
            "detail": f"Reminders would be sent to {len(borrower_ids)} borrowers for {borrowings.count()} overdue books."
        })

class LibraryReportViewSet(viewsets.ModelViewSet):
    serializer_class = LibraryReportSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'report_type']
    ordering_fields = ['created_at', 'start_date', 'end_date']
    ordering = ['-created_at']

    def get_queryset(self):
        queryset = LibraryReport.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by report type
        report_type = self.request.query_params.get('report_type')
        if report_type:
            queryset = queryset.filter(report_type=report_type)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(
                Q(start_date__gte=start_date) & Q(end_date__lte=end_date)
            )

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            generated_by=self.request.user,
            school_branch=self.request.user.school_branch
        )

    @action(detail=False, methods=['post'])
    def generate_borrowing_report(self, request):
        start_date = request.data.get('start_date')
        end_date = request.data.get('end_date')

        if not start_date or not end_date:
            return Response(
                {"detail": "Start date and end date are required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get borrowings in the date range
        borrowings = Borrowing.objects.filter(
            borrow_date__range=[start_date, end_date],
            school_branch=request.user.school_branch
        )

        # Generate report data
        report_data = {
            'total_borrowings': borrowings.count(),
            'returned': borrowings.filter(is_returned=True).count(),
            'not_returned': borrowings.filter(is_returned=False).count(),
            'overdue': borrowings.filter(
                is_returned=False,
                due_date__lt=timezone.now().date()
            ).count(),
            'total_fines': float(borrowings.aggregate(Sum('fine_amount'))['fine_amount__sum'] or 0),
            'fines_paid': float(borrowings.filter(fine_paid=True).aggregate(Sum('fine_amount'))['fine_amount__sum'] or 0),
            'fines_unpaid': float(borrowings.filter(fine_paid=False).aggregate(Sum('fine_amount'))['fine_amount__sum'] or 0),
            'most_borrowed_books': list(
                borrowings.values('book__title').annotate(count=Count('book')).order_by('-count')[:10]
            ),
            'borrowings_by_day': list(
                borrowings.values('borrow_date').annotate(count=Count('id')).order_by('borrow_date')
            )
        }

        # Create report
        report = LibraryReport.objects.create(
            title=f"Borrowing Report ({start_date} to {end_date})",
            report_type='BORROWING',
            start_date=start_date,
            end_date=end_date,
            generated_by=request.user,
            school_branch=request.user.school_branch,
            report_data=report_data
        )

        serializer = self.get_serializer(report)
        return Response(serializer.data, status=status.HTTP_201_CREATED)