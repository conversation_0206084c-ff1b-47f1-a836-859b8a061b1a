<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset - ShuleXcel</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #4f46e5;
            color: white;
            padding: 25px;
            text-align: center;
            border-radius: 10px 10px 0 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            letter-spacing: 0.5px;
        }
        .header p {
            margin: 5px 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        .content {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 0 0 10px 10px;
            border: 1px solid #e5e7eb;
            border-top: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 12px;
            color: #6b7280;
            padding: 10px;
        }
        .button {
            display: inline-block;
            background-color: #22c55e;
            background-image: linear-gradient(to right, #22c55e, #16a34a);
            color: white !important;
            text-decoration: none;
            padding: 14px 30px;
            border-radius: 6px;
            margin-top: 20px;
            font-weight: bold;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 6px rgba(34, 197, 94, 0.3);
            transition: all 0.3s ease;
            border: none;
            text-align: center;
        }
        .button:hover {
            background-image: linear-gradient(to right, #16a34a, #15803d);
            box-shadow: 0 6px 10px rgba(34, 197, 94, 0.4);
            transform: translateY(-1px);
            color: white !important;
        }
        .button span {
            color: white !important;
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
        }
        .link-box {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            word-break: break-all;
            font-family: monospace;
            font-size: 14px;
            color: #4b5563;
        }
        .highlight {
            font-weight: bold;
            color: #4f46e5;
        }
        .card {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            color: #4f46e5;
            margin-top: 0;
            margin-bottom: 15px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 10px;
        }
        .card-item {
            display: flex;
            margin-bottom: 10px;
        }
        .card-label {
            font-weight: bold;
            width: 120px;
            color: #4b5563;
        }
        .card-value {
            flex: 1;
            color: #111827;
        }
        .warning {
            background-color: #fffbeb;
            border-left: 4px solid #f59e0b;
            padding: 10px 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .logo {
            max-width: 150px;
            margin: 0 auto 10px;
            display: block;
        }
        .social-links {
            margin-top: 15px;
            text-align: center;
        }
        .social-links a {
            display: inline-block;
            margin: 0 5px;
            color: #6b7280;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="https://i.ibb.co/Qp1Jb4C/shulexcel-logo.png" alt="ShuleXcel Logo" class="logo">
            <h1>Password Reset</h1>
            <p>ShuleXcel School Management System</p>
        </div>
        <div class="content">
            <p>Dear {{ user_name }},</p>

            <p>We received a request to reset your password for your <strong>{{ site_name }}</strong> account. To create a new password, click the button below:</p>

            <div style="text-align: center;">
                <a href="{{ full_link }}" class="button"><span style="font-weight: 600; letter-spacing: 0.8px; text-transform: uppercase; font-size: 14px; color: white;">Reset My Password</span></a>
            </div>

            <div class="card">
                <h3 class="card-title">Password Reset Details</h3>
                <div class="card-item">
                    <div class="card-label">Account Email:</div>
                    <div class="card-value">{{ email_address }}</div>
                </div>
                <div class="card-item">
                    <div class="card-label">Request Time:</div>
                    <div class="card-value">{% now "F j, Y, g:i a" %}</div>
                </div>
                <div class="card-item">
                    <div class="card-label">Expires In:</div>
                    <div class="card-value"><span class="highlight">{{ valid_hours }} hours</span></div>
                </div>
            </div>

            <p>If the button above doesn't work, copy and paste this link into your browser:</p>

            <div class="link-box">
                {{ full_link }}
            </div>

            <div class="warning">
                <strong>Important:</strong> This link will expire in {{ valid_hours }} hours. If you didn't request a password reset, please ignore this email or contact your school administrator if you have concerns.
            </div>

            <p>Best regards,<br>The {{ site_name }} Team</p>
        </div>
        <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
            <div class="social-links">
                <a href="#">Facebook</a> | <a href="#">Twitter</a> | <a href="#">Instagram</a>
            </div>
            <p>&copy; {% now "Y" %} {{ site_name }}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
