# Generated by Django 5.2.1 on 2025-06-01 13:25

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('academics', '0002_initial'),
        ('schools', '0001_initial'),
        ('users', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='classroom',
            name='class_teacher',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_classes', to='users.teacher'),
        ),
        migrations.AddField(
            model_name='classroom',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='schools.school'),
        ),
        migrations.AddField(
            model_name='classroom',
            name='school_branch',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='classes', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='classroom',
            name='students',
            field=models.ManyToManyField(blank=True, related_name='enrolled_classes', to='users.student'),
        ),
        migrations.AddField(
            model_name='assessment',
            name='class_room',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assessments', to='academics.classroom'),
        ),
        migrations.AddField(
            model_name='curriculumcompliancecheck',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_compliance_checks', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='curriculumcompliancecheck',
            name='school',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='compliance_checks', to='schools.school'),
        ),
        migrations.AddField(
            model_name='curriculumcompliancenotification',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_compliance_notifications', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='curriculumcompliancenotification',
            name='resolved_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_compliance_notifications', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='curriculumcompliancenotification',
            name='scheduled_check',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='notifications', to='academics.curriculumcompliancecheck'),
        ),
        migrations.AddField(
            model_name='curriculumcompliancenotification',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='compliance_notifications', to='schools.school'),
        ),
        migrations.AddField(
            model_name='curriculumconfighistory',
            name='changed_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='curriculum_config_changes', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='curriculumconfigtemplate',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_curriculum_templates', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='curriculumconfigtemplate',
            name='school',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='curriculum_templates', to='schools.school'),
        ),
        migrations.AddField(
            model_name='curriculumconfighistory',
            name='template_used',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='applications', to='academics.curriculumconfigtemplate'),
        ),
        migrations.AddField(
            model_name='curriculumcompliancenotification',
            name='template',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='compliance_notifications', to='academics.curriculumconfigtemplate'),
        ),
        migrations.AddField(
            model_name='curriculumcompliancecheck',
            name='template',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='compliance_checks', to='academics.curriculumconfigtemplate'),
        ),
        migrations.AddField(
            model_name='curriculumpropagationhistory',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='propagation_history', to='schools.school'),
        ),
        migrations.AddField(
            model_name='curriculumpropagationhistory',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='propagation_history', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='curriculumpropagationhistory',
            name='user',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='propagation_history', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='curriculumconfigtemplate',
            name='primary_curriculum',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='primary_templates', to='academics.curriculumsystem'),
        ),
        migrations.AddField(
            model_name='curriculumconfigtemplate',
            name='secondary_curriculum',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='secondary_templates', to='academics.curriculumsystem'),
        ),
        migrations.AddField(
            model_name='curriculumconfighistory',
            name='primary_curriculum',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='primary_history', to='academics.curriculumsystem'),
        ),
        migrations.AddField(
            model_name='curriculumconfighistory',
            name='secondary_curriculum',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='secondary_history', to='academics.curriculumsystem'),
        ),
        migrations.AddField(
            model_name='branchcurriculumconfig',
            name='primary_curriculum',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='primary_branches', to='academics.curriculumsystem'),
        ),
        migrations.AddField(
            model_name='branchcurriculumconfig',
            name='secondary_curriculum',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='secondary_branches', to='academics.curriculumsystem'),
        ),
        migrations.AddField(
            model_name='department',
            name='department_teacher',
            field=models.ManyToManyField(blank=True, related_name='department_teacher', to='users.teacher'),
        ),
        migrations.AddField(
            model_name='department',
            name='head_of_department',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='headed_department', to='users.teacher'),
        ),
        migrations.AddField(
            model_name='department',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='schools.school'),
        ),
        migrations.AddField(
            model_name='department',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='departments', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='educationlevel',
            name='curriculum_system',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.curriculumsystem'),
        ),
        migrations.AddField(
            model_name='classroom',
            name='education_level',
            field=models.ForeignKey(help_text='Links to curriculum-specific education level (Grade 4 CBC, Form 2 8-4-4, etc.)', on_delete=django.db.models.deletion.CASCADE, to='academics.educationlevel'),
        ),
        migrations.AddField(
            model_name='classprogressionrule',
            name='current_level',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='progression_from', to='academics.educationlevel'),
        ),
        migrations.AddField(
            model_name='classprogressionrule',
            name='next_level',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='progression_to', to='academics.educationlevel'),
        ),
        migrations.AddField(
            model_name='enhancedexam',
            name='class_name',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.classroom'),
        ),
        migrations.AddField(
            model_name='enhancedexam',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_exams', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='enhancedexam',
            name='invigilator',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='invigilated_exams', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='enhancedexamresult',
            name='exam',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.enhancedexam'),
        ),
        migrations.AddField(
            model_name='enhancedexamresult',
            name='marked_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='marked_results', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='enhancedexamresult',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='exam_results', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='enhancedexamresult',
            name='verified_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_results', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='enhancedtimetable',
            name='academic_year',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.academicyear'),
        ),
        migrations.AddField(
            model_name='enhancedtimetable',
            name='approved_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_timetables', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='enhancedtimetable',
            name='class_name',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.classroom'),
        ),
        migrations.AddField(
            model_name='enhancedtimetable',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_timetables', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='exammalpractice',
            name='exam',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.enhancedexam'),
        ),
        migrations.AddField(
            model_name='exammalpractice',
            name='reported_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reported_malpractices', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='exammalpractice',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='malpractice_incidents', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='examregistration',
            name='exam',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.enhancedexam'),
        ),
        migrations.AddField(
            model_name='examregistration',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='exam_registrations', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='examsession',
            name='academic_year',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.academicyear'),
        ),
        migrations.AddField(
            model_name='examsession',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_exam_sessions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='enhancedexam',
            name='exam_session',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.examsession'),
        ),
        migrations.AddField(
            model_name='examstatistics',
            name='exam',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='academics.enhancedexam'),
        ),
        migrations.AddField(
            model_name='examsession',
            name='exam_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.examtype'),
        ),
        migrations.AddField(
            model_name='gradingsystem',
            name='academic_year',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.academicyear'),
        ),
        migrations.AddField(
            model_name='gradingsystem',
            name='classes',
            field=models.ManyToManyField(blank=True, related_name='grading_systems', to='academics.classroom'),
        ),
        migrations.AddField(
            model_name='gradingsystem',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='users.teacher'),
        ),
        migrations.AddField(
            model_name='gradingsystem',
            name='curriculum_system',
            field=models.ForeignKey(help_text='Curriculum system this grading applies to', on_delete=django.db.models.deletion.CASCADE, to='academics.curriculumsystem'),
        ),
        migrations.AddField(
            model_name='gradingsystem',
            name='education_levels',
            field=models.ManyToManyField(blank=True, help_text='Education levels this grading system applies to', to='academics.educationlevel'),
        ),
        migrations.AddField(
            model_name='gradingsystem',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='schools.school'),
        ),
        migrations.AddField(
            model_name='gradescale',
            name='grading_system',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='grades', to='academics.gradingsystem'),
        ),
        migrations.AddField(
            model_name='lessonobservation',
            name='lesson_delivery',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.lessondelivery'),
        ),
        migrations.AddField(
            model_name='lessonobservation',
            name='observer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lesson_observations', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='lessonplan',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_lesson_plans', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='lessonplan',
            name='class_name',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.classroom'),
        ),
        migrations.AddField(
            model_name='lessonplan',
            name='teacher',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lesson_plans', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='lessondelivery',
            name='lesson_plan',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.lessonplan'),
        ),
        migrations.AddField(
            model_name='lessonresource',
            name='lesson_plan',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.lessonplan'),
        ),
        migrations.AddField(
            model_name='lessonresource',
            name='uploaded_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='uploaded_lesson_resources', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='lessonseries',
            name='class_name',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.classroom'),
        ),
        migrations.AddField(
            model_name='lessonseries',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_lesson_series', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='lessonserieslesson',
            name='lesson_plan',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.lessonplan'),
        ),
        migrations.AddField(
            model_name='lessonserieslesson',
            name='series',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.lessonseries'),
        ),
        migrations.AddField(
            model_name='livesession',
            name='class_name',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.classroom'),
        ),
        migrations.AddField(
            model_name='livesession',
            name='teacher',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='live_sessions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='livesession',
            name='platform',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.livestreamingplatform'),
        ),
        migrations.AddField(
            model_name='performanceanalytics',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='performance_analytics', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='publicationaccess',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='publication_accesses', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='resultpublication',
            name='academic_year',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.academicyear'),
        ),
        migrations.AddField(
            model_name='resultpublication',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_publications', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='resultpublication',
            name='classes',
            field=models.ManyToManyField(blank=True, to='academics.classroom'),
        ),
        migrations.AddField(
            model_name='resultpublication',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_publications', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='resultpublication',
            name='reviewed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_publications', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='publicationaccess',
            name='publication',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.resultpublication'),
        ),
        migrations.AddField(
            model_name='schoolcurriculumconfig',
            name='primary_curriculum',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='primary_schools', to='academics.curriculumsystem'),
        ),
        migrations.AddField(
            model_name='schoolcurriculumconfig',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='schools.school'),
        ),
        migrations.AddField(
            model_name='schoolcurriculumconfig',
            name='secondary_curriculum',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='secondary_schools', to='academics.curriculumsystem'),
        ),
        migrations.AddField(
            model_name='curriculumconfighistory',
            name='school_config',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='academics.schoolcurriculumconfig'),
        ),
        migrations.AddField(
            model_name='sessionattendance',
            name='session',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.livesession'),
        ),
        migrations.AddField(
            model_name='sessionattendance',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='session_attendances', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='sessionchat',
            name='session',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.livesession'),
        ),
        migrations.AddField(
            model_name='sessionchat',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='session_chats', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='sessionparticipant',
            name='session',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.livesession'),
        ),
        migrations.AddField(
            model_name='sessionparticipant',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='session_participations', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='sessionrecording',
            name='allowed_viewers',
            field=models.ManyToManyField(blank=True, related_name='accessible_recordings', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='sessionrecording',
            name='session',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='academics.livesession'),
        ),
        migrations.AddField(
            model_name='smartintervention',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interventions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stream',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='schools.school'),
        ),
        migrations.AddField(
            model_name='classroom',
            name='stream',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='classes', to='academics.stream'),
        ),
        migrations.AddField(
            model_name='streamgradingsystem',
            name='grading_system',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.gradingsystem'),
        ),
        migrations.AddField(
            model_name='streamgradingsystem',
            name='stream',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.stream'),
        ),
        migrations.AddField(
            model_name='studentattendance',
            name='class_room',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendances', to='academics.classroom'),
        ),
        migrations.AddField(
            model_name='studentattendance',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendances', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='studentcareerguidance',
            name='counselor',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='counseling_sessions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='studentcareerguidance',
            name='recommended_careers',
            field=models.ManyToManyField(blank=True, to='academics.careerpath'),
        ),
        migrations.AddField(
            model_name='studentcareerguidance',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='career_guidance_sessions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='studentlessonfeedback',
            name='lesson_delivery',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.lessondelivery'),
        ),
        migrations.AddField(
            model_name='studentlessonfeedback',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lesson_feedbacks', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='studentprogression',
            name='current_class',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='progression_students', to='academics.classroom'),
        ),
        migrations.AddField(
            model_name='studentprogression',
            name='decision_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='users.teacher'),
        ),
        migrations.AddField(
            model_name='studentprogression',
            name='next_class',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='incoming_students', to='academics.classroom'),
        ),
        migrations.AddField(
            model_name='studentprogression',
            name='progression_rule',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='academics.classprogressionrule'),
        ),
        migrations.AddField(
            model_name='studentprogression',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='progressions', to='users.student'),
        ),
        migrations.AddField(
            model_name='subject',
            name='academic_year',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.academicyear'),
        ),
        migrations.AddField(
            model_name='subject',
            name='curriculum_system',
            field=models.ForeignKey(help_text='Curriculum system this subject belongs to', on_delete=django.db.models.deletion.CASCADE, to='academics.curriculumsystem'),
        ),
        migrations.AddField(
            model_name='subject',
            name='department',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.department'),
        ),
        migrations.AddField(
            model_name='subject',
            name='education_levels',
            field=models.ManyToManyField(help_text='Education levels where this subject is offered', to='academics.educationlevel'),
        ),
        migrations.AddField(
            model_name='subject',
            name='grading_system',
            field=models.ForeignKey(blank=True, help_text='Subject-specific grading system', null=True, on_delete=django.db.models.deletion.SET_NULL, to='academics.gradingsystem'),
        ),
        migrations.AddField(
            model_name='subject',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='schools.school'),
        ),
        migrations.AddField(
            model_name='subject',
            name='school_branch',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subjects', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='subject',
            name='teachers',
            field=models.ManyToManyField(blank=True, related_name='taught_subjects', to='users.teacher'),
        ),
        migrations.AddField(
            model_name='smartintervention',
            name='subject',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interventions', to='academics.subject'),
        ),
        migrations.AddField(
            model_name='performanceanalytics',
            name='subject',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='performance_analytics', to='academics.subject'),
        ),
        migrations.AddField(
            model_name='livesession',
            name='subject',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.subject'),
        ),
        migrations.AddField(
            model_name='lessonseries',
            name='subject',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.subject'),
        ),
        migrations.AddField(
            model_name='lessonplan',
            name='subject',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.subject'),
        ),
        migrations.AddField(
            model_name='learningpath',
            name='subject',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='learning_paths', to='academics.subject'),
        ),
        migrations.AddField(
            model_name='enhancedexam',
            name='subject',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.subject'),
        ),
        migrations.AddField(
            model_name='classroom',
            name='subjects',
            field=models.ManyToManyField(blank=True, related_name='assigned_classes', to='academics.subject'),
        ),
        migrations.AddField(
            model_name='careerpathsubjectrequirement',
            name='subject',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.subject'),
        ),
        migrations.AddField(
            model_name='careerpath',
            name='required_subjects',
            field=models.ManyToManyField(through='academics.CareerPathSubjectRequirement', to='academics.subject'),
        ),
        migrations.AddField(
            model_name='assessment',
            name='subject',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assessments', to='academics.subject'),
        ),
        migrations.AddField(
            model_name='subjectcombination',
            name='career_paths',
            field=models.ManyToManyField(blank=True, to='academics.careerpath'),
        ),
        migrations.AddField(
            model_name='subjectcombination',
            name='education_levels',
            field=models.ManyToManyField(to='academics.educationlevel'),
        ),
        migrations.AddField(
            model_name='subjectcombination',
            name='subjects',
            field=models.ManyToManyField(to='academics.subject'),
        ),
        migrations.AddField(
            model_name='teacherdepartmentassignment',
            name='department',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='teacher_assignments', to='academics.department'),
        ),
        migrations.AddField(
            model_name='teacherdepartmentassignment',
            name='teacher',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='academic_department_assignments', to='users.teacher'),
        ),
        migrations.AddField(
            model_name='teacherperformancemetrics',
            name='class_room',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='teacher_performance_metrics', to='academics.classroom'),
        ),
        migrations.AddField(
            model_name='teacherperformancemetrics',
            name='subject',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='teacher_performance_metrics', to='academics.subject'),
        ),
        migrations.AddField(
            model_name='teacherperformancemetrics',
            name='teacher',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='performance_metrics', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='teachertimetable',
            name='academic_year',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.academicyear'),
        ),
        migrations.AddField(
            model_name='teachertimetable',
            name='classes_taught',
            field=models.ManyToManyField(blank=True, to='academics.classroom'),
        ),
        migrations.AddField(
            model_name='teachertimetable',
            name='subjects_taught',
            field=models.ManyToManyField(blank=True, to='academics.subject'),
        ),
        migrations.AddField(
            model_name='teachertimetable',
            name='teacher',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='teacher_timetables', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='term',
            name='academic_year',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='terms', to='academics.academicyear'),
        ),
        migrations.AddField(
            model_name='term',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='schools.school'),
        ),
        migrations.AddField(
            model_name='term',
            name='school_branch',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='terms', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='teachertimetable',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.term'),
        ),
        migrations.AddField(
            model_name='teacherperformancemetrics',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='teacher_performance_metrics', to='academics.term'),
        ),
        migrations.AddField(
            model_name='studentprogression',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.term'),
        ),
        migrations.AddField(
            model_name='streamgradingsystem',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.term'),
        ),
        migrations.AddField(
            model_name='resultpublication',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.term'),
        ),
        migrations.AddField(
            model_name='performanceanalytics',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='performance_analytics', to='academics.term'),
        ),
        migrations.AddField(
            model_name='livesession',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.term'),
        ),
        migrations.AddField(
            model_name='gradingsystem',
            name='terms',
            field=models.ManyToManyField(blank=True, related_name='grading_systems', to='academics.term'),
        ),
        migrations.AddField(
            model_name='examsession',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.term'),
        ),
        migrations.AddField(
            model_name='enhancedtimetable',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.term'),
        ),
        migrations.AddField(
            model_name='assessment',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assessments', to='academics.term'),
        ),
        migrations.AddField(
            model_name='termresult',
            name='class_room',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='term_results', to='academics.classroom'),
        ),
        migrations.AddField(
            model_name='termresult',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='term_results', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='termresult',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='term_results', to='academics.term'),
        ),
        migrations.AddField(
            model_name='termtemplate',
            name='academic_year_template',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='term_templates', to='academics.academicyeartemplate'),
        ),
        migrations.AddField(
            model_name='term',
            name='template',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='terms', to='academics.termtemplate'),
        ),
        migrations.AddField(
            model_name='timetableconflict',
            name='resolved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_conflicts', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='timetableconflict',
            name='timetable',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.enhancedtimetable'),
        ),
        migrations.AddField(
            model_name='timetableentry',
            name='original_teacher',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='original_classes', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='timetableentry',
            name='subject',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.subject'),
        ),
        migrations.AddField(
            model_name='timetableentry',
            name='teacher',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='timetable_entries', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='timetableentry',
            name='time_slot',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.enhancedtimeslot'),
        ),
        migrations.AddField(
            model_name='timetableentry',
            name='timetable',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.enhancedtimetable'),
        ),
        migrations.AddField(
            model_name='timetableconflict',
            name='entry1',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conflicts_as_entry1', to='academics.timetableentry'),
        ),
        migrations.AddField(
            model_name='timetableconflict',
            name='entry2',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='conflicts_as_entry2', to='academics.timetableentry'),
        ),
        migrations.AddField(
            model_name='timetablesubstitution',
            name='approved_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_substitutions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='timetablesubstitution',
            name='original_entry',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.timetableentry'),
        ),
        migrations.AddField(
            model_name='timetablesubstitution',
            name='substitute_teacher',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='substitution_assignments', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='timetabletemplate',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_timetable_templates', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='timetabletemplate',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='schools.school'),
        ),
        migrations.AddField(
            model_name='enhancedtimetable',
            name='template',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='academics.timetabletemplate'),
        ),
        migrations.AddField(
            model_name='enhancedtimeslot',
            name='template',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='academics.timetabletemplate'),
        ),
        migrations.AlterUniqueTogether(
            name='academicyeartemplate',
            unique_together={('year', 'school')},
        ),
        migrations.AddIndex(
            model_name='academicyear',
            index=models.Index(fields=['school_name', 'is_current'], name='academics_a_school__9965d9_idx'),
        ),
        migrations.AddIndex(
            model_name='academicyear',
            index=models.Index(fields=['school_branch', 'is_current'], name='academics_a_school__2a31e9_idx'),
        ),
        migrations.AddIndex(
            model_name='academicyear',
            index=models.Index(fields=['is_archived', 'is_current'], name='academics_a_is_arch_ee6e1e_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='academicyear',
            unique_together={('year', 'school_branch')},
        ),
        migrations.AddIndex(
            model_name='assessmentresult',
            index=models.Index(fields=['assessment', 'student'], name='academics_a_assessm_417ad1_idx'),
        ),
        migrations.AddIndex(
            model_name='assessmentresult',
            index=models.Index(fields=['is_submitted', 'is_graded'], name='academics_a_is_subm_9a59b6_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='assessmentresult',
            unique_together={('assessment', 'student')},
        ),
        migrations.AlterUniqueTogether(
            name='department',
            unique_together={('code', 'school', 'school_branch')},
        ),
        migrations.AlterUniqueTogether(
            name='educationlevel',
            unique_together={('curriculum_system', 'code')},
        ),
        migrations.AlterUniqueTogether(
            name='classprogressionrule',
            unique_together={('current_level', 'next_level', 'school')},
        ),
        migrations.AlterUniqueTogether(
            name='enhancedexamresult',
            unique_together={('exam', 'student')},
        ),
        migrations.AlterUniqueTogether(
            name='examregistration',
            unique_together={('student', 'exam')},
        ),
        migrations.AlterUniqueTogether(
            name='gradescale',
            unique_together={('grading_system', 'grade')},
        ),
        migrations.AlterUniqueTogether(
            name='lessondelivery',
            unique_together={('lesson_plan', 'scheduled_date')},
        ),
        migrations.AlterUniqueTogether(
            name='lessonserieslesson',
            unique_together={('series', 'lesson_plan')},
        ),
        migrations.AlterUniqueTogether(
            name='publicationaccess',
            unique_together={('publication', 'user')},
        ),
        migrations.AlterUniqueTogether(
            name='sessionattendance',
            unique_together={('session', 'student')},
        ),
        migrations.AlterUniqueTogether(
            name='sessionparticipant',
            unique_together={('session', 'user')},
        ),
        migrations.AlterUniqueTogether(
            name='studentattendance',
            unique_together={('student', 'date', 'class_room')},
        ),
        migrations.AlterUniqueTogether(
            name='studentlessonfeedback',
            unique_together={('lesson_delivery', 'student')},
        ),
        migrations.AlterUniqueTogether(
            name='subject',
            unique_together={('code', 'curriculum_system', 'school'), ('name', 'curriculum_system', 'academic_year', 'school_branch')},
        ),
        migrations.AlterUniqueTogether(
            name='learningpath',
            unique_together={('subject', 'current_level', 'target_level')},
        ),
        migrations.AlterUniqueTogether(
            name='enhancedexam',
            unique_together={('exam_session', 'subject', 'class_name')},
        ),
        migrations.AlterUniqueTogether(
            name='classroom',
            unique_together={('education_level', 'stream', 'academic_year', 'school_branch'), ('name', 'stream', 'academic_year', 'school_branch')},
        ),
        migrations.AlterUniqueTogether(
            name='careerpathsubjectrequirement',
            unique_together={('career_path', 'subject')},
        ),
        migrations.AlterUniqueTogether(
            name='teacherdepartmentassignment',
            unique_together={('teacher', 'department')},
        ),
        migrations.AlterUniqueTogether(
            name='teachertimetable',
            unique_together={('teacher', 'academic_year', 'term')},
        ),
        migrations.AlterUniqueTogether(
            name='teacherperformancemetrics',
            unique_together={('teacher', 'subject', 'term', 'class_room')},
        ),
        migrations.AlterUniqueTogether(
            name='studentprogression',
            unique_together={('student', 'term')},
        ),
        migrations.AlterUniqueTogether(
            name='streamgradingsystem',
            unique_together={('stream', 'term')},
        ),
        migrations.AlterUniqueTogether(
            name='performanceanalytics',
            unique_together={('student', 'subject', 'term')},
        ),
        migrations.AlterUniqueTogether(
            name='gradingsystem',
            unique_together={('name', 'school', 'academic_year', 'curriculum_system')},
        ),
        migrations.AlterUniqueTogether(
            name='examsession',
            unique_together={('academic_year', 'term', 'exam_type')},
        ),
        migrations.AddIndex(
            model_name='assessment',
            index=models.Index(fields=['subject', 'class_room', 'term'], name='academics_a_subject_1e6b9f_idx'),
        ),
        migrations.AddIndex(
            model_name='assessment',
            index=models.Index(fields=['assessment_type', 'is_published'], name='academics_a_assessm_e5ee52_idx'),
        ),
        migrations.AddIndex(
            model_name='assessment',
            index=models.Index(fields=['scheduled_date'], name='academics_a_schedul_dd57e5_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='termresult',
            unique_together={('student', 'term', 'class_room')},
        ),
        migrations.AlterUniqueTogether(
            name='termtemplate',
            unique_together={('name', 'academic_year_template')},
        ),
        migrations.AlterUniqueTogether(
            name='term',
            unique_together={('name', 'academic_year', 'school_branch')},
        ),
        migrations.AlterUniqueTogether(
            name='timetableentry',
            unique_together={('timetable', 'time_slot')},
        ),
        migrations.AlterUniqueTogether(
            name='enhancedtimetable',
            unique_together={('class_name', 'academic_year', 'term')},
        ),
        migrations.AlterUniqueTogether(
            name='enhancedtimeslot',
            unique_together={('day_of_week', 'period_number', 'template')},
        ),
    ]
