from django.db import models
from schools.models import SchoolBranch

class BranchAnalytics(models.Model):
    school_branch = models.OneToOneField(SchoolBranch, on_delete=models.CASCADE)
    student_count = models.IntegerField(default=0)
    teacher_count = models.IntegerField(default=0)
    fee_collection_rate = models.DecimalField(max_digits=5, decimal_places=2)
    average_performance = models.DecimalField(max_digits=5, decimal_places=2)
    last_updated = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name_plural = "Branch Analytics"

class PerformanceMetrics(models.Model):
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE)
    term = models.ForeignKey('academics.Term', on_delete=models.CASCADE)
    metrics_data = models.JSONField()  # Changed from postgres JSONField to Django's J<PERSON>NField
    date_generated = models.DateTimeField(auto_now_add=True)

class EnrollmentMetrics(models.Model):
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE)
    academic_year = models.ForeignKey('academics.AcademicYear', on_delete=models.CASCADE)
    total_enrollments = models.IntegerField(default=0)
    new_enrollments = models.IntegerField(default=0)
    transfers_in = models.IntegerField(default=0)
    transfers_out = models.IntegerField(default=0)
    dropouts = models.IntegerField(default=0)
    date_generated = models.DateTimeField(auto_now_add=True)
