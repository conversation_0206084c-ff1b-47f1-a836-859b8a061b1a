import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { useSchool } from '../../contexts/SchoolContext';
import { getAllDashboardData } from '../../services/dashboardService';

// Import Heroicons
import {
  UserGroupIcon,
  BookOpenIcon,
  CalendarIcon,
  ClipboardDocumentCheckIcon,
  ArrowRightIcon,
  ChartBarIcon,
  AcademicCapIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

// Define types for teacher dashboard data
interface TeacherDashboardStats {
  totalStudents: number;
  totalClasses: number;
  totalSubjects: number;
  pendingAssessments: number;
  upcomingEvents: number;
}

interface ClassInfo {
  id: number;
  name: string;
  studentCount: number;
  stream: string;
}

interface AssignmentInfo {
  id: number;
  title: string;
  dueDate: string;
  className: string;
  submissionCount: number;
  totalStudents: number;
}

interface TeacherDashboardData {
  stats: TeacherDashboardStats;
  classes: ClassInfo[];
  assignments: AssignmentInfo[];
}

// Default dashboard data for when API calls fail
const defaultDashboardData: TeacherDashboardData = {
  stats: {
    totalStudents: 0,
    totalClasses: 0,
    totalSubjects: 0,
    pendingAssessments: 0,
    upcomingEvents: 0
  },
  classes: [],
  assignments: []
};

const TeacherDashboardContent: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<TeacherDashboardData>(defaultDashboardData);
  const [loading, setLoading] = useState<boolean>(true);
  const navigate = useNavigate();

  // Get the selected school and branch from context
  const { selectedSchool, selectedBranch } = useSchool();

  useEffect(() => {
    const fetchData = async () => {
      try {
        // In a real implementation, this would call a teacher-specific dashboard API
        // For now, we'll adapt the admin dashboard data
        const data = await getAllDashboardData({
          schoolId: selectedSchool?.id,
          branchId: selectedBranch?.id
        });

        if (data) {
          // Use real academic data from the API
          const realClasses = data.academicData?.classes || [];
          const realSubjects = data.academicData?.subjects || [];

          setDashboardData({
            stats: {
              totalStudents: data.stats.totalStudents,
              totalClasses: realClasses.length,
              totalSubjects: realSubjects.length,
              pendingAssessments: Math.floor(Math.random() * 10), // Mock data - would come from assessments API
              upcomingEvents: data.upcomingEvents.length
            },
            classes: realClasses.slice(0, 3).map((classItem: any, index: number) => ({
              id: classItem.id || index + 1,
              name: classItem.name,
              studentCount: classItem.max_capacity || 30, // Use max_capacity as student count estimate
              stream: classItem.stream?.name || 'General'
            })),
            assignments: [
              // Mock data - in a real implementation, this would come from the API
              { id: 1, title: 'Physics Mid-Term', dueDate: '2025-05-15', className: 'Grade 10A', submissionCount: 28, totalStudents: 32 },
              { id: 2, title: 'Literature Essay', dueDate: '2025-05-10', className: 'Grade 9B', submissionCount: 20, totalStudents: 28 },
              { id: 3, title: 'Economics Project', dueDate: '2025-05-20', className: 'Grade 11C', submissionCount: 15, totalStudents: 30 },
            ]
          });
        } else {
          // If no data is returned, use default data
          setDashboardData(defaultDashboardData);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // If there's an error, use default data
        setDashboardData(defaultDashboardData);
      } finally {
        setLoading(false);
      }
    };

    // Only fetch data if we have a selected school and branch
    if (selectedSchool && selectedBranch) {
      setLoading(true);
      fetchData();
    } else {
      // If no school or branch is selected, use default data and stop loading
      setDashboardData(defaultDashboardData);
      setLoading(false);
    }
  }, [selectedSchool, selectedBranch]); // Re-fetch when school or branch changes

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 dark:border-blue-400"></div>
        <span className="sr-only">Loading dashboard data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white">Teacher Dashboard</h1>
        <div className="mt-4 md:mt-0 flex items-center space-x-3">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {selectedSchool && selectedBranch && (
              <span>
                <span className="font-medium text-gray-700 dark:text-gray-300">{selectedSchool.name}</span>
                {selectedBranch && ` - ${selectedBranch.name}`}
              </span>
            )}
          </div>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {format(new Date(), 'EEEE, MMMM d, yyyy')}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        {/* Summary Cards */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-blue-50 dark:bg-blue-900/20">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-blue-100 dark:bg-blue-800/50 rounded-lg">
                <UserGroupIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white ml-3">Students</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-800 dark:text-white">{dashboardData.stats.totalStudents}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Total students</p>
              </div>
            </div>
          </div>
          <div className="px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700">
            <button
              type="button"
              onClick={() => navigate('/AllStudents')}
              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium flex items-center"
            >
              View Students
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-green-50 dark:bg-green-900/20">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-green-100 dark:bg-green-800/50 rounded-lg">
                <AcademicCapIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white ml-3">Classes</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-800 dark:text-white">{dashboardData.stats.totalClasses}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Assigned classes</p>
              </div>
            </div>
          </div>
          <div className="px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700">
            <button
              type="button"
              onClick={() => navigate('/classes')}
              className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 text-sm font-medium flex items-center"
            >
              View Classes
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-purple-50 dark:bg-purple-900/20">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-purple-100 dark:bg-purple-800/50 rounded-lg">
                <BookOpenIcon className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white ml-3">Subjects</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-800 dark:text-white">{dashboardData.stats.totalSubjects}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Teaching subjects</p>
              </div>
            </div>
          </div>
          <div className="px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700">
            <button
              type="button"
              onClick={() => navigate('/academics/subjects')}
              className="text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 text-sm font-medium flex items-center"
            >
              View Subjects
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-amber-50 dark:bg-amber-900/20">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-amber-100 dark:bg-amber-800/50 rounded-lg">
                <ClipboardDocumentCheckIcon className="h-6 w-6 text-amber-600 dark:text-amber-400" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white ml-3">Assessments</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-800 dark:text-white">{dashboardData.stats.pendingAssessments}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Pending assessments</p>
              </div>
            </div>
          </div>
          <div className="px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700">
            <button
              type="button"
              onClick={() => navigate('/assessments')}
              className="text-amber-600 dark:text-amber-400 hover:text-amber-800 dark:hover:text-amber-300 text-sm font-medium flex items-center"
            >
              View Assessments
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-indigo-50 dark:bg-indigo-900/20">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-indigo-100 dark:bg-indigo-800/50 rounded-lg">
                <CalendarIcon className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white ml-3">Events</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-800 dark:text-white">{dashboardData.stats.upcomingEvents}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Upcoming events</p>
              </div>
            </div>
          </div>
          <div className="px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700">
            <button
              type="button"
              onClick={() => navigate('/calendar')}
              className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 text-sm font-medium flex items-center"
            >
              View Calendar
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>
      </div>

      {/* Classes and Assignments */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* My Classes */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white">My Classes</h2>
              <button
                type="button"
                onClick={() => navigate('/classes')}
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium flex items-center"
              >
                View All
                <ArrowRightIcon className="h-4 w-4 ml-1" />
              </button>
            </div>
            <div className="border-t border-gray-100 dark:border-gray-700 -mx-6 px-6 py-2"></div>
            <div className="mt-4">
              {dashboardData.classes.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Class
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Stream
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Students
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {dashboardData.classes.map((classInfo) => (
                        <tr key={classInfo.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">{classInfo.name}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500 dark:text-gray-400">{classInfo.stream}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500 dark:text-gray-400">{classInfo.studentCount}</div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-full mb-3">
                    <AcademicCapIcon className="h-8 w-8 text-blue-500 dark:text-blue-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-1">No classes assigned</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 max-w-xs">You don't have any classes assigned to you yet.</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Pending Assignments */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white">Pending Assignments</h2>
              <button
                type="button"
                onClick={() => navigate('/assignments')}
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium flex items-center"
              >
                View All
                <ArrowRightIcon className="h-4 w-4 ml-1" />
              </button>
            </div>
            <div className="border-t border-gray-100 dark:border-gray-700 -mx-6 px-6 py-2"></div>
            <div className="mt-4">
              {dashboardData.assignments.length > 0 ? (
                <div className="space-y-4">
                  {dashboardData.assignments.map((assignment) => (
                    <div key={assignment.id} className="flex items-center justify-between py-3 border-b border-gray-100 dark:border-gray-700 last:border-0">
                      <div className="flex items-start space-x-3">
                        <div className="p-2 bg-amber-50 dark:bg-amber-900/20 rounded-lg mt-1">
                          <ClipboardDocumentCheckIcon className="h-5 w-5 text-amber-500 dark:text-amber-400" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-800 dark:text-white">{assignment.title}</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">{assignment.className}</p>
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        <div className="flex items-center text-sm font-medium text-amber-600 dark:text-amber-400">
                          <ClockIcon className="h-4 w-4 mr-1" />
                          {format(new Date(assignment.dueDate), 'MMM dd')}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {assignment.submissionCount}/{assignment.totalStudents} submitted
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <div className="p-3 bg-amber-50 dark:bg-amber-900/20 rounded-full mb-3">
                    <ClipboardDocumentCheckIcon className="h-8 w-8 text-amber-500 dark:text-amber-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-1">No pending assignments</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 max-w-xs">You don't have any pending assignments to review.</p>
                  <button
                    type="button"
                    onClick={() => navigate('/assignments/create')}
                    className="mt-4 px-4 py-2 bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300 rounded-lg hover:bg-amber-200 dark:hover:bg-amber-800/40 transition-colors duration-200 flex items-center"
                  >
                    <ClipboardDocumentCheckIcon className="h-4 w-4 mr-2" />
                    Create Assignment
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
        <div className="p-6">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            <button
              type="button"
              onClick={() => navigate('/assignments/create')}
              className="flex items-center justify-center px-4 py-3 bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300 rounded-lg hover:bg-amber-100 dark:hover:bg-amber-800/30 transition-colors duration-200"
            >
              <ClipboardDocumentCheckIcon className="h-5 w-5 mr-2" />
              Create Assignment
            </button>
            <button
              type="button"
              onClick={() => navigate('/grades')}
              className="flex items-center justify-center px-4 py-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-100 dark:hover:bg-green-800/30 transition-colors duration-200"
            >
              <ChartBarIcon className="h-5 w-5 mr-2" />
              Enter Grades
            </button>
            <button
              type="button"
              onClick={() => navigate('/announcements')}
              className="flex items-center justify-center px-4 py-3 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-800/30 transition-colors duration-200"
            >
              <CalendarIcon className="h-5 w-5 mr-2" />
              Post Announcement
            </button>
            <button
              type="button"
              onClick={() => navigate('/class-timetable')}
              className="flex items-center justify-center px-4 py-3 bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-800/30 transition-colors duration-200"
            >
              <ClockIcon className="h-5 w-5 mr-2" />
              View Timetable
            </button>
            <button
              type="button"
              onClick={() => navigate('/performance-analytics')}
              className="flex items-center justify-center px-4 py-3 bg-indigo-50 dark:bg-indigo-900/20 text-indigo-700 dark:text-indigo-300 rounded-lg hover:bg-indigo-100 dark:hover:bg-indigo-800/30 transition-colors duration-200"
            >
              <ChartBarIcon className="h-5 w-5 mr-2" />
              Performance Analytics
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeacherDashboardContent;
