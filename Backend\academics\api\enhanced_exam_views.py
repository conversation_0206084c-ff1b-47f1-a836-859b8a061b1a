from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.db.models import Q, Avg, Count, <PERSON>, Min
from django.utils import timezone

from academics.models import (
    ExamType, ExamSession, EnhancedExam, ExamRegistration,
    EnhancedExamResult, ExamStatistics, ExamMalpractice
)
from .serializers import (
    ExamTypeSerializer, ExamSessionSerializer, EnhancedExamSerializer,
    ExamRegistrationSerializer, EnhancedExamResultSerializer,
    ExamStatisticsSerializer, ExamMalpracticeSerializer
)


class ExamTypeViewSet(viewsets.ModelViewSet):
    """ViewSet for managing exam types"""
    queryset = ExamType.objects.filter(is_active=True)
    serializer_class = ExamTypeSerializer
    permission_classes = [IsAuthenticated]


class ExamSessionViewSet(viewsets.ModelViewSet):
    """ViewSet for managing exam sessions"""
    serializer_class = ExamSessionSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = ExamSession.objects.all()
        academic_year = self.request.query_params.get('academic_year', None)
        term = self.request.query_params.get('term', None)
        is_active = self.request.query_params.get('is_active', None)
        
        if academic_year:
            queryset = queryset.filter(academic_year_id=academic_year)
        if term:
            queryset = queryset.filter(term_id=term)
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
            
        return queryset.select_related('academic_year', 'term', 'exam_type', 'created_by')
    
    @action(detail=True, methods=['post'])
    def publish(self, request, pk=None):
        """Publish an exam session"""
        exam_session = self.get_object()
        exam_session.is_published = True
        exam_session.save()
        
        return Response({'message': 'Exam session published successfully'})
    
    @action(detail=True, methods=['get'])
    def exams(self, request, pk=None):
        """Get all exams in this session"""
        exam_session = self.get_object()
        exams = EnhancedExam.objects.filter(exam_session=exam_session)
        serializer = EnhancedExamSerializer(exams, many=True)
        return Response(serializer.data)


class EnhancedExamViewSet(viewsets.ModelViewSet):
    """ViewSet for managing enhanced exams"""
    serializer_class = EnhancedExamSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = EnhancedExam.objects.all()
        exam_session = self.request.query_params.get('exam_session', None)
        subject = self.request.query_params.get('subject', None)
        class_name = self.request.query_params.get('class_name', None)
        
        if exam_session:
            queryset = queryset.filter(exam_session_id=exam_session)
        if subject:
            queryset = queryset.filter(subject_id=subject)
        if class_name:
            queryset = queryset.filter(class_name_id=class_name)
            
        return queryset.select_related(
            'exam_session', 'subject', 'class_name', 'invigilator', 'created_by'
        )
    
    @action(detail=True, methods=['post'])
    def publish(self, request, pk=None):
        """Publish an exam"""
        exam = self.get_object()
        exam.is_published = True
        exam.save()
        
        return Response({'message': 'Exam published successfully'})
    
    @action(detail=True, methods=['post'])
    def publish_results(self, request, pk=None):
        """Publish exam results"""
        exam = self.get_object()
        exam.results_published = True
        exam.save()
        
        # Calculate and update statistics
        self._calculate_exam_statistics(exam)
        
        return Response({'message': 'Exam results published successfully'})
    
    @action(detail=True, methods=['get'])
    def registrations(self, request, pk=None):
        """Get exam registrations"""
        exam = self.get_object()
        registrations = ExamRegistration.objects.filter(exam=exam)
        serializer = ExamRegistrationSerializer(registrations, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def results(self, request, pk=None):
        """Get exam results"""
        exam = self.get_object()
        results = EnhancedExamResult.objects.filter(exam=exam)
        serializer = EnhancedExamResultSerializer(results, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def statistics(self, request, pk=None):
        """Get exam statistics"""
        exam = self.get_object()
        try:
            statistics = ExamStatistics.objects.get(exam=exam)
            serializer = ExamStatisticsSerializer(statistics)
            return Response(serializer.data)
        except ExamStatistics.DoesNotExist:
            # Calculate statistics if not exists
            stats = self._calculate_exam_statistics(exam)
            serializer = ExamStatisticsSerializer(stats)
            return Response(serializer.data)
    
    def _calculate_exam_statistics(self, exam):
        """Calculate and save exam statistics"""
        results = EnhancedExamResult.objects.filter(exam=exam)
        registrations = ExamRegistration.objects.filter(exam=exam)
        
        total_students = registrations.count()
        students_present = registrations.filter(is_present=True).count()
        students_absent = total_students - students_present
        
        if results.exists():
            scores = results.values_list('raw_score', flat=True)
            highest_score = max(scores)
            lowest_score = min(scores)
            average_score = sum(scores) / len(scores)
            sorted_scores = sorted(scores)
            median_score = sorted_scores[len(sorted_scores) // 2]
            
            pass_count = results.filter(raw_score__gte=exam.pass_mark).count()
            pass_rate = (pass_count / len(scores)) * 100 if scores else 0
            
            distinction_count = results.filter(raw_score__gte=exam.total_marks * 0.75).count()
            distinction_rate = (distinction_count / len(scores)) * 100 if scores else 0
            
            failure_rate = 100 - pass_rate
            
            # Grade distribution
            grade_distribution = {}
            for result in results:
                grade = result.grade
                grade_distribution[grade] = grade_distribution.get(grade, 0) + 1
        else:
            highest_score = lowest_score = average_score = median_score = 0
            pass_rate = distinction_rate = failure_rate = 0
            grade_distribution = {}
        
        statistics, created = ExamStatistics.objects.update_or_create(
            exam=exam,
            defaults={
                'total_students': total_students,
                'students_present': students_present,
                'students_absent': students_absent,
                'highest_score': highest_score,
                'lowest_score': lowest_score,
                'average_score': average_score,
                'median_score': median_score,
                'pass_rate': pass_rate,
                'distinction_rate': distinction_rate,
                'failure_rate': failure_rate,
                'grade_distribution': grade_distribution,
            }
        )
        
        return statistics


class ExamRegistrationViewSet(viewsets.ModelViewSet):
    """ViewSet for managing exam registrations"""
    serializer_class = ExamRegistrationSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = ExamRegistration.objects.all()
        exam = self.request.query_params.get('exam', None)
        student = self.request.query_params.get('student', None)
        
        if exam:
            queryset = queryset.filter(exam_id=exam)
        if student:
            queryset = queryset.filter(student_id=student)
            
        return queryset.select_related('student', 'exam')
    
    @action(detail=False, methods=['post'])
    def bulk_register(self, request):
        """Bulk register students for an exam"""
        exam_id = request.data.get('exam_id')
        student_ids = request.data.get('student_ids', [])
        
        if not exam_id or not student_ids:
            return Response(
                {'error': 'exam_id and student_ids are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        exam = get_object_or_404(EnhancedExam, id=exam_id)
        created_registrations = []
        errors = []
        
        for student_id in student_ids:
            try:
                registration, created = ExamRegistration.objects.get_or_create(
                    exam=exam,
                    student_id=student_id,
                    defaults={'is_registered': True}
                )
                if created:
                    created_registrations.append(registration.id)
            except Exception as e:
                errors.append({
                    'student_id': student_id,
                    'error': str(e)
                })
        
        return Response({
            'created_registrations': created_registrations,
            'errors': errors
        })
    
    @action(detail=False, methods=['post'])
    def mark_attendance(self, request):
        """Mark attendance for exam"""
        exam_id = request.data.get('exam_id')
        attendance_data = request.data.get('attendance_data', [])
        
        if not exam_id or not attendance_data:
            return Response(
                {'error': 'exam_id and attendance_data are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        updated_count = 0
        for attendance in attendance_data:
            student_id = attendance.get('student_id')
            is_present = attendance.get('is_present', False)
            
            try:
                registration = ExamRegistration.objects.get(
                    exam_id=exam_id,
                    student_id=student_id
                )
                registration.is_present = is_present
                registration.save()
                updated_count += 1
            except ExamRegistration.DoesNotExist:
                continue
        
        return Response({
            'message': f'Attendance marked for {updated_count} students'
        })


class EnhancedExamResultViewSet(viewsets.ModelViewSet):
    """ViewSet for managing enhanced exam results"""
    serializer_class = EnhancedExamResultSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = EnhancedExamResult.objects.all()
        exam = self.request.query_params.get('exam', None)
        student = self.request.query_params.get('student', None)
        
        if exam:
            queryset = queryset.filter(exam_id=exam)
        if student:
            queryset = queryset.filter(student_id=student)
            
        return queryset.select_related('exam', 'student', 'marked_by', 'verified_by')
    
    @action(detail=False, methods=['post'])
    def bulk_create_results(self, request):
        """Bulk create exam results"""
        exam_id = request.data.get('exam_id')
        results_data = request.data.get('results_data', [])
        
        if not exam_id or not results_data:
            return Response(
                {'error': 'exam_id and results_data are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        exam = get_object_or_404(EnhancedExam, id=exam_id)
        created_results = []
        errors = []
        
        for result_data in results_data:
            result_data['exam'] = exam.id
            serializer = self.get_serializer(data=result_data)
            
            if serializer.is_valid():
                result = serializer.save()
                created_results.append(result.id)
            else:
                errors.append({
                    'student_id': result_data.get('student'),
                    'errors': serializer.errors
                })
        
        return Response({
            'created_results': created_results,
            'errors': errors
        })
    
    @action(detail=True, methods=['post'])
    def verify(self, request, pk=None):
        """Verify an exam result"""
        result = self.get_object()
        result.is_verified = True
        result.verified_by = request.user.teacher
        result.verification_date = timezone.now()
        result.save()
        
        return Response({'message': 'Result verified successfully'})


class ExamMalpracticeViewSet(viewsets.ModelViewSet):
    """ViewSet for managing exam malpractice incidents"""
    serializer_class = ExamMalpracticeSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = ExamMalpractice.objects.all()
        exam = self.request.query_params.get('exam', None)
        student = self.request.query_params.get('student', None)
        is_resolved = self.request.query_params.get('is_resolved', None)
        
        if exam:
            queryset = queryset.filter(exam_id=exam)
        if student:
            queryset = queryset.filter(student_id=student)
        if is_resolved is not None:
            queryset = queryset.filter(is_resolved=is_resolved.lower() == 'true')
            
        return queryset.select_related('exam', 'student', 'reported_by')
    
    @action(detail=True, methods=['post'])
    def resolve(self, request, pk=None):
        """Resolve a malpractice incident"""
        incident = self.get_object()
        incident.is_resolved = True
        incident.resolution_date = timezone.now()
        incident.action_taken = request.data.get('action_taken', '')
        incident.penalty_applied = request.data.get('penalty_applied', '')
        incident.save()
        
        return Response({'message': 'Malpractice incident resolved successfully'})
