# Generated by Django 5.2.1 on 2025-06-01 13:25

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BranchAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('student_count', models.IntegerField(default=0)),
                ('teacher_count', models.IntegerField(default=0)),
                ('fee_collection_rate', models.DecimalField(decimal_places=2, max_digits=5)),
                ('average_performance', models.DecimalField(decimal_places=2, max_digits=5)),
                ('last_updated', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Branch Analytics',
            },
        ),
        migrations.CreateModel(
            name='EnrollmentMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_enrollments', models.IntegerField(default=0)),
                ('new_enrollments', models.IntegerField(default=0)),
                ('transfers_in', models.IntegerField(default=0)),
                ('transfers_out', models.IntegerField(default=0)),
                ('dropouts', models.IntegerField(default=0)),
                ('date_generated', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='PerformanceMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('metrics_data', models.JSONField()),
                ('date_generated', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
