import axiosWithAuth from '../utils/axiosInterceptor';

// API URLs with correct paths
const API_ENDPOINTS = {
  curriculumSystems: '/api/academics/curriculum/systems/',
  educationLevels: '/api/academics/curriculum/education-levels/',
  schoolCurriculumConfig: '/api/academics/curriculum/school-config/',
  branchCurriculumConfig: '/api/academics/curriculum/branch-config/',
  curriculumTemplates: '/api/academics/curriculum/templates/'
};

// Temporary API_URL for backward compatibility - TODO: Replace all instances with API_ENDPOINTS
const API_URL = '/api/academics';

/**
 * API client for curriculum-related endpoints
 */
export const curriculumApi = {
  /**
   * Get all curriculum systems
   *
   * @returns {Promise} Promise with curriculum systems data
   */
  getCurriculumSystems: async (params = {}) => {
    try {
      console.log('Fetching curriculum systems with params:', params);
      const response = await axiosWithAuth.get(API_ENDPOINTS.curriculumSystems, { params });
      const data = response.data;

      // Handle paginated response
      if (data && data.results && Array.isArray(data.results)) {
        return data.results;
      } else if (Array.isArray(data)) {
        return data;
      } else {
        console.warn('API returned unexpected data format for curriculum systems:', data);
        return [];
      }
    } catch (error) {
      console.error('Error fetching curriculum systems:', error);
      // Return a default system instead of throwing an error
      // This makes the application more resilient
      return [{
        id: 0,
        code: 'DEFAULT',
        name: 'Default Curriculum',
        description: 'Default curriculum system',
        country_code: 'KE'
      }];
    }
  },

  /**
   * Get a specific curriculum system
   *
   * @param {number} id - Curriculum system ID
   * @returns {Promise} Promise with curriculum system data
   */
  getCurriculumSystem: async (id) => {
    try {
      const response = await axiosWithAuth.get(`${API_URL}/curriculum/systems/${id}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching curriculum system:', error);
      throw error;
    }
  },

  /**
   * Create a new curriculum system
   *
   * @param {Object} data - Curriculum system data
   * @returns {Promise} Promise with created curriculum system data
   */
  createCurriculumSystem: async (data) => {
    try {
      const response = await axiosWithAuth.post(`${API_URL}/curriculum/systems/`, data);
      return response.data;
    } catch (error) {
      console.error('Error creating curriculum system:', error);
      throw error;
    }
  },

  /**
   * Update a curriculum system
   *
   * @param {number} id - Curriculum system ID
   * @param {Object} data - Updated curriculum system data
   * @returns {Promise} Promise with updated curriculum system data
   */
  updateCurriculumSystem: async (id, data) => {
    try {
      const response = await axiosWithAuth.put(`${API_URL}/curriculum/systems/${id}/`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating curriculum system:', error);
      throw error;
    }
  },

  /**
   * Delete a curriculum system
   *
   * @param {number} id - Curriculum system ID
   * @returns {Promise} Promise with deletion status
   */
  deleteCurriculumSystem: async (id) => {
    try {
      const response = await axiosWithAuth.delete(`${API_URL}/curriculum/systems/${id}/`);
      return response.data;
    } catch (error) {
      console.error('Error deleting curriculum system:', error);
      throw error;
    }
  },

  /**
   * Get all education levels
   *
   * @returns {Promise} Promise with education levels data
   */
  getEducationLevels: async (params = {}) => {
    try {
      console.log('Fetching education levels with params:', params);
      const response = await axiosWithAuth.get(API_ENDPOINTS.educationLevels, { params });
      const data = response.data;

      // Handle paginated response
      if (data && data.results && Array.isArray(data.results)) {
        return data.results;
      } else if (Array.isArray(data)) {
        return data;
      } else {
        console.warn('API returned unexpected data format for education levels:', data);
        return [];
      }
    } catch (error) {
      console.error('Error fetching education levels:', error);
      throw error;
    }
  },

  /**
   * Get a specific education level
   *
   * @param {number} id - Education level ID
   * @returns {Promise} Promise with education level data
   */
  getEducationLevel: async (id) => {
    try {
      const response = await axiosWithAuth.get(`${API_URL}/education-levels/${id}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching education level:', error);
      throw error;
    }
  },

  /**
   * Create a new education level
   *
   * @param {Object} data - Education level data
   * @returns {Promise} Promise with created education level data
   */
  createEducationLevel: async (data) => {
    try {
      // Make sure we're using curriculum_system_id for the API
      const apiData = { ...data };
      if (apiData.curriculum_system_id) {
        // The API expects curriculum_system_id
        apiData.curriculum_system_id = data.curriculum_system_id;
      }

      const response = await axiosWithAuth.post(`${API_URL}/education-levels/`, apiData);
      return response.data;
    } catch (error) {
      console.error('Error creating education level:', error);
      throw error;
    }
  },

  /**
   * Update an education level
   *
   * @param {number} id - Education level ID
   * @param {Object} data - Updated education level data
   * @returns {Promise} Promise with updated education level data
   */
  updateEducationLevel: async (id, data) => {
    try {
      // Make sure we're using curriculum_system_id for the API
      const apiData = { ...data };
      if (apiData.curriculum_system_id) {
        // The API expects curriculum_system_id
        apiData.curriculum_system_id = data.curriculum_system_id;
      }

      const response = await axiosWithAuth.put(`${API_URL}/education-levels/${id}/`, apiData);
      return response.data;
    } catch (error) {
      console.error('Error updating education level:', error);
      throw error;
    }
  },

  /**
   * Delete an education level
   *
   * @param {number} id - Education level ID
   * @returns {Promise} Promise with deletion status
   */
  deleteEducationLevel: async (id) => {
    try {
      const response = await axiosWithAuth.delete(`${API_URL}/education-levels/${id}/`);
      return response.data;
    } catch (error) {
      console.error('Error deleting education level:', error);
      throw error;
    }
  },

  /**
   * Get all class progression rules
   *
   * @returns {Promise} Promise with class progression rules data
   */
  getClassProgressionRules: async () => {
    try {
      const response = await axiosWithAuth.get(`${API_URL}/class-progression/`);
      const data = response.data;

      // Handle paginated response
      if (data && data.results && Array.isArray(data.results)) {
        return data.results;
      } else if (Array.isArray(data)) {
        return data;
      } else {
        console.warn('API returned unexpected data format for class progression rules:', data);
        return [];
      }
    } catch (error) {
      console.error('Error fetching class progression rules:', error);
      throw error;
    }
  },

  /**
   * Get a specific class progression rule
   *
   * @param {number} id - Class progression rule ID
   * @returns {Promise} Promise with class progression rule data
   */
  getClassProgressionRule: async (id) => {
    try {
      const response = await axiosWithAuth.get(`${API_URL}/class-progression/${id}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching class progression rule:', error);
      throw error;
    }
  },

  /**
   * Create a new class progression rule
   *
   * @param {Object} data - Class progression rule data
   * @returns {Promise} Promise with created class progression rule data
   */
  createClassProgressionRule: async (data) => {
    try {
      const response = await axiosWithAuth.post(`${API_URL}/class-progression/`, data);
      return response.data;
    } catch (error) {
      console.error('Error creating class progression rule:', error);
      throw error;
    }
  },

  /**
   * Update a class progression rule
   *
   * @param {number} id - Class progression rule ID
   * @param {Object} data - Updated class progression rule data
   * @returns {Promise} Promise with updated class progression rule data
   */
  updateClassProgressionRule: async (id, data) => {
    try {
      const response = await axiosWithAuth.put(`${API_URL}/class-progression/${id}/`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating class progression rule:', error);
      throw error;
    }
  },

  /**
   * Delete a class progression rule
   *
   * @param {number} id - Class progression rule ID
   * @returns {Promise} Promise with deletion status
   */
  deleteClassProgressionRule: async (id) => {
    try {
      const response = await axiosWithAuth.delete(`${API_URL}/class-progression/${id}/`);
      return response.data;
    } catch (error) {
      console.error('Error deleting class progression rule:', error);
      throw error;
    }
  },

  /**
   * Get all education levels for a school
   *
   * @returns {Promise} Promise with education levels data
   */
  getSchoolEducationLevels: async () => {
    try {
      const response = await axiosWithAuth.get(`${API_URL}/education-levels/`);
      const data = response.data;

      // Handle paginated response
      if (data && data.results && Array.isArray(data.results)) {
        return data.results;
      } else if (Array.isArray(data)) {
        return data;
      } else {
        console.warn('API returned unexpected data format for school education levels:', data);
        return [];
      }
    } catch (error) {
      console.error('Error fetching school education levels:', error);
      throw error;
    }
  },

  /**
   * Get education levels by curriculum system
   *
   * @param {string} curriculumCode - Curriculum system code
   * @returns {Promise} Promise with education levels data
   */
  getEducationLevelsByCurriculum: async (curriculumCode) => {
    try {
      // Handle the special case for 8-4-4 curriculum
      let apiCurriculumCode = curriculumCode;
      if (curriculumCode === '8-4-4') {
        apiCurriculumCode = '844';
        console.log('Converting 8-4-4 to 844 for API call');
      }

      console.log(`Fetching education levels for curriculum: ${apiCurriculumCode} (original: ${curriculumCode})`);

      // Try different API endpoints if the first one fails
      let response;
      try {
        // First try the standard endpoint
        response = await axiosWithAuth.get(`${API_URL}/education-levels/`, {
          params: { curriculum_system: apiCurriculumCode }
        });
      } catch (firstError) {
        console.warn(`First attempt failed: ${firstError.message}`);
        // Try alternative endpoint
        try {
          response = await axiosWithAuth.get(`${API_URL}/curriculum/education-levels/`, {
            params: { curriculum_system: apiCurriculumCode }
          });
        } catch (secondError) {
          console.warn(`Second attempt failed: ${secondError.message}`);
          // Try without params as last resort
          response = await axiosWithAuth.get(`${API_URL}/education-levels/`);
        }
      }

      const data = response.data;
      console.log(`Education levels response for ${apiCurriculumCode}:`, data);

      // Handle paginated response
      if (data && data.results && Array.isArray(data.results)) {
        console.log(`Found ${data.results.length} education levels`);
        return data.results;
      } else if (Array.isArray(data)) {
        console.log(`Found ${data.length} education levels`);
        return data;
      } else {
        console.warn('API returned unexpected data format for education levels by curriculum:', data);
        return [];
      }
    } catch (error) {
      console.error(`Error fetching education levels by curriculum (${curriculumCode}):`, error);
      console.error('Error details:', error.response?.data || error.message);
      return []; // Return empty array instead of throwing error
    }
  },

  /**
   * Get school curriculum configuration
   *
   * @param {number} schoolId - Optional school ID to get configuration for a specific school
   * @returns {Promise} Promise with school curriculum configuration data
   */
  getSchoolCurriculumConfig: async (schoolId = null) => {
    try {
      console.log('Fetching school curriculum config for school:', schoolId);

      // If schoolId is provided, add it as a query parameter
      const params = schoolId ? { school: schoolId } : {};
      const response = await axiosWithAuth.get(API_ENDPOINTS.schoolCurriculumConfig, { params });
      console.log('Curriculum config response:', response.data);

      // Handle different response formats
      if (Array.isArray(response.data) && response.data.length > 0) {
        // If it's an array (list endpoint), return the first item
        console.log('Received array of configs, using first one:', response.data[0]);
        return response.data[0];
      } else if (response.data.results && Array.isArray(response.data.results) && response.data.results.length > 0) {
        // If it's a paginated response, return the first item
        console.log('Received paginated configs, using first one:', response.data.results[0]);
        return response.data.results[0];
      } else {
        // Otherwise return the data as is (likely a single object)
        return response.data;
      }
    } catch (error) {
      console.error('Error fetching school curriculum configuration:', error);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }

      // If it's a 404 error, return a default configuration object
      // This prevents the UI from breaking when no configuration exists yet
      if (error.response && (error.response.status === 404 || error.response.status === 403)) {
        console.log('No configuration found or permission denied, creating default config');
        // Get curriculum systems to create a default config
        try {
          // Use a direct API call to avoid circular reference
          const systemsResponse = await axiosWithAuth.get(`${API_URL}/curriculum/systems/`);
          const systems = systemsResponse.data;
          const primarySystem = systems.length > 0 ? systems[0] : null;
          const secondarySystem = systems.length > 1 ? systems[1] : primarySystem;

          const defaultConfig = {
            primary_curriculum: primarySystem,
            secondary_curriculum: secondarySystem,
            is_configured: false,
            id: null,
            school: null
          };

          console.log('Created default config:', defaultConfig);
          return defaultConfig;
        } catch (sysError) {
          console.error('Error loading curriculum systems for default config:', sysError);
        }
      }

      throw error;
    }
  },

  /**
   * Update school curriculum configuration
   *
   * @param {Object} data - Updated curriculum configuration data
   * @returns {Promise} Promise with updated curriculum configuration data
   */
  updateSchoolCurriculumConfig: async (data) => {
    try {
      console.log('Updating curriculum config with data:', data);

      // Determine the URL based on whether we're creating or updating
      let url;
      let method;

      if (data.id) {
        // Update existing config
        url = `${API_URL}/curriculum/school-config/${data.id}/`;
        method = 'put';
      } else {
        // Create new config
        url = `${API_URL}/curriculum/school-config/`;
        method = 'post';
      }

      // Prepare the request data
      const requestData = {
        primary_curriculum_id: data.primary_curriculum_id,
        secondary_curriculum_id: data.secondary_curriculum_id,
        is_transition_period: data.is_transition_period || false,
        transition_details: data.transition_details || '',
        school_id: data.school_id,
        is_provisional: false // Always set to false when manually updated
      };

      console.log('Sending request to:', url);
      console.log('Request method:', method);
      console.log('Request data:', requestData);

      const response = await axiosInstance[method](url, requestData);
      console.log('Response received:', response.data);

      // Store the config in localStorage for backup/fallback
      try {
        localStorage.setItem('lastCurriculumConfig', JSON.stringify({
          ...response.data,
          timestamp: new Date().toISOString()
        }));
        console.log('Saved curriculum config to localStorage');
      } catch (storageError) {
        console.warn('Could not save curriculum config to localStorage:', storageError);
      }

      return response.data;
    } catch (error) {
      console.error('Error updating school curriculum configuration:', error);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }
      throw error;
    }
  },

  /**
   * Get branch curriculum configuration
   *
   * @param {number} branchId - Optional branch ID to get configuration for a specific branch
   * @returns {Promise} Promise with branch curriculum configuration data
   */
  getBranchCurriculumConfig: async (branchId = null) => {
    try {
      // If branchId is provided, add it as a query parameter
      const url = branchId
        ? `${API_URL}/curriculum/branch-config/?branch_id=${branchId}`
        : `${API_URL}/curriculum/branch-config/current/`;

      const response = await axiosWithAuth.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching branch curriculum configuration:', error);
      throw error;
    }
  },

  /**
   * Update branch curriculum configuration
   *
   * @param {Object} data - Updated curriculum configuration data
   * @returns {Promise} Promise with updated curriculum configuration data
   */
  updateBranchCurriculumConfig: async (data) => {
    try {
      const url = data.id
        ? `${API_URL}/curriculum/branch-config/${data.id}/`
        : `${API_URL}/curriculum/branch-config/`;

      // Use PUT for update, POST for create
      const method = data.id ? 'put' : 'post';
      const response = await axiosInstance[method](url, {
        primary_curriculum_id: data.primary_curriculum_id,
        secondary_curriculum_id: data.secondary_curriculum_id,
        is_transition_period: data.is_transition_period,
        transition_details: data.transition_details,
        school_branch_id: data.school_branch_id,
        is_inherited_from_school: data.is_inherited_from_school
      });

      return response.data;
    } catch (error) {
      console.error('Error updating branch curriculum configuration:', error);
      throw error;
    }
  },

  /**
   * Propagate school curriculum configuration to branches
   *
   * @param {number} schoolId - School ID
   * @param {number[]} branchIds - Optional array of branch IDs to propagate to
   * @returns {Promise} Promise with propagation results
   */
  propagateCurriculumConfig: async (schoolId, branchIds = []) => {
    try {
      const response = await axiosWithAuth.post(
        `${API_URL}/curriculum/branch-config/propagate/`,
        {
          school_id: schoolId,
          branch_ids: branchIds
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error propagating curriculum configuration:', error);
      throw error;
    }
  },

  /**
   * Check if the current school has a provisional curriculum configuration
   *
   * @returns {Promise} Promise with provisional configuration check result
   */
  checkProvisionalConfig: async () => {
    try {
      const response = await axiosWithAuth.get(`${API_ENDPOINTS.schoolCurriculumConfig}check_provisional/`);
      return response.data;
    } catch (error) {
      console.error('Error checking provisional curriculum configuration:', error);
      return { has_provisional: false };
    }
  },

  /**
   * Get curriculum configuration templates
   *
   * @param {Object} params - Query parameters
   * @param {boolean} params.is_system - Filter by system templates
   * @param {number} params.school_id - Filter by school ID
   * @returns {Promise} Promise with templates
   */
  getConfigTemplates: async (params = {}) => {
    try {
      const response = await axiosWithAuth.get(`${API_URL}/curriculum/templates/`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching curriculum configuration templates:', error);
      throw error;
    }
  },

  /**
   * Get a specific curriculum configuration template
   *
   * @param {number} id - Template ID
   * @returns {Promise} Promise with template details
   */
  getConfigTemplate: async (id) => {
    try {
      const response = await axiosWithAuth.get(`${API_URL}/curriculum/templates/${id}/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching curriculum configuration template ${id}:`, error);
      throw error;
    }
  },

  /**
   * Create a new curriculum configuration template
   *
   * @param {Object} data - Template data
   * @returns {Promise} Promise with created template
   */
  createConfigTemplate: async (data) => {
    try {
      const response = await axiosWithAuth.post(`${API_URL}/curriculum/templates/`, {
        name: data.name,
        description: data.description,
        primary_curriculum_id: data.primary_curriculum_id,
        secondary_curriculum_id: data.secondary_curriculum_id,
        is_transition_period: data.is_transition_period,
        transition_details: data.transition_details,
        curriculum_modifications: data.curriculum_modifications,
        is_system_template: data.is_system_template,
        school_id: data.school_id
      });
      return response.data;
    } catch (error) {
      console.error('Error creating curriculum configuration template:', error);
      throw error;
    }
  },

  /**
   * Update a curriculum configuration template
   *
   * @param {number} id - Template ID
   * @param {Object} data - Template data
   * @returns {Promise} Promise with updated template
   */
  updateConfigTemplate: async (id, data) => {
    try {
      const response = await axiosWithAuth.put(`${API_URL}/curriculum/templates/${id}/`, {
        name: data.name,
        description: data.description,
        primary_curriculum_id: data.primary_curriculum_id,
        secondary_curriculum_id: data.secondary_curriculum_id,
        is_transition_period: data.is_transition_period,
        transition_details: data.transition_details,
        curriculum_modifications: data.curriculum_modifications,
        is_system_template: data.is_system_template,
        school_id: data.school_id
      });
      return response.data;
    } catch (error) {
      console.error(`Error updating curriculum configuration template ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete a curriculum configuration template
   *
   * @param {number} id - Template ID
   * @returns {Promise} Promise with deletion result
   */
  deleteConfigTemplate: async (id) => {
    try {
      const response = await axiosWithAuth.delete(`${API_URL}/curriculum/templates/${id}/`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting curriculum configuration template ${id}:`, error);
      throw error;
    }
  },

  /**
   * Apply a template to a school
   *
   * @param {number} templateId - Template ID
   * @param {number} schoolId - School ID
   * @returns {Promise} Promise with application result
   */
  applyTemplateToSchool: async (templateId, schoolId) => {
    try {
      const response = await axiosWithAuth.post(
        `${API_URL}/curriculum/templates/${templateId}/apply_to_school/`,
        { school_id: schoolId }
      );
      return response.data;
    } catch (error) {
      console.error(`Error applying template ${templateId} to school ${schoolId}:`, error);
      throw error;
    }
  },

  /**
   * Apply a template to multiple schools at once
   *
   * @param {number} templateId - Template ID
   * @param {number[]} schoolIds - Array of school IDs
   * @returns {Promise} Promise with bulk application result
   */
  applyTemplateToSchools: async (templateId, schoolIds) => {
    try {
      const response = await axiosWithAuth.post(
        `${API_URL}/curriculum/templates/${templateId}/apply_to_schools/`,
        { school_ids: schoolIds }
      );
      return response.data;
    } catch (error) {
      console.error(`Error applying template ${templateId} to multiple schools:`, error);
      throw error;
    }
  },

  /**
   * Get the history of changes for a curriculum configuration
   *
   * @param {number} configId - Configuration ID
   * @param {Object} params - Query parameters
   * @returns {Promise} Promise with history entries
   */
  getConfigHistory: async (configId, params = {}) => {
    try {
      const response = await axiosWithAuth.get(
        `${API_URL}/curriculum/school-config/${configId}/history/`,
        { params }
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching history for configuration ${configId}:`, error);
      throw error;
    }
  },

  /**
   * Compare curriculum configurations between schools
   *
   * @param {number} sourceSchoolId - Source school ID
   * @param {number} targetSchoolId - Target school ID
   * @returns {Promise} Promise with comparison result
   */
  compareConfigurations: async (sourceSchoolId, targetSchoolId) => {
    try {
      const response = await axiosWithAuth.get(
        `${API_URL}/curriculum/school-config/compare/`,
        {
          params: {
            source_school_id: sourceSchoolId,
            target_school_id: targetSchoolId
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error(`Error comparing configurations between schools ${sourceSchoolId} and ${targetSchoolId}:`, error);
      throw error;
    }
  },

  /**
   * Roll back a curriculum configuration to a previous state
   *
   * @param {number} configId - Configuration ID
   * @param {number} historyId - History entry ID to roll back to
   * @returns {Promise} Promise with rollback result
   */
  rollbackConfiguration: async (configId, historyId) => {
    try {
      const response = await axiosWithAuth.post(
        `${API_URL}/curriculum/school-config/${configId}/rollback/`,
        { history_id: historyId }
      );
      return response.data;
    } catch (error) {
      console.error(`Error rolling back configuration ${configId} to history ${historyId}:`, error);
      throw error;
    }
  },

  /**
   * Export a curriculum configuration
   *
   * @param {number} configId - Configuration ID
   * @returns {Promise} Promise with exported configuration data
   */
  exportConfiguration: async (configId) => {
    try {
      const response = await axiosWithAuth.get(
        `${API_URL}/curriculum/school-config/${configId}/export/`
      );
      return response.data;
    } catch (error) {
      console.error(`Error exporting configuration ${configId}:`, error);
      throw error;
    }
  },

  /**
   * Import a curriculum configuration
   *
   * @param {Object} configData - Configuration data to import
   * @param {number} schoolId - Target school ID
   * @returns {Promise} Promise with import result
   */
  importConfiguration: async (configData, schoolId) => {
    try {
      const response = await axiosWithAuth.post(
        `${API_URL}/curriculum/school-config/import_config/`,
        {
          config_data: configData,
          school_id: schoolId
        }
      );
      return response.data;
    } catch (error) {
      console.error(`Error importing configuration for school ${schoolId}:`, error);
      throw error;
    }
  },

  /**
   * Batch import curriculum configurations for multiple schools
   *
   * @param {Object} configData - Configuration data to import
   * @param {Array<{school_id: number}>} schoolMappings - Array of school IDs to apply the configuration to
   * @returns {Promise} Promise with batch import result
   */
  batchImportConfiguration: async (configData, schoolMappings) => {
    try {
      const response = await axiosWithAuth.post(
        `${API_URL}/curriculum/school-config/batch_import/`,
        {
          config_data: configData,
          school_mappings: schoolMappings
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error batch importing configurations:', error);
      throw error;
    }
  },

  /**
   * Compare a template with a school's configuration
   *
   * @param {number} templateId - Template ID
   * @param {number} schoolId - School ID to compare with
   * @returns {Promise} Promise with comparison result
   */
  compareTemplateWithSchool: async (templateId, schoolId) => {
    try {
      const response = await axiosWithAuth.post(
        `${API_URL}/curriculum/templates/${templateId}/compare_with_school/`,
        { school_id: schoolId }
      );
      return response.data;
    } catch (error) {
      console.error(`Error comparing template ${templateId} with school ${schoolId}:`, error);
      throw error;
    }
  },

  /**
   * Generate a compliance report for all schools against a template
   *
   * @param {number} templateId - Template ID
   * @param {Object} params - Query parameters
   * @returns {Promise} Promise with compliance report
   */
  getComplianceReport: async (templateId, params = {}) => {
    try {
      const response = await axiosWithAuth.get(
        `${API_URL}/curriculum/templates/${templateId}/compliance_report/`,
        { params }
      );
      return response.data;
    } catch (error) {
      console.error(`Error generating compliance report for template ${templateId}:`, error);
      throw error;
    }
  },

  /**
   * Get compliance checks
   *
   * @param {Object} params - Query parameters
   * @returns {Promise} Promise with compliance checks
   */
  getComplianceChecks: async (params = {}) => {
    try {
      const response = await axiosWithAuth.get(
        `${API_URL}/curriculum/compliance-checks/`,
        { params }
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching compliance checks:', error);
      throw error;
    }
  },

  /**
   * Get a single compliance check
   *
   * @param {number} checkId - Check ID
   * @returns {Promise} Promise with compliance check
   */
  getComplianceCheck: async (checkId) => {
    try {
      const response = await axiosWithAuth.get(
        `${API_URL}/curriculum/compliance-checks/${checkId}/`
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching compliance check ${checkId}:`, error);
      throw error;
    }
  },

  /**
   * Create a compliance check
   *
   * @param {Object} checkData - Check data
   * @returns {Promise} Promise with created compliance check
   */
  createComplianceCheck: async (checkData) => {
    try {
      const response = await axiosWithAuth.post(
        `${API_URL}/curriculum/compliance-checks/`,
        checkData
      );
      return response.data;
    } catch (error) {
      console.error('Error creating compliance check:', error);
      throw error;
    }
  },

  /**
   * Update a compliance check
   *
   * @param {number} checkId - Check ID
   * @param {Object} checkData - Check data
   * @returns {Promise} Promise with updated compliance check
   */
  updateComplianceCheck: async (checkId, checkData) => {
    try {
      const response = await axiosWithAuth.put(
        `${API_URL}/curriculum/compliance-checks/${checkId}/`,
        checkData
      );
      return response.data;
    } catch (error) {
      console.error(`Error updating compliance check ${checkId}:`, error);
      throw error;
    }
  },

  /**
   * Delete a compliance check
   *
   * @param {number} checkId - Check ID
   * @returns {Promise} Promise with delete result
   */
  deleteComplianceCheck: async (checkId) => {
    try {
      const response = await axiosWithAuth.delete(
        `${API_URL}/curriculum/compliance-checks/${checkId}/`
      );
      return response.data;
    } catch (error) {
      console.error(`Error deleting compliance check ${checkId}:`, error);
      throw error;
    }
  },

  /**
   * Run a compliance check immediately
   *
   * @param {number} checkId - Check ID
   * @returns {Promise} Promise with run result
   */
  runComplianceCheck: async (checkId) => {
    try {
      const response = await axiosWithAuth.post(
        `${API_URL}/curriculum/compliance-checks/${checkId}/run_now/`
      );
      return response.data;
    } catch (error) {
      console.error(`Error running compliance check ${checkId}:`, error);
      throw error;
    }
  },

  /**
   * Get compliance notifications
   *
   * @param {Object} params - Query parameters
   * @returns {Promise} Promise with compliance notifications
   */
  getComplianceNotifications: async (params = {}) => {
    try {
      const response = await axiosWithAuth.get(
        `${API_URL}/curriculum/compliance-notifications/`,
        { params }
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching compliance notifications:', error);
      throw error;
    }
  },

  /**
   * Resolve a compliance notification
   *
   * @param {number} notificationId - Notification ID
   * @param {string} comment - Resolution comment
   * @returns {Promise} Promise with resolve result
   */
  resolveComplianceNotification: async (notificationId, comment = '') => {
    try {
      const response = await axiosWithAuth.post(
        `${API_URL}/curriculum/compliance-notifications/${notificationId}/resolve/`,
        { comment }
      );
      return response.data;
    } catch (error) {
      console.error(`Error resolving compliance notification ${notificationId}:`, error);
      throw error;
    }
  },

  /**
   * Bulk resolve compliance notifications
   *
   * @param {number[]} notificationIds - Notification IDs
   * @param {string} comment - Resolution comment
   * @returns {Promise} Promise with bulk resolve result
   */
  bulkResolveComplianceNotifications: async (notificationIds, comment = '') => {
    try {
      const response = await axiosWithAuth.post(
        `${API_URL}/curriculum/compliance-notifications/bulk_resolve/`,
        {
          notification_ids: notificationIds,
          comment
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error bulk resolving compliance notifications:', error);
      throw error;
    }
  },
};
