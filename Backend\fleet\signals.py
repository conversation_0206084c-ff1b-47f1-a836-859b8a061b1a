from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
from .models import Vehicle, VehicleMaintenance, FuelConsumption

@receiver(post_save, sender=VehicleMaintenance)
def update_vehicle_status(sender, instance, created, **kwargs):
    """Update vehicle status when maintenance is created or updated"""
    if instance.status in ['SCHEDULED', 'IN_PROGRESS']:
        # If maintenance is scheduled or in progress, update vehicle status to maintenance
        if instance.vehicle.status != 'MAINTENANCE':
            instance.vehicle.status = 'MAINTENANCE'
            instance.vehicle.save()
    elif instance.status == 'COMPLETED':
        # If maintenance is completed, check if there are other active maintenance records
        active_maintenance = VehicleMaintenance.objects.filter(
            vehicle=instance.vehicle,
            status__in=['SCHEDULED', 'IN_PROGRESS']
        ).exists()
        
        if not active_maintenance and instance.vehicle.status == 'MAINTENANCE':
            # If no other active maintenance and vehicle is in maintenance, set to active
            instance.vehicle.status = 'ACTIVE'
            instance.vehicle.save()

@receiver(post_save, sender=FuelConsumption)
def update_vehicle_mileage(sender, instance, created, **kwargs):
    """Update vehicle mileage when fuel consumption is recorded"""
    if created and instance.odometer_reading > instance.vehicle.current_mileage:
        instance.vehicle.current_mileage = instance.odometer_reading
        instance.vehicle.save()
