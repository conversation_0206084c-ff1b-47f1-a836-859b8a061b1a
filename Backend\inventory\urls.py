from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    AssetViewSet, AssetCategoryViewSet, SupplyViewSet, SupplyCategoryViewSet,
    SupplyTransactionViewSet, MaintenanceViewSet, SupplierViewSet,
    PurchaseOrderViewSet, PurchaseOrderItemViewSet, InventoryReportViewSet
)

router = DefaultRouter()
router.register('assets', AssetViewSet, basename='asset')
router.register('asset-categories', AssetCategoryViewSet, basename='asset-category')
router.register('supplies', SupplyViewSet, basename='supply')
router.register('supply-categories', SupplyCategoryViewSet, basename='supply-category')
router.register('supply-transactions', SupplyTransactionViewSet, basename='supply-transaction')
router.register('maintenance', MaintenanceViewSet, basename='maintenance')
router.register('purchase-orders', PurchaseOrderViewSet, basename='purchase-order')
router.register('purchase-order-items', PurchaseOrderItemViewSet, basename='purchase-order-item')
router.register('suppliers', SupplierViewSet, basename='supplier')
router.register('reports', InventoryReportViewSet, basename='inventory-report')

urlpatterns = [
    path('', include(router.urls)),
]
