from django import forms
from schools.models import School
from .curriculum_models import CurriculumSystem, SchoolCurriculumConfig, EducationLevel
from .models import ClassRoom

class SchoolCurriculumConfigForm(forms.ModelForm):
    class Meta:
        model = SchoolCurriculumConfig
        fields = ['primary_curriculum', 'secondary_curriculum', 'is_transition_period', 'transition_details']
        widgets = {
            'transition_details': forms.Textarea(attrs={'rows': 4}),
        }
    
    def __init__(self, *args, **kwargs):
        self.school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        # Limit curriculum choices to active ones
        active_curricula = CurriculumSystem.objects.filter(is_active=True)
        self.fields['primary_curriculum'].queryset = active_curricula
        self.fields['secondary_curriculum'].queryset = active_curricula
        
        # Add help text
        self.fields['is_transition_period'].help_text = "Check this if the school is transitioning between curriculum systems"
        self.fields['transition_details'].help_text = "Provide details about the transition (e.g., which grades are using which curriculum)"

class ClassRoomForm(forms.ModelForm):
    education_level = forms.ModelChoiceField(
        queryset=EducationLevel.objects.none(),
        required=False,
        help_text="Select the curriculum-specific education level for this class"
    )
    
    class Meta:
        model = ClassRoom
        fields = ['name', 'grade_level', 'academic_year', 'stream', 'education_level']
    
    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)
        
        if school:
            try:
                # Get school's curriculum config
                config = SchoolCurriculumConfig.objects.get(school=school)
                
                # Get education levels from both primary and secondary curricula
                primary_levels = EducationLevel.objects.filter(curriculum_system=config.primary_curriculum)
                secondary_levels = EducationLevel.objects.filter(curriculum_system=config.secondary_curriculum)
                
                # Combine querysets
                self.fields['education_level'].queryset = primary_levels.union(secondary_levels)
            except SchoolCurriculumConfig.DoesNotExist:
                # If no config exists, show all education levels
                self.fields['education_level'].queryset = EducationLevel.objects.all()
        else:
            # If no school provided, show all education levels
            self.fields['education_level'].queryset = EducationLevel.objects.all()
