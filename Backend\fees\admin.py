from django.contrib import admin
from .models import (
    FeeStructure,
    FeeType,
    FeePayment,
    PaymentPlan,
    PaymentSchedule,
    PaymentReminder,
    Invoice,
    Receipt,
    FeeDiscount,
    FeeArrears,
    Lease,
    LeasePayment
)

@admin.register(FeeStructure)
class FeeStructureAdmin(admin.ModelAdmin):
    list_display = ('name', 'school', 'academic_year', 'term', 'total_amount', 'is_active')
    list_filter = ('school', 'academic_year', 'term', 'is_active')
    search_fields = ('name', 'school__name')

@admin.register(FeeType)
class FeeTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'school', 'description', 'amount', 'is_mandatory')
    list_filter = ('school', 'is_mandatory')
    search_fields = ('name', 'description')

@admin.register(FeePayment)
class FeePaymentAdmin(admin.ModelAdmin):
    list_display = ('student', 'amount_paid', 'payment_date', 'payment_method', 'verified')
    list_filter = ('payment_method', 'verified', 'payment_date')
    search_fields = ('student__first_name', 'student__last_name', 'reference_number')
    date_hierarchy = 'payment_date'

@admin.register(PaymentPlan)
class PaymentPlanAdmin(admin.ModelAdmin):
    list_display = ('student', 'term', 'total_amount', 'installments', 'status')
    list_filter = ('status', 'term')
    search_fields = ('student__first_name', 'student__last_name')

@admin.register(PaymentSchedule)
class PaymentScheduleAdmin(admin.ModelAdmin):
    list_display = ('payment_plan', 'due_date', 'amount', 'paid', 'payment_status')
    list_filter = ('payment_status', 'due_date')
    search_fields = ('payment_plan__student__first_name', 'payment_plan__student__last_name')

@admin.register(PaymentReminder)
class PaymentReminderAdmin(admin.ModelAdmin):
    list_display = ('student', 'due_date', 'amount_due', 'reminder_type', 'sent')
    list_filter = ('reminder_type', 'sent', 'due_date')
    search_fields = ('student__first_name', 'student__last_name')

@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = ('invoice_number', 'student', 'total_amount', 'due_date', 'status')
    list_filter = ('status', 'due_date')
    search_fields = ('invoice_number', 'student__first_name', 'student__last_name')
    date_hierarchy = 'created_at'

@admin.register(Receipt)
class ReceiptAdmin(admin.ModelAdmin):
    list_display = ('receipt_number', 'payment', 'amount', 'date_issued')
    search_fields = ('receipt_number', 'payment__student__first_name', 'payment__student__last_name')
    date_hierarchy = 'date_issued'

@admin.register(FeeDiscount)
class FeeDiscountAdmin(admin.ModelAdmin):
    list_display = ('name', 'discount_type', 'amount', 'percentage', 'active')
    list_filter = ('discount_type', 'active')
    search_fields = ('name', 'description')

@admin.register(FeeArrears)
class FeeArrearsAdmin(admin.ModelAdmin):
    list_display = ('student', 'term', 'amount', 'status')
    list_filter = ('status', 'term')
    search_fields = ('student__first_name', 'student__last_name')

@admin.register(Lease)
class LeaseAdmin(admin.ModelAdmin):
    list_display = ('asset_name', 'school', 'lease_start_date', 'lease_end_date', 'lease_amount', 'interest_rate')
    list_filter = ('school', 'lease_start_date', 'lease_end_date')
    search_fields = ('asset_name', 'school__name')

@admin.register(LeasePayment)
class LeasePaymentAdmin(admin.ModelAdmin):
    list_display = ('lease', 'payment_date', 'amount')
    list_filter = ('payment_date',)
    search_fields = ('lease__asset_name',)
