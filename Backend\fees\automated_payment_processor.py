"""
Automated Payment Verification and Processing System
Handles automatic payment verification with fraud detection and safeguards
"""

import logging
import json
from decimal import Decimal
from datetime import datetime, timedelta
from django.utils import timezone
from django.db import transaction
from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string

from .school_billing_models import SchoolPayment, SchoolInvoice, SchoolBillingAccount
# from .models import PaymentVerificationRule, PaymentAuditLog, AutomatedPaymentSettings
from schools.models import School
from settings_app.license_models import LicenseSubscription
from mpesa_integration.models import MpesaTransaction, MpesaCallback
from .receipt_generator import ReceiptGenerator
from .notification_service import NotificationService

logger = logging.getLogger(__name__)


class PaymentVerificationEngine:
    """
    Core engine for automated payment verification with fraud detection
    """
    
    def __init__(self):
        self.receipt_generator = ReceiptGenerator()
        self.notification_service = NotificationService()
        
    def process_payment_verification(self, payment_id, verification_source='AUTO'):
        """
        Main method to process payment verification with all safeguards
        """
        try:
            with transaction.atomic():
                payment = SchoolPayment.objects.select_for_update().get(id=payment_id)
                
                # Check if payment is eligible for auto-verification
                if not self._is_eligible_for_auto_verification(payment):
                    logger.info(f"Payment {payment.payment_reference} not eligible for auto-verification")
                    return False
                
                # Run fraud detection checks
                fraud_score = self._calculate_fraud_score(payment)
                if fraud_score > self._get_fraud_threshold():
                    self._flag_for_manual_review(payment, fraud_score)
                    return False
                
                # Verify payment details
                verification_result = self._verify_payment_details(payment)
                if not verification_result['valid']:
                    self._log_verification_failure(payment, verification_result['reason'])
                    return False
                
                # Process the verification
                success = self._execute_payment_verification(payment, verification_source)
                
                if success:
                    # Generate receipt
                    receipt = self._generate_payment_receipt(payment)
                    
                    # Send notifications
                    self._send_payment_notifications(payment, receipt)
                    
                    # Log successful verification
                    self._log_verification_success(payment, verification_source)
                    
                return success
                
        except Exception as e:
            logger.error(f"Error processing payment verification for {payment_id}: {e}")
            self._log_verification_error(payment_id, str(e))
            return False
    
    def _is_eligible_for_auto_verification(self, payment):
        """
        Check if payment meets criteria for automatic verification
        """
        try:
            # Check if auto-verification is enabled for the school
            # settings = AutomatedPaymentSettings.objects.get(school=payment.school)
            
            # For now, we log and return False as we don't have the settings model
            logger.warning(f"AutomatedPaymentSettings not available. Defaulting to manual verification for {payment.school.name}")
            return False
            
            if not settings.auto_verification_enabled:
                return False
            
            # Check amount limits
            if payment.amount > settings.max_auto_verification_amount:
                logger.info(f"Payment amount {payment.amount} exceeds auto-verification limit {settings.max_auto_verification_amount}")
                return False
            
            # Check payment method eligibility
            if payment.payment_method not in settings.allowed_auto_verification_methods:
                return False
            
            # Check if payment is already verified
            if payment.status == 'COMPLETED':
                return False
            
            # Check if payment is too old
            age_limit = timezone.now() - timedelta(days=settings.max_payment_age_days)
            if payment.payment_date < age_limit:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking auto-verification eligibility: {e}")
            return False
    
    def _calculate_fraud_score(self, payment):
        """
        Calculate fraud risk score for the payment
        """
        score = 0
        
        try:
            # Check for duplicate payments
            duplicate_count = SchoolPayment.objects.filter(
                school=payment.school,
                amount=payment.amount,
                payment_date__date=payment.payment_date.date(),
                external_reference=payment.external_reference
            ).exclude(id=payment.id).count()
            
            if duplicate_count > 0:
                score += 50  # High risk for duplicates
            
            # Check payment frequency
            recent_payments = SchoolPayment.objects.filter(
                school=payment.school,
                payment_date__gte=timezone.now() - timedelta(hours=1)
            ).count()
            
            if recent_payments > 5:
                score += 30  # Multiple payments in short time
            
            # Check amount patterns
            if payment.amount > Decimal('100000'):  # Large amounts
                score += 20
            
            # Check for round numbers (potential fraud indicator)
            if payment.amount % 1000 == 0 and payment.amount > 10000:
                score += 10
            
            # Check external reference format
            if payment.payment_method == 'MOBILE_MONEY':
                if not self._validate_mpesa_reference(payment.external_reference):
                    score += 25
            
            # Check school payment history
            failed_payments = SchoolPayment.objects.filter(
                school=payment.school,
                status='FAILED',
                payment_date__gte=timezone.now() - timedelta(days=30)
            ).count()
            
            if failed_payments > 3:
                score += 15
            
            return min(score, 100)  # Cap at 100
            
        except Exception as e:
            logger.error(f"Error calculating fraud score: {e}")
            return 100  # High score on error for safety
    
    def _get_fraud_threshold(self):
        """Get the fraud threshold from settings"""
        return getattr(settings, 'PAYMENT_FRAUD_THRESHOLD', 70)
    
    def _validate_mpesa_reference(self, reference):
        """Validate M-Pesa transaction reference format"""
        if not reference:
            return False
        
        # M-Pesa references are typically 10 characters, alphanumeric
        if len(reference) < 8 or len(reference) > 12:
            return False
        
        # Should contain both letters and numbers
        has_letter = any(c.isalpha() for c in reference)
        has_number = any(c.isdigit() for c in reference)
        
        return has_letter and has_number
    
    def _verify_payment_details(self, payment):
        """
        Verify payment details against external sources
        """
        try:
            if payment.payment_method == 'MOBILE_MONEY':
                return self._verify_mpesa_payment(payment)
            elif payment.payment_method == 'BANK_TRANSFER':
                return self._verify_bank_payment(payment)
            else:
                # For other methods, basic validation
                return {'valid': True, 'reason': 'Basic validation passed'}
                
        except Exception as e:
            logger.error(f"Error verifying payment details: {e}")
            return {'valid': False, 'reason': f'Verification error: {str(e)}'}
    
    def _verify_mpesa_payment(self, payment):
        """
        Verify M-Pesa payment against transaction records
        """
        try:
            # Look for matching M-Pesa transaction
            mpesa_transaction = MpesaTransaction.objects.filter(
                mpesa_receipt_number=payment.external_reference,
                amount=payment.amount,
                status='COMPLETED'
            ).first()
            
            if mpesa_transaction:
                return {'valid': True, 'reason': 'M-Pesa transaction verified'}
            
            # Check if it's a manual entry with valid format
            if self._validate_mpesa_reference(payment.external_reference):
                return {'valid': True, 'reason': 'M-Pesa reference format valid'}
            
            return {'valid': False, 'reason': 'M-Pesa transaction not found or invalid'}
            
        except Exception as e:
            logger.error(f"Error verifying M-Pesa payment: {e}")
            return {'valid': False, 'reason': f'M-Pesa verification error: {str(e)}'}
    
    def _verify_bank_payment(self, payment):
        """
        Verify bank transfer payment
        """
        # For bank transfers, we rely on the reference number format
        # In a real implementation, you might integrate with bank APIs
        
        if not payment.external_reference or len(payment.external_reference) < 5:
            return {'valid': False, 'reason': 'Invalid bank reference'}
        
        return {'valid': True, 'reason': 'Bank reference format valid'}
    
    def _execute_payment_verification(self, payment, verification_source):
        """
        Execute the actual payment verification and license activation
        """
        try:
            # Update payment status
            payment.status = 'COMPLETED'
            payment.verified_at = timezone.now()
            payment.save()
            
            # Update invoice if linked
            if payment.invoice:
                invoice = payment.invoice
                invoice.paid_amount += payment.amount
                
                if invoice.paid_amount >= invoice.total_amount:
                    invoice.status = 'PAID'
                    invoice.paid_at = timezone.now()
                
                invoice.save()
                
                # Activate/extend license
                self._activate_school_license(payment.school, invoice)
            
            # Update billing account balance
            billing_account = payment.school.billing_account
            billing_account.current_balance = max(
                billing_account.current_balance - payment.amount, 
                Decimal('0.00')
            )
            billing_account.save()
            
            return True
            
        except Exception as e:
            logger.error(f"Error executing payment verification: {e}")
            return False
    
    def _activate_school_license(self, school, invoice):
        """
        Activate or extend school license based on payment
        """
        try:
            license_sub = school.license
            
            # Calculate extension period based on invoice billing period
            billing_days = (invoice.billing_period_end - invoice.billing_period_start).days
            
            # If license is expired or cancelled, reactivate it
            if license_sub.subscription_status in ['EXPIRED', 'CANCELLED']:
                license_sub.subscription_status = 'ACTIVE'
                license_sub.start_date = timezone.now().date()
                license_sub.expiry_date = timezone.now().date() + timedelta(days=billing_days)
            else:
                # Extend existing license
                if license_sub.expiry_date < timezone.now().date():
                    license_sub.expiry_date = timezone.now().date() + timedelta(days=billing_days)
                else:
                    license_sub.expiry_date += timedelta(days=billing_days)
            
            license_sub.save()
            
            logger.info(f"License activated for {school.name} until {license_sub.expiry_date}")
            
        except LicenseSubscription.DoesNotExist:
            logger.warning(f"No license found for school {school.name}")
        except Exception as e:
            logger.error(f"Error activating license for {school.name}: {e}")
    
    def _generate_payment_receipt(self, payment):
        """
        Generate payment receipt
        """
        try:
            return self.receipt_generator.generate_receipt(payment)
        except Exception as e:
            logger.error(f"Error generating receipt: {e}")
            return None
    
    def _send_payment_notifications(self, payment, receipt):
        """
        Send payment confirmation notifications
        """
        try:
            self.notification_service.send_payment_confirmation(payment, receipt)
        except Exception as e:
            logger.error(f"Error sending notifications: {e}")
    
    def _flag_for_manual_review(self, payment, fraud_score):
        """
        Flag payment for manual review due to high fraud score
        """
        try:
            # logger.warning(...) instead of PaymentAuditLog.objects.create(...)
            logger.warning(f"Payment {payment.payment_reference} flagged for manual review (fraud score: {fraud_score})")
            
            # Send alert to administrators
            self.notification_service.send_fraud_alert(payment, fraud_score)
            
        except Exception as e:
            logger.error(f"Error flagging payment for review: {e}")
    
    def _log_verification_success(self, payment, source):
        """Log successful verification"""
        # logger.warning(...) instead of PaymentAuditLog.objects.create(...)
        logger.warning(f"Payment {payment.payment_reference} auto-verified successfully")
    
    def _log_verification_failure(self, payment, reason):
        """Log verification failure"""
        # logger.warning(...) instead of PaymentAuditLog.objects.create(...)
        logger.warning(f"Payment {payment.payment_reference} verification failed: {reason}")
    
    def _log_verification_error(self, payment_id, error):
        """Log verification error"""
        # pass instead of PaymentAuditLog.objects.create(...)
        pass


# Singleton instance
payment_verification_engine = PaymentVerificationEngine()
