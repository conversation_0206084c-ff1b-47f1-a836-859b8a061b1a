# Generated by Django 5.2.1 on 2025-06-01 13:25

import django.db.models.deletion
import django.utils.timezone
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('schools', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='FeeArrears',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('PARTIALLY_PAID', 'Partially Paid'), ('CLEARED', 'Cleared'), ('WRITTEN_OFF', 'Written Off')], default='PENDING', max_length=20)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='FeeBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('amount_paid', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('balance', models.DecimalField(decimal_places=2, max_digits=10)),
            ],
        ),
        migrations.CreateModel(
            name='FeeDiscount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('discount_type', models.CharField(choices=[('FIXED', 'Fixed Amount'), ('PERCENTAGE', 'Percentage')], max_length=10)),
                ('amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('description', models.TextField(blank=True)),
                ('active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='FeePayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount_paid', models.DecimalField(decimal_places=2, max_digits=10)),
                ('payment_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('payment_method', models.CharField(choices=[('CASH', 'Cash'), ('BANK', 'Bank Transfer'), ('MOBILE_MONEY', 'Mobile Money'), ('CHEQUE', 'Cheque')], max_length=20)),
                ('reference_number', models.CharField(max_length=100, unique=True)),
                ('verified', models.BooleanField(default=False)),
                ('remarks', models.TextField(blank=True)),
            ],
        ),
        migrations.CreateModel(
            name='FeeStatement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('statement_number', models.CharField(max_length=20, unique=True)),
                ('generated_date', models.DateTimeField(auto_now_add=True)),
                ('pdf_file', models.FileField(blank=True, null=True, upload_to='statements/')),
            ],
        ),
        migrations.CreateModel(
            name='FeeStructure',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('academic_year', models.CharField(max_length=9)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('is_active', models.BooleanField(default=True)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='FeeType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('is_mandatory', models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=20, unique=True)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('amount_paid', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('balance', models.DecimalField(decimal_places=2, max_digits=10)),
                ('due_date', models.DateField()),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('PAID', 'Paid'), ('PARTIALLY_PAID', 'Partially Paid'), ('OVERDUE', 'Overdue'), ('CANCELLED', 'Cancelled')], default='PENDING', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Lease',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('asset_name', models.CharField(max_length=100)),
                ('lease_start_date', models.DateField()),
                ('lease_end_date', models.DateField()),
                ('lease_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('interest_rate', models.DecimalField(decimal_places=2, max_digits=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='LeasePayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_date', models.DateField()),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='PaymentAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('total_expected', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_collected', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('collection_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('outstanding_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('number_of_payments', models.IntegerField(default=0)),
                ('average_payment_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
            ],
        ),
        migrations.CreateModel(
            name='PaymentAuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('AUTO_VERIFIED', 'Automatically Verified'), ('MANUAL_VERIFIED', 'Manually Verified'), ('FLAGGED_FOR_REVIEW', 'Flagged for Review'), ('VERIFICATION_FAILED', 'Verification Failed'), ('VERIFICATION_ERROR', 'Verification Error'), ('FRAUD_DETECTED', 'Fraud Detected'), ('RULE_TRIGGERED', 'Rule Triggered')], max_length=30)),
                ('reason', models.TextField()),
                ('automated', models.BooleanField(default=False)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('details', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PaymentNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('PAYMENT_CONFIRMED', 'Payment Confirmed'), ('RECEIPT_GENERATED', 'Receipt Generated'), ('LICENSE_ACTIVATED', 'License Activated'), ('FRAUD_ALERT', 'Fraud Alert'), ('VERIFICATION_FAILED', 'Verification Failed')], max_length=30)),
                ('channel', models.CharField(choices=[('EMAIL', 'Email'), ('SMS', 'SMS'), ('PUSH', 'Push Notification'), ('WEBHOOK', 'Webhook')], max_length=20)),
                ('recipient', models.CharField(max_length=255)),
                ('subject', models.CharField(blank=True, max_length=255)),
                ('message', models.TextField()),
                ('status', models.CharField(default='PENDING', max_length=20)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('delivered_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='PaymentPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('installments', models.IntegerField()),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('COMPLETED', 'Completed'), ('DEFAULTED', 'Defaulted')], default='ACTIVE', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='PaymentReceipt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('receipt_number', models.CharField(max_length=50, unique=True)),
                ('receipt_data', models.JSONField(help_text='Complete receipt data in JSON format')),
                ('pdf_file', models.FileField(blank=True, null=True, upload_to='receipts/')),
                ('template_used', models.CharField(default='default', max_length=50)),
                ('generated_at', models.DateTimeField(auto_now_add=True)),
                ('emailed_to', models.EmailField(blank=True, max_length=254)),
                ('emailed_at', models.DateTimeField(blank=True, null=True)),
                ('email_status', models.CharField(default='PENDING', max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='PaymentReminder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('due_date', models.DateField()),
                ('amount_due', models.DecimalField(decimal_places=2, max_digits=10)),
                ('reminder_type', models.CharField(choices=[('EMAIL', 'Email'), ('SMS', 'SMS'), ('BOTH', 'Both')], max_length=10)),
                ('message', models.TextField()),
                ('sent', models.BooleanField(default=False)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='PaymentSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('due_date', models.DateField()),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('paid', models.BooleanField(default=False)),
                ('payment_status', models.CharField(choices=[('PENDING', 'Pending'), ('PAID', 'Paid'), ('OVERDUE', 'Overdue')], default='PENDING', max_length=20)),
                ('payment_date', models.DateField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='PaymentVerificationRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('rule_type', models.CharField(choices=[('AMOUNT_LIMIT', 'Amount Limit'), ('PAYMENT_METHOD', 'Payment Method'), ('TIME_WINDOW', 'Time Window'), ('FREQUENCY_LIMIT', 'Frequency Limit'), ('CUSTOM', 'Custom Rule')], max_length=20)),
                ('description', models.TextField()),
                ('conditions', models.JSONField(help_text='Rule conditions in JSON format')),
                ('actions', models.JSONField(help_text='Actions to take when rule is triggered')),
                ('applies_to_all_schools', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('priority', models.PositiveIntegerField(default=0, help_text='Higher numbers = higher priority')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-priority', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Receipt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('receipt_number', models.CharField(max_length=20, unique=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('date_issued', models.DateTimeField(auto_now_add=True)),
                ('receipt_template', models.TextField(blank=True, null=True)),
                ('is_digital', models.BooleanField(default=True)),
                ('pdf_file', models.FileField(blank=True, null=True, upload_to='receipts/')),
            ],
        ),
        migrations.CreateModel(
            name='SchoolBillingAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('billing_status', models.CharField(choices=[('ACTIVE', 'Active'), ('SUSPENDED', 'Suspended'), ('DELINQUENT', 'Delinquent'), ('CANCELLED', 'Cancelled')], default='ACTIVE', max_length=20)),
                ('billing_cycle', models.CharField(choices=[('MONTHLY', 'Monthly'), ('QUARTERLY', 'Quarterly'), ('ANNUALLY', 'Annually'), ('CUSTOM', 'Custom')], default='ANNUALLY', max_length=20)),
                ('billing_contact_name', models.CharField(max_length=200)),
                ('billing_contact_email', models.EmailField(max_length=254)),
                ('billing_contact_phone', models.CharField(max_length=20)),
                ('billing_address', models.TextField()),
                ('billing_city', models.CharField(max_length=100)),
                ('billing_country', models.CharField(default='Kenya', max_length=100)),
                ('billing_postal_code', models.CharField(blank=True, max_length=20)),
                ('tax_id', models.CharField(blank=True, help_text='VAT/Tax ID Number', max_length=50)),
                ('tax_rate', models.DecimalField(decimal_places=2, default=Decimal('16.00'), help_text='Tax rate percentage', max_digits=5)),
                ('auto_billing_enabled', models.BooleanField(default=False)),
                ('payment_grace_period_days', models.PositiveIntegerField(default=7)),
                ('current_balance', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('credit_limit', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_billing_date', models.DateTimeField(blank=True, null=True)),
                ('next_billing_date', models.DateTimeField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='SchoolInvoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=50, unique=True)),
                ('invoice_date', models.DateField(default=django.utils.timezone.now)),
                ('due_date', models.DateField()),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('SENT', 'Sent'), ('PAID', 'Paid'), ('OVERDUE', 'Overdue'), ('CANCELLED', 'Cancelled'), ('REFUNDED', 'Refunded')], default='DRAFT', max_length=20)),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=12)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('paid_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('billing_period_start', models.DateField()),
                ('billing_period_end', models.DateField()),
                ('notes', models.TextField(blank=True)),
                ('internal_notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('paid_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'ordering': ['-invoice_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SchoolInvoiceLineItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.CharField(max_length=200)),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('billing_period_start', models.DateField()),
                ('billing_period_end', models.DateField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='SchoolPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_reference', models.CharField(max_length=100, unique=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('payment_method', models.CharField(choices=[('BANK_TRANSFER', 'Bank Transfer'), ('MOBILE_MONEY', 'Mobile Money'), ('CREDIT_CARD', 'Credit Card'), ('CASH', 'Cash'), ('CHEQUE', 'Cheque'), ('OTHER', 'Other')], max_length=20)),
                ('payment_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('REFUNDED', 'Refunded')], default='PENDING', max_length=20)),
                ('external_reference', models.CharField(blank=True, help_text='External payment gateway reference', max_length=200)),
                ('transaction_id', models.CharField(blank=True, max_length=200)),
                ('notes', models.TextField(blank=True)),
                ('verified_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='SchoolSubscriptionPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('plan_type', models.CharField(choices=[('BASIC', 'Basic'), ('STANDARD', 'Standard'), ('PREMIUM', 'Premium'), ('ENTERPRISE', 'Enterprise'), ('CUSTOM', 'Custom')], max_length=20)),
                ('description', models.TextField()),
                ('monthly_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('quarterly_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('annual_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('max_students', models.PositiveIntegerField()),
                ('max_staff', models.PositiveIntegerField()),
                ('max_branches', models.PositiveIntegerField(default=1)),
                ('storage_limit_gb', models.PositiveIntegerField(default=10)),
                ('included_modules', models.JSONField(default=list, help_text='List of included module codes')),
                ('features', models.JSONField(default=list, help_text='List of included features')),
                ('is_active', models.BooleanField(default=True)),
                ('is_featured', models.BooleanField(default=False)),
                ('sort_order', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='StudentFeeAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_fees', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_paid', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('balance', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('discount_applied', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('last_payment_date', models.DateTimeField(blank=True, null=True)),
                ('is_fully_paid', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='AutomatedPaymentSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('auto_verification_enabled', models.BooleanField(default=True)),
                ('max_auto_verification_amount', models.DecimalField(decimal_places=2, default=Decimal('50000.00'), help_text='Maximum amount for automatic verification', max_digits=12)),
                ('allowed_auto_verification_methods', models.JSONField(default=list, help_text='List of payment methods allowed for auto-verification')),
                ('fraud_detection_enabled', models.BooleanField(default=True)),
                ('fraud_threshold', models.IntegerField(default=70, help_text='Fraud score threshold (0-100) above which payments are flagged')),
                ('max_payment_age_days', models.PositiveIntegerField(default=7, help_text='Maximum age of payment in days for auto-verification')),
                ('send_auto_verification_notifications', models.BooleanField(default=True)),
                ('notification_email', models.EmailField(blank=True, max_length=254)),
                ('auto_generate_receipts', models.BooleanField(default=True)),
                ('receipt_template', models.CharField(default='default', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('school', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='auto_payment_settings', to='schools.school')),
            ],
        ),
    ]
