from django.utils import timezone
from django.core.management import call_command
import logging

logger = logging.getLogger(__name__)

def update_expired_licenses():
    """
    Scheduled task to update expired licenses
    This can be run daily via a cron job or task scheduler
    """
    try:
        logger.info("Running scheduled task to update expired licenses")
        call_command('update_expired_licenses')
        logger.info("Completed updating expired licenses")
    except Exception as e:
        logger.error(f"Error updating expired licenses: {str(e)}")
