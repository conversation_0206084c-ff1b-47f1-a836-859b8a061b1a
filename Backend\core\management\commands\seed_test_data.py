from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from core.models import CustomUser
from schools.models import School, SchoolBranch
from library.models import Book, EResource
from inventory.models import AssetCategory, Asset, Supply, SupplyCategory
from communication.models import Announcement, Message
from settings_app.models import SchoolProfile, SystemConfiguration, Role
import random
from datetime import datetime, timedelta

class Command(BaseCommand):
    help = 'Seeds the database with test data for development and testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--flush',
            action='store_true',
            help='Delete existing data before seeding',
        )

    def handle(self, *args, **options):
        # Start transaction
        with transaction.atomic():
            if options['flush']:
                self.stdout.write(self.style.WARNING('Flushing existing data...'))
                # Delete existing data (in reverse order of dependencies)
                # Note: This is a simplified approach. In a real application, you might want to use Django's flush command
                # or implement a more sophisticated approach to handle dependencies correctly.
                self.flush_data()

            self.stdout.write(self.style.SUCCESS('Creating test data...'))

            # Create school branches
            branches = self.create_school_branches()

            # Create users
            users = self.create_users(branches)

            # Create library data
            self.create_library_data(branches, users)

            # Create inventory data
            self.create_inventory_data(branches, users)

            # Create communication data
            self.create_communication_data(branches, users)

            # Create settings data
            self.create_settings_data(branches, users)

            self.stdout.write(self.style.SUCCESS('Test data created successfully!'))

    def flush_data(self):
        """Delete existing data"""
        from django.apps import apps

        # Get all models
        all_models = apps.get_models()

        # Exclude some models that should not be flushed
        excluded_models = ['ContentType', 'Session', 'Permission', 'Group']
        models_to_flush = [model for model in all_models if model.__name__ not in excluded_models]

        # Delete data from each model
        for model in models_to_flush:
            try:
                model.objects.all().delete()
                self.stdout.write(f"Deleted all data from {model.__name__}")
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error deleting data from {model.__name__}: {e}"))

    def create_school_branches(self):
        """Create test school branches"""
        branches = []

        # Create a school
        school = School.objects.create(
            name="ShuleXcel Academy",
            address="123 Education Avenue, City",
            phone="1234567890",
            email="<EMAIL>",
            registration_number="SX-REG-2023-001",
            established_date="2000-01-01"
        )

        # Main branch
        main_branch = SchoolBranch.objects.create(
            school=school,
            name="Main Campus",
            address="123 Main Street, City",
            phone="1234567890",
            email="<EMAIL>",
            registration_number="SX-BR-001",
            established_date="2000-01-01"
        )
        branches.append(main_branch)

        # Secondary branch
        secondary_branch = SchoolBranch.objects.create(
            school=school,
            name="Secondary Campus",
            address="456 Second Street, City",
            phone="0987654321",
            email="<EMAIL>",
            registration_number="SX-BR-002",
            established_date="2005-01-01"
        )
        branches.append(secondary_branch)

        self.stdout.write(f"Created 1 school and {len(branches)} school branches")
        return branches

    def create_users(self, branches):
        """Create test users"""
        users = []

        # Create superuser
        superuser = CustomUser.objects.create_superuser(
            username="admin",
            email="<EMAIL>",
            password="admin123",
            first_name="Admin",
            last_name="User",
            school_branch=branches[0]
        )
        users.append(superuser)

        # Create staff users for each branch
        for branch in branches:
            # Principal
            principal = CustomUser.objects.create_user(
                username=f"principal_{branch.id}",
                email=f"principal_{branch.id}@school.com",
                password="password123",
                first_name="Principal",
                last_name=branch.name,
                is_staff=True,
                school_branch=branch
            )
            users.append(principal)

            # Librarian
            librarian = CustomUser.objects.create_user(
                username=f"librarian_{branch.id}",
                email=f"librarian_{branch.id}@school.com",
                password="password123",
                first_name="Librarian",
                last_name=branch.name,
                school_branch=branch
            )
            users.append(librarian)

            # Inventory Manager
            inventory_manager = CustomUser.objects.create_user(
                username=f"inventory_{branch.id}",
                email=f"inventory_{branch.id}@school.com",
                password="password123",
                first_name="Inventory",
                last_name="Manager",
                school_branch=branch
            )
            users.append(inventory_manager)

            # Teacher
            teacher = CustomUser.objects.create_user(
                username=f"teacher_{branch.id}",
                email=f"teacher_{branch.id}@school.com",
                password="password123",
                first_name="Teacher",
                last_name=branch.name,
                school_branch=branch
            )
            users.append(teacher)

        self.stdout.write(f"Created {len(users)} users")
        return users

    def create_library_data(self, branches, users):
        """Create test library data"""
        books_count = 0
        resources_count = 0

        book_titles = [
            "To Kill a Mockingbird", "1984", "The Great Gatsby",
            "Pride and Prejudice", "The Catcher in the Rye",
            "Harry Potter and the Philosopher's Stone", "The Hobbit",
            "The Lord of the Rings", "Animal Farm", "Brave New World"
        ]

        authors = [
            "Harper Lee", "George Orwell", "F. Scott Fitzgerald",
            "Jane Austen", "J.D. Salinger", "J.K. Rowling",
            "J.R.R. Tolkien", "George Orwell", "Aldous Huxley"
        ]

        categories = ["Fiction", "Non-Fiction", "Science Fiction", "Fantasy", "Mystery", "Biography"]

        # Create books for each branch
        for branch in branches:
            for i in range(5):  # 5 books per branch
                book = Book.objects.create(
                    title=random.choice(book_titles),
                    author=random.choice(authors),
                    isbn=f"978-{random.randint(1000000000, 9999999999)}",
                    publisher="Test Publisher",
                    publication_year=random.randint(1950, 2023),
                    category=random.choice(categories),
                    pages=random.randint(100, 500),
                    quantity=random.randint(1, 10),
                    available_quantity=random.randint(1, 10),
                    location=f"Shelf {random.choice('ABCDE')}-{random.randint(1, 10)}",
                    status="AVAILABLE",
                    school_branch=branch,
                    added_by=users[0]  # Admin user
                )
                books_count += 1

            # Create e-resources for each branch
            for i in range(3):  # 3 e-resources per branch
                resource = EResource.objects.create(
                    title=f"E-Resource {i+1}",
                    author=random.choice(authors),
                    resource_type=random.choice(["EBOOK", "JOURNAL", "ARTICLE", "VIDEO"]),
                    publisher="Digital Publisher",
                    publication_year=random.randint(2000, 2023),
                    category=random.choice(categories),
                    url="https://example.com/resource",
                    access_level=random.choice(["PUBLIC", "RESTRICTED", "PRIVATE"]),
                    school_branch=branch,
                    added_by=users[0]  # Admin user
                )
                resources_count += 1

        self.stdout.write(f"Created {books_count} books and {resources_count} e-resources")

    def create_inventory_data(self, branches, users):
        """Create test inventory data"""
        asset_categories_count = 0
        supply_categories_count = 0
        assets_count = 0
        supplies_count = 0

        asset_category_names = ["Furniture", "Electronics", "Sports Equipment", "Lab Equipment", "Office Supplies"]
        supply_category_names = ["Stationery", "Cleaning Supplies", "Teaching Materials", "Office Supplies", "IT Supplies"]
        asset_names = ["Computer", "Desk", "Chair", "Projector", "Whiteboard", "Microscope", "Printer"]
        supply_names = ["Paper", "Pens", "Notebooks", "Markers", "Printer Toner", "Staplers"]

        # Create asset categories for each branch
        for branch in branches:
            for name in asset_category_names:
                category = AssetCategory.objects.create(
                    name=name,
                    description=f"Asset Category for {name}",
                    school_branch=branch
                )
                asset_categories_count += 1

            # Create supply categories for each branch
            for name in supply_category_names:
                category = SupplyCategory.objects.create(
                    name=name,
                    description=f"Supply Category for {name}",
                    school_branch=branch
                )
                supply_categories_count += 1

            # Get categories for this branch
            branch_asset_categories = AssetCategory.objects.filter(school_branch=branch)
            branch_supply_categories = SupplyCategory.objects.filter(school_branch=branch)

            # Create assets for each branch
            for i in range(10):  # 10 assets per branch
                asset = Asset.objects.create(
                    name=random.choice(asset_names),
                    asset_number=f"ASSET-{branch.id}-{i+1:03d}",
                    category=random.choice(branch_asset_categories),
                    description=f"Test asset description {i+1}",
                    purchase_date=(datetime.now() - timedelta(days=random.randint(1, 365))).strftime('%Y-%m-%d'),
                    purchase_price=random.randint(100, 5000),
                    location=f"Room {random.randint(101, 110)}",
                    status=random.choice(["AVAILABLE", "IN_USE", "UNDER_MAINTENANCE", "DISPOSED"]),
                    school_branch=branch,
                    added_by=users[0]  # Admin user
                )
                assets_count += 1

            # Create supplies for each branch
            for i in range(5):  # 5 supplies per branch
                supply = Supply.objects.create(
                    name=random.choice(supply_names),
                    category=random.choice(branch_supply_categories),
                    description=f"Test supply description {i+1}",
                    unit=random.choice(["Box", "Pack", "Ream", "Each"]),
                    quantity=random.randint(10, 100),
                    minimum_quantity=random.randint(5, 20),
                    unit_price=random.randint(5, 50),
                    location=f"Storage {random.choice('ABCDE')}",
                    school_branch=branch,
                    added_by=users[0]  # Admin user
                )
                supplies_count += 1

        self.stdout.write(f"Created {asset_categories_count} asset categories, {supply_categories_count} supply categories, {assets_count} assets, and {supplies_count} supplies")

    def create_communication_data(self, branches, users):
        """Create test communication data"""
        announcements_count = 0
        messages_count = 0

        announcement_titles = [
            "School Closure Notice", "Upcoming Parent-Teacher Meeting",
            "End of Term Exams", "Sports Day Announcement",
            "Holiday Schedule", "New Library Books"
        ]

        # Create announcements for each branch
        for branch in branches:
            for i in range(3):  # 3 announcements per branch
                announcement = Announcement.objects.create(
                    title=random.choice(announcement_titles),
                    content=f"This is a test announcement content {i+1}.",
                    announcement_type=random.choice(["GENERAL", "ACADEMIC", "EVENT", "EMERGENCY"]),
                    priority=random.choice(["LOW", "MEDIUM", "HIGH", "URGENT"]),
                    target_audience=random.choice(["ALL", "STUDENTS", "TEACHERS", "PARENTS"]),
                    publish_date=(datetime.now() - timedelta(days=random.randint(0, 10))).strftime('%Y-%m-%d'),
                    expiry_date=(datetime.now() + timedelta(days=random.randint(1, 30))).strftime('%Y-%m-%d'),
                    is_active=True,
                    school_branch=branch,
                    created_by=users[0]  # Admin user
                )
                announcements_count += 1

            # Create messages between users
            branch_users = [user for user in users if user.school_branch == branch]
            for i in range(5):  # 5 messages per branch
                sender = random.choice(branch_users)
                recipient = random.choice([u for u in branch_users if u != sender])

                message = Message.objects.create(
                    sender=sender,
                    recipient=recipient,
                    subject=f"Test Message {i+1}",
                    content=f"This is a test message content {i+1}.",
                    status="SENT",
                    is_read=random.choice([True, False]),
                    school_branch=branch
                )
                messages_count += 1

        self.stdout.write(f"Created {announcements_count} announcements and {messages_count} messages")

    def create_settings_data(self, branches, users):
        """Create test settings data"""
        profiles_count = 0
        configs_count = 0
        roles_count = 0

        # Create school profiles for each branch
        for branch in branches:
            profile = SchoolProfile.objects.create(
                school_branch=branch,
                logo="https://example.com/logo.png",
                mission="To provide quality education and nurture future leaders.",
                vision="To be a leading educational institution known for excellence and innovation.",
                core_values="Integrity, Excellence, Respect, Innovation, Teamwork",
                about="This is a test school profile about section.",
                contact_email=f"contact@{branch.name.lower().replace(' ', '')}.com",
                contact_phone=branch.phone,
                website=f"https://www.{branch.name.lower().replace(' ', '')}.com",
                address=branch.address,
                updated_by=users[0]  # Admin user
            )
            profiles_count += 1

            # Create system configuration for each branch
            config = SystemConfiguration.objects.create(
                school_branch=branch,
                academic_year_start_month=1,
                academic_year_end_month=12,
                current_academic_year="2023-2024",
                current_term="Term 2",
                grading_system="PERCENTAGE",
                attendance_tracking_method="DAILY",
                enable_online_payments=True,
                enable_sms_notifications=True,
                enable_email_notifications=True,
                default_language="en",
                timezone="UTC",
                date_format="YYYY-MM-DD",
                time_format="HH:mm",
                system_maintenance_mode=False,
                updated_by=users[0]  # Admin user
            )
            configs_count += 1

            # Create roles for each branch
            role_names = ["Principal", "Teacher", "Librarian", "Inventory Manager", "Student", "Parent"]
            for name in role_names:
                # Make the role name unique by adding the branch name
                unique_name = f"{name} - {branch.name}"
                try:
                    role = Role.objects.create(
                        name=unique_name,
                        description=f"Role for {name} in {branch.name}",
                        permissions={"can_view_dashboard": True},
                        is_active=True,
                        is_system_role=True,
                        school_branch=branch,
                        created_by=users[0]  # Admin user
                    )
                    roles_count += 1
                except Exception as e:
                    self.stdout.write(self.style.WARNING(f"Error creating role {unique_name}: {e}"))

        self.stdout.write(f"Created {profiles_count} school profiles, {configs_count} system configurations, and {roles_count} roles")
