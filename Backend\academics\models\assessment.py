from django.db import models
from django.conf import settings

class Assessment(models.Model):
    """
    Model for managing different types of assessments
    """
    ASSESSMENT_TYPES = [
        ('QUIZ', 'Quiz'),
        ('TEST', 'Test'),
        ('ASSIGNMENT', 'Assignment'),
        ('PROJECT', 'Project'),
        ('PRESENTATION', 'Presentation'),
        ('OBSERVATION', 'Observation'),
        ('EXAM', 'Examination'),
        ('OTHER', 'Other')
    ]

    # Basic information
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    assessment_type = models.CharField(max_length=20, choices=ASSESSMENT_TYPES)
    
    # Subject and class context
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE, related_name='assessments')
    class_room = models.ForeignKey('academics.ClassRoom', on_delete=models.CASCADE, related_name='assessments')
    term = models.ForeignKey('academics.Term', on_delete=models.CASCADE, related_name='assessments')
    
    # Assessment details
    total_marks = models.DecimalField(max_digits=5, decimal_places=2)
    passing_marks = models.DecimalField(max_digits=5, decimal_places=2)
    duration = models.DurationField(null=True, blank=True)
    
    # Curriculum alignment
    curriculum_strand = models.CharField(max_length=100, blank=True)
    learning_outcomes = models.TextField(blank=True)
    assessment_criteria = models.JSONField(null=True, blank=True)
    
    # Scheduling
    scheduled_date = models.DateTimeField()
    submission_deadline = models.DateTimeField(null=True, blank=True)
    
    # Status
    is_published = models.BooleanField(default=False)
    is_graded = models.BooleanField(default=False)
    
    # Metadata
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='created_assessments')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'academics'
        ordering = ['-scheduled_date']
        indexes = [
            models.Index(fields=['subject', 'class_room', 'term']),
            models.Index(fields=['assessment_type', 'is_published']),
            models.Index(fields=['scheduled_date']),
        ]

    def __str__(self):
        return f"{self.title} - {self.get_assessment_type_display()}"

    def get_student_results(self):
        """Get all results for this assessment"""
        return self.results.all()

    def get_class_average(self):
        """Calculate class average for this assessment"""
        results = self.results.all()
        if not results:
            return 0
        return sum(result.score for result in results) / len(results)

    def get_passing_rate(self):
        """Calculate passing rate for this assessment"""
        results = self.results.all()
        if not results:
            return 0
        passing = sum(1 for result in results if result.score >= self.passing_marks)
        return (passing / len(results)) * 100

class AssessmentResult(models.Model):
    """
    Model for storing assessment results
    """
    assessment = models.ForeignKey(Assessment, on_delete=models.CASCADE, related_name='results')
    student = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='assessment_results')
    
    # Scores
    score = models.DecimalField(max_digits=5, decimal_places=2)
    percentage = models.DecimalField(max_digits=5, decimal_places=2)
    grade = models.CharField(max_length=2, blank=True)
    
    # Feedback
    teacher_feedback = models.TextField(blank=True)
    student_feedback = models.TextField(blank=True)
    
    # Status
    is_submitted = models.BooleanField(default=False)
    is_graded = models.BooleanField(default=False)
    
    # Metadata
    submitted_at = models.DateTimeField(null=True, blank=True)
    graded_at = models.DateTimeField(null=True, blank=True)
    graded_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='graded_results'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'academics'
        unique_together = ['assessment', 'student']
        ordering = ['-submitted_at']
        indexes = [
            models.Index(fields=['assessment', 'student']),
            models.Index(fields=['is_submitted', 'is_graded']),
        ]

    def __str__(self):
        return f"{self.student} - {self.assessment.title}"

    def save(self, *args, **kwargs):
        # Calculate percentage
        if self.assessment.total_marks > 0:
            self.percentage = (self.score / self.assessment.total_marks) * 100
        super().save(*args, **kwargs) 