# Generated by Django 5.2.1 on 2025-06-01 13:25

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('schools', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Book',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('author', models.Char<PERSON>ield(max_length=255)),
                ('isbn', models.CharField(max_length=20, unique=True)),
                ('publisher', models.CharField(max_length=255)),
                ('publication_year', models.PositiveIntegerField()),
                ('edition', models.CharField(blank=True, max_length=50, null=True)),
                ('category', models.CharField(max_length=100)),
                ('subject', models.CharField(blank=True, max_length=100, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('pages', models.PositiveIntegerField()),
                ('cover_image', models.ImageField(blank=True, null=True, upload_to='book_covers/')),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('available_quantity', models.PositiveIntegerField(default=1)),
                ('location', models.CharField(help_text='Shelf/Section location', max_length=100)),
                ('status', models.CharField(choices=[('AVAILABLE', 'Available'), ('BORROWED', 'Borrowed'), ('RESERVED', 'Reserved'), ('MAINTENANCE', 'Under Maintenance'), ('LOST', 'Lost')], default='AVAILABLE', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('added_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='added_books', to=settings.AUTH_USER_MODEL)),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='books', to='schools.schoolbranch')),
            ],
            options={
                'ordering': ['title', 'author'],
            },
        ),
        migrations.CreateModel(
            name='Borrowing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('borrow_date', models.DateField(default=django.utils.timezone.now)),
                ('due_date', models.DateField()),
                ('return_date', models.DateField(blank=True, null=True)),
                ('is_returned', models.BooleanField(default=False)),
                ('is_renewed', models.BooleanField(default=False)),
                ('renewal_count', models.PositiveIntegerField(default=0)),
                ('fine_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('fine_paid', models.BooleanField(default=False)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('book', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='borrowings', to='library.book')),
                ('borrower', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='borrowings', to=settings.AUTH_USER_MODEL)),
                ('issued_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='issued_borrowings', to=settings.AUTH_USER_MODEL)),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='borrowings', to='schools.schoolbranch')),
            ],
            options={
                'ordering': ['-borrow_date'],
            },
        ),
        migrations.CreateModel(
            name='EResource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('author', models.CharField(max_length=255)),
                ('resource_type', models.CharField(choices=[('EBOOK', 'E-Book'), ('JOURNAL', 'Journal'), ('ARTICLE', 'Article'), ('VIDEO', 'Video'), ('AUDIO', 'Audio'), ('DOCUMENT', 'Document'), ('OTHER', 'Other')], max_length=20)),
                ('publisher', models.CharField(blank=True, max_length=255, null=True)),
                ('publication_year', models.PositiveIntegerField(blank=True, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('category', models.CharField(max_length=100)),
                ('subject', models.CharField(blank=True, max_length=100, null=True)),
                ('url', models.URLField(blank=True, null=True)),
                ('file', models.FileField(blank=True, null=True, upload_to='e_resources/')),
                ('thumbnail', models.ImageField(blank=True, null=True, upload_to='e_resource_thumbnails/')),
                ('access_level', models.CharField(default='ALL', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('added_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='added_e_resources', to=settings.AUTH_USER_MODEL)),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='e_resources', to='schools.schoolbranch')),
            ],
            options={
                'ordering': ['title', 'resource_type'],
            },
        ),
        migrations.CreateModel(
            name='LibraryReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('report_type', models.CharField(choices=[('BORROWING', 'Borrowing Report'), ('OVERDUE', 'Overdue Report'), ('INVENTORY', 'Inventory Report'), ('FINE', 'Fine Collection Report'), ('USAGE', 'Usage Statistics Report')], max_length=20)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('report_data', models.JSONField(blank=True, null=True)),
                ('file', models.FileField(blank=True, null=True, upload_to='library_reports/')),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('generated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='generated_library_reports', to=settings.AUTH_USER_MODEL)),
                ('school_branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='library_reports', to='schools.schoolbranch')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Return',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('return_date', models.DateField(default=django.utils.timezone.now)),
                ('condition', models.CharField(default='GOOD', max_length=50)),
                ('fine_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('fine_paid', models.BooleanField(default=False)),
                ('payment_date', models.DateField(blank=True, null=True)),
                ('payment_method', models.CharField(blank=True, max_length=50, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('borrowing', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='return_record', to='library.borrowing')),
                ('received_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='received_returns', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-return_date'],
            },
        ),
        migrations.AddIndex(
            model_name='book',
            index=models.Index(fields=['isbn'], name='library_boo_isbn_951e8b_idx'),
        ),
        migrations.AddIndex(
            model_name='book',
            index=models.Index(fields=['title'], name='library_boo_title_c38ef2_idx'),
        ),
        migrations.AddIndex(
            model_name='book',
            index=models.Index(fields=['author'], name='library_boo_author_66aacb_idx'),
        ),
        migrations.AddIndex(
            model_name='book',
            index=models.Index(fields=['category'], name='library_boo_categor_49bcef_idx'),
        ),
        migrations.AddIndex(
            model_name='borrowing',
            index=models.Index(fields=['borrow_date'], name='library_bor_borrow__0e5e19_idx'),
        ),
        migrations.AddIndex(
            model_name='borrowing',
            index=models.Index(fields=['due_date'], name='library_bor_due_dat_bd56d0_idx'),
        ),
        migrations.AddIndex(
            model_name='borrowing',
            index=models.Index(fields=['is_returned'], name='library_bor_is_retu_f11d45_idx'),
        ),
        migrations.AddIndex(
            model_name='eresource',
            index=models.Index(fields=['title'], name='library_ere_title_595187_idx'),
        ),
        migrations.AddIndex(
            model_name='eresource',
            index=models.Index(fields=['resource_type'], name='library_ere_resourc_9ff6a9_idx'),
        ),
        migrations.AddIndex(
            model_name='eresource',
            index=models.Index(fields=['category'], name='library_ere_categor_7c93c2_idx'),
        ),
        migrations.AddIndex(
            model_name='libraryreport',
            index=models.Index(fields=['report_type'], name='library_lib_report__8a381c_idx'),
        ),
        migrations.AddIndex(
            model_name='libraryreport',
            index=models.Index(fields=['start_date'], name='library_lib_start_d_30ca8d_idx'),
        ),
        migrations.AddIndex(
            model_name='libraryreport',
            index=models.Index(fields=['end_date'], name='library_lib_end_dat_e056f2_idx'),
        ),
        migrations.AddIndex(
            model_name='return',
            index=models.Index(fields=['return_date'], name='library_ret_return__2de65d_idx'),
        ),
        migrations.AddIndex(
            model_name='return',
            index=models.Index(fields=['fine_paid'], name='library_ret_fine_pa_130811_idx'),
        ),
    ]
