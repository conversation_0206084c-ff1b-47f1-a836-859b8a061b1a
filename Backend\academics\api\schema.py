from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

# Request/Response schemas
student_performance_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        'performance_prediction': openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'predicted_score': openapi.Schema(type=openapi.TYPE_NUMBER),
                'confidence': openapi.Schema(type=openapi.TYPE_NUMBER),
                'risk_level': openapi.Schema(type=openapi.TYPE_INTEGER),
                'recommendations': openapi.Schema(type=openapi.TYPE_ARRAY, 
                    items=openapi.Schema(type=openapi.TYPE_STRING))
            }
        ),
        'learning_style': openapi.Schema(type=openapi.TYPE_OBJECT),
        'risk_assessment': openapi.Schema(type=openapi.TYPE_OBJECT),
        'improvement_recommendations': openapi.Schema(
            type=openapi.TYPE_ARRAY,
            items=openapi.Schema(type=openapi.TYPE_STRING)
        )
    }
)

learning_path_request = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    required=['subject_id'],
    properties={
        'subject_id': openapi.Schema(type=openapi.TYPE_INTEGER),
        'target_level': openapi.Schema(type=openapi.TYPE_STRING),
        'preferences': openapi.Schema(type=openapi.TYPE_OBJECT)
    }
)

intervention_analytics_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        'active_interventions': openapi.Schema(type=openapi.TYPE_ARRAY),
        'effectiveness_metrics': openapi.Schema(type=openapi.TYPE_OBJECT),
        'recommended_actions': openapi.Schema(type=openapi.TYPE_ARRAY)
    }
)
