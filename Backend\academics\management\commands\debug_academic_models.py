from django.core.management.base import BaseCommand
from django.db import connection
from django.apps import apps
import traceback


class Command(BaseCommand):
    help = 'Debug and validate academic models for all educational systems'

    def add_arguments(self, parser):
        parser.add_argument(
            '--check-models',
            action='store_true',
            help='Check model definitions and relationships',
        )
        parser.add_argument(
            '--check-migrations',
            action='store_true',
            help='Check migration status',
        )
        parser.add_argument(
            '--test-curriculum-support',
            action='store_true',
            help='Test support for different curriculum systems',
        )
        parser.add_argument(
            '--validate-grading',
            action='store_true',
            help='Validate grading system flexibility',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Run all checks',
        )

    def handle(self, *args, **options):
        if options['all']:
            options.update({
                'check_models': True,
                'check_migrations': True,
                'test_curriculum_support': True,
                'validate_grading': True,
            })

        self.stdout.write(
            self.style.SUCCESS('🔍 Starting Academic Module Debug & Validation')
        )

        if options['check_models']:
            self.check_models()

        if options['check_migrations']:
            self.check_migrations()

        if options['test_curriculum_support']:
            self.test_curriculum_support()

        if options['validate_grading']:
            self.validate_grading()

        self.stdout.write(
            self.style.SUCCESS('✅ Academic Module Debug Complete')
        )

    def check_models(self):
        """Check model definitions and relationships"""
        self.stdout.write('\n📋 Checking Model Definitions...')
        
        try:
            # Get all academic models
            academic_app = apps.get_app_config('academics')
            models = academic_app.get_models()
            
            self.stdout.write(f'Found {len(models)} models in academics app')
            
            # Check critical models
            critical_models = [
                'School', 'SchoolBranch', 'Class', 'Subject', 'GradingSystem',
                'CurriculumSystem', 'EducationLevel', 'AcademicYear', 'Term'
            ]
            
            for model_name in critical_models:
                try:
                    model = apps.get_model('academics', model_name)
                    self.stdout.write(f'✅ {model_name}: OK')
                except LookupError:
                    try:
                        model = apps.get_model('schools', model_name)
                        self.stdout.write(f'✅ {model_name}: OK (in schools app)')
                    except LookupError:
                        self.stdout.write(
                            self.style.ERROR(f'❌ {model_name}: NOT FOUND')
                        )
            
            # Check enhanced models
            enhanced_models = [
                'ClassProgressionRule', 'StudentProgression', 'CareerPath',
                'ExamType', 'ExamSession', 'EnhancedExam',
                'LiveStreamingPlatform', 'LiveSession',
                'LessonPlan', 'LessonDelivery'
            ]
            
            self.stdout.write('\n📋 Checking Enhanced Models...')
            for model_name in enhanced_models:
                try:
                    model = apps.get_model('academics', model_name)
                    self.stdout.write(f'✅ {model_name}: OK')
                except LookupError:
                    self.stdout.write(
                        self.style.WARNING(f'⚠️  {model_name}: NOT FOUND (may need migration)')
                    )
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error checking models: {e}')
            )

    def check_migrations(self):
        """Check migration status"""
        self.stdout.write('\n🔄 Checking Migration Status...')
        
        try:
            from django.db.migrations.executor import MigrationExecutor
            executor = MigrationExecutor(connection)
            
            # Check for unapplied migrations
            plan = executor.migration_plan(executor.loader.graph.leaf_nodes())
            
            if plan:
                self.stdout.write(
                    self.style.WARNING(f'⚠️  {len(plan)} unapplied migrations found')
                )
                for migration, backwards in plan:
                    self.stdout.write(f'  - {migration}')
                    
                self.stdout.write('\n💡 Run: python manage.py migrate')
            else:
                self.stdout.write('✅ All migrations applied')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error checking migrations: {e}')
            )

    def test_curriculum_support(self):
        """Test support for different curriculum systems"""
        self.stdout.write('\n🎓 Testing Curriculum System Support...')
        
        try:
            from academics.curriculum_models import CurriculumSystem, EducationLevel
            
            # Test curriculum systems
            test_curricula = [
                {
                    'name': 'Competency Based Curriculum (CBC)',
                    'code': 'CBC',
                    'country': 'Kenya',
                    'levels': [
                        ('Pre-Primary 1', 'PP1', 1),
                        ('Pre-Primary 2', 'PP2', 2),
                        ('Grade 1', 'G1', 3),
                        ('Grade 2', 'G2', 4),
                        ('Grade 3', 'G3', 5),
                        ('Grade 4', 'G4', 6),
                        ('Grade 5', 'G5', 7),
                        ('Grade 6', 'G6', 8),
                        ('Grade 7', 'G7', 9),
                        ('Grade 8', 'G8', 10),
                        ('Grade 9', 'G9', 11),
                    ]
                },
                {
                    'name': '8-4-4 System',
                    'code': '844',
                    'country': 'Kenya',
                    'levels': [
                        ('Standard 1', 'STD1', 1),
                        ('Standard 2', 'STD2', 2),
                        ('Standard 3', 'STD3', 3),
                        ('Standard 4', 'STD4', 4),
                        ('Standard 5', 'STD5', 5),
                        ('Standard 6', 'STD6', 6),
                        ('Standard 7', 'STD7', 7),
                        ('Standard 8', 'STD8', 8),
                        ('Form 1', 'F1', 9),
                        ('Form 2', 'F2', 10),
                        ('Form 3', 'F3', 11),
                        ('Form 4', 'F4', 12),
                    ]
                },
                {
                    'name': 'International General Certificate of Secondary Education',
                    'code': 'IGCSE',
                    'country': 'International',
                    'levels': [
                        ('Year 7', 'Y7', 7),
                        ('Year 8', 'Y8', 8),
                        ('Year 9', 'Y9', 9),
                        ('Year 10', 'Y10', 10),
                        ('Year 11', 'Y11', 11),
                        ('Year 12', 'Y12', 12),
                        ('Year 13', 'Y13', 13),
                    ]
                }
            ]
            
            for curriculum_data in test_curricula:
                self.stdout.write(f'\n📚 Testing {curriculum_data["name"]}...')
                
                # Check if curriculum exists or can be created
                curriculum, created = CurriculumSystem.objects.get_or_create(
                    code=curriculum_data['code'],
                    defaults={
                        'name': curriculum_data['name'],
                        'country': curriculum_data['country'],
                        'is_active': True
                    }
                )
                
                if created:
                    self.stdout.write(f'✅ Created curriculum: {curriculum.name}')
                else:
                    self.stdout.write(f'✅ Found existing curriculum: {curriculum.name}')
                
                # Check education levels
                for level_name, level_code, sequence in curriculum_data['levels']:
                    level, created = EducationLevel.objects.get_or_create(
                        curriculum_system=curriculum,
                        code=level_code,
                        defaults={
                            'name': level_name,
                            'sequence': sequence,
                            'is_active': True
                        }
                    )
                    
                    if created:
                        self.stdout.write(f'  ✅ Created level: {level.name}')
                
                self.stdout.write(f'✅ {curriculum.name} support validated')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error testing curriculum support: {e}')
            )
            traceback.print_exc()

    def validate_grading(self):
        """Validate grading system flexibility"""
        self.stdout.write('\n📊 Validating Grading System Flexibility...')
        
        try:
            from academics.models import GradingSystem, GradeScale
            from academics.curriculum_models import CurriculumSystem
            
            # Test different grading systems
            test_grading_systems = [
                {
                    'name': 'CBC Competency Grading',
                    'type': 'competency',
                    'curriculum': 'CBC',
                    'grades': [
                        ('EE', 'Exceeds Expectations', 90, 100, 4.0),
                        ('ME', 'Meets Expectations', 70, 89, 3.0),
                        ('AE', 'Approaches Expectations', 50, 69, 2.0),
                        ('BE', 'Below Expectations', 0, 49, 1.0),
                    ]
                },
                {
                    'name': '8-4-4 Letter Grading',
                    'type': 'letter',
                    'curriculum': '844',
                    'grades': [
                        ('A', 'Excellent', 80, 100, 12),
                        ('A-', 'Very Good', 75, 79, 11),
                        ('B+', 'Good', 70, 74, 10),
                        ('B', 'Good', 65, 69, 9),
                        ('B-', 'Satisfactory', 60, 64, 8),
                        ('C+', 'Satisfactory', 55, 59, 7),
                        ('C', 'Average', 50, 54, 6),
                        ('C-', 'Average', 45, 49, 5),
                        ('D+', 'Below Average', 40, 44, 4),
                        ('D', 'Below Average', 35, 39, 3),
                        ('D-', 'Poor', 30, 34, 2),
                        ('E', 'Very Poor', 0, 29, 1),
                    ]
                },
                {
                    'name': 'IGCSE Grading',
                    'type': 'letter',
                    'curriculum': 'IGCSE',
                    'grades': [
                        ('A*', 'Outstanding', 90, 100, 9),
                        ('A', 'Excellent', 80, 89, 8),
                        ('B', 'Very Good', 70, 79, 7),
                        ('C', 'Good', 60, 69, 6),
                        ('D', 'Satisfactory', 50, 59, 5),
                        ('E', 'Pass', 40, 49, 4),
                        ('F', 'Fail', 30, 39, 3),
                        ('G', 'Fail', 20, 29, 2),
                        ('U', 'Ungraded', 0, 19, 1),
                    ]
                }
            ]
            
            for grading_data in test_grading_systems:
                self.stdout.write(f'\n📊 Testing {grading_data["name"]}...')
                
                try:
                    curriculum = CurriculumSystem.objects.get(code=grading_data['curriculum'])
                    
                    # Create or get grading system
                    grading_system, created = GradingSystem.objects.get_or_create(
                        name=grading_data['name'],
                        curriculum_system=curriculum,
                        defaults={
                            'grading_type': grading_data['type'],
                            'is_active': True,
                            'is_default': True
                        }
                    )
                    
                    if created:
                        self.stdout.write(f'✅ Created grading system: {grading_system.name}')
                        
                        # Create grade scales
                        for grade, description, min_score, max_score, points in grading_data['grades']:
                            GradeScale.objects.create(
                                grading_system=grading_system,
                                grade=grade,
                                min_score=min_score,
                                max_score=max_score,
                                points=points,
                                remarks=description
                            )
                        
                        self.stdout.write(f'✅ Created {len(grading_data["grades"])} grade scales')
                    else:
                        self.stdout.write(f'✅ Found existing grading system: {grading_system.name}')
                        
                except CurriculumSystem.DoesNotExist:
                    self.stdout.write(
                        self.style.WARNING(f'⚠️  Curriculum {grading_data["curriculum"]} not found')
                    )
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error validating grading systems: {e}')
            )
            traceback.print_exc()
