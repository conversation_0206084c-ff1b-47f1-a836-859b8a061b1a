"""
Enhanced tests for the settings module.
"""
from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from schools.models import School, SchoolBranch
from .enhanced_models import (
    AdvancedSchoolProfile, SystemSettings, NotificationTemplate,
    IntegrationSettings, CustomField, AuditLog
)
from .enhanced_serializers import (
    AdvancedSchoolProfileSerializer, SystemSettingsSerializer,
    NotificationTemplateSerializer
)
import json

User = get_user_model()


class AdvancedSchoolProfileModelTest(TestCase):
    """Test cases for AdvancedSchoolProfile model"""
    
    def setUp(self):
        self.school = School.objects.create(name="Test School")
        self.school_branch = SchoolBranch.objects.create(
            school=self.school,
            name="Main Branch",
            address="123 Test St"
        )
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            school_branch=self.school_branch
        )
    
    def test_create_advanced_profile(self):
        """Test creating an advanced school profile"""
        profile = AdvancedSchoolProfile.objects.create(
            school_branch=self.school_branch,
            school_type='private',
            motto='Excellence in Education',
            vision='To be the best school',
            mission='Educate students well',
            primary_color='#1f2937',
            secondary_color='#3b82f6',
            updated_by=self.user
        )
        
        self.assertEqual(profile.school_branch, self.school_branch)
        self.assertEqual(profile.school_type, 'private')
        self.assertEqual(profile.motto, 'Excellence in Education')
        self.assertTrue(profile.is_active)
    
    def test_color_validation(self):
        """Test hex color validation"""
        profile = AdvancedSchoolProfile(
            school_branch=self.school_branch,
            primary_color='invalid_color'
        )
        
        with self.assertRaises(ValidationError):
            profile.full_clean()
    
    def test_academic_year_validation(self):
        """Test academic year month validation"""
        profile = AdvancedSchoolProfile(
            school_branch=self.school_branch,
            academic_year_start_month=6,
            academic_year_end_month=6  # Same month should fail
        )
        
        with self.assertRaises(ValidationError):
            profile.clean()
    
    def test_capacity_utilization(self):
        """Test capacity utilization calculation"""
        profile = AdvancedSchoolProfile.objects.create(
            school_branch=self.school_branch,
            max_students=100,
            current_students=80,
            max_staff=20,
            current_staff=15
        )
        
        utilization = profile.get_capacity_utilization()
        self.assertEqual(utilization['students'], 80.0)
        self.assertEqual(utilization['staff'], 75.0)
    
    def test_academic_year_duration(self):
        """Test academic year duration calculation"""
        profile = AdvancedSchoolProfile.objects.create(
            school_branch=self.school_branch,
            academic_year_start_month=1,
            academic_year_end_month=12
        )
        
        duration = profile.get_academic_year_duration()
        self.assertEqual(duration, 12)
        
        # Test cross-year duration
        profile.academic_year_start_month = 9
        profile.academic_year_end_month = 6
        duration = profile.get_academic_year_duration()
        self.assertEqual(duration, 10)  # Sep to Jun = 10 months
    
    def test_full_address(self):
        """Test full address formatting"""
        profile = AdvancedSchoolProfile.objects.create(
            school_branch=self.school_branch,
            address_line_1='123 Main St',
            address_line_2='Suite 100',
            city='Test City',
            state_province='Test State',
            postal_code='12345',
            country='Test Country'
        )
        
        full_address = profile.get_full_address()
        expected = '123 Main St, Suite 100, Test City, Test State, 12345, Test Country'
        self.assertEqual(full_address, expected)


class SystemSettingsModelTest(TestCase):
    """Test cases for SystemSettings model"""
    
    def setUp(self):
        self.school = School.objects.create(name="Test School")
        self.school_branch = SchoolBranch.objects.create(
            school=self.school,
            name="Main Branch",
            address="123 Test St"
        )
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            school_branch=self.school_branch
        )
    
    def test_create_system_settings(self):
        """Test creating system settings"""
        settings = SystemSettings.objects.create(
            school_branch=self.school_branch,
            environment='production',
            session_timeout=3600,
            max_login_attempts=5,
            updated_by=self.user
        )
        
        self.assertEqual(settings.school_branch, self.school_branch)
        self.assertEqual(settings.environment, 'production')
        self.assertFalse(settings.maintenance_mode)
    
    def test_session_timeout_validation(self):
        """Test session timeout validation"""
        settings = SystemSettings(
            school_branch=self.school_branch,
            session_timeout=200  # Less than 5 minutes
        )
        
        with self.assertRaises(ValidationError):
            settings.clean()
    
    def test_max_login_attempts_validation(self):
        """Test max login attempts validation"""
        settings = SystemSettings(
            school_branch=self.school_branch,
            max_login_attempts=2  # Less than 3
        )
        
        with self.assertRaises(ValidationError):
            settings.clean()


class NotificationTemplateModelTest(TestCase):
    """Test cases for NotificationTemplate model"""
    
    def setUp(self):
        self.school = School.objects.create(name="Test School")
        self.school_branch = SchoolBranch.objects.create(
            school=self.school,
            name="Main Branch",
            address="123 Test St"
        )
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            school_branch=self.school_branch
        )
    
    def test_create_notification_template(self):
        """Test creating a notification template"""
        template = NotificationTemplate.objects.create(
            school_branch=self.school_branch,
            name='Welcome Email',
            template_type='email',
            trigger_event='user_registration',
            subject='Welcome to {{school_name}}',
            content='Dear {{name}}, welcome to our school!',
            created_by=self.user
        )
        
        self.assertEqual(template.name, 'Welcome Email')
        self.assertEqual(template.template_type, 'email')
        self.assertTrue(template.is_active)
    
    def test_template_rendering(self):
        """Test template content rendering"""
        template = NotificationTemplate.objects.create(
            school_branch=self.school_branch,
            name='Test Template',
            template_type='email',
            trigger_event='custom',
            content='Hello {{name}}, welcome to {{school_name}}!',
            created_by=self.user
        )
        
        context = {
            'name': 'John Doe',
            'school_name': 'Test School'
        }
        
        rendered = template.render_content(context)
        expected = 'Hello John Doe, welcome to Test School!'
        self.assertEqual(rendered, expected)


class CustomFieldModelTest(TestCase):
    """Test cases for CustomField model"""
    
    def setUp(self):
        self.school = School.objects.create(name="Test School")
        self.school_branch = SchoolBranch.objects.create(
            school=self.school,
            name="Main Branch",
            address="123 Test St"
        )
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            school_branch=self.school_branch
        )
    
    def test_create_custom_field(self):
        """Test creating a custom field"""
        field = CustomField.objects.create(
            school_branch=self.school_branch,
            name='emergency_contact',
            field_type='text',
            target_model='student',
            label='Emergency Contact',
            is_required=True,
            created_by=self.user
        )
        
        self.assertEqual(field.name, 'emergency_contact')
        self.assertEqual(field.field_type, 'text')
        self.assertTrue(field.is_required)
    
    def test_field_widget_mapping(self):
        """Test field widget mapping"""
        field = CustomField.objects.create(
            school_branch=self.school_branch,
            name='test_field',
            field_type='email',
            target_model='student',
            label='Test Field',
            created_by=self.user
        )
        
        widget = field.get_field_widget()
        self.assertEqual(widget, 'EmailInput')


class AdvancedSchoolProfileAPITest(APITestCase):
    """API test cases for AdvancedSchoolProfile"""
    
    def setUp(self):
        self.school = School.objects.create(name="Test School")
        self.school_branch = SchoolBranch.objects.create(
            school=self.school,
            name="Main Branch",
            address="123 Test St"
        )
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            school_branch=self.school_branch,
            is_staff=True
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
    
    def test_create_profile_api(self):
        """Test creating profile via API"""
        url = reverse('advanced-school-profile-list')
        data = {
            'school_branch': self.school_branch.id,
            'school_type': 'private',
            'motto': 'Excellence in Education',
            'vision': 'To be the best',
            'mission': 'Educate well',
            'primary_color': '#1f2937',
            'secondary_color': '#3b82f6'
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(AdvancedSchoolProfile.objects.count(), 1)
    
    def test_profile_validation_api(self):
        """Test profile validation via API"""
        url = reverse('advanced-school-profile-list')
        data = {
            'school_branch': self.school_branch.id,
            'primary_color': 'invalid_color'  # Invalid hex color
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('primary_color', response.data)
    
    def test_capacity_report_api(self):
        """Test capacity report endpoint"""
        profile = AdvancedSchoolProfile.objects.create(
            school_branch=self.school_branch,
            max_students=100,
            current_students=80,
            max_staff=20,
            current_staff=15
        )
        
        url = reverse('advanced-school-profile-capacity-report', kwargs={'pk': profile.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('utilization', response.data)
        self.assertIn('recommendations', response.data)
        self.assertEqual(response.data['utilization']['students'], 80.0)


class NotificationTemplateAPITest(APITestCase):
    """API test cases for NotificationTemplate"""
    
    def setUp(self):
        self.school = School.objects.create(name="Test School")
        self.school_branch = SchoolBranch.objects.create(
            school=self.school,
            name="Main Branch",
            address="123 Test St"
        )
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            school_branch=self.school_branch,
            is_staff=True
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
    
    def test_template_test_api(self):
        """Test template testing endpoint"""
        template = NotificationTemplate.objects.create(
            school_branch=self.school_branch,
            name='Test Template',
            template_type='email',
            trigger_event='custom',
            content='Hello {{name}}, welcome to {{school_name}}!',
            created_by=self.user
        )
        
        url = reverse('notification-template-test-template', kwargs={'pk': template.pk})
        data = {
            'context': {
                'name': 'John Doe',
                'school_name': 'Test School'
            }
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('rendered_content', response.data)
        self.assertEqual(
            response.data['rendered_content'],
            'Hello John Doe, welcome to Test School!'
        )
    
    def test_available_variables_api(self):
        """Test available variables endpoint"""
        url = reverse('notification-template-available-variables')
        response = self.client.get(url, {'trigger_event': 'user_registration'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('variables', response.data)
        self.assertIsInstance(response.data['variables'], list)
