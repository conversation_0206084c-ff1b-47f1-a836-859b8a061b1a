from rest_framework import serializers
from .models import (
    Announcement, Message, Email, SMS, Newsletter, Event, ParentTeacherMeeting
)
from core.Serializers import UserSerializer
from academics.serializers import ClassRoomSerializer, StreamSerializer

# Import the Student serializer from users app
# from users.serializers import StudentSerializer

class AnnouncementSerializer(serializers.ModelSerializer):
    created_by_details = UserSerializer(source='created_by', read_only=True)
    target_class_details = ClassRoomSerializer(source='target_class', read_only=True)
    target_stream_details = StreamSerializer(source='target_stream', read_only=True)

    class Meta:
        model = Announcement
        fields = '__all__'
        extra_kwargs = {
            'created_by': {'write_only': True},
            'target_class': {'write_only': True},
            'target_stream': {'write_only': True},
            'school_branch': {'required': True}
        }

class MessageSerializer(serializers.ModelSerializer):
    sender_details = UserSerializer(source='sender', read_only=True)
    recipient_details = UserSerializer(source='recipient', read_only=True)

    class Meta:
        model = Message
        fields = '__all__'
        extra_kwargs = {
            'sender': {'write_only': True},
            'recipient': {'write_only': True},
            'school_branch': {'required': True}
        }

class EmailSerializer(serializers.ModelSerializer):
    sender_details = UserSerializer(source='sender', read_only=True)

    class Meta:
        model = Email
        fields = '__all__'
        ref_name = 'CommunicationEmail'
        extra_kwargs = {
            'sender': {'write_only': True},
            'school_branch': {'required': True}
        }

class SMSSerializer(serializers.ModelSerializer):
    sender_details = UserSerializer(source='sender', read_only=True)

    class Meta:
        model = SMS
        fields = '__all__'
        extra_kwargs = {
            'sender': {'write_only': True},
            'school_branch': {'required': True}
        }

class NewsletterSerializer(serializers.ModelSerializer):
    created_by_details = UserSerializer(source='created_by', read_only=True)

    class Meta:
        model = Newsletter
        fields = '__all__'
        extra_kwargs = {
            'created_by': {'write_only': True},
            'school_branch': {'required': True}
        }

class EventSerializer(serializers.ModelSerializer):
    organizer_details = UserSerializer(source='organizer', read_only=True)
    attendees_details = UserSerializer(source='attendees', many=True, read_only=True)

    class Meta:
        model = Event
        fields = '__all__'
        extra_kwargs = {
            'organizer': {'write_only': True},
            'school_branch': {'required': True}
        }

class ParentTeacherMeetingSerializer(serializers.ModelSerializer):
    teacher_details = UserSerializer(source='teacher', read_only=True)
    parent_details = UserSerializer(source='parent', read_only=True)
    # Temporarily comment out this field until the StudentSerializer is available
    # student_details = StudentSerializer(source='student', read_only=True)

    class Meta:
        model = ParentTeacherMeeting
        fields = '__all__'
        extra_kwargs = {
            'teacher': {'write_only': True},
            'parent': {'write_only': True},
            'student': {'write_only': True},
            'school_branch': {'required': True}
        }
