from django.conf import settings
from django.db import models
import re
from django.utils.crypto import get_random_string

class School(models.Model):
    SCHOOL_TYPES = [
        ('public', 'Public School'),
        ('private', 'Private School'),
        ('international', 'International School'),
        ('boarding', 'Boarding School'),
        ('day', 'Day School'),
        ('mixed', 'Mixed (Day & Boarding)'),
    ]

    EDUCATION_LEVELS = [
        ('primary', 'Primary Only'),
        ('secondary', 'Secondary Only'),
        ('primary_secondary', 'Primary & Secondary'),
        ('pre_primary', 'Pre-Primary Only'),
        ('all_levels', 'All Levels'),
    ]

    name = models.CharField(max_length=200)
    code = models.CharField(max_length=50, unique=True, null=True, blank=True)
    address = models.TextField()
    phone = models.CharField(max_length=20)
    email = models.EmailField()
    website = models.URLField(blank=True, null=True)
    registration_number = models.CharField(max_length=50, unique=True)
    logo = models.ImageField(upload_to='school_logos/', blank=True, null=True)
    established_date = models.DateField()

    # Enhanced school information
    school_type = models.CharField(max_length=20, choices=SCHOOL_TYPES, default='public')
    education_levels = models.CharField(max_length=20, choices=EDUCATION_LEVELS, default='primary_secondary')
    student_capacity = models.PositiveIntegerField(null=True, blank=True)
    current_enrollment = models.PositiveIntegerField(default=0)
    motto = models.CharField(max_length=200, blank=True, null=True)
    vision = models.TextField(blank=True, null=True)
    mission = models.TextField(blank=True, null=True)

    # Contact information
    principal_name = models.CharField(max_length=100, blank=True, null=True)
    principal_phone = models.CharField(max_length=20, blank=True, null=True)
    principal_email = models.EmailField(blank=True, null=True)

    # Location details
    county = models.CharField(max_length=50, blank=True, null=True)
    sub_county = models.CharField(max_length=50, blank=True, null=True)
    ward = models.CharField(max_length=50, blank=True, null=True)
    postal_code = models.CharField(max_length=10, blank=True, null=True)

    # Status fields
    is_active = models.BooleanField(default=True)
    is_archived = models.BooleanField(default=False)
    archived_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']

class SchoolBranch(models.Model):
    school = models.ForeignKey(School, on_delete=models.CASCADE, related_name='branch')
    name = models.CharField(max_length=200)
    code = models.CharField(max_length=50, unique=True, null=True, blank=True)
    address = models.TextField()
    phone = models.CharField(max_length=20)
    email = models.EmailField()
    website = models.URLField(blank=True, null=True)
    registration_number = models.CharField(max_length=50, unique=True)
    logo = models.ImageField(upload_to='branch_logos/', blank=True, null=True)
    established_date = models.DateField()

    # Enhanced branch information
    branch_type = models.CharField(max_length=20, choices=School.SCHOOL_TYPES, default='private')
    student_capacity = models.PositiveIntegerField(null=True, blank=True)
    current_enrollment = models.PositiveIntegerField(default=0)

    # Branch manager/principal
    branch_manager = models.CharField(max_length=100, blank=True, null=True)
    manager_phone = models.CharField(max_length=20, blank=True, null=True)
    manager_email = models.EmailField(blank=True, null=True)

    # Location details
    county = models.CharField(max_length=50, blank=True, null=True)
    sub_county = models.CharField(max_length=50, blank=True, null=True)
    ward = models.CharField(max_length=50, blank=True, null=True)
    postal_code = models.CharField(max_length=10, blank=True, null=True)

    # Facilities
    has_library = models.BooleanField(default=False)
    has_laboratory = models.BooleanField(default=False)
    has_computer_lab = models.BooleanField(default=False)
    has_playground = models.BooleanField(default=False)
    has_boarding_facilities = models.BooleanField(default=False)

    # Status fields
    is_active = models.BooleanField(default=True)
    is_archived = models.BooleanField(default=False)
    archived_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Use string reference for AUTH_USER_MODEL
    users = models.ManyToManyField('core.CustomUser', related_name='branch', blank=True)

    def _generate_branch_code(self):
        """Generates a unique branch code using school initials and an incremental number."""
        # Extract initials from the school name
        initials = ''.join(re.findall(r'\b\w', self.school.name.upper()))  # Example: "Greenwood Academy" → "GA"

        # Find the last assigned branch code for this school
        last_branch = SchoolBranch.objects.filter(school=self.school).order_by('-id').first()

        if last_branch and last_branch.code:
            # Extract the last numerical part and increment it
            last_number = int(last_branch.code[-3:])  # Get last three digits
            new_number = str(last_number + 1).zfill(3)  # Increment and pad with zeros
        else:
            new_number = "001"  # First branch for this school

        return f"{initials}-{new_number}"  # Format: "GA-001"

    def save(self, *args, **kwargs):
        if not self.code:  # Generate code only if not already set
            self.code = self._generate_branch_code()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} - {self.code}"

    class Meta:
        ordering = ['name']

class UserBranchRole(models.Model):
    # Use string reference for AUTH_USER_MODEL
    user = models.ForeignKey('core.CustomUser', on_delete=models.CASCADE, related_name='branch_roles')
    branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='user_roles')
    role = models.CharField(max_length=20)  # Remove choices reference to avoid circular import

    class Meta:
        unique_together = ('user', 'branch', 'role')

    def __str__(self):
        return f"{self.user.get_full_name()} ({self.role}) @ {self.branch.name}"