"""
Management command to set up default settings for schools.
"""
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from schools.models import SchoolBranch
from settings_app.enhanced_models import AdvancedSchoolProfile, SystemSettings, NotificationTemplate
from settings_app.models import SchoolProfile, SystemConfiguration
import json


class Command(BaseCommand):
    help = 'Set up default settings for schools'

    def add_arguments(self, parser):
        parser.add_argument(
            '--school-id',
            type=int,
            help='Specific school branch ID to set up',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force overwrite existing settings',
        )
        parser.add_argument(
            '--enhanced',
            action='store_true',
            help='Create enhanced settings models',
        )

    def handle(self, *args, **options):
        school_id = options.get('school_id')
        force = options.get('force', False)
        enhanced = options.get('enhanced', False)

        if school_id:
            try:
                school_branch = SchoolBranch.objects.get(id=school_id)
                schools = [school_branch]
            except SchoolBranch.DoesNotExist:
                raise CommandError(f'School branch with ID {school_id} does not exist')
        else:
            schools = SchoolBranch.objects.all()

        self.stdout.write(f'Setting up default settings for {len(schools)} school(s)...')

        for school in schools:
            try:
                with transaction.atomic():
                    if enhanced:
                        self.setup_enhanced_settings(school, force)
                    else:
                        self.setup_basic_settings(school, force)
                    
                    self.stdout.write(
                        self.style.SUCCESS(f'✓ Settings created for {school.name}')
                    )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'✗ Failed to create settings for {school.name}: {str(e)}')
                )

        self.stdout.write(self.style.SUCCESS('Default settings setup completed!'))

    def setup_basic_settings(self, school, force):
        """Set up basic settings models"""
        # Create or update SchoolProfile
        profile, created = SchoolProfile.objects.get_or_create(
            school_branch=school,
            defaults={
                'motto': f'Excellence in Education - {school.name}',
                'vision': 'To be a leading educational institution providing quality education.',
                'mission': 'To nurture and develop students to their full potential.',
                'timezone': 'UTC',
                'language': 'en',
                'currency': 'USD',
                'academic_year_start_month': 1,
                'academic_year_end_month': 12,
            }
        )

        if not created and force:
            profile.motto = f'Excellence in Education - {school.name}'
            profile.vision = 'To be a leading educational institution providing quality education.'
            profile.mission = 'To nurture and develop students to their full potential.'
            profile.save()

        # Create or update SystemConfiguration
        config, created = SystemConfiguration.objects.get_or_create(
            school_branch=school,
            defaults={
                'maintenance_mode': False,
                'allow_registration': True,
                'email_notifications': True,
                'sms_notifications': False,
                'backup_enabled': True,
                'max_file_size': 10485760,  # 10MB
                'session_timeout': 3600,  # 1 hour
            }
        )

        if not created and force:
            config.maintenance_mode = False
            config.allow_registration = True
            config.email_notifications = True
            config.save()

    def setup_enhanced_settings(self, school, force):
        """Set up enhanced settings models"""
        # Create or update AdvancedSchoolProfile
        profile, created = AdvancedSchoolProfile.objects.get_or_create(
            school_branch=school,
            defaults={
                'school_type': 'private',
                'motto': f'Excellence in Education - {school.name}',
                'vision': 'To be a leading educational institution providing quality education.',
                'mission': 'To nurture and develop students to their full potential.',
                'core_values': [
                    'Excellence',
                    'Integrity',
                    'Innovation',
                    'Inclusivity',
                    'Collaboration'
                ],
                'primary_color': '#1f2937',
                'secondary_color': '#3b82f6',
                'accent_color': '#10b981',
                'timezone': 'UTC',
                'language': 'en',
                'currency': 'USD',
                'academic_year_start_month': 1,
                'academic_year_end_month': 12,
                'terms_per_year': 3,
                'enabled_features': {
                    'online_payments': True,
                    'sms_notifications': True,
                    'email_notifications': True,
                    'parent_portal': True,
                    'student_portal': True,
                    'mobile_app': True,
                },
                'notification_settings': {
                    'fee_reminders': True,
                    'grade_notifications': True,
                    'attendance_alerts': True,
                    'event_reminders': True,
                },
                'is_active': True,
            }
        )

        # Create or update SystemSettings
        settings, created = SystemSettings.objects.get_or_create(
            school_branch=school,
            defaults={
                'environment': 'production',
                'debug_mode': False,
                'maintenance_mode': False,
                'session_timeout': 3600,
                'password_expiry_days': 90,
                'max_login_attempts': 5,
                'lockout_duration': 1800,
                'require_mfa': False,
                'auto_backup_enabled': True,
                'backup_frequency': 'daily',
                'backup_retention_days': 30,
                'email_notifications_enabled': True,
                'smtp_port': 587,
                'smtp_use_tls': True,
                'sms_notifications_enabled': False,
                'max_file_size': 10485760,
                'allowed_file_types': ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.jpg', '.png'],
                'cache_timeout': 3600,
                'page_size': 20,
                'max_export_records': 10000,
                'api_rate_limit': 1000,
                'webhook_timeout': 30,
            }
        )

        # Create default notification templates
        self.create_default_templates(school)

    def create_default_templates(self, school):
        """Create default notification templates"""
        templates = [
            {
                'name': 'Welcome Email',
                'template_type': 'email',
                'trigger_event': 'user_registration',
                'subject': 'Welcome to {{school_name}}',
                'content': '''Dear {{name}},

Welcome to {{school_name}}! Your account has been successfully created.

Your login credentials:
Username: {{username}}
Password: {{password}}

Please change your password after your first login.

Best regards,
{{school_name}} Administration''',
                'available_variables': ['name', 'email', 'school_name', 'username', 'password']
            },
            {
                'name': 'Password Reset',
                'template_type': 'email',
                'trigger_event': 'password_reset',
                'subject': 'Password Reset - {{school_name}}',
                'content': '''Dear {{name}},

You have requested a password reset for your {{school_name}} account.

Click the link below to reset your password:
{{reset_link}}

This link will expire in {{expiry_time}}.

If you did not request this reset, please ignore this email.

Best regards,
{{school_name}} Administration''',
                'available_variables': ['name', 'reset_link', 'school_name', 'expiry_time']
            },
            {
                'name': 'Fee Payment Confirmation',
                'template_type': 'email',
                'trigger_event': 'fee_payment',
                'subject': 'Payment Confirmation - {{school_name}}',
                'content': '''Dear {{name}},

Thank you for your payment to {{school_name}}.

Payment Details:
Amount: {{amount}}
Date: {{payment_date}}
Receipt Number: {{receipt_number}}
Remaining Balance: {{balance}}

Best regards,
{{school_name}} Finance Department''',
                'available_variables': ['name', 'amount', 'payment_date', 'receipt_number', 'balance']
            },
            {
                'name': 'Grade Published',
                'template_type': 'email',
                'trigger_event': 'grade_published',
                'subject': 'Grades Published - {{school_name}}',
                'content': '''Dear Parent/Guardian,

{{student_name}}'s grades for {{subject}} have been published.

Grade: {{grade}}
Term: {{term}}
Academic Year: {{academic_year}}

You can view detailed results by logging into the parent portal.

Best regards,
{{school_name}} Academic Department''',
                'available_variables': ['student_name', 'subject', 'grade', 'term', 'academic_year']
            },
            {
                'name': 'Attendance Alert',
                'template_type': 'sms',
                'trigger_event': 'attendance_alert',
                'content': 'Alert: {{student_name}} was {{status}} on {{date}}. Contact school if needed. - {{school_name}}',
                'available_variables': ['student_name', 'date', 'status', 'parent_name']
            }
        ]

        for template_data in templates:
            template, created = NotificationTemplate.objects.get_or_create(
                school_branch=school,
                name=template_data['name'],
                template_type=template_data['template_type'],
                defaults=template_data
            )

            if created:
                self.stdout.write(f'  ✓ Created template: {template_data["name"]}')
            else:
                self.stdout.write(f'  - Template exists: {template_data["name"]}')

    def create_custom_fields_examples(self, school):
        """Create example custom fields"""
        from settings_app.enhanced_models import CustomField
        
        custom_fields = [
            {
                'name': 'emergency_contact',
                'field_type': 'text',
                'target_model': 'student',
                'label': 'Emergency Contact',
                'help_text': 'Emergency contact person name',
                'is_required': True,
                'order': 1
            },
            {
                'name': 'blood_type',
                'field_type': 'choice',
                'target_model': 'student',
                'label': 'Blood Type',
                'choices': ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'],
                'order': 2
            },
            {
                'name': 'special_needs',
                'field_type': 'textarea',
                'target_model': 'student',
                'label': 'Special Needs',
                'help_text': 'Any special needs or accommodations required',
                'order': 3
            }
        ]

        for field_data in custom_fields:
            field, created = CustomField.objects.get_or_create(
                school_branch=school,
                name=field_data['name'],
                target_model=field_data['target_model'],
                defaults=field_data
            )

            if created:
                self.stdout.write(f'  ✓ Created custom field: {field_data["label"]}')
