<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Curriculum Compliance Alert</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-bottom: 3px solid #0d6efd;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            color: #0d6efd;
            font-size: 24px;
        }
        .content {
            padding: 0 20px 20px;
        }
        .school-info {
            background-color: #e9f5ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .compliance-score {
            font-size: 24px;
            font-weight: bold;
        }
        .status-compliant {
            color: #198754;
        }
        .status-mostly_compliant {
            color: #ffc107;
        }
        .status-partially_compliant {
            color: #fd7e14;
        }
        .status-non_compliant {
            color: #dc3545;
        }
        .status-no_config {
            color: #6c757d;
        }
        .differences {
            margin-top: 20px;
        }
        .difference-item {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .severity-high {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        .severity-medium {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        .severity-low {
            background-color: #d1e7dd;
            border-left: 4px solid #198754;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #6c757d;
        }
        .button {
            display: inline-block;
            background-color: #0d6efd;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Curriculum Compliance Alert</h1>
    </div>
    
    <div class="content">
        <p>This is an automated notification about a curriculum compliance issue that requires your attention.</p>
        
        <div class="school-info">
            <h2>{{ school.name }}</h2>
            <p><strong>Template:</strong> {{ template.name }}</p>
            <p><strong>Compliance Score:</strong> <span class="compliance-score status-{{ compliance_status|lower }}">{{ compliance_score }}%</span></p>
            <p><strong>Status:</strong> <span class="status-{{ compliance_status|lower }}">{{ compliance_status }}</span></p>
            
            {% if compliance_status == 'no_config' %}
                <p><strong>Issue:</strong> This school does not have a curriculum configuration.</p>
            {% endif %}
        </div>
        
        {% if differences %}
            <div class="differences">
                <h3>Configuration Differences</h3>
                
                {% for diff in differences %}
                    <div class="difference-item severity-{{ diff.severity }}">
                        <p><strong>{{ diff.field|title|replace:"_":" " }}</strong></p>
                        <p><strong>Template Value:</strong> {{ diff.template_value }}</p>
                        <p><strong>School Value:</strong> {{ diff.school_value }}</p>
                        <p><strong>Severity:</strong> {{ diff.severity|title }}</p>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
        
        <a href="{{ app_url }}/academics/curriculum/compliance" class="button">View in ShuleXcel</a>
        
        <div class="footer">
            <p>This is an automated message from ShuleXcel. Please do not reply to this email.</p>
            <p>If you have any questions, please contact your system administrator.</p>
        </div>
    </div>
</body>
</html>
