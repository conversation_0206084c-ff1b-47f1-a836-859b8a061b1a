from rest_framework import serializers
from .models import (
    SchoolProfile, SystemConfiguration, Permission, Role, UserRole,
    BackupConfiguration, Backup, Integration, AuditLog
)
from .license_models import LicenseSubscription, ModuleActivation, LicenseHistory
from .modules import AVAILABLE_MODULES, PA<PERSON>KAGE_TIERS
from core.Serializers import UserSerializer
from schools.serializers import SchoolBranchSerializer

class PermissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Permission
        fields = '__all__'

class SchoolProfileSerializer(serializers.ModelSerializer):
    updated_by_details = UserSerializer(source='updated_by', read_only=True)
    school_branch_details = SchoolBranchSerializer(source='school_branch', read_only=True)

    class Meta:
        model = SchoolProfile
        fields = '__all__'
        extra_kwargs = {
            'updated_by': {'write_only': True},
            'school_branch': {'write_only': True}
        }

class SystemConfigurationSerializer(serializers.ModelSerializer):
    updated_by_details = UserSerializer(source='updated_by', read_only=True)
    school_branch_details = SchoolBranchSerializer(source='school_branch', read_only=True)

    class Meta:
        model = SystemConfiguration
        fields = '__all__'
        extra_kwargs = {
            'updated_by': {'write_only': True},
            'school_branch': {'write_only': True}
        }

class RoleSerializer(serializers.ModelSerializer):
    created_by_details = UserSerializer(source='created_by', read_only=True)
    permissions_details = PermissionSerializer(source='permissions', many=True, read_only=True)

    class Meta:
        model = Role
        fields = '__all__'
        extra_kwargs = {
            'created_by': {'write_only': True},
            'school_branch': {'required': True}
        }

class UserRoleSerializer(serializers.ModelSerializer):
    user_details = UserSerializer(source='user', read_only=True)
    role_details = RoleSerializer(source='role', read_only=True)
    assigned_by_details = UserSerializer(source='assigned_by', read_only=True)

    class Meta:
        model = UserRole
        fields = '__all__'
        extra_kwargs = {
            'user': {'write_only': True},
            'role': {'write_only': True},
            'assigned_by': {'write_only': True},
            'school_branch': {'required': True}
        }

class BackupConfigurationSerializer(serializers.ModelSerializer):
    updated_by_details = UserSerializer(source='updated_by', read_only=True)
    school_branch_details = SchoolBranchSerializer(source='school_branch', read_only=True)

    class Meta:
        model = BackupConfiguration
        fields = '__all__'
        extra_kwargs = {
            'updated_by': {'write_only': True},
            'school_branch': {'write_only': True}
        }

class BackupSerializer(serializers.ModelSerializer):
    created_by_details = UserSerializer(source='created_by', read_only=True)

    class Meta:
        model = Backup
        fields = '__all__'
        extra_kwargs = {
            'created_by': {'write_only': True},
            'school_branch': {'required': True}
        }

class IntegrationSerializer(serializers.ModelSerializer):
    created_by_details = UserSerializer(source='created_by', read_only=True)

    class Meta:
        model = Integration
        fields = '__all__'
        extra_kwargs = {
            'created_by': {'write_only': True},
            'school_branch': {'required': True},
            'api_secret': {'write_only': True}  # For security, don't expose API secrets in responses
        }

class AuditLogSerializer(serializers.ModelSerializer):
    user_details = UserSerializer(source='user', read_only=True)

    class Meta:
        model = AuditLog
        fields = '__all__'
        extra_kwargs = {
            'user': {'write_only': True},
            'school_branch': {'required': True}
        }

class LicenseSubscriptionSerializer(serializers.ModelSerializer):
    package_name = serializers.SerializerMethodField()
    school_name = serializers.SerializerMethodField()
    is_active = serializers.SerializerMethodField()
    enabled_modules = serializers.SerializerMethodField()
    formatted_license_key = serializers.SerializerMethodField()

    class Meta:
        model = LicenseSubscription
        fields = ['id', 'school', 'school_name', 'package_type', 'package_name', 'subscription_status', 'is_active',
                 'start_date', 'expiry_date', 'max_students', 'max_staff', 'max_branches',
                 'custom_modules', 'enabled_modules', 'license_key', 'formatted_license_key',
                 'created_at', 'updated_at']
        extra_kwargs = {
            'license_key': {'write_only': True, 'required': False}
        }

    def get_formatted_license_key(self, obj):
        """Return a formatted version of the license key for display"""
        if not obj.license_key:
            return None

        # If the license key is already in the new format, return it as is
        if obj.license_key.count('-') >= 3:
            return obj.license_key

        # For old format keys, just return them as is
        return obj.license_key

    def get_package_name(self, obj):
        package_info = PACKAGE_TIERS.get(obj.package_type, {})
        return package_info.get('name', obj.package_type)

    def get_school_name(self, obj):
        return obj.school.name if obj.school else None

    def get_is_active(self, obj):
        return obj.is_active()

    def get_enabled_modules(self, obj):
        modules = obj.get_enabled_modules()
        return [
            {
                'code': code,
                'name': AVAILABLE_MODULES.get(code, {}).get('name', code),
                'description': AVAILABLE_MODULES.get(code, {}).get('description', '')
            }
            for code in modules
        ]

class ModuleActivationSerializer(serializers.ModelSerializer):
    enabled_modules_details = serializers.SerializerMethodField()
    available_modules = serializers.SerializerMethodField()

    class Meta:
        model = ModuleActivation
        fields = '__all__'

    def get_enabled_modules_details(self, obj):
        return [
            {
                'code': code,
                'name': AVAILABLE_MODULES.get(code, {}).get('name', code),
                'description': AVAILABLE_MODULES.get(code, {}).get('description', '')
            }
            for code in obj.enabled_modules
        ]

    def get_available_modules(self, obj):
        try:
            available_modules = obj.school_branch.school.license.get_enabled_modules()
        except:
            available_modules = [code for code, info in AVAILABLE_MODULES.items() if info.get('is_core', False)]

        return [
            {
                'code': code,
                'name': AVAILABLE_MODULES.get(code, {}).get('name', code),
                'description': AVAILABLE_MODULES.get(code, {}).get('description', ''),
                'is_enabled': code in obj.enabled_modules,
                'status': AVAILABLE_MODULES.get(code, {}).get('status', 'development'),
                'is_visible': obj.is_module_visible(code, self.context.get('request').user if self.context.get('request') else None)
            }
            for code in available_modules
        ]

class ModuleToggleSerializer(serializers.Serializer):
    module_code = serializers.CharField(max_length=50)
    enable = serializers.BooleanField()

class ModuleStatusSerializer(serializers.Serializer):
    module_code = serializers.CharField(max_length=50)
    status = serializers.CharField(max_length=20)


class LicenseHistorySerializer(serializers.ModelSerializer):
    action_display = serializers.CharField(source='get_action_display', read_only=True)
    user_name = serializers.SerializerMethodField()
    school_name = serializers.SerializerMethodField()

    class Meta:
        model = LicenseHistory
        fields = ['id', 'license', 'action', 'action_display', 'user', 'user_name', 'timestamp',
                 'previous_state', 'new_state', 'notes', 'school_name']
        read_only_fields = ['id', 'timestamp']

    def get_user_name(self, obj):
        if obj.user:
            return f"{obj.user.first_name} {obj.user.last_name}".strip() or obj.user.username
        return "System"

    def get_school_name(self, obj):
        return obj.license.school.name
