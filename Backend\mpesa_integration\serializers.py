from rest_framework import serializers
from .models import MpesaCredential, MpesaTransaction, MpesaCallback

class MpesaCredentialSerializer(serializers.ModelSerializer):
    class Meta:
        model = MpesaCredential
        fields = '__all__'
        extra_kwargs = {
            'consumer_key': {'write_only': True},
            'consumer_secret': {'write_only': True},
            'passkey': {'write_only': True}
        }

class MpesaTransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = MpesaTransaction
        fields = '__all__'

class MpesaCallbackSerializer(serializers.ModelSerializer):
    class Meta:
        model = MpesaCallback
        fields = '__all__'

class STKPushSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=15)
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    reference = serializers.CharField(max_length=100)
    description = serializers.Char<PERSON>ield(required=False, allow_blank=True)
    fee_payment_id = serializers.IntegerField(required=False, allow_null=True)

class TransactionStatusSerializer(serializers.Serializer):
    transaction_id = serializers.CharField(max_length=50)
