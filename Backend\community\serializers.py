from rest_framework import serializers
from .models import (
    Community, CommunityMember, Post, Comment, ClubMeeting,
    MeetingAttendance, Reaction, Notification
)
from core.Serializers import UserSerializer
from academics.serializers import ClassRoomSerializer, StreamSerializer

class CommunitySerializer(serializers.ModelSerializer):
    created_by_details = UserSerializer(source='created_by', read_only=True)
    school_class_details = ClassRoomSerializer(source='school_class', read_only=True)
    stream_details = StreamSerializer(source='stream', read_only=True)
    member_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Community
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at', 'created_by_details', 'school_class_details', 
                           'stream_details', 'member_count']
    
    def get_member_count(self, obj):
        return obj.members.filter(is_active=True).count()

class CommunityMemberSerializer(serializers.ModelSerializer):
    user_details = UserSerializer(source='user', read_only=True)
    community_details = CommunitySerializer(source='community', read_only=True)
    
    class Meta:
        model = CommunityMember
        fields = '__all__'
        read_only_fields = ['joined_at']

class PostSerializer(serializers.ModelSerializer):
    author_details = UserSerializer(source='author', read_only=True)
    community_details = CommunitySerializer(source='community', read_only=True)
    comment_count = serializers.SerializerMethodField()
    reaction_count = serializers.SerializerMethodField()
    user_reaction = serializers.SerializerMethodField()
    
    class Meta:
        model = Post
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at', 'is_edited', 'edited_at', 
                           'author_details', 'community_details', 'comment_count', 
                           'reaction_count', 'user_reaction']
    
    def get_comment_count(self, obj):
        return obj.comments.count()
    
    def get_reaction_count(self, obj):
        return obj.reactions.count()
    
    def get_user_reaction(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            reaction = obj.reactions.filter(user=request.user).first()
            if reaction:
                return reaction.reaction_type
        return None

class CommentSerializer(serializers.ModelSerializer):
    author_details = UserSerializer(source='author', read_only=True)
    post_details = PostSerializer(source='post', read_only=True)
    parent_comment_details = serializers.SerializerMethodField()
    reply_count = serializers.SerializerMethodField()
    reaction_count = serializers.SerializerMethodField()
    user_reaction = serializers.SerializerMethodField()
    
    class Meta:
        model = Comment
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at', 'is_edited', 'edited_at', 
                           'author_details', 'post_details', 'parent_comment_details', 
                           'reply_count', 'reaction_count', 'user_reaction']
    
    def get_parent_comment_details(self, obj):
        if obj.parent_comment:
            return {
                'id': obj.parent_comment.id,
                'author': {
                    'id': obj.parent_comment.author.id,
                    'name': f"{obj.parent_comment.author.first_name} {obj.parent_comment.author.last_name}",
                    'profile_picture': obj.parent_comment.author.profile_picture.url if obj.parent_comment.author.profile_picture else None
                },
                'content': obj.parent_comment.content[:100] + '...' if len(obj.parent_comment.content) > 100 else obj.parent_comment.content
            }
        return None
    
    def get_reply_count(self, obj):
        return obj.replies.count()
    
    def get_reaction_count(self, obj):
        return obj.reactions.count()
    
    def get_user_reaction(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            reaction = obj.reactions.filter(user=request.user).first()
            if reaction:
                return reaction.reaction_type
        return None

class ClubMeetingSerializer(serializers.ModelSerializer):
    community_details = CommunitySerializer(source='community', read_only=True)
    organizer_details = UserSerializer(source='organizer', read_only=True)
    attendance_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ClubMeeting
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at', 'community_details', 
                           'organizer_details', 'attendance_count']
    
    def get_attendance_count(self, obj):
        return obj.attendance.filter(is_present=True).count()

class MeetingAttendanceSerializer(serializers.ModelSerializer):
    meeting_details = ClubMeetingSerializer(source='meeting', read_only=True)
    member_details = CommunityMemberSerializer(source='member', read_only=True)
    
    class Meta:
        model = MeetingAttendance
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at', 'meeting_details', 'member_details']

class ReactionSerializer(serializers.ModelSerializer):
    user_details = UserSerializer(source='user', read_only=True)
    
    class Meta:
        model = Reaction
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at', 'user_details']
    
    def validate(self, data):
        if 'post' not in data and 'comment' not in data:
            raise serializers.ValidationError("Reaction must be associated with either a post or a comment")
        if 'post' in data and 'comment' in data:
            raise serializers.ValidationError("Reaction cannot be associated with both a post and a comment")
        return data

class NotificationSerializer(serializers.ModelSerializer):
    user_details = UserSerializer(source='user', read_only=True)
    community_details = CommunitySerializer(source='community', read_only=True)
    
    class Meta:
        model = Notification
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at', 'user_details', 'community_details']

# Dashboard serializers
class CommunityDashboardSerializer(serializers.Serializer):
    total_communities = serializers.IntegerField()
    active_communities = serializers.IntegerField()
    user_communities = serializers.ListField(child=CommunitySerializer())
    recent_posts = serializers.ListField(child=PostSerializer())
    upcoming_meetings = serializers.ListField(child=ClubMeetingSerializer())
    unread_notifications = serializers.IntegerField()

class CommunityStatisticsSerializer(serializers.Serializer):
    community = CommunitySerializer()
    total_members = serializers.IntegerField()
    active_members = serializers.IntegerField()
    total_posts = serializers.IntegerField()
    total_comments = serializers.IntegerField()
    posts_per_day = serializers.FloatField()
    comments_per_post = serializers.FloatField()
    most_active_members = serializers.ListField(
        child=serializers.DictField(
            child=serializers.CharField()
        )
    )
    engagement_rate = serializers.FloatField()
    meeting_attendance_rate = serializers.FloatField()
