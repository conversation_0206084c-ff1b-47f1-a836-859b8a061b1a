from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from django.contrib.auth import get_user_model
from schools.models import School
from settings_app.license_models import LicenseSubscription

User = get_user_model()

class Command(BaseCommand):
    help = 'Test license expiry and login restrictions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user_id',
            type=int,
            default=1,
            help='User ID to check for school association',
        )
        
        parser.add_argument(
            '--action',
            type=str,
            choices=['expire', 'activate'],
            default='expire',
            help='Action to perform: expire or activate the license',
        )

    def handle(self, *args, **options):
        user_id = options.get('user_id')
        action = options.get('action')
        
        try:
            user = User.objects.get(id=user_id)
            self.stdout.write(f"User: {user.username}, Email: {user.email}")
            
            # Check if user has a school branch
            if not hasattr(user, 'school_branch') or not user.school_branch:
                self.stdout.write(self.style.ERROR("User is not associated with any school branch"))
                return
                
            school = user.school_branch.school
            self.stdout.write(f"School: {school.name} (ID: {school.id})")
            
            # Check if license exists
            try:
                license = LicenseSubscription.objects.get(school=school)
                self.stdout.write(f"Found license: {license.license_key}")
                self.stdout.write(f"Current status: {license.subscription_status}, Expiry: {license.expiry_date}")
                self.stdout.write(f"Is active: {license.is_active()}")
                
                if action == 'expire':
                    # Set license to expired
                    license.subscription_status = 'EXPIRED'
                    license.expiry_date = timezone.now().date() - timedelta(days=1)
                    license.save()
                    self.stdout.write(self.style.SUCCESS(f"License set to EXPIRED with expiry date {license.expiry_date}"))
                    self.stdout.write(f"Is active now: {license.is_active()}")
                    
                elif action == 'activate':
                    # Set license to active
                    license.subscription_status = 'ACTIVE'
                    license.expiry_date = timezone.now().date() + timedelta(days=365)
                    license.save()
                    self.stdout.write(self.style.SUCCESS(f"License set to ACTIVE with expiry date {license.expiry_date}"))
                    self.stdout.write(f"Is active now: {license.is_active()}")
                    
            except LicenseSubscription.DoesNotExist:
                self.stdout.write(self.style.ERROR(f"No license found for school {school.name}"))
                
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"User with ID {user_id} not found"))
