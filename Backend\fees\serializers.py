from rest_framework import serializers
from .models import (
    FeeStructure, FeeType, FeePayment, FeeBalance,
    PaymentPlan, PaymentSchedule, PaymentReminder,
    Invoice, Receipt, FeeDiscount, FeeArrears,
    Lease, LeasePayment, StudentFeeAccount,
    FeeStatement, PaymentAnalytics
)
from users.models import Student
from .school_billing_models import AutomatedPaymentSettings
from schools.serializers import SchoolBasicSerializer

class FeeStructureSerializer(serializers.ModelSerializer):
    class Meta:
        model = FeeStructure
        fields = '__all__'

class FeeTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = FeeType
        fields = '__all__'

class FeePaymentSerializer(serializers.ModelSerializer):
    class Meta:
        model = FeePayment
        fields = '__all__'

class FeeBalanceSerializer(serializers.ModelSerializer):
    class Meta:
        model = FeeBalance
        fields = '__all__'

class PaymentPlanSerializer(serializers.ModelSerializer):
    class Meta:
        model = PaymentPlan
        fields = '__all__'

class PaymentScheduleSerializer(serializers.ModelSerializer):
    class Meta:
        model = PaymentSchedule
        fields = '__all__'

class PaymentReminderSerializer(serializers.ModelSerializer):
    class Meta:
        model = PaymentReminder
        fields = '__all__'

class InvoiceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Invoice
        fields = '__all__'

class ReceiptSerializer(serializers.ModelSerializer):
    class Meta:
        model = Receipt
        fields = '__all__'

class FeeDiscountSerializer(serializers.ModelSerializer):
    class Meta:
        model = FeeDiscount
        fields = '__all__'

class FeeArrearsSerializer(serializers.ModelSerializer):
    class Meta:
        model = FeeArrears
        fields = '__all__'

class LeaseSerializer(serializers.ModelSerializer):
    class Meta:
        model = Lease
        fields = '__all__'

class LeasePaymentSerializer(serializers.ModelSerializer):
    class Meta:
        model = LeasePayment
        fields = '__all__'


class StudentFeeAccountSerializer(serializers.ModelSerializer):
    student_name = serializers.CharField(source='student.first_name', read_only=True)
    student_last_name = serializers.CharField(source='student.last_name', read_only=True)
    term_name = serializers.CharField(source='term.name', read_only=True)

    class Meta:
        model = StudentFeeAccount
        fields = '__all__'


class FeeStatementSerializer(serializers.ModelSerializer):
    student_name = serializers.CharField(source='student.first_name', read_only=True)
    student_last_name = serializers.CharField(source='student.last_name', read_only=True)
    term_name = serializers.CharField(source='term.name', read_only=True)

    class Meta:
        model = FeeStatement
        fields = '__all__'


class PaymentAnalyticsSerializer(serializers.ModelSerializer):
    school_name = serializers.CharField(source='school.name', read_only=True)
    term_name = serializers.CharField(source='term.name', read_only=True)

    class Meta:
        model = PaymentAnalytics
        fields = '__all__'


class ParentChildrenFeesSerializer(serializers.Serializer):
    """
    Serializer for parent's children fee information
    """
    student_id = serializers.IntegerField()
    student_name = serializers.CharField()
    class_name = serializers.CharField()
    term_name = serializers.CharField()
    total_fees = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_paid = serializers.DecimalField(max_digits=10, decimal_places=2)
    balance = serializers.DecimalField(max_digits=10, decimal_places=2)
    last_payment_date = serializers.DateTimeField(allow_null=True)
    is_fully_paid = serializers.BooleanField()


class StudentFeeSummarySerializer(serializers.Serializer):
    """
    Comprehensive fee summary for a student
    """
    student = serializers.CharField()
    term = serializers.CharField()
    total_fees = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_paid = serializers.DecimalField(max_digits=10, decimal_places=2)
    balance = serializers.DecimalField(max_digits=10, decimal_places=2)
    payment_history = serializers.ListField()
    recent_payments = FeePaymentSerializer(many=True, read_only=True)
    invoices = InvoiceSerializer(many=True, read_only=True)


class PaymentAnalyticsDetailSerializer(serializers.Serializer):
    """
    Detailed payment analytics
    """
    total_payments = serializers.IntegerField()
    total_amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    average_payment = serializers.DecimalField(max_digits=10, decimal_places=2)
    collection_rate = serializers.DecimalField(max_digits=5, decimal_places=2)
    payment_methods = serializers.ListField()
    daily_trends = serializers.ListField()
    total_outstanding = serializers.DecimalField(max_digits=12, decimal_places=2)
    students_with_balance = serializers.IntegerField()


class AutomatedPaymentSettingsSerializer(serializers.ModelSerializer):
    """Serializer for automated payment settings"""
    school = SchoolBasicSerializer(read_only=True)
    school_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = AutomatedPaymentSettings
        fields = [
            'id', 'school', 'school_id', 'auto_verification_enabled',
            'max_auto_verification_amount', 'allowed_auto_verification_methods',
            'fraud_detection_enabled', 'fraud_threshold', 'max_payment_age_days',
            'send_auto_verification_notifications', 'notification_email',
            'auto_generate_receipts', 'receipt_template', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def validate_allowed_auto_verification_methods(self, value):
        """Validate that the allowed methods are valid payment methods"""
        valid_methods = ['MOBILE_MONEY', 'BANK_TRANSFER', 'CASH', 'CHEQUE', 'CREDIT_CARD']
        for method in value:
            if method not in valid_methods:
                raise serializers.ValidationError(f"Invalid payment method: {method}")
        return value

    def validate_fraud_threshold(self, value):
        """Validate that the fraud threshold is between 0 and 100"""
        if not 0 <= value <= 100:
            raise serializers.ValidationError("Fraud threshold must be between 0 and 100")
        return value
