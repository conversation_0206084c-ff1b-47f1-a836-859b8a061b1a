from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
import uuid
from schools.models import School, SchoolBranch
from settings_app.license_models import LicenseSubscription, ModuleActivation, generate_license_key
from settings_app.modules import AVAILABLE_MODULES, get_package_modules

class Command(BaseCommand):
    help = 'Set up initial module activations for existing schools'

    def add_arguments(self, parser):
        parser.add_argument(
            '--package',
            type=str,
            default='premium',
            help='Package type to assign (basic, standard, premium, custom)',
        )

        parser.add_argument(
            '--school',
            type=int,
            help='School ID to set up (optional, all schools if not specified)',
        )

    def handle(self, *args, **options):
        package_type = options.get('package', 'premium')
        school_id = options.get('school')

        if school_id:
            schools = School.objects.filter(id=school_id)
            if not schools.exists():
                self.stdout.write(self.style.ERROR(f'School with ID {school_id} not found'))
                return
        else:
            schools = School.objects.all()
            if not schools.exists():
                self.stdout.write(self.style.ERROR('No schools found'))
                return

        for school in schools:
            self.setup_school(school, package_type)

        self.stdout.write(self.style.SUCCESS('Module activations set up successfully'))

    def setup_school(self, school, package_type):
        """Set up license and module activations for a school"""
        self.stdout.write(f'Setting up {school.name} with {package_type} package')

        # Create or update license subscription with more secure key
        expiry_date = timezone.now().date() + timedelta(days=365)  # 1 year from now
        license_key = generate_license_key(school, expiry_date)  # Now using 8 random characters

        license_sub, created = LicenseSubscription.objects.update_or_create(
            school=school,
            defaults={
                'package_type': package_type,
                'subscription_status': 'ACTIVE',
                'start_date': timezone.now().date(),
                'expiry_date': expiry_date,
                'license_key': license_key
            }
        )

        if created:
            self.stdout.write(f'  Created license subscription with key {license_key}')
        else:
            self.stdout.write(f'  Updated license subscription with key {license_key}')

        # Get modules for this package
        if package_type == 'custom':
            # For custom package, enable all non-core modules
            enabled_modules = [code for code in AVAILABLE_MODULES.keys()
                              if not AVAILABLE_MODULES[code].get('is_core', False)]
            license_sub.custom_modules = enabled_modules
            license_sub.save()
        else:
            enabled_modules = get_package_modules(package_type)

        # Set up module activations for each branch
        branches = SchoolBranch.objects.filter(school=school)
        for branch in branches:
            module_activation, created = ModuleActivation.objects.update_or_create(
                school_branch=branch,
                defaults={
                    'enabled_modules': enabled_modules
                }
            )

            if created:
                self.stdout.write(f'  Created module activation for {branch.name}')
            else:
                self.stdout.write(f'  Updated module activation for {branch.name}')
