# Generated by Django 5.2.1 on 2025-06-01 13:25

import academics.models.live_streaming
import datetime
import django.core.validators
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AcademicYear',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.CharField(db_index=True, max_length=9)),
                ('start_date', models.DateField(db_index=True)),
                ('end_date', models.DateField()),
                ('is_current', models.BooleanField(db_index=True, default=False)),
                ('is_archived', models.BooleanField(db_index=True, default=False)),
                ('is_template', models.BooleanField(db_index=True, default=False, help_text='Whether this academic year is a template that can be applied to multiple branches')),
                ('is_customized', models.BooleanField(default=False, help_text='Whether this academic year has been customized from its template')),
            ],
            options={
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='AcademicYearTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.CharField(max_length=9)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('is_active', models.BooleanField(default=True)),
                ('is_archived', models.BooleanField(default=False)),
                ('is_global', models.BooleanField(default=False, help_text='If true, this template can be used across all schools')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='Assessment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('assessment_type', models.CharField(choices=[('QUIZ', 'Quiz'), ('TEST', 'Test'), ('ASSIGNMENT', 'Assignment'), ('PROJECT', 'Project'), ('PRESENTATION', 'Presentation'), ('OBSERVATION', 'Observation'), ('EXAM', 'Examination'), ('OTHER', 'Other')], max_length=20)),
                ('total_marks', models.DecimalField(decimal_places=2, max_digits=5)),
                ('passing_marks', models.DecimalField(decimal_places=2, max_digits=5)),
                ('duration', models.DurationField(blank=True, null=True)),
                ('curriculum_strand', models.CharField(blank=True, max_length=100)),
                ('learning_outcomes', models.TextField(blank=True)),
                ('assessment_criteria', models.JSONField(blank=True, null=True)),
                ('scheduled_date', models.DateTimeField()),
                ('submission_deadline', models.DateTimeField(blank=True, null=True)),
                ('is_published', models.BooleanField(default=False)),
                ('is_graded', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-scheduled_date'],
            },
        ),
        migrations.CreateModel(
            name='AssessmentResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('score', models.DecimalField(decimal_places=2, max_digits=5)),
                ('percentage', models.DecimalField(decimal_places=2, max_digits=5)),
                ('grade', models.CharField(blank=True, max_length=2)),
                ('teacher_feedback', models.TextField(blank=True)),
                ('student_feedback', models.TextField(blank=True)),
                ('is_submitted', models.BooleanField(default=False)),
                ('is_graded', models.BooleanField(default=False)),
                ('submitted_at', models.DateTimeField(blank=True, null=True)),
                ('graded_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-submitted_at'],
            },
        ),
        migrations.CreateModel(
            name='BranchCurriculumConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_transition_period', models.BooleanField(default=False)),
                ('transition_details', models.TextField(blank=True, null=True)),
                ('is_inherited_from_school', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Branch Curriculum Configuration',
                'verbose_name_plural': 'Branch Curriculum Configurations',
            },
        ),
        migrations.CreateModel(
            name='CareerPath',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('category', models.CharField(max_length=50)),
                ('minimum_grade', models.CharField(help_text='Minimum overall grade required', max_length=2)),
                ('job_prospects', models.TextField(blank=True)),
                ('salary_range', models.CharField(blank=True, max_length=100)),
                ('growth_outlook', models.TextField(blank=True)),
                ('further_education_options', models.TextField(blank=True)),
                ('recommended_universities', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='CareerPathSubjectRequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('minimum_grade', models.CharField(max_length=2)),
                ('is_mandatory', models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name='ClassProgressionRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_default_progression', models.BooleanField(default=True, help_text='Whether this is the default progression path')),
                ('requirements', models.JSONField(blank=True, help_text='Requirements for progression (e.g., minimum grades)', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Class Progression Rule',
                'verbose_name_plural': 'Class Progression Rules',
            },
        ),
        migrations.CreateModel(
            name='ClassRoom',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="e.g., 'Grade 4', 'Form 2', 'Year 10'", max_length=100)),
                ('display_name', models.CharField(blank=True, help_text='Human-readable name', max_length=100)),
                ('numeric_level', models.PositiveIntegerField(blank=True, help_text='Numeric representation for sorting (1-12 for most systems)', null=True)),
                ('max_capacity', models.PositiveIntegerField(default=40)),
                ('current_enrollment', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Class Rooms',
                'ordering': ['education_level__sequence', 'stream__name'],
            },
        ),
        migrations.CreateModel(
            name='CurriculumComplianceCheck',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('compliance_threshold', models.IntegerField(default=80, help_text='Minimum compliance score (0-100) required to pass the check')),
                ('frequency', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('quarterly', 'Quarterly')], default='weekly', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('last_check_at', models.DateTimeField(blank=True, null=True)),
                ('next_check_at', models.DateTimeField()),
                ('school_name_filter', models.CharField(blank=True, max_length=100, null=True)),
                ('notify_missing_configs', models.BooleanField(default=True, help_text='Notify about schools with no configuration')),
                ('notify_school_admins', models.BooleanField(default=False, help_text='Send notifications to school administrators')),
                ('send_email_notifications', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Curriculum Compliance Check',
                'verbose_name_plural': 'Curriculum Compliance Checks',
            },
        ),
        migrations.CreateModel(
            name='CurriculumComplianceNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('compliance_score', models.IntegerField()),
                ('compliance_status', models.CharField(choices=[('compliant', 'Compliant'), ('mostly_compliant', 'Mostly Compliant'), ('partially_compliant', 'Partially Compliant'), ('non_compliant', 'Non-Compliant'), ('no_config', 'No Configuration')], max_length=20)),
                ('differences', models.JSONField(default=list)),
                ('is_resolved', models.BooleanField(default=False)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('resolution_comment', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Curriculum Compliance Notification',
                'verbose_name_plural': 'Curriculum Compliance Notifications',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CurriculumConfigHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_transition_period', models.BooleanField(default=False)),
                ('transition_details', models.JSONField(blank=True, null=True)),
                ('curriculum_modifications', models.JSONField(blank=True, null=True)),
                ('is_provisional', models.BooleanField(default=False)),
                ('change_type', models.CharField(choices=[('created', 'Created'), ('updated', 'Updated'), ('applied_template', 'Applied Template')], max_length=20)),
                ('change_comment', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Curriculum Configuration History',
                'verbose_name_plural': 'Curriculum Configuration History',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CurriculumConfigTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_transition_period', models.BooleanField(default=False)),
                ('transition_details', models.JSONField(blank=True, null=True)),
                ('curriculum_modifications', models.JSONField(blank=True, null=True)),
                ('is_system_template', models.BooleanField(default=False, help_text='If true, this template is available to all schools')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Curriculum Configuration Template',
                'verbose_name_plural': 'Curriculum Configuration Templates',
            },
        ),
        migrations.CreateModel(
            name='CurriculumPropagationHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('propagated_at', models.DateTimeField(auto_now_add=True)),
                ('primary_curriculum_id', models.IntegerField()),
                ('primary_curriculum_name', models.CharField(max_length=100)),
                ('secondary_curriculum_id', models.IntegerField(blank=True, null=True)),
                ('secondary_curriculum_name', models.CharField(blank=True, max_length=100, null=True)),
                ('is_transition_period', models.BooleanField(default=False)),
                ('transition_details', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('success', 'Success'), ('failed', 'Failed')], max_length=20)),
                ('error_message', models.TextField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Curriculum Propagation History',
                'verbose_name_plural': 'Curriculum Propagation History',
                'ordering': ['-propagated_at'],
            },
        ),
        migrations.CreateModel(
            name='CurriculumSystem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=20, unique=True)),
                ('description', models.TextField()),
                ('is_active', models.BooleanField(default=True)),
                ('structure', models.JSONField(help_text='JSON defining the education structure')),
                ('academic_year_start_month', models.IntegerField(default=1)),
                ('academic_year_end_month', models.IntegerField(default=12)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=10)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='EducationLevel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=20)),
                ('stage_code', models.CharField(max_length=10)),
                ('sequence', models.IntegerField(help_text='Ordering within the curriculum')),
                ('system_specific_data', models.JSONField(blank=True, null=True)),
            ],
            options={
                'ordering': ['curriculum_system', 'sequence'],
            },
        ),
        migrations.CreateModel(
            name='EnhancedExam',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('exam_date', models.DateField()),
                ('start_time', models.TimeField()),
                ('end_time', models.TimeField()),
                ('duration', models.DurationField()),
                ('venue', models.CharField(max_length=100)),
                ('total_marks', models.PositiveIntegerField(default=100)),
                ('pass_mark', models.PositiveIntegerField(default=40)),
                ('question_paper_file', models.FileField(blank=True, null=True, upload_to='exam_papers/')),
                ('marking_scheme_file', models.FileField(blank=True, null=True, upload_to='marking_schemes/')),
                ('is_published', models.BooleanField(default=False)),
                ('results_published', models.BooleanField(default=False)),
                ('special_instructions', models.TextField(blank=True)),
                ('materials_allowed', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='EnhancedExamResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('raw_score', models.DecimalField(decimal_places=2, max_digits=5)),
                ('percentage', models.DecimalField(decimal_places=2, max_digits=5)),
                ('grade', models.CharField(max_length=2)),
                ('points', models.DecimalField(decimal_places=1, max_digits=4)),
                ('position_in_class', models.PositiveIntegerField(blank=True, null=True)),
                ('position_in_stream', models.PositiveIntegerField(blank=True, null=True)),
                ('position_in_subject', models.PositiveIntegerField(blank=True, null=True)),
                ('strengths', models.TextField(blank=True)),
                ('weaknesses', models.TextField(blank=True)),
                ('recommendations', models.TextField(blank=True)),
                ('marking_date', models.DateTimeField(blank=True, null=True)),
                ('verification_date', models.DateTimeField(blank=True, null=True)),
                ('is_verified', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='EnhancedTimeSlot',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day_of_week', models.CharField(choices=[('monday', 'Monday'), ('tuesday', 'Tuesday'), ('wednesday', 'Wednesday'), ('thursday', 'Thursday'), ('friday', 'Friday'), ('saturday', 'Saturday'), ('sunday', 'Sunday')], max_length=10)),
                ('period_number', models.PositiveIntegerField()),
                ('start_time', models.TimeField()),
                ('end_time', models.TimeField()),
                ('period_type', models.CharField(choices=[('lesson', 'Lesson'), ('break', 'Break'), ('lunch', 'Lunch'), ('assembly', 'Assembly'), ('games', 'Games/Sports'), ('study', 'Study Period'), ('club', 'Club Activity')], default='lesson', max_length=10)),
            ],
            options={
                'ordering': ['day_of_week', 'period_number'],
            },
        ),
        migrations.CreateModel(
            name='EnhancedTimetable',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_customized', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('is_published', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='ExamMalpractice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('malpractice_type', models.CharField(choices=[('cheating', 'Cheating'), ('impersonation', 'Impersonation'), ('unauthorized_material', 'Unauthorized Material'), ('collusion', 'Collusion'), ('disruption', 'Disruption'), ('other', 'Other')], max_length=100)),
                ('description', models.TextField()),
                ('evidence', models.FileField(blank=True, null=True, upload_to='malpractice_evidence/')),
                ('report_date', models.DateTimeField(auto_now_add=True)),
                ('action_taken', models.TextField(blank=True)),
                ('penalty_applied', models.TextField(blank=True)),
                ('is_resolved', models.BooleanField(default=False)),
                ('resolution_date', models.DateTimeField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='ExamRegistration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('registration_date', models.DateTimeField(auto_now_add=True)),
                ('special_needs', models.TextField(blank=True)),
                ('extra_time_granted', models.DurationField(blank=True, null=True)),
                ('is_registered', models.BooleanField(default=True)),
                ('is_present', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='ExamSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('registration_deadline', models.DateField()),
                ('instructions', models.TextField(blank=True)),
                ('rules', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_published', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='ExamStatistics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_students', models.PositiveIntegerField()),
                ('students_present', models.PositiveIntegerField()),
                ('students_absent', models.PositiveIntegerField()),
                ('highest_score', models.DecimalField(decimal_places=2, max_digits=5)),
                ('lowest_score', models.DecimalField(decimal_places=2, max_digits=5)),
                ('average_score', models.DecimalField(decimal_places=2, max_digits=5)),
                ('median_score', models.DecimalField(decimal_places=2, max_digits=5)),
                ('pass_rate', models.DecimalField(decimal_places=2, max_digits=5)),
                ('distinction_rate', models.DecimalField(decimal_places=2, max_digits=5)),
                ('failure_rate', models.DecimalField(decimal_places=2, max_digits=5)),
                ('grade_distribution', models.JSONField(default=dict)),
                ('calculated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='ExamType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('code', models.CharField(max_length=10, unique=True)),
                ('description', models.TextField(blank=True)),
                ('weight_percentage', models.DecimalField(decimal_places=2, help_text='Weight of this exam type in final grade calculation', max_digits=5)),
                ('is_active', models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name='GradeScale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('grade', models.CharField(max_length=2)),
                ('min_score', models.DecimalField(decimal_places=2, max_digits=5)),
                ('max_score', models.DecimalField(decimal_places=2, max_digits=5)),
                ('points', models.DecimalField(decimal_places=1, max_digits=3)),
                ('remarks', models.CharField(blank=True, max_length=100)),
            ],
            options={
                'ordering': ['-min_score'],
            },
        ),
        migrations.CreateModel(
            name='GradingSystem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('grading_type', models.CharField(choices=[('letter', 'Letter Grades (A, B, C, D, E)'), ('percentage', 'Percentage Based'), ('points', 'Points Based'), ('competency', 'Competency Based (CBC)'), ('mixed', 'Mixed System')], default='letter', max_length=20)),
                ('description', models.TextField(blank=True)),
                ('max_score', models.DecimalField(decimal_places=2, default=Decimal('100.00'), max_digits=5)),
                ('min_score', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=5)),
                ('pass_mark', models.DecimalField(decimal_places=2, default=Decimal('50.00'), max_digits=5)),
                ('uses_competency_levels', models.BooleanField(default=False, help_text='Whether this system uses CBC competency levels')),
                ('competency_config', models.JSONField(blank=True, default=dict, help_text='Configuration for competency-based assessment')),
                ('is_active', models.BooleanField(default=True)),
                ('is_default', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['curriculum_system', 'name'],
            },
        ),
        migrations.CreateModel(
            name='LearningPath',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('current_level', models.CharField(max_length=100)),
                ('target_level', models.CharField(max_length=100)),
                ('milestones', models.JSONField(blank=True, default=list)),
                ('completion_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['subject', 'current_level'],
            },
        ),
        migrations.CreateModel(
            name='LessonDelivery',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scheduled_date', models.DateField()),
                ('actual_date', models.DateField(blank=True, null=True)),
                ('scheduled_start', models.TimeField()),
                ('scheduled_end', models.TimeField()),
                ('actual_start', models.TimeField(blank=True, null=True)),
                ('actual_end', models.TimeField(blank=True, null=True)),
                ('venue', models.CharField(max_length=100)),
                ('students_present', models.PositiveIntegerField(default=0)),
                ('students_absent', models.PositiveIntegerField(default=0)),
                ('objectives_achieved', models.TextField(blank=True)),
                ('activities_completed', models.TextField(blank=True)),
                ('challenges_faced', models.TextField(blank=True)),
                ('student_understanding_level', models.CharField(blank=True, choices=[('excellent', 'Excellent'), ('good', 'Good'), ('average', 'Average'), ('poor', 'Poor')], max_length=10)),
                ('what_went_well', models.TextField(blank=True)),
                ('areas_for_improvement', models.TextField(blank=True)),
                ('next_lesson_adjustments', models.TextField(blank=True)),
                ('is_completed', models.BooleanField(default=False)),
                ('completion_percentage', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='LessonObservation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lesson_planning', models.PositiveIntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], help_text='Lesson planning quality (1-5)')),
                ('content_delivery', models.PositiveIntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], help_text='Content delivery effectiveness (1-5)')),
                ('student_engagement', models.PositiveIntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], help_text='Student engagement level (1-5)')),
                ('classroom_management', models.PositiveIntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], help_text='Classroom management (1-5)')),
                ('use_of_resources', models.PositiveIntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], help_text='Effective use of resources (1-5)')),
                ('strengths', models.TextField()),
                ('areas_for_improvement', models.TextField()),
                ('recommendations', models.TextField()),
                ('overall_rating', models.CharField(choices=[('outstanding', 'Outstanding'), ('good', 'Good'), ('satisfactory', 'Satisfactory'), ('needs_improvement', 'Needs Improvement'), ('unsatisfactory', 'Unsatisfactory')], max_length=100)),
                ('follow_up_required', models.BooleanField(default=False)),
                ('follow_up_date', models.DateField(blank=True, null=True)),
                ('follow_up_notes', models.TextField(blank=True)),
                ('observation_date', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='LessonPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('lesson_type', models.CharField(choices=[('introduction', 'Introduction'), ('development', 'Development'), ('revision', 'Revision'), ('assessment', 'Assessment'), ('practical', 'Practical'), ('field_trip', 'Field Trip')], max_length=15)),
                ('topic', models.CharField(max_length=200)),
                ('subtopic', models.CharField(blank=True, max_length=200)),
                ('curriculum_strand', models.CharField(blank=True, max_length=100)),
                ('learning_outcomes', models.TextField()),
                ('success_criteria', models.TextField()),
                ('duration', models.DurationField()),
                ('introduction_activities', models.TextField()),
                ('main_activities', models.TextField()),
                ('conclusion_activities', models.TextField()),
                ('required_materials', models.TextField(blank=True)),
                ('digital_resources', models.TextField(blank=True)),
                ('assessment_methods', models.TextField(blank=True)),
                ('homework_assignment', models.TextField(blank=True)),
                ('differentiation_strategies', models.TextField(blank=True)),
                ('special_needs_accommodations', models.TextField(blank=True)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('last_modified', models.DateTimeField(auto_now=True)),
                ('is_approved', models.BooleanField(default=False)),
            ],
            options={
                'ordering': ['-date_created'],
            },
        ),
        migrations.CreateModel(
            name='LessonResource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('resource_type', models.CharField(choices=[('document', 'Document'), ('presentation', 'Presentation'), ('video', 'Video'), ('audio', 'Audio'), ('image', 'Image'), ('link', 'Web Link'), ('interactive', 'Interactive Content')], max_length=15)),
                ('file', models.FileField(blank=True, null=True, upload_to='lesson_resources/')),
                ('url', models.URLField(blank=True)),
                ('download_count', models.PositiveIntegerField(default=0)),
                ('view_count', models.PositiveIntegerField(default=0)),
                ('file_size', models.BigIntegerField(blank=True, null=True)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='LessonSeries',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('total_lessons', models.PositiveIntegerField()),
                ('estimated_duration', models.DurationField()),
                ('curriculum_unit', models.CharField(max_length=100)),
                ('learning_objectives', models.TextField()),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('is_active', models.BooleanField(default=True)),
                ('completion_percentage', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='LessonSeriesLesson',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order', models.PositiveIntegerField()),
                ('prerequisites', models.TextField(blank=True)),
                ('is_mandatory', models.BooleanField(default=True)),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='LiveSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('session_type', models.CharField(choices=[('lesson', 'Regular Lesson'), ('exam', 'Exam Session'), ('meeting', 'Class Meeting'), ('assembly', 'Assembly'), ('event', 'Special Event')], max_length=10)),
                ('scheduled_start', models.DateTimeField()),
                ('scheduled_end', models.DateTimeField()),
                ('actual_start', models.DateTimeField(blank=True, null=True)),
                ('actual_end', models.DateTimeField(blank=True, null=True)),
                ('stream_url', models.URLField(blank=True)),
                ('stream_key', models.CharField(blank=True, max_length=200)),
                ('meeting_id', models.CharField(blank=True, max_length=100)),
                ('meeting_password', models.CharField(blank=True, max_length=50)),
                ('is_recorded', models.BooleanField(default=True)),
                ('recording_url', models.URLField(blank=True)),
                ('allow_chat', models.BooleanField(default=True)),
                ('require_approval', models.BooleanField(default=False)),
                ('max_participants', models.PositiveIntegerField(blank=True, null=True)),
                ('status', models.CharField(choices=[('scheduled', 'Scheduled'), ('live', 'Live'), ('ended', 'Ended'), ('cancelled', 'Cancelled'), ('paused', 'Paused')], default='scheduled', max_length=10)),
                ('is_public', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-scheduled_start'],
            },
        ),
        migrations.CreateModel(
            name='LiveStreamingPlatform',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('code', models.CharField(max_length=20, unique=True)),
                ('api_endpoint', models.URLField(blank=True)),
                ('requires_api_key', models.BooleanField(default=True)),
                ('supports_recording', models.BooleanField(default=True)),
                ('supports_chat', models.BooleanField(default=True)),
                ('max_participants', models.PositiveIntegerField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name='PerformanceAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('average_score', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('highest_score', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('lowest_score', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('standard_deviation', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('attendance_rate', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('total_absences', models.PositiveIntegerField(default=0)),
                ('total_late_attendance', models.PositiveIntegerField(default=0)),
                ('participation_rate', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('homework_completion_rate', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('improvement_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('learning_gaps', models.JSONField(blank=True, default=list)),
                ('strengths', models.JSONField(blank=True, default=list)),
                ('weaknesses', models.JSONField(blank=True, default=list)),
                ('study_time', models.DurationField(blank=True, null=True)),
                ('resource_utilization', models.JSONField(blank=True, default=dict)),
                ('assessment_patterns', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_analyzed', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Performance Analytics',
                'verbose_name_plural': 'Performance Analytics',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PublicationAccess',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_accessed', models.DateTimeField(auto_now_add=True)),
                ('last_accessed', models.DateTimeField(auto_now=True)),
                ('access_count', models.PositiveIntegerField(default=1)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
            ],
        ),
        migrations.CreateModel(
            name='ResultPublication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('publication_type', models.CharField(choices=[('term_results', 'Term Results'), ('exam_results', 'Exam Results'), ('progress_reports', 'Progress Reports'), ('annual_reports', 'Annual Reports')], max_length=20)),
                ('description', models.TextField(blank=True)),
                ('publish_date', models.DateTimeField()),
                ('expiry_date', models.DateTimeField(blank=True, null=True)),
                ('is_public', models.BooleanField(default=False)),
                ('requires_authentication', models.BooleanField(default=True)),
                ('allowed_user_types', models.JSONField(default=list)),
                ('notify_students', models.BooleanField(default=True)),
                ('notify_parents', models.BooleanField(default=True)),
                ('notification_message', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('review', 'Under Review'), ('approved', 'Approved'), ('published', 'Published'), ('archived', 'Archived')], default='draft', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('published_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SchoolCurriculumConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_transition_period', models.BooleanField(default=False)),
                ('transition_details', models.JSONField(blank=True, null=True)),
                ('is_provisional', models.BooleanField(default=False, help_text='Indicates if this configuration was auto-created and needs review')),
                ('curriculum_modifications', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='SessionAttendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_present', models.BooleanField(default=False)),
                ('attendance_percentage', models.DecimalField(decimal_places=2, default=academics.models.live_streaming.default_decimal, max_digits=5)),
                ('questions_asked', models.PositiveIntegerField(default=0)),
                ('chat_messages', models.PositiveIntegerField(default=0)),
                ('engagement_score', models.DecimalField(decimal_places=2, default=academics.models.live_streaming.default_decimal, max_digits=5)),
                ('connection_issues', models.BooleanField(default=False)),
                ('technical_notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='SessionChat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('message_type', models.CharField(choices=[('text', 'Text'), ('question', 'Question'), ('answer', 'Answer'), ('system', 'System')], default='text', max_length=10)),
                ('is_flagged', models.BooleanField(default=False)),
                ('is_deleted', models.BooleanField(default=False)),
            ],
            options={
                'ordering': ['timestamp'],
            },
        ),
        migrations.CreateModel(
            name='SessionParticipant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('participant_type', models.CharField(choices=[('student', 'Student'), ('teacher', 'Teacher'), ('parent', 'Parent'), ('guest', 'Guest')], max_length=10)),
                ('joined_at', models.DateTimeField(blank=True, null=True)),
                ('left_at', models.DateTimeField(blank=True, null=True)),
                ('duration', models.DurationField(blank=True, null=True)),
                ('can_speak', models.BooleanField(default=True)),
                ('can_chat', models.BooleanField(default=True)),
                ('can_share_screen', models.BooleanField(default=False)),
                ('is_moderator', models.BooleanField(default=False)),
                ('is_approved', models.BooleanField(default=True)),
                ('is_present', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='SessionRecording',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('recording_url', models.URLField()),
                ('file_size', models.BigIntegerField(blank=True, null=True)),
                ('duration', models.DurationField(blank=True, null=True)),
                ('is_processed', models.BooleanField(default=False)),
                ('processing_status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('is_public', models.BooleanField(default=False)),
                ('thumbnail_url', models.URLField(blank=True)),
                ('transcript', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='SmartIntervention',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('trigger_type', models.CharField(choices=[('PERFORMANCE', 'Performance'), ('ATTENDANCE', 'Attendance'), ('BEHAVIOR', 'Behavior'), ('OTHER', 'Other')], max_length=20)),
                ('risk_level', models.CharField(max_length=20)),
                ('automated_suggestions', models.JSONField(blank=True, default=list)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Stream',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=10, unique=True)),
                ('description', models.TextField(blank=True)),
            ],
        ),
        migrations.CreateModel(
            name='StreamGradingSystem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('min_average', models.DecimalField(decimal_places=2, help_text='Minimum expected average for the stream', max_digits=5)),
                ('target_average', models.DecimalField(decimal_places=2, help_text='Target average for the stream', max_digits=5)),
            ],
        ),
        migrations.CreateModel(
            name='StudentAttendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('status', models.CharField(choices=[('present', 'Present'), ('absent', 'Absent'), ('late', 'Late'), ('excused', 'Excused')], default='present', max_length=10)),
                ('remarks', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-date', 'student'],
            },
        ),
        migrations.CreateModel(
            name='StudentCareerGuidance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('strengths', models.TextField(blank=True)),
                ('areas_for_improvement', models.TextField(blank=True)),
                ('session_date', models.DateField()),
                ('session_notes', models.TextField()),
                ('next_session_date', models.DateField(blank=True, null=True)),
                ('action_items', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='StudentLessonFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content_clarity', models.PositiveIntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], help_text='How clear was the content? (1-5)')),
                ('pace_rating', models.PositiveIntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], help_text='Was the pace appropriate? (1-5)')),
                ('engagement_level', models.PositiveIntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], help_text='How engaging was the lesson? (1-5)')),
                ('what_learned', models.TextField(blank=True)),
                ('questions_raised', models.TextField(blank=True)),
                ('suggestions', models.TextField(blank=True)),
                ('difficulty_level', models.CharField(blank=True, choices=[('too_easy', 'Too Easy'), ('just_right', 'Just Right'), ('too_hard', 'Too Hard')], max_length=10)),
                ('submitted_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='StudentProgression',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('PENDING', 'Pending Review'), ('APPROVED', 'Approved'), ('REPEAT', 'Repeat Class'), ('TRANSFER', 'Transfer'), ('GRADUATED', 'Graduated'), ('WITHDRAWN', 'Withdrawn')], default='PENDING', max_length=20)),
                ('average_score', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('total_points', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('position_in_class', models.IntegerField(blank=True, null=True)),
                ('decision_date', models.DateField(blank=True, null=True)),
                ('decision_notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-term', 'student'],
            },
        ),
        migrations.CreateModel(
            name='Subject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=20)),
                ('short_name', models.CharField(blank=True, max_length=20)),
                ('description', models.TextField(blank=True)),
                ('subject_type', models.CharField(choices=[('core', 'Core Subject'), ('optional', 'Optional Subject'), ('elective', 'Elective Subject'), ('co_curricular', 'Co-curricular Activity'), ('life_skills', 'Life Skills')], default='core', max_length=20)),
                ('is_compulsory', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('credit_hours', models.PositiveIntegerField(default=1)),
                ('learning_areas', models.JSONField(blank=True, default=list, help_text='CBC learning areas for this subject')),
                ('competencies', models.JSONField(blank=True, default=list, help_text='Key competencies for this subject')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['curriculum_system', 'name'],
            },
        ),
        migrations.CreateModel(
            name='SubjectCombination',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='TeacherDepartmentAssignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_head', models.BooleanField(default=False, help_text='Whether this teacher is the head of the department')),
                ('date_assigned', models.DateField(auto_now_add=True)),
                ('date_ended', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Teacher Department Assignment',
                'verbose_name_plural': 'Teacher Department Assignments',
                'ordering': ['-date_assigned'],
            },
        ),
        migrations.CreateModel(
            name='TeacherPerformanceMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('class_average', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('pass_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('completion_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('avg_value_addition', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('number_of_distinctions', models.PositiveIntegerField(default=0)),
                ('number_of_failures', models.PositiveIntegerField(default=0)),
                ('remarks', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-term', 'teacher'],
            },
        ),
        migrations.CreateModel(
            name='TeacherTimetable',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_periods_per_week', models.PositiveIntegerField(default=0)),
                ('free_periods', models.JSONField(default=list)),
                ('is_complete', models.BooleanField(default=False)),
                ('conflicts_detected', models.BooleanField(default=False)),
                ('conflict_details', models.JSONField(default=list)),
                ('last_updated', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Term',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('is_current', models.BooleanField(default=False)),
                ('is_archived', models.BooleanField(default=False)),
                ('is_customized', models.BooleanField(default=False, help_text='Whether this term has been customized from its template')),
            ],
            options={
                'ordering': ['-academic_year', 'start_date'],
            },
        ),
        migrations.CreateModel(
            name='TermResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_score', models.DecimalField(decimal_places=2, max_digits=6)),
                ('average_score', models.DecimalField(decimal_places=2, max_digits=5)),
                ('position_in_class', models.PositiveIntegerField(blank=True, null=True)),
                ('grade', models.CharField(blank=True, max_length=5)),
                ('remarks', models.TextField(blank=True)),
                ('is_published', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-term', 'student'],
            },
        ),
        migrations.CreateModel(
            name='TermTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['start_date'],
            },
        ),
        migrations.CreateModel(
            name='TimeSlot',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_time', models.TimeField()),
                ('end_time', models.TimeField()),
                ('day_of_week', models.IntegerField(choices=[(0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'), (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')])),
            ],
            options={
                'ordering': ['day_of_week', 'start_time'],
            },
        ),
        migrations.CreateModel(
            name='TimetableConflict',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('conflict_type', models.CharField(choices=[('teacher_double_booking', 'Teacher Double Booking'), ('classroom_double_booking', 'Classroom Double Booking'), ('student_clash', 'Student Subject Clash'), ('resource_unavailable', 'Resource Unavailable'), ('teacher_unavailable', 'Teacher Unavailable')], max_length=25)),
                ('description', models.TextField()),
                ('severity', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='medium', max_length=10)),
                ('is_resolved', models.BooleanField(default=False)),
                ('resolution_notes', models.TextField(blank=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('detected_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='TimetableEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('classroom', models.CharField(blank=True, max_length=50)),
                ('venue_type', models.CharField(choices=[('classroom', 'Classroom'), ('laboratory', 'Laboratory'), ('library', 'Library'), ('hall', 'Hall'), ('field', 'Field'), ('computer_lab', 'Computer Lab'), ('other', 'Other')], default='classroom', max_length=20)),
                ('is_double_period', models.BooleanField(default=False)),
                ('special_notes', models.TextField(blank=True)),
                ('is_substitution', models.BooleanField(default=False)),
                ('substitution_reason', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='TimetableSubstitution',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('reason', models.TextField()),
                ('approval_date', models.DateTimeField(blank=True, null=True)),
                ('is_approved', models.BooleanField(default=False)),
                ('original_teacher_notified', models.BooleanField(default=False)),
                ('substitute_notified', models.BooleanField(default=False)),
                ('students_notified', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='TimetableTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('periods_per_day', models.PositiveIntegerField(default=8)),
                ('break_periods', models.JSONField(default=list)),
                ('lunch_period', models.PositiveIntegerField(blank=True, null=True)),
                ('school_start_time', models.TimeField(default=datetime.time(8, 0))),
                ('school_end_time', models.TimeField(default=datetime.time(16, 0))),
                ('period_duration', models.DurationField(default=datetime.timedelta(seconds=2400))),
                ('break_duration', models.DurationField(default=datetime.timedelta(seconds=1200))),
                ('lunch_duration', models.DurationField(default=datetime.timedelta(seconds=3600))),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
