from django.shortcuts import render, redirect, get_object_or_404
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.urls import reverse_lazy
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from .curriculum_models import CurriculumSystem, SchoolCurriculumConfig, EducationLevel
from .models import ClassProgressionRule
from .forms import SchoolCurriculumConfigForm
from .curriculum_serializers import (
    CurriculumSystemSerializer, SchoolCurriculumConfigSerializer, EducationLevelSerializer
)
from .serializers import ClassProgressionRuleSerializer

# Django Views for Web Interface

class SchoolCurriculumConfigView(LoginRequiredMixin, UpdateView):
    model = SchoolCurriculumConfig
    form_class = SchoolCurriculumConfigForm
    template_name = 'academics/school_curriculum_config.html'
    success_url = reverse_lazy('academics:curriculum_config')

    def get_object(self):
        # Get or create config for the current school
        school = self.request.user.school_branch.school
        obj, created = SchoolCurriculumConfig.objects.get_or_create(
            school=school,
            defaults={
                'primary_curriculum': CurriculumSystem.objects.get(code='CBC'),
                'secondary_curriculum': CurriculumSystem.objects.get(code='CBC')
            }
        )
        return obj

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['school'] = self.request.user.school_branch.school
        return kwargs

    def form_valid(self, form):
        messages.success(self.request, 'Curriculum configuration updated successfully.')
        return super().form_valid(form)

class EducationLevelListView(LoginRequiredMixin, ListView):
    model = EducationLevel
    template_name = 'academics/education_level_list.html'
    context_object_name = 'education_levels'

    def get_queryset(self):
        # Get education levels for the school's curricula
        school = self.request.user.school_branch.school
        try:
            config = SchoolCurriculumConfig.objects.get(school=school)
            primary_levels = EducationLevel.objects.filter(curriculum_system=config.primary_curriculum)
            secondary_levels = EducationLevel.objects.filter(curriculum_system=config.secondary_curriculum)
            return list(primary_levels) + list(secondary_levels)
        except SchoolCurriculumConfig.DoesNotExist:
            return EducationLevel.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        school = self.request.user.school_branch.school
        try:
            context['curriculum_config'] = SchoolCurriculumConfig.objects.get(school=school)
        except SchoolCurriculumConfig.DoesNotExist:
            context['curriculum_config'] = None
        return context

# DRF API Views

class CurriculumSystemViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for curriculum systems
    """
    queryset = CurriculumSystem.objects.filter(is_active=True)
    serializer_class = CurriculumSystemSerializer
    permission_classes = [IsAuthenticated]

class SchoolCurriculumConfigViewSet(viewsets.ModelViewSet):
    """
    API endpoint for school curriculum configuration
    """
    serializer_class = SchoolCurriculumConfigSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if user.is_superuser:
            return SchoolCurriculumConfig.objects.all()
        return SchoolCurriculumConfig.objects.filter(school=user.school_branch.school)

    @action(detail=False, methods=['get'])
    def my_config(self, request):
        """Get curriculum configuration for the current user's school"""
        school = request.user.school_branch.school
        try:
            config = SchoolCurriculumConfig.objects.get(school=school)
            serializer = self.get_serializer(config)
            return Response(serializer.data)
        except SchoolCurriculumConfig.DoesNotExist:
            return Response(
                {"error": "No curriculum configuration found for your school"},
                status=status.HTTP_404_NOT_FOUND
            )

class EducationLevelViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for education levels
    """
    queryset = EducationLevel.objects.all()
    serializer_class = EducationLevelSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = EducationLevel.objects.all()

        # Filter by curriculum system
        curriculum_system = self.request.query_params.get('curriculum_system')
        if curriculum_system:
            queryset = queryset.filter(curriculum_system__code=curriculum_system)

        # Filter by stage
        stage = self.request.query_params.get('stage')
        if stage:
            queryset = queryset.filter(stage_code=stage)

        return queryset

    @action(detail=False, methods=['get'])
    def school_levels(self, request):
        """Get education levels for the current user's school curricula"""
        school = request.user.school_branch.school
        try:
            config = SchoolCurriculumConfig.objects.get(school=school)
            primary_levels = EducationLevel.objects.filter(curriculum_system=config.primary_curriculum)
            secondary_levels = EducationLevel.objects.filter(curriculum_system=config.secondary_curriculum)

            # Combine querysets
            levels = list(primary_levels) + list(secondary_levels)
            serializer = self.get_serializer(levels, many=True)
            return Response(serializer.data)
        except SchoolCurriculumConfig.DoesNotExist:
            return Response(
                {"error": "No curriculum configuration found for your school"},
                status=status.HTTP_404_NOT_FOUND
            )


class ClassProgressionRuleViewSet(viewsets.ModelViewSet):
    queryset = ClassProgressionRule.objects.all()
    serializer_class = ClassProgressionRuleSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        # Get the school from the user's profile
        if hasattr(self.request.user, 'staff_profile') and self.request.user.staff_profile:
            school = self.request.user.staff_profile.school_branch.school
        elif hasattr(self.request.user, 'teacher_profile') and self.request.user.teacher_profile:
            school = self.request.user.teacher_profile.school_branch.school
        else:
            return ClassProgressionRule.objects.none()

        return queryset.filter(school=school)

    @action(detail=False, methods=['get'])
    def by_education_level(self, request):
        """Get progression rules for a specific education level"""
        education_level_id = request.query_params.get('education_level')
        if not education_level_id:
            return Response({'error': 'education_level parameter is required'}, status=400)

        queryset = self.get_queryset().filter(current_level_id=education_level_id)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_curriculum(self, request):
        """Get progression rules for a specific curriculum system"""
        curriculum_code = request.query_params.get('curriculum_system')
        if not curriculum_code:
            return Response({'error': 'curriculum_system parameter is required'}, status=400)

        queryset = self.get_queryset().filter(current_level__curriculum_system__code=curriculum_code)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)