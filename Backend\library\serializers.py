from rest_framework import serializers
from .models import Book, EResource, Borrowing, Return, LibraryReport
from core.Serializers import UserSerializer

class BookSerializer(serializers.ModelSerializer):
    added_by_details = UserSerializer(source='added_by', read_only=True)
    
    class Meta:
        model = Book
        fields = '__all__'
        extra_kwargs = {
            'added_by': {'write_only': True},
            'school_branch': {'required': True}
        }

class EResourceSerializer(serializers.ModelSerializer):
    added_by_details = UserSerializer(source='added_by', read_only=True)
    
    class Meta:
        model = EResource
        fields = '__all__'
        extra_kwargs = {
            'added_by': {'write_only': True},
            'school_branch': {'required': True}
        }

class BorrowingSerializer(serializers.ModelSerializer):
    borrower_details = UserSerializer(source='borrower', read_only=True)
    issued_by_details = UserSerializer(source='issued_by', read_only=True)
    book_details = BookSerializer(source='book', read_only=True)
    is_overdue = serializers.BooleanField(read_only=True)
    days_overdue = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = Borrowing
        fields = '__all__'
        extra_kwargs = {
            'borrower': {'write_only': True},
            'issued_by': {'write_only': True},
            'book': {'write_only': True},
            'school_branch': {'required': True}
        }
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['is_overdue'] = instance.is_overdue()
        representation['days_overdue'] = instance.days_overdue()
        return representation

class ReturnSerializer(serializers.ModelSerializer):
    borrowing_details = BorrowingSerializer(source='borrowing', read_only=True)
    received_by_details = UserSerializer(source='received_by', read_only=True)
    
    class Meta:
        model = Return
        fields = '__all__'
        extra_kwargs = {
            'borrowing': {'write_only': True},
            'received_by': {'write_only': True}
        }

class OverdueBookSerializer(serializers.ModelSerializer):
    book_details = BookSerializer(source='book', read_only=True)
    borrower_details = UserSerializer(source='borrower', read_only=True)
    days_overdue = serializers.IntegerField(read_only=True)
    calculated_fine = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    
    class Meta:
        model = Borrowing
        fields = [
            'id', 'book', 'book_details', 'borrower', 'borrower_details',
            'borrow_date', 'due_date', 'days_overdue', 'calculated_fine',
            'fine_amount', 'fine_paid', 'notes', 'school_branch'
        ]
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['days_overdue'] = instance.days_overdue()
        representation['calculated_fine'] = instance.calculate_fine()
        return representation

class LibraryReportSerializer(serializers.ModelSerializer):
    generated_by_details = UserSerializer(source='generated_by', read_only=True)
    
    class Meta:
        model = LibraryReport
        fields = '__all__'
        extra_kwargs = {
            'generated_by': {'write_only': True},
            'school_branch': {'required': True}
        }
