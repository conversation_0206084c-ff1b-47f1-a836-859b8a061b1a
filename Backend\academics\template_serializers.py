from rest_framework import serializers
from academics.academic_templates import AcademicYearTemplate, TermTemplate
from academics.models import AcademicYear, TermTemplate

class TermTemplateSerializer(serializers.ModelSerializer):
    academic_year_template_name = serializers.CharField(source='academic_year_template.year', read_only=True)
    school_name = serializers.CharField(source='academic_year_template.school.name', read_only=True)

    class Meta:
        model = TermTemplate
        fields = ['id', 'name', 'academic_year_template', 'academic_year_template_name',
                 'school_name', 'start_date', 'end_date', 'created_at', 'updated_at']

class AcademicYearTemplateSerializer(serializers.ModelSerializer):
    school_name = serializers.SerializerMethodField(read_only=True)
    terms = TermTemplateSerializer(source='term_templates', many=True, read_only=True)
    created_by_name = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = AcademicYearTemplate
        fields = ['id', 'year', 'school', 'school_name', 'start_date', 'end_date',
                 'is_active', 'is_archived', 'is_global', 'created_by', 'created_by_name',
                 'terms', 'created_at', 'updated_at']

    def get_school_name(self, obj):
        if obj.school:
            return obj.school.name
        return "Global" if obj.is_global else None

    def get_created_by_name(self, obj):
        if obj.created_by:
            return f"{obj.created_by.first_name} {obj.created_by.last_name}"
        return None

    def create(self, validated_data):
        # Get the current user from the request
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            validated_data['created_by'] = request.user

        # Only superusers can create global templates
        if validated_data.get('is_global', False) and not (request and request.user.is_superuser):
            validated_data['is_global'] = False

        # Create the academic year template
        academic_year_template = AcademicYearTemplate.objects.create(**validated_data)
        return academic_year_template

class ApplyTemplateSerializer(serializers.Serializer):
    template_id = serializers.IntegerField(required=True)
    school_branch_id = serializers.IntegerField(required=True)
    customize = serializers.BooleanField(default=False)

    def validate(self, attrs):
        # Validate that the template exists
        try:
            template = AcademicYearTemplate.objects.get(pk=attrs['template_id'])
        except AcademicYearTemplate.DoesNotExist:
            raise serializers.ValidationError({
                'template_id': 'Academic year template does not exist'
            })

        # Validate that the school branch exists and belongs to the template's school
        from schools.models import SchoolBranch
        try:
            branch = SchoolBranch.objects.get(pk=attrs['school_branch_id'])

            # For super users or global templates, allow cross-school template application
            request = self.context.get('request')
            user = request.user if request and hasattr(request, 'user') else None
            # If template has no school (fully global) or is global, or user is superuser, allow cross-school application
            if not ((user and user.is_superuser) or template.is_global or template.school is None) and template.school and branch.school.pk != template.school.pk:
                raise serializers.ValidationError({
                    'school_branch_id': 'School branch does not belong to the template\'s school. Only super users or global templates can be applied across schools.'
                })
        except SchoolBranch.DoesNotExist:
            raise serializers.ValidationError({
                'school_branch_id': 'School branch does not exist'
            })

        # Check if an academic year with the same name already exists for this branch        
        if AcademicYear.objects.filter(year=template.year, school_branch=branch).exists():
            raise serializers.ValidationError({
                'template_id': f'An academic year with name "{template.year}" already exists for this branch'
            })

        # Check if the template has terms
        if not template.termtemplate_set.exists():
            raise serializers.ValidationError({
                'template_id': 'This template has no terms defined. Please add terms to the template before applying it.'
            })

        return attrs
