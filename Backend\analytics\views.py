from rest_framework import viewsets, views
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action
from .services import BranchAnalyticsService
from core.permissions import IsSchoolBranchAdmin

class BranchAnalyticsViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated, IsSchoolBranchAdmin]

    def get_branch(self, request):
        return request.user.staff.school_branch

    def list(self, request):
        branch = self.get_branch(request)
        analytics_service = BranchAnalyticsService(branch)
        
        return Response({
            'fee_metrics': analytics_service.calculate_fee_metrics(),
            'performance_metrics': analytics_service.calculate_performance_metrics(),
            'enrollment_metrics': analytics_service.get_enrollment_metrics()
        })

    @action(detail=False, methods=['get'])
    def fee_analytics(self, request):
        branch = self.get_branch(request)
        analytics_service = BranchAnalyticsService(branch)
        return Response(analytics_service.calculate_fee_metrics())

    @action(detail=False, methods=['get'])
    def performance_analytics(self, request):
        branch = self.get_branch(request)
        analytics_service = BranchAnalyticsService(branch)
        return Response(analytics_service.calculate_performance_metrics())

    @action(detail=False, methods=['get'])
    def enrollment_analytics(self, request):
        branch = self.get_branch(request)
        analytics_service = BranchAnalyticsService(branch)
        return Response(analytics_service.get_enrollment_metrics())
