from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.conf import settings
from ..curriculum_models import EducationLevel
from schools.models import School
from .student_progression import StudentProgression


class ClassProgressionRule(models.Model):
    """
    Rules for class progression based on curriculum systems
    """
    current_level = models.ForeignKey(EducationLevel, on_delete=models.CASCADE, related_name='progression_from')
    next_level = models.ForeignKey(EducationLevel, on_delete=models.CASCADE, related_name='progression_to')
    school = models.ForeignKey(School, on_delete=models.CASCADE, help_text="School-specific progression rules")    
    is_default_progression = models.BooleanField(default=True, help_text="Whether this is the default progression path")
    requirements = models.JSONField(null=True, blank=True, help_text="Requirements for progression (e.g., minimum grades)")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'academics'
        unique_together = ['current_level', 'next_level', 'school']
        verbose_name = "Class Progression Rule"
        verbose_name_plural = "Class Progression Rules"

    def __str__(self):
        return f"{self.current_level} to {self.next_level} - {self.school.name}"


class CareerPath(models.Model):
    """Career paths available to students"""
    name = models.CharField(max_length=100)
    description = models.TextField()
    category = models.CharField(max_length=50)  # e.g., 'STEM', 'Arts', 'Business'
    
    # Requirements
    required_subjects = models.ManyToManyField(
        'academics.Subject',
        through='CareerPathSubjectRequirement'
    )
    minimum_grade = models.CharField(max_length=2, help_text="Minimum overall grade required")
    
    # Career information
    job_prospects = models.TextField(blank=True)
    salary_range = models.CharField(max_length=100, blank=True)
    growth_outlook = models.TextField(blank=True)
    
    # Education pathway
    further_education_options = models.TextField(blank=True)
    recommended_universities = models.TextField(blank=True)
    
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        app_label = 'academics'
        
    def __str__(self):
        return self.name


class CareerPathSubjectRequirement(models.Model):
    """Subject requirements for specific career paths"""
    career_path = models.ForeignKey(CareerPath, on_delete=models.CASCADE)
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE)
    minimum_grade = models.CharField(max_length=2)
    is_mandatory = models.BooleanField(default=True)
    
    class Meta:
        app_label = 'academics'
        unique_together = ['career_path', 'subject']


class StudentCareerGuidance(models.Model):
    """Career guidance records for students"""
    student = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='career_guidance_sessions')
    recommended_careers = models.ManyToManyField(CareerPath, blank=True)

    # Current performance analysis
    strengths = models.TextField(blank=True)
    areas_for_improvement = models.TextField(blank=True)

    # Guidance session details
    counselor = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='counseling_sessions')
    session_date = models.DateField()
    session_notes = models.TextField()
    
    # Follow-up
    next_session_date = models.DateField(null=True, blank=True)
    action_items = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        app_label = 'academics'
        
    def __str__(self):
        return f"Career Guidance - {self.student} ({self.session_date})"


class SubjectCombination(models.Model):
    """Subject combinations for different career paths"""
    name = models.CharField(max_length=100)
    description = models.TextField()
    subjects = models.ManyToManyField('academics.Subject')
    career_paths = models.ManyToManyField(CareerPath, blank=True)
    
    # Applicable education levels
    education_levels = models.ManyToManyField('academics.EducationLevel')

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        app_label = 'academics'

    def __str__(self):
        return self.name
