from django.db.models import Avg, Count, Sum, F, Q
from django.db.models.functions import Coalesce
from ..models import (
    TeacherPerformanceMetrics, Subject, Term, ClassRoom,
    EnhancedExamResult, StudentAttendance, Assessment
)

class TeacherPerformanceService:
    def __init__(self, teacher):
        self.teacher = teacher

    def calculate_metrics(self, term, subject, class_room=None):
        """Calculate performance metrics for a teacher in a specific term and subject"""
        # Get all exam results for the teacher's subject and term
        exam_results = EnhancedExamResult.objects.filter(
            exam__subject=subject,
            exam__term=term
        )
        if class_room:
            exam_results = exam_results.filter(student__class_room=class_room)

        # Calculate class average
        class_average = exam_results.aggregate(
            avg=Coalesce(Avg('score'), 0)
        )['avg']

        # Calculate pass rate (assuming 40% is passing)
        total_students = exam_results.count()
        passing_students = exam_results.filter(score__gte=40).count()
        pass_rate = (passing_students / total_students * 100) if total_students > 0 else 0

        # Calculate completion rate
        total_assignments = Assessment.objects.filter(
            subject=subject,
            term=term
        ).count()
        completed_assignments = exam_results.count()
        completion_rate = (completed_assignments / total_assignments * 100) if total_assignments > 0 else 0

        # Calculate value addition (comparing with previous term)
        previous_term = Term.objects.filter(
            academic_year=term.academic_year,
            start_date__lt=term.start_date
        ).order_by('-start_date').first()

        if previous_term:
            prev_results = EnhancedExamResult.objects.filter(
                exam__subject=subject,
                exam__term=previous_term
            )
            if class_room:
                prev_results = prev_results.filter(student__class_room=class_room)
            
            prev_average = prev_results.aggregate(
                avg=Coalesce(Avg('score'), 0)
            )['avg']
            avg_value_addition = class_average - prev_average
        else:
            avg_value_addition = 0

        # Count distinctions and failures
        number_of_distinctions = exam_results.filter(score__gte=80).count()
        number_of_failures = exam_results.filter(score__lt=40).count()

        # Create or update metrics
        metrics, created = TeacherPerformanceMetrics.objects.update_or_create(
            teacher=self.teacher,
            subject=subject,
            term=term,
            class_room=class_room,
            defaults={
                'class_average': class_average,
                'pass_rate': pass_rate,
                'completion_rate': completion_rate,
                'avg_value_addition': avg_value_addition,
                'number_of_distinctions': number_of_distinctions,
                'number_of_failures': number_of_failures
            }
        )

        return metrics

    def update_all_metrics(self, term):
        """Update metrics for all subjects taught by the teacher in a term"""
        subjects = Subject.objects.filter(teacher=self.teacher)
        metrics = []

        for subject in subjects:
            # Update metrics for each class the teacher teaches
            class_rooms = ClassRoom.objects.filter(
                subjects=subject,
                term=term
            )
            
            for class_room in class_rooms:
                metric = self.calculate_metrics(term, subject, class_room)
                metrics.append(metric)

        return metrics

    def get_performance_summary(self, term):
        """Get a summary of teacher's performance across all subjects"""
        metrics = TeacherPerformanceMetrics.objects.filter(
            teacher=self.teacher,
            term=term
        )

        if not metrics.exists():
            return None

        return {
            'overall_average': metrics.aggregate(
                avg=Coalesce(Avg('class_average'), 0)
            )['avg'],
            'overall_pass_rate': metrics.aggregate(
                avg=Coalesce(Avg('pass_rate'), 0)
            )['avg'],
            'total_distinctions': metrics.aggregate(
                total=Sum('number_of_distinctions')
            )['total'] or 0,
            'total_failures': metrics.aggregate(
                total=Sum('number_of_failures')
            )['total'] or 0,
            'subjects': metrics.values(
                'subject__name',
                'class_average',
                'pass_rate',
                'completion_rate',
                'avg_value_addition'
            )
        } 