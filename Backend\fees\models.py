from django.db import models
from django.utils import timezone
from schools.models import School
from users.models import Student

class FeeStructure(models.Model):
    name = models.CharField(max_length=100)
    school = models.ForeignKey(School, on_delete=models.CASCADE)
    academic_year = models.CharField(max_length=9)  # e.g., "2023-2024"
    term = models.ForeignKey('academics.Term', on_delete=models.CASCADE)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    is_active = models.BooleanField(default=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['school', 'academic_year', 'term']

    def __str__(self):
        return f"{self.name} - {self.academic_year} ({self.term})"

class FeeType(models.Model):
    name = models.CharField(max_length=100)
    school = models.Foreign<PERSON>ey(School, on_delete=models.CASCADE)
    description = models.TextField(blank=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    is_mandatory = models.BooleanField(default=True)
    fee_structure = models.ForeignKey(FeeStructure, on_delete=models.CASCADE, related_name='fee_types')

    def __str__(self):
        return f"{self.name} - {self.fee_structure}"

class FeePayment(models.Model):
    PAYMENT_METHODS = [
        ('CASH', 'Cash'),
        ('BANK', 'Bank Transfer'),
        ('MOBILE_MONEY', 'Mobile Money'),
        ('CHEQUE', 'Cheque')
    ]

    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    fee_type = models.ForeignKey(FeeType, on_delete=models.CASCADE)
    term = models.ForeignKey('academics.Term', on_delete=models.CASCADE)  # Add this line
    amount_paid = models.DecimalField(max_digits=10, decimal_places=2)
    payment_date = models.DateTimeField(default=timezone.now)
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHODS)
    reference_number = models.CharField(max_length=100, unique=True)
    verified = models.BooleanField(default=False)
    verified_by = models.ForeignKey('users.Staff', on_delete=models.SET_NULL, null=True, blank=True)
    remarks = models.TextField(blank=True)

    def __str__(self):
        return f"{self.student} - {self.amount_paid} ({self.payment_date})"

class FeeBalance(models.Model):
    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    term = models.ForeignKey('academics.Term', on_delete=models.CASCADE)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    amount_paid = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    balance = models.DecimalField(max_digits=10, decimal_places=2)

    def calculate_balance(self):
        self.balance = self.total_amount - self.amount_paid
        return self.balance

    def __str__(self):
        return f"{self.student} - {self.term} Balance"

class PaymentPlan(models.Model):
    STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('COMPLETED', 'Completed'),
        ('DEFAULTED', 'Defaulted')
    ]

    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    term = models.ForeignKey('academics.Term', on_delete=models.CASCADE)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    installments = models.IntegerField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='ACTIVE')
    approved_by = models.ForeignKey('users.Staff', on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.student} - {self.term} Plan"

class PaymentSchedule(models.Model):
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('PAID', 'Paid'),
        ('OVERDUE', 'Overdue')
    ]

    payment_plan = models.ForeignKey(PaymentPlan, on_delete=models.CASCADE, related_name='schedules')
    due_date = models.DateField()
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    paid = models.BooleanField(default=False)
    payment_status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    payment_date = models.DateField(null=True, blank=True)

    def __str__(self):
        return f"{self.payment_plan.student} - {self.due_date}"

class PaymentReminder(models.Model):
    REMINDER_TYPES = [
        ('EMAIL', 'Email'),
        ('SMS', 'SMS'),
        ('BOTH', 'Both')
    ]

    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    due_date = models.DateField()
    amount_due = models.DecimalField(max_digits=10, decimal_places=2)
    reminder_type = models.CharField(max_length=10, choices=REMINDER_TYPES)
    message = models.TextField()
    sent = models.BooleanField(default=False)
    sent_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Reminder for {self.student} - Due: {self.due_date}"

class Invoice(models.Model):
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('PAID', 'Paid'),
        ('PARTIALLY_PAID', 'Partially Paid'),
        ('OVERDUE', 'Overdue'),
        ('CANCELLED', 'Cancelled')
    ]

    invoice_number = models.CharField(max_length=20, unique=True)
    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    fee_structure = models.ForeignKey(FeeStructure, on_delete=models.CASCADE)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    amount_paid = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    balance = models.DecimalField(max_digits=10, decimal_places=2)
    due_date = models.DateField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Invoice #{self.invoice_number} - {self.student}"

class Receipt(models.Model):
    receipt_number = models.CharField(max_length=20, unique=True)
    payment = models.OneToOneField(FeePayment, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    date_issued = models.DateTimeField(auto_now_add=True)
    issued_by = models.ForeignKey('users.Staff', on_delete=models.SET_NULL, null=True)
    receipt_template = models.TextField(blank=True, null=True)  # Store HTML template
    is_digital = models.BooleanField(default=True)
    pdf_file = models.FileField(upload_to='receipts/', blank=True, null=True)

    def save(self, *args, **kwargs):
        if not self.receipt_number:
            self.receipt_number = self._generate_receipt_number()
        super().save(*args, **kwargs)

    def _generate_receipt_number(self):
        from django.utils.crypto import get_random_string
        import datetime
        year = datetime.datetime.now().year
        while True:
            random_digits = get_random_string(6, '**********')
            receipt_number = f"RCP/{year}/{random_digits}"
            if not Receipt.objects.filter(receipt_number=receipt_number).exists():
                return receipt_number

    def __str__(self):
        return f"Receipt #{self.receipt_number} - {self.payment.student}"

class FeeDiscount(models.Model):
    DISCOUNT_TYPES = [
        ('FIXED', 'Fixed Amount'),
        ('PERCENTAGE', 'Percentage')
    ]

    name = models.CharField(max_length=100)
    discount_type = models.CharField(max_length=10, choices=DISCOUNT_TYPES)
    amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    percentage = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    description = models.TextField(blank=True)
    active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    school = models.ForeignKey(School, on_delete=models.CASCADE)

    def __str__(self):
        return self.name

class FeeArrears(models.Model):
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('PARTIALLY_PAID', 'Partially Paid'),
        ('CLEARED', 'Cleared'),
        ('WRITTEN_OFF', 'Written Off')
    ]

    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    term = models.ForeignKey('academics.Term', on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.student} - {self.term} Arrears"

class Lease(models.Model):
    school = models.ForeignKey(School, on_delete=models.CASCADE)
    asset_name = models.CharField(max_length=100)
    lease_start_date = models.DateField()
    lease_end_date = models.DateField()
    lease_amount = models.DecimalField(max_digits=10, decimal_places=2)
    interest_rate = models.DecimalField(max_digits=5, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.asset_name} - {self.school.name}"

    def calculate_lease_liability(self):
        # Implement logic to calculate lease liability based on IFRS 16
        pass

    def calculate_right_of_use_asset(self):
        # Implement logic to calculate right-of-use asset based on IFRS 16
        pass

class LeasePayment(models.Model):
    lease = models.ForeignKey(Lease, on_delete=models.CASCADE, related_name='payments')
    payment_date = models.DateField()
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Payment for {self.lease.asset_name} on {self.payment_date}"


class StudentFeeAccount(models.Model):
    """
    Comprehensive fee account for each student per term
    """
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='fee_accounts')
    term = models.ForeignKey('academics.Term', on_delete=models.CASCADE)
    fee_structure = models.ForeignKey(FeeStructure, on_delete=models.CASCADE)
    total_fees = models.DecimalField(max_digits=10, decimal_places=2)
    total_paid = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    balance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    discount_applied = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    last_payment_date = models.DateTimeField(null=True, blank=True)
    is_fully_paid = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['student', 'term']

    def calculate_balance(self):
        self.balance = self.total_fees - self.total_paid - self.discount_applied
        self.is_fully_paid = self.balance <= 0
        return self.balance

    def __str__(self):
        return f"{self.student} - {self.term} Account"


class FeeStatement(models.Model):
    """
    Detailed fee statement for students
    """
    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    term = models.ForeignKey('academics.Term', on_delete=models.CASCADE)
    statement_number = models.CharField(max_length=20, unique=True)
    generated_date = models.DateTimeField(auto_now_add=True)
    generated_by = models.ForeignKey('users.Staff', on_delete=models.SET_NULL, null=True)
    pdf_file = models.FileField(upload_to='statements/', blank=True, null=True)

    def save(self, *args, **kwargs):
        if not self.statement_number:
            self.statement_number = self._generate_statement_number()
        super().save(*args, **kwargs)

    def _generate_statement_number(self):
        from django.utils.crypto import get_random_string
        import datetime
        year = datetime.datetime.now().year
        while True:
            random_digits = get_random_string(6, '**********')
            statement_number = f"STMT/{year}/{random_digits}"
            if not FeeStatement.objects.filter(statement_number=statement_number).exists():
                return statement_number

    def __str__(self):
        return f"Statement #{self.statement_number} - {self.student}"


class PaymentAnalytics(models.Model):
    """
    Store aggregated payment analytics data
    """
    school = models.ForeignKey(School, on_delete=models.CASCADE)
    term = models.ForeignKey('academics.Term', on_delete=models.CASCADE)
    date = models.DateField()
    total_expected = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_collected = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    collection_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    outstanding_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    number_of_payments = models.IntegerField(default=0)
    average_payment_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    class Meta:
        unique_together = ['school', 'term', 'date']

    def __str__(self):
        return f"{self.school} - {self.term} - {self.date}"


# Import school billing models for super admin billing system
from .school_billing_models import (
    SchoolBillingAccount, SchoolSubscriptionPlan, SchoolInvoice,
    SchoolInvoiceLineItem, SchoolPayment
)
