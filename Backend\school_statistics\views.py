from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Count, Q
from django.utils import timezone

from .serializers import SchoolStatisticsSerializer
from schools.models import School, SchoolBranch
from users.models import Student, Teacher, Staff, Parent
from core.models import CustomUser

class StatisticsViewSet(viewsets.ViewSet):
    """API endpoint for retrieving school statistics"""
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['get'])
    def school(self, request):
        """Get statistics for the current user's school"""
        user = request.user

        # For super users, check if school_id parameter is provided
        if user.is_superuser:
            school_id = request.query_params.get('school_id')
            if school_id:
                try:
                    school = School.objects.get(pk=school_id)
                    return self._get_school_statistics(school)
                except School.DoesNotExist:
                    return Response(
                        {"error": "School not found"},
                        status=status.HTTP_404_NOT_FOUND
                    )
            else:
                # Return aggregate statistics for super users
                return self._get_aggregate_statistics()

        # Check if user has a school branch
        if not hasattr(user, 'school_branch') or not user.school_branch:
            return Response(
                {"error": "User is not associated with any school"},
                status=status.HTTP_400_BAD_REQUEST
            )

        school = user.school_branch.school
        return self._get_school_statistics(school)

    @action(detail=False, methods=['get'], url_path='schools/(?P<school_id>[^/.]+)')
    def schools(self, request, school_id=None):
        """Get statistics for a specific school (admin only)"""
        user = request.user

        # Only superusers can access other schools' statistics
        if not user.is_superuser:
            return Response(
                {"error": "Permission denied"},
                status=status.HTTP_403_FORBIDDEN
            )

        try:
            school = School.objects.get(pk=school_id)
        except School.DoesNotExist:
            return Response(
                {"error": "School not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        return self._get_school_statistics(school)

    def _get_aggregate_statistics(self):
        """Helper method to get aggregate statistics for super users"""
        # Count all students across all schools
        student_count = Student.objects.count()

        # Count all staff (teachers + staff) across all schools
        teacher_count = Teacher.objects.count()
        staff_count = Staff.objects.count()
        total_staff_count = teacher_count + staff_count

        # Count all branches across all schools
        branch_count = SchoolBranch.objects.count()

        # Count all active and total users
        active_users = CustomUser.objects.filter(is_active=True).count()
        total_users = CustomUser.objects.count()

        # Count users by type
        student_users = CustomUser.objects.filter(user_type='student').count()
        teacher_users = CustomUser.objects.filter(user_type='teacher').count()
        staff_users = CustomUser.objects.filter(user_type='staff').count()

        # Count users active in the last month
        last_month = timezone.now() - timezone.timedelta(days=30)
        active_last_month = CustomUser.objects.filter(
            last_login__gte=last_month
        ).count()

        # Prepare aggregate statistics data
        statistics = {
            'student_count': student_count,
            'staff_count': total_staff_count,
            'branch_count': branch_count,
            'active_users_count': active_users,
            'total_users_count': total_users,
            'student_users': student_users,
            'teacher_users': teacher_users,
            'staff_users': staff_users,
            'active_last_month': active_last_month,
            'is_aggregate': True,  # Flag to indicate this is aggregate data
            'total_schools': School.objects.count()
        }

        serializer = SchoolStatisticsSerializer(statistics)
        return Response(serializer.data)

    def _get_school_statistics(self, school):
        """Helper method to get statistics for a school"""
        # Count students
        student_count = Student.objects.filter(user__school_branch__school=school).count()

        # Count staff (teachers + staff)
        teacher_count = Teacher.objects.filter(user__school_branch__school=school).count()
        staff_count = Staff.objects.filter(user__school_branch__school=school).count()
        total_staff_count = teacher_count + staff_count

        # Count branches
        branch_count = SchoolBranch.objects.filter(school=school).count()

        # Count active and total users
        active_users = CustomUser.objects.filter(
            school_branch__school=school,
            is_active=True
        ).count()

        total_users = CustomUser.objects.filter(
            school_branch__school=school
        ).count()

        # Count users by type
        student_users = Student.objects.filter(user__school_branch__school=school).count()
        teacher_users = Teacher.objects.filter(user__school_branch__school=school).count()
        staff_users = Staff.objects.filter(user__school_branch__school=school).count()

        # Count active users in the last 30 days
        thirty_days_ago = timezone.now() - timezone.timedelta(days=30)
        active_last_month = CustomUser.objects.filter(
            school_branch__school=school,
            last_login__gte=thirty_days_ago
        ).count()

        # Prepare statistics data
        statistics = {
            'student_count': student_count,
            'staff_count': total_staff_count,
            'branch_count': branch_count,
            'active_users_count': active_users,
            'total_users_count': total_users,
            'student_users': student_users,
            'teacher_users': teacher_users,
            'staff_users': staff_users,
            'active_last_month': active_last_month
        }

        serializer = SchoolStatisticsSerializer(statistics)
        return Response(serializer.data)
