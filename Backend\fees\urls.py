from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    FeeManagementViewSet, PaymentCallbackView,
    ParentFeeViewSet, PaymentAnalyticsViewSet, ReceiptViewSet,
    ifrs16_report, fee_collection_report,
    outstanding_fees_report, student_performance_report,
    attendance_report, teacher_performance_report,
    individual_student_report, individual_teacher_report
)
from . import school_billing_views
from . import school_admin_billing_views
from .payment_automation_views import PaymentAutomationViewSet, AutomationSettingsViewSet

router = DefaultRouter()
router.register(r'management', FeeManagementViewSet, basename='fee-management')
router.register(r'parent', ParentFeeViewSet, basename='parent-fees')
router.register(r'analytics', PaymentAnalyticsViewSet, basename='payment-analytics')
router.register(r'receipts', ReceiptViewSet, basename='receipts')

# Super Admin School Billing Router
school_billing_router = DefaultRouter()
school_billing_router.register(r'billing-accounts', school_billing_views.SchoolBillingAccountViewSet)
school_billing_router.register(r'subscription-plans', school_billing_views.SchoolSubscriptionPlanViewSet)
school_billing_router.register(r'invoices', school_billing_views.SchoolInvoiceViewSet)
school_billing_router.register(r'payments', school_billing_views.SchoolPaymentViewSet)

# Payment Automation Router
automation_router = DefaultRouter()
automation_router.register(r'automation', PaymentAutomationViewSet, basename='payment-automation')
automation_router.register(r'automation-settings', AutomationSettingsViewSet, basename='automation-settings')

urlpatterns = [
    # Super Admin School Billing (requires superuser permissions)
    path('school-billing/', include(school_billing_router.urls)),

    # Payment Automation (requires superuser permissions)
    path('', include(automation_router.urls)),

    # School Admin Billing (for school administrators)
    path('school-admin-billing/billing_overview/', school_admin_billing_views.SchoolAdminBillingViewSet.as_view({'get': 'billing_overview'}), name='school-admin-billing-overview'),
    path('school-admin-billing/invoices/', school_admin_billing_views.SchoolAdminBillingViewSet.as_view({'get': 'invoices'}), name='school-admin-invoices'),
    path('school-admin-billing/payments/', school_admin_billing_views.SchoolAdminBillingViewSet.as_view({'get': 'payments'}), name='school-admin-payments'),
    path('school-admin-billing/initiate_payment/', school_admin_billing_views.SchoolAdminBillingViewSet.as_view({'post': 'initiate_payment'}), name='school-admin-initiate-payment'),
    path('school-admin-billing/download_invoice/', school_admin_billing_views.SchoolAdminBillingViewSet.as_view({'get': 'download_invoice'}), name='school-admin-download-invoice'),

    path('payment/callback/', PaymentCallbackView.as_view(), name='payment-callback'),
    path('reports/ifrs16/<int:school_id>/', ifrs16_report, name='ifrs16_report'),
    path('reports/fee_collection/<int:school_id>/', fee_collection_report, name='fee_collection_report'),  # You can now call this with: /api/fees/reports/fee_collection/1/?term=2
    path('reports/outstanding_fees/<int:school_id>/', outstanding_fees_report, name='outstanding_fees_report'),
    path('reports/student_performance/<int:school_id>/', student_performance_report, name='student_performance_report'),
    path('reports/attendance/<int:school_id>/', attendance_report, name='attendance_report'),
    path('reports/teacher_performance/<int:school_id>/', teacher_performance_report, name='teacher_performance_report'),
    path('reports/student/<int:student_id>/', individual_student_report, name='individual_student_report'),
    path('reports/teacher/<int:teacher_id>/', individual_teacher_report, name='individual_teacher_report'),
]

urlpatterns += router.urls
