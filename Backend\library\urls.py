from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from .views import (
    BookViewSet, EResourceViewSet, BorrowingViewSet, 
    ReturnViewSet, OverdueBookViewSet, LibraryReportViewSet
)

router = DefaultRouter()
router.register('books', BookViewSet, basename='book')
router.register('e-resources', EResourceViewSet, basename='e-resource')
router.register('borrowings', BorrowingViewSet, basename='borrowing')
router.register('returns', ReturnViewSet, basename='return')
router.register('overdue', OverdueBookViewSet, basename='overdue')
router.register('reports', LibraryReportViewSet, basename='library-report')

urlpatterns = [
    path('', include(router.urls)),
]
