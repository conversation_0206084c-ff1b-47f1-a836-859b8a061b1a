from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from schools.models import School, SchoolBranch
from fees.models import FeeStructure, FeeType
from academics.models import Term
from fleet.models import Route
from fleet.transport_fees import TransportFee, TransportFeeDiscount

User = get_user_model()

class TransportFeeAPITests(TestCase):
    def setUp(self):
        # Create test school
        self.school = School.objects.create(name="Test School", code="TS001")
        self.branch = SchoolBranch.objects.create(
            name="Main Branch",
            school=self.school,
            address="123 Test St",
            phone="1234567890"
        )
        
        # Create test user
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
            school_branch=self.branch
        )
        
        # Create test term
        self.term = Term.objects.create(
            name="Term 1 2025",
            start_date="2025-01-01",
            end_date="2025-04-30",
            school=self.school
        )
        
        # Create test fee structure
        self.fee_structure = FeeStructure.objects.create(
            name="2025 Fee Structure",
            school=self.school,
            academic_year="2025",
            term=self.term,
            total_amount=50000.00,
            is_active=True
        )
        
        # Create test fee type
        self.fee_type = FeeType.objects.create(
            name="Transport Fee",
            school=self.school,
            description="Transport fee",
            amount=5000.00,
            is_mandatory=False,
            fee_structure=self.fee_structure
        )
        
        # Create test route
        self.route = Route.objects.create(
            name="North Route",
            description="Route to northern suburbs",
            start_location="School",
            end_location="North End",
            distance=10.5,
            estimated_time=45,
            stops=["Stop 1", "Stop 2", "Stop 3"],
            is_active=True,
            school_branch=self.branch
        )
        
        # Create test transport fee
        self.transport_fee = TransportFee.objects.create(
            route=self.route,
            fee_type=self.fee_type,
            fee_structure=self.fee_structure,
            amount=4000.00,
            is_active=True,
            school_branch=self.branch
        )
        
        # Create test discount
        today = timezone.now().date()
        self.discount = TransportFeeDiscount.objects.create(
            name="Early Bird Discount",
            transport_fee=self.transport_fee,
            discount_type="PERCENTAGE",
            percentage=10.00,
            start_date=today,
            end_date=today + timedelta(days=30),
            is_active=True,
            school_branch=self.branch
        )
        
        # Set up API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
    
    def test_transport_fee_list(self):
        """Test retrieving transport fees"""
        url = reverse('transport-fee-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['route'], self.route.id)
        self.assertEqual(response.data[0]['fee_type'], self.fee_type.id)
    
    def test_transport_fee_create(self):
        """Test creating a new transport fee"""
        url = reverse('transport-fee-list')
        data = {
            'route': self.route.id,
            'fee_type': self.fee_type.id,
            'fee_structure': self.fee_structure.id,
            'amount': 6000.00,
            'is_active': True
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(TransportFee.objects.count(), 2)
        self.assertEqual(float(TransportFee.objects.last().amount), 6000.00)
    
    def test_transport_fee_detail(self):
        """Test retrieving a specific transport fee"""
        url = reverse('transport-fee-detail', args=[self.transport_fee.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], self.transport_fee.id)
        self.assertEqual(float(response.data['amount']), 4000.00)
    
    def test_transport_fee_update(self):
        """Test updating a transport fee"""
        url = reverse('transport-fee-detail', args=[self.transport_fee.id])
        data = {
            'route': self.route.id,
            'fee_type': self.fee_type.id,
            'fee_structure': self.fee_structure.id,
            'amount': 4500.00,
            'is_active': True
        }
        response = self.client.put(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.transport_fee.refresh_from_db()
        self.assertEqual(float(self.transport_fee.amount), 4500.00)
    
    def test_transport_fee_discounts(self):
        """Test retrieving discounts for a transport fee"""
        url = reverse('transport-fee-discounts', args=[self.transport_fee.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['name'], "Early Bird Discount")
        self.assertEqual(float(response.data[0]['percentage']), 10.00)
    
    def test_transport_discount_create(self):
        """Test creating a new transport fee discount"""
        url = reverse('transport-fee-discount-list')
        today = timezone.now().date()
        data = {
            'name': 'Family Discount',
            'transport_fee': self.transport_fee.id,
            'discount_type': 'FIXED',
            'amount': 500.00,
            'start_date': today.isoformat(),
            'end_date': (today + timedelta(days=60)).isoformat(),
            'is_active': True
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(TransportFeeDiscount.objects.count(), 2)
        self.assertEqual(TransportFeeDiscount.objects.last().name, 'Family Discount')
        self.assertEqual(float(TransportFeeDiscount.objects.last().amount), 500.00)
