"""
Security audit logging system for ShuleXcel.
"""
import logging
import json
from datetime import datetime
from django.contrib.auth import get_user_model
from django.db import models
from django.utils import timezone
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey

logger = logging.getLogger('security_audit')
User = get_user_model()


class AuditEvent(models.Model):
    """
    Model to store security audit events.
    """
    EVENT_TYPES = [
        ('LOGIN_SUCCESS', 'Successful Login'),
        ('LOGIN_FAILED', 'Failed Login'),
        ('LOGOUT', 'Logout'),
        ('PASSWORD_CHANGE', 'Password Change'),
        ('PASSWORD_RESET', 'Password Reset'),
        ('MFA_SETUP', 'MFA Setup'),
        ('MFA_DISABLE', 'MFA Disable'),
        ('PERMISSION_DENIED', 'Permission Denied'),
        ('DATA_ACCESS', 'Data Access'),
        ('DATA_MODIFY', 'Data Modification'),
        ('DATA_DELETE', 'Data Deletion'),
        ('ADMIN_ACCESS', 'Admin Panel Access'),
        ('SUSPICIOUS_ACTIVITY', 'Suspicious Activity'),
        ('SECURITY_VIOLATION', 'Security Violation'),
        ('FILE_UPLOAD', 'File Upload'),
        ('EXPORT_DATA', 'Data Export'),
        ('IMPORT_DATA', 'Data Import'),
        ('SYSTEM_CONFIG', 'System Configuration Change'),
    ]
    
    SEVERITY_LEVELS = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]
    
    timestamp = models.DateTimeField(auto_now_add=True, db_index=True)
    event_type = models.CharField(max_length=50, choices=EVENT_TYPES, db_index=True)
    severity = models.CharField(max_length=10, choices=SEVERITY_LEVELS, default='LOW')
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    description = models.TextField()
    additional_data = models.JSONField(default=dict, blank=True)
    
    # Generic foreign key for related objects
    content_type = models.ForeignKey(ContentType, on_delete=models.SET_NULL, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    
    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['timestamp', 'event_type']),
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['ip_address', 'timestamp']),
            models.Index(fields=['severity', 'timestamp']),
        ]
    
    def __str__(self):
        return f"{self.timestamp} - {self.event_type} - {self.user or 'Anonymous'}"


class SecurityAuditLogger:
    """
    Security audit logger for tracking security events.
    """
    
    @staticmethod
    def log_event(event_type, request=None, user=None, description="", 
                  severity="LOW", additional_data=None, related_object=None):
        """
        Log a security audit event.
        """
        if request:
            ip_address = SecurityAuditLogger._get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            if not user and hasattr(request, 'user') and request.user.is_authenticated:
                user = request.user
        else:
            ip_address = '127.0.0.1'
            user_agent = ''
        
        # Create audit event
        audit_event = AuditEvent.objects.create(
            event_type=event_type,
            severity=severity,
            user=user,
            ip_address=ip_address,
            user_agent=user_agent,
            description=description,
            additional_data=additional_data or {},
            content_object=related_object
        )
        
        # Log to file as well
        log_data = {
            'timestamp': audit_event.timestamp.isoformat(),
            'event_type': event_type,
            'severity': severity,
            'user_id': user.id if user else None,
            'user_email': user.email if user else None,
            'ip_address': ip_address,
            'description': description,
            'additional_data': additional_data or {}
        }
        
        # Choose log level based on severity
        if severity == 'CRITICAL':
            logger.critical(json.dumps(log_data))
        elif severity == 'HIGH':
            logger.error(json.dumps(log_data))
        elif severity == 'MEDIUM':
            logger.warning(json.dumps(log_data))
        else:
            logger.info(json.dumps(log_data))
        
        return audit_event
    
    @staticmethod
    def log_login_success(request, user):
        """Log successful login."""
        return SecurityAuditLogger.log_event(
            'LOGIN_SUCCESS',
            request=request,
            user=user,
            description=f"User {user.email} logged in successfully",
            severity='LOW'
        )
    
    @staticmethod
    def log_login_failed(request, email=None, reason="Invalid credentials"):
        """Log failed login attempt."""
        description = f"Failed login attempt"
        if email:
            description += f" for {email}"
        description += f": {reason}"
        
        return SecurityAuditLogger.log_event(
            'LOGIN_FAILED',
            request=request,
            description=description,
            severity='MEDIUM',
            additional_data={'email': email, 'reason': reason}
        )
    
    @staticmethod
    def log_logout(request, user):
        """Log user logout."""
        return SecurityAuditLogger.log_event(
            'LOGOUT',
            request=request,
            user=user,
            description=f"User {user.email} logged out",
            severity='LOW'
        )
    
    @staticmethod
    def log_password_change(request, user):
        """Log password change."""
        return SecurityAuditLogger.log_event(
            'PASSWORD_CHANGE',
            request=request,
            user=user,
            description=f"User {user.email} changed password",
            severity='MEDIUM'
        )
    
    @staticmethod
    def log_mfa_setup(request, user):
        """Log MFA setup."""
        return SecurityAuditLogger.log_event(
            'MFA_SETUP',
            request=request,
            user=user,
            description=f"User {user.email} set up MFA",
            severity='MEDIUM'
        )
    
    @staticmethod
    def log_mfa_disable(request, user):
        """Log MFA disable."""
        return SecurityAuditLogger.log_event(
            'MFA_DISABLE',
            request=request,
            user=user,
            description=f"User {user.email} disabled MFA",
            severity='HIGH'
        )
    
    @staticmethod
    def log_permission_denied(request, resource="", action=""):
        """Log permission denied."""
        user = request.user if hasattr(request, 'user') and request.user.is_authenticated else None
        description = f"Permission denied for {action} on {resource}"
        if user:
            description += f" by user {user.email}"
        
        return SecurityAuditLogger.log_event(
            'PERMISSION_DENIED',
            request=request,
            user=user,
            description=description,
            severity='MEDIUM',
            additional_data={'resource': resource, 'action': action}
        )
    
    @staticmethod
    def log_suspicious_activity(request, activity_type="", details=""):
        """Log suspicious activity."""
        user = request.user if hasattr(request, 'user') and request.user.is_authenticated else None
        description = f"Suspicious activity detected: {activity_type}"
        if details:
            description += f" - {details}"
        
        return SecurityAuditLogger.log_event(
            'SUSPICIOUS_ACTIVITY',
            request=request,
            user=user,
            description=description,
            severity='HIGH',
            additional_data={'activity_type': activity_type, 'details': details}
        )
    
    @staticmethod
    def log_data_access(request, user, resource, object_id=None):
        """Log data access."""
        description = f"User {user.email} accessed {resource}"
        if object_id:
            description += f" (ID: {object_id})"
        
        return SecurityAuditLogger.log_event(
            'DATA_ACCESS',
            request=request,
            user=user,
            description=description,
            severity='LOW',
            additional_data={'resource': resource, 'object_id': object_id}
        )
    
    @staticmethod
    def log_data_modification(request, user, resource, object_id=None, changes=None):
        """Log data modification."""
        description = f"User {user.email} modified {resource}"
        if object_id:
            description += f" (ID: {object_id})"
        
        return SecurityAuditLogger.log_event(
            'DATA_MODIFY',
            request=request,
            user=user,
            description=description,
            severity='MEDIUM',
            additional_data={
                'resource': resource, 
                'object_id': object_id,
                'changes': changes or {}
            }
        )
    
    @staticmethod
    def log_data_deletion(request, user, resource, object_id=None):
        """Log data deletion."""
        description = f"User {user.email} deleted {resource}"
        if object_id:
            description += f" (ID: {object_id})"
        
        return SecurityAuditLogger.log_event(
            'DATA_DELETE',
            request=request,
            user=user,
            description=description,
            severity='HIGH',
            additional_data={'resource': resource, 'object_id': object_id}
        )
    
    @staticmethod
    def log_file_upload(request, user, filename, file_size):
        """Log file upload."""
        description = f"User {user.email} uploaded file: {filename} ({file_size} bytes)"
        
        return SecurityAuditLogger.log_event(
            'FILE_UPLOAD',
            request=request,
            user=user,
            description=description,
            severity='LOW',
            additional_data={'filename': filename, 'file_size': file_size}
        )
    
    @staticmethod
    def log_admin_access(request, user, action=""):
        """Log admin panel access."""
        description = f"User {user.email} accessed admin panel"
        if action:
            description += f": {action}"
        
        return SecurityAuditLogger.log_event(
            'ADMIN_ACCESS',
            request=request,
            user=user,
            description=description,
            severity='MEDIUM',
            additional_data={'action': action}
        )
    
    @staticmethod
    def _get_client_ip(request):
        """Get the real client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
        return ip


# Decorator for automatic audit logging
def audit_log(event_type, severity='LOW', description_template=""):
    """
    Decorator to automatically log audit events.
    """
    def decorator(func):
        def wrapper(request, *args, **kwargs):
            try:
                result = func(request, *args, **kwargs)
                
                # Log successful operation
                user = request.user if hasattr(request, 'user') and request.user.is_authenticated else None
                description = description_template.format(
                    user=user.email if user else 'Anonymous',
                    **kwargs
                )
                
                SecurityAuditLogger.log_event(
                    event_type,
                    request=request,
                    user=user,
                    description=description,
                    severity=severity
                )
                
                return result
            except Exception as e:
                # Log failed operation
                user = request.user if hasattr(request, 'user') and request.user.is_authenticated else None
                description = f"Failed operation: {description_template.format(user=user.email if user else 'Anonymous', **kwargs)} - Error: {str(e)}"
                
                SecurityAuditLogger.log_event(
                    event_type,
                    request=request,
                    user=user,
                    description=description,
                    severity='HIGH'
                )
                
                raise
        
        return wrapper
    return decorator
