from django.contrib.auth.models import Group, Permission
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import CustomUser
from .Serializers import GroupSerializer, PermissionSerializer
from typing import Any
from django.db.models.query import QuerySet
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db.models import Q
from django.utils.translation import gettext_lazy as _

class IsAdminUser(permissions.BasePermission):
    """
    Custom permission to only allow admin users.
    """
    def has_permission(self, request, view):
        # Allow superusers
        if request.user and request.user.is_superuser:
            return True

        # Check for admin-type roles
        admin_roles = [
            'system_admin',
            'school_admin',
            'branch_admin',
            'ict_admin',
            'admin'  # Include legacy 'admin' type
        ]
        if request.user and request.user.user_type in admin_roles:
            return True
        return False

class GroupViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows groups to be viewed or edited.
    """
    queryset = Group.objects.all()
    serializer_class = GroupSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminUser]


    def get_queryset(self) -> 'QuerySet[Any]':
        """
        Optionally restricts the returned groups to those related to a specific branch,
        by filtering against a 'branch' query parameter in the URL.
        """
        queryset = Group.objects.all()
        branch_id = self.request.query_params.get('branch', None)
        if branch_id is not None:
            # Filter for branch-specific groups (groups that start with branch code)
            from schools.models import SchoolBranch
            try:
                branch = SchoolBranch.objects.get(id=branch_id)
                queryset = queryset.filter(name__startswith=f"{branch.code}_")
            except SchoolBranch.DoesNotExist:
                pass
        return queryset

    @action(detail=True, methods=['post'])
    def add_user(self, request, pk=None):
        """Add a user to this group"""
        group = self.get_object()
        user_id = request.data.get('user_id')

        if not user_id:
            return Response({"error": "User ID is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = CustomUser.objects.get(id=user_id)
            user.groups.add(group)
            return Response({
                "message": f"User {user.get_full_name()} added to group {group.name}",
                "user_id": user.id,
                "group_id": group.id
            })
        except CustomUser.DoesNotExist:
            return Response({"error": "User not found"}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def remove_user(self, request, pk=None):
        """Remove a user from this group"""
        group = self.get_object()
        user_id = request.data.get('user_id')

        if not user_id:
            return Response({"error": "User ID is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = CustomUser.objects.get(id=user_id)
            user.groups.remove(group)
            return Response({
                "message": f"User {user.get_full_name()} removed from group {group.name}",
                "user_id": user.id,
                "group_id": group.id
            })
        except CustomUser.DoesNotExist:
            return Response({"error": "User not found"}, status=status.HTTP_404_NOT_FOUND)    @action(detail=True, methods=['get'])
    def users(self, request, pk=None):
        """Get all users in this group"""
        group = self.get_object()
        users = CustomUser.objects.filter(groups=group)
        from .Serializers import UserSerializer
        serializer = UserSerializer(users, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def permissions(self, request, pk=None):
        """Get all permissions for this group"""
        group = self.get_object()
        permissions = group.permissions.all()
        serializer = PermissionSerializer(permissions, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def add_permission(self, request, pk=None):
        """Add a permission to this group"""
        group = self.get_object()
        permission_id = request.data.get('permission_id')

        if not permission_id:
            return Response({"error": "Permission ID is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            permission = Permission.objects.get(id=permission_id)
            group.permissions.add(permission)
            return Response({
                "message": f"Permission {permission.name} added to group {group.name}",
                "permission_id": permission.id,
                "group_id": group.id
            })
        except Permission.DoesNotExist:
            return Response({"error": "Permission not found"}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def remove_permission(self, request, pk=None):
        """Remove a permission from this group"""
        group = self.get_object()
        permission_id = request.data.get('permission_id')

        if not permission_id:
            return Response({"error": "Permission ID is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            permission = Permission.objects.get(id=permission_id)
            group.permissions.remove(permission)
            return Response({
                "message": f"Permission {permission.name} removed from group {group.name}",
                "permission_id": permission.id,
                "group_id": group.id
            })
        except Permission.DoesNotExist:
            return Response({"error": "Permission not found"}, status=status.HTTP_404_NOT_FOUND)

class PermissionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows permissions to be viewed.
    """
    queryset = Permission.objects.all()
    serializer_class = PermissionSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminUser]

    def get_queryset(self):
        """
        Optionally restricts the returned permissions to a specific app,
        by filtering against an 'app' query parameter in the URL.
        """
        queryset = Permission.objects.all()
        app = self.request.query_params.get('app', None)
        if app is not None:
            queryset = queryset.filter(content_type__app_label=app)
        return queryset

class RoleBasedPermissionMixin:
    """Mixin to check permissions based on user groups"""

    required_groups = []  # List of group names required to access this view

    def check_permissions(self, request):
        super().check_permissions(request)

        # Skip group check for superusers
        if request.user.is_superuser:
            return

        # Check if user belongs to any of the required groups
        user_groups = request.user.groups.values_list('name', flat=True)
        if not any(group in user_groups for group in self.required_groups):
            self.permission_denied(
                request,
                message="You do not have permission to perform this action."
            )
