from django.contrib import admin
from .models import (
    Community, CommunityMember, Post, Comment, ClubMeeting,
    MeetingAttendance, Reaction, Notification
)


@admin.register(Community)
class CommunityAdmin(admin.ModelAdmin):
    list_display = ('name', 'community_type', 'school_branch', 'created_by', 'is_active', 'created_at')
    list_filter = ('community_type', 'is_active', 'is_private', 'school_branch')
    search_fields = ('name', 'description', 'created_by__email')
    date_hierarchy = 'created_at'
    readonly_fields = ('created_at', 'updated_at')


@admin.register(CommunityMember)
class CommunityMemberAdmin(admin.ModelAdmin):
    list_display = ('user', 'community', 'role', 'is_active', 'joined_at')
    list_filter = ('role', 'is_active', 'community')
    search_fields = ('user__email', 'user__first_name', 'user__last_name', 'community__name')
    date_hierarchy = 'joined_at'
    readonly_fields = ('joined_at',)


@admin.register(Post)
class PostAdmin(admin.ModelAdmin):
    list_display = ('title', 'community', 'author', 'is_announcement', 'is_pinned', 'created_at')
    list_filter = ('is_announcement', 'is_pinned', 'is_edited', 'community')
    search_fields = ('title', 'content', 'author__email', 'community__name')
    date_hierarchy = 'created_at'
    readonly_fields = ('created_at', 'updated_at', 'is_edited', 'edited_at')


@admin.register(Comment)
class CommentAdmin(admin.ModelAdmin):
    list_display = ('get_short_content', 'post', 'author', 'parent_comment', 'created_at')
    list_filter = ('is_edited', 'post__community')
    search_fields = ('content', 'author__email', 'post__title')
    date_hierarchy = 'created_at'
    readonly_fields = ('created_at', 'updated_at', 'is_edited', 'edited_at')
    
    def get_short_content(self, obj):
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    get_short_content.short_description = 'Content'


@admin.register(ClubMeeting)
class ClubMeetingAdmin(admin.ModelAdmin):
    list_display = ('title', 'community', 'start_time', 'end_time', 'status', 'organizer')
    list_filter = ('status', 'community')
    search_fields = ('title', 'description', 'location', 'organizer__email', 'community__name')
    date_hierarchy = 'start_time'
    readonly_fields = ('created_at', 'updated_at')


@admin.register(MeetingAttendance)
class MeetingAttendanceAdmin(admin.ModelAdmin):
    list_display = ('meeting', 'member', 'is_present', 'check_in_time', 'check_out_time')
    list_filter = ('is_present', 'meeting')
    search_fields = ('member__user__email', 'meeting__title', 'notes')
    date_hierarchy = 'created_at'
    readonly_fields = ('created_at', 'updated_at')


@admin.register(Reaction)
class ReactionAdmin(admin.ModelAdmin):
    list_display = ('user', 'reaction_type', 'get_target', 'created_at')
    list_filter = ('reaction_type',)
    search_fields = ('user__email', 'post__title', 'comment__content')
    date_hierarchy = 'created_at'
    readonly_fields = ('created_at', 'updated_at')
    
    def get_target(self, obj):
        return obj.post.title if obj.post else f"Comment: {obj.comment.content[:30]}..."
    get_target.short_description = 'Target'


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('user', 'notification_type', 'title', 'is_read', 'created_at')
    list_filter = ('notification_type', 'is_read', 'community')
    search_fields = ('user__email', 'title', 'message')
    date_hierarchy = 'created_at'
    readonly_fields = ('created_at', 'updated_at', 'read_at')
    actions = ['mark_as_read']
    
    def mark_as_read(self, request, queryset):
        from django.utils import timezone
        updated = queryset.update(is_read=True, read_at=timezone.now())
        self.message_user(request, f"{updated} notifications marked as read.")
    mark_as_read.short_description = "Mark selected notifications as read"
