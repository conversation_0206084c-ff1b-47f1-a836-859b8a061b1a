from rest_framework import serializers
from .models import School, SchoolBranch
from django.utils import timezone
from datetime import timedelta

class SchoolBasicSerializer(serializers.ModelSerializer):
    """Basic serializer for School model with essential fields only"""
    class Meta:
        model = School
        fields = ['id', 'name', 'code', 'registration_number', 'is_active']

class SchoolSerializer(serializers.ModelSerializer):
    # Add a field to show the number of branches for each school
    branch_count = serializers.SerializerMethodField()

    def get_branch_count(self, obj):
        return obj.branch.count()

    def create(self, validated_data):
        # Create the school
        school = super().create(validated_data)

        # Auto-create a default license for the school
        try:
            from settings_app.license_models import LicenseSubscription
            from settings_app.license_models import generate_license_key

            # Create a trial license by default
            expiry_date = timezone.now().date() + timedelta(days=30)  # 30-day trial
            license_key = generate_license_key(school, expiry_date)

            LicenseSubscription.objects.create(
                school=school,
                package_type='basic',  # Default to basic package
                subscription_status='TRIAL',
                start_date=timezone.now().date(),
                expiry_date=expiry_date,
                max_students=100,  # Trial limits
                max_staff=10,
                max_branches=1,
                license_key=license_key
            )
        except Exception as e:
            # Log the error but don't fail school creation
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to create license for school {school.id}: {str(e)}")

        return school

    class Meta:
        model = School
        fields = '__all__'

class SchoolBranchSerializer(serializers.ModelSerializer):
    # Add school name for easier reference
    school_name = serializers.ReadOnlyField(source='school.name')

    class Meta:
        model = SchoolBranch
        fields = '__all__'
