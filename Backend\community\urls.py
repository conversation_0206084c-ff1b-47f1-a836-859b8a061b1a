from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    CommunityViewSet, CommunityMemberViewSet, PostViewSet, CommentViewSet,
    ClubMeetingViewSet, MeetingAttendanceViewSet, ReactionViewSet, NotificationViewSet,
    CommunityDashboardView
)

router = DefaultRouter()
router.register('communities', CommunityViewSet, basename='community')
router.register('community-members', CommunityMemberViewSet, basename='community-member')
router.register('posts', PostViewSet, basename='post')
router.register('comments', CommentViewSet, basename='comment')
router.register('club-meetings', ClubMeetingViewSet, basename='club-meeting')
router.register('meeting-attendance', MeetingAttendanceViewSet, basename='meeting-attendance')
router.register('reactions', ReactionViewSet, basename='reaction')
router.register('notifications', NotificationViewSet, basename='notification')

urlpatterns = [
    path('', include(router.urls)),
    path('dashboard/', CommunityDashboardView.as_view(), name='community-dashboard'),
]
