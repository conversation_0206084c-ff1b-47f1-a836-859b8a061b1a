from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.db.models import Q, Avg, Count
from django.utils import timezone

from academics.models.class_progression import (
    ClassProgressionRule, StudentProgression, CareerPath,
    CareerPathSubjectRequirement, StudentCareerGuidance, SubjectCombination
)
from users.models import Student
from .serializers import (
    ClassProgressionRuleSerializer, StudentProgressionSerializer,
    CareerPathSerializer, StudentCareerGuidanceSerializer,
    SubjectCombinationSerializer
)


class ClassProgressionRuleViewSet(viewsets.ModelViewSet):
    """ViewSet for managing class progression rules"""
    serializer_class = ClassProgressionRuleSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = ClassProgressionRule.objects.all()
        school = self.request.query_params.get('school', None)
        current_level = self.request.query_params.get('current_level', None)
        
        if school:
            queryset = queryset.filter(school_id=school)
        if current_level:
            queryset = queryset.filter(current_level_id=current_level)
            
        return queryset.select_related('current_level', 'next_level', 'school')
    
    @action(detail=False, methods=['get'])
    def progression_options(self, request):
        """Get progression options for a specific class"""
        class_id = request.query_params.get('class_id')
        if not class_id:
            return Response(
                {'error': 'class_id parameter is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        from academics.models import Class
        class_obj = get_object_or_404(Class, id=class_id)
        
        if class_obj.education_level:
            options = class_obj.get_next_class_options()
            return Response({'progression_options': options})
        
        return Response({'progression_options': []})


class StudentProgressionViewSet(viewsets.ModelViewSet):
    """ViewSet for managing student progression records"""
    serializer_class = StudentProgressionSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = StudentProgression.objects.all()
        student = self.request.query_params.get('student', None)
        academic_year = self.request.query_params.get('academic_year', None)
        status_filter = self.request.query_params.get('status', None)
        
        if student:
            queryset = queryset.filter(student_id=student)
        if academic_year:
            queryset = queryset.filter(academic_year_id=academic_year)
        if status_filter:
            queryset = queryset.filter(status=status_filter)
            
        return queryset.select_related(
            'student', 'from_class', 'to_class', 'academic_year', 'decided_by'
        )
    
    @action(detail=False, methods=['post'])
    def bulk_progression(self, request):
        """Process bulk student progression"""
        student_ids = request.data.get('student_ids', [])
        progression_data = request.data.get('progression_data', {})
        
        if not student_ids or not progression_data:
            return Response(
                {'error': 'student_ids and progression_data are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        created_progressions = []
        errors = []
        
        for student_id in student_ids:
            try:
                student = Student.objects.get(id=student_id)
                progression_data['student'] = student.id
                
                serializer = self.get_serializer(data=progression_data)
                if serializer.is_valid():
                    progression = serializer.save()
                    created_progressions.append(progression.id)
                else:
                    errors.append({
                        'student_id': student_id,
                        'errors': serializer.errors
                    })
            except Student.DoesNotExist:
                errors.append({
                    'student_id': student_id,
                    'errors': 'Student not found'
                })
        
        return Response({
            'created_progressions': created_progressions,
            'errors': errors
        })
    
    @action(detail=False, methods=['get'])
    def progression_statistics(self, request):
        """Get progression statistics for an academic year"""
        academic_year = request.query_params.get('academic_year')
        if not academic_year:
            return Response(
                {'error': 'academic_year parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        stats = StudentProgression.objects.filter(
            academic_year_id=academic_year
        ).aggregate(
            total_students=Count('id'),
            promoted=Count('id', filter=Q(status='promoted')),
            repeated=Count('id', filter=Q(status='repeated')),
            transferred=Count('id', filter=Q(status='transferred')),
            graduated=Count('id', filter=Q(status='graduated')),
            dropped_out=Count('id', filter=Q(status='dropped_out')),
            average_score=Avg('average_score')
        )
        
        return Response(stats)


class CareerPathViewSet(viewsets.ModelViewSet):
    """ViewSet for managing career paths"""
    serializer_class = CareerPathSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = CareerPath.objects.filter(is_active=True)
        category = self.request.query_params.get('category', None)
        
        if category:
            queryset = queryset.filter(category=category)
            
        return queryset.prefetch_related('required_subjects')
    
    @action(detail=False, methods=['get'])
    def categories(self, request):
        """Get all career path categories"""
        categories = CareerPath.objects.values_list('category', flat=True).distinct()
        return Response({'categories': list(categories)})
    
    @action(detail=True, methods=['get'])
    def requirements(self, request, pk=None):
        """Get detailed requirements for a career path"""
        career_path = self.get_object()
        requirements = CareerPathSubjectRequirement.objects.filter(
            career_path=career_path
        ).select_related('subject')
        
        requirement_data = []
        for req in requirements:
            requirement_data.append({
                'subject': req.subject.name,
                'minimum_grade': req.minimum_grade,
                'is_mandatory': req.is_mandatory
            })
        
        return Response({
            'career_path': career_path.name,
            'requirements': requirement_data
        })
    
    @action(detail=False, methods=['post'])
    def recommend_careers(self, request):
        """Recommend careers based on student performance"""
        student_id = request.data.get('student_id')
        if not student_id:
            return Response(
                {'error': 'student_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get student's recent performance
        from academics.models import TermResult
        recent_results = TermResult.objects.filter(
            student_id=student_id
        ).select_related('subject').order_by('-term__end_date')[:10]
        
        if not recent_results:
            return Response({'recommended_careers': []})
        
        # Simple recommendation logic based on subject performance
        subject_grades = {}
        for result in recent_results:
            subject_name = result.subject.name
            if subject_name not in subject_grades:
                subject_grades[subject_name] = []
            subject_grades[subject_name].append(result.grade)
        
        # Calculate average grades per subject
        avg_grades = {}
        for subject, grades in subject_grades.items():
            # Convert grades to numeric values for averaging
            numeric_grades = []
            for grade in grades:
                if grade == 'A':
                    numeric_grades.append(12)
                elif grade == 'A-':
                    numeric_grades.append(11)
                elif grade == 'B+':
                    numeric_grades.append(10)
                # Add more grade conversions as needed
            
            if numeric_grades:
                avg_grades[subject] = sum(numeric_grades) / len(numeric_grades)
        
        # Find matching career paths
        recommended_careers = []
        for career in CareerPath.objects.filter(is_active=True):
            requirements = CareerPathSubjectRequirement.objects.filter(
                career_path=career
            )
            
            meets_requirements = True
            for req in requirements:
                subject_avg = avg_grades.get(req.subject.name, 0)
                # Simple grade comparison logic
                if req.is_mandatory and subject_avg < 8:  # Below B- equivalent
                    meets_requirements = False
                    break
            
            if meets_requirements:
                recommended_careers.append({
                    'id': career.id,
                    'name': career.name,
                    'category': career.category,
                    'description': career.description
                })
        
        return Response({'recommended_careers': recommended_careers})


class StudentCareerGuidanceViewSet(viewsets.ModelViewSet):
    """ViewSet for managing student career guidance"""
    serializer_class = StudentCareerGuidanceSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = StudentCareerGuidance.objects.all()
        student = self.request.query_params.get('student', None)
        counselor = self.request.query_params.get('counselor', None)
        
        if student:
            queryset = queryset.filter(student_id=student)
        if counselor:
            queryset = queryset.filter(counselor_id=counselor)
            
        return queryset.select_related('student', 'counselor').prefetch_related('recommended_careers')
    
    @action(detail=False, methods=['get'])
    def upcoming_sessions(self, request):
        """Get upcoming guidance sessions"""
        upcoming = StudentCareerGuidance.objects.filter(
            next_session_date__gte=timezone.now().date()
        ).order_by('next_session_date')
        
        serializer = self.get_serializer(upcoming, many=True)
        return Response(serializer.data)


class SubjectCombinationViewSet(viewsets.ModelViewSet):
    """ViewSet for managing subject combinations"""
    serializer_class = SubjectCombinationSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = SubjectCombination.objects.filter(is_active=True)
        education_level = self.request.query_params.get('education_level', None)
        
        if education_level:
            queryset = queryset.filter(education_levels__id=education_level)
            
        return queryset.prefetch_related('subjects', 'career_paths', 'education_levels')
    
    @action(detail=False, methods=['post'])
    def recommend_combinations(self, request):
        """Recommend subject combinations based on career interests"""
        career_path_ids = request.data.get('career_path_ids', [])
        if not career_path_ids:
            return Response(
                {'error': 'career_path_ids is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        combinations = SubjectCombination.objects.filter(
            career_paths__id__in=career_path_ids,
            is_active=True
        ).distinct()
        
        serializer = self.get_serializer(combinations, many=True)
        return Response({'recommended_combinations': serializer.data})
