from django.core.management.base import BaseCommand
from academics.curriculum_models import CurriculumSystem, EducationLevel
from academics.curriculum_subjects import SubjectCategory, CurriculumSubject

class Command(BaseCommand):
    help = 'Seed curriculum subjects for different curriculum systems'

    def handle(self, *args, **options):
        self.stdout.write('Seeding curriculum subjects...')

        # CBC System
        cbc_system = CurriculumSystem.objects.get(code='CBC')

        # Create subject categories for CBC
        cbc_categories = [
            {'name': 'Learning Area', 'code': 'LA', 'description': 'Core learning areas in CBC'},
            {'name': 'Competency', 'code': 'CP', 'description': 'Competencies within learning areas'},
            {'name': 'Subject', 'code': 'SUB', 'description': 'Specific subjects within competencies'}
        ]

        for category_data in cbc_categories:
            SubjectCategory.objects.update_or_create(
                curriculum_system=cbc_system,
                code=category_data['code'],
                defaults=category_data
            )
            self.stdout.write(f'Created {category_data["name"]} category for CBC')

        # 8-4-4 System
        old_system = CurriculumSystem.objects.get(code='844')

        # Create subject categories for 8-4-4
        old_categories = [
            {'name': 'Subject Group', 'code': 'SG', 'description': 'Subject groups in the 8-4-4 system'},
            {'name': 'Subject', 'code': 'SUB', 'description': 'Specific subjects within subject groups'}
        ]

        for category_data in old_categories:
            SubjectCategory.objects.update_or_create(
                curriculum_system=old_system,
                code=category_data['code'],
                defaults=category_data
            )
            self.stdout.write(f'Created {category_data["name"]} category for 8-4-4')

        # IGCSE/A-Levels System
        igcse_system = CurriculumSystem.objects.get(code='IGCSE')

        # Create subject categories for IGCSE/A-Levels
        igcse_categories = [
            {'name': 'Subject Group', 'code': 'SG', 'description': 'Subject groups in the IGCSE/A-Levels system'},
            {'name': 'Subject', 'code': 'SUB', 'description': 'Specific subjects within subject groups'}
        ]

        for category_data in igcse_categories:
            SubjectCategory.objects.update_or_create(
                curriculum_system=igcse_system,
                code=category_data['code'],
                defaults=category_data
            )
            self.stdout.write(f'Created {category_data["name"]} category for IGCSE/A-Levels')
        self.stdout.write(self.style.SUCCESS('Successfully seeded curriculum subjects'))

