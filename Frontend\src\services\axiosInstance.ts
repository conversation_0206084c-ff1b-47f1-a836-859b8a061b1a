import axios, { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { secureStorage } from '../utils/secureStorage';
import { API_BASE_URL } from '../config/constants';

// Create an axios instance with default configuration
const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // Increased timeout for production
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Enable cookies for refresh token
});

// Request interceptor to include auth token and handle token refresh
axiosInstance.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    // Get access token from secure storage
    const accessToken = secureStorage.getAccessToken();

    if (accessToken && !secureStorage.isTokenExpired()) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    } else if (secureStorage.isTokenExpired()) {
      // Try to refresh token before making the request
      try {
        await refreshAuthToken();
        const newAccessToken = secureStorage.getAccessToken();
        if (newAccessToken) {
          config.headers.Authorization = `Bearer ${newAccessToken}`;
        }
      } catch (refreshError) {
        // If refresh fails, clear tokens and redirect to login
        handleAuthFailure();
        return Promise.reject(refreshError);
      }
    }

    // Add CSRF token if available
    const csrfToken = getCsrfToken();
    if (csrfToken) {
      config.headers['X-CSRFToken'] = csrfToken;
    }

    // Add request ID for tracking
    config.headers['X-Request-ID'] = generateRequestId();

    return config;
  },
  (error: AxiosError) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors and token refresh
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as InternalAxiosRequestConfig & { _retry?: boolean };

    // Handle 401 Unauthorized errors
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh the token
        await refreshAuthToken();

        // Retry the original request with new token
        const newAccessToken = secureStorage.getAccessToken();
        if (newAccessToken && originalRequest.headers) {
          originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
          return axiosInstance(originalRequest);
        }
      } catch (refreshError) {
        // If refresh fails, handle auth failure
        handleAuthFailure();
        return Promise.reject(refreshError);
      }
    }

    // Handle 403 Forbidden errors
    if (error.response?.status === 403) {
      console.warn('Access forbidden:', error.response.data);
      // You might want to show a user-friendly message here
    }

    // Handle 429 Rate Limit errors
    if (error.response?.status === 429) {
      console.warn('Rate limit exceeded:', error.response.data);
      // You might want to implement exponential backoff here
    }

    // Handle 500+ Server errors
    if (error.response?.status && error.response.status >= 500) {
      console.error('Server error:', error.response.data);
      // You might want to show a server error message here
    }

    // Handle network errors
    if (!error.response) {
      console.error('Network Error:', error.message);
      // You might want to show a network error message here
    }

    return Promise.reject(error);
  }
);

/**
 * Refresh authentication token
 */
async function refreshAuthToken(): Promise<void> {
  const refreshToken = secureStorage.getRefreshToken();

  if (!refreshToken) {
    throw new Error('No refresh token available');
  }

  try {
    const response = await axios.post(
      `${API_BASE_URL}/api/core/auth/token/refresh/`,
      { refresh: refreshToken },
      {
        headers: { 'Content-Type': 'application/json' },
        withCredentials: true,
      }
    );

    const { access, refresh: newRefresh } = response.data;

    // Store new tokens
    secureStorage.setTokens(access, newRefresh || refreshToken);

    console.log('Token refreshed successfully');
  } catch (error) {
    console.error('Token refresh failed:', error);
    throw error;
  }
}

/**
 * Handle authentication failure
 */
function handleAuthFailure(): void {
  // Clear all stored tokens
  secureStorage.clearTokens();

  // Redirect to signin if not already there
  if (window.location.pathname !== '/signin') {
    window.location.href = '/signin';
  }
}

/**
 * Get CSRF token from cookies
 */
function getCsrfToken(): string | null {
  const name = 'csrftoken';
  const cookies = document.cookie.split(';');

  for (let cookie of cookies) {
    const [cookieName, cookieValue] = cookie.trim().split('=');
    if (cookieName === name) {
      return decodeURIComponent(cookieValue);
    }
  }

  return null;
}

/**
 * Generate unique request ID for tracking
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export default axiosInstance;
