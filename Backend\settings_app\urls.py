from django.urls import path, include
from rest_framework.routers import DefaultRout<PERSON>
from .views import (
    SchoolProfileViewSet, SystemConfigViewSet, PermissionViewSet,
    RolePermissionViewSet, UserManagementViewSet, BackupRestoreViewSet,
    IntegrationViewSet, AuditLogViewSet
)
from .module_views import (
    LicenseSubscriptionViewSet, ModuleActivationViewSet, LicenseHistoryViewSet
)

router = DefaultRouter()
router.register('school-profiles', SchoolProfileViewSet, basename='school-profile')
router.register('system-config', SystemConfigViewSet, basename='system-config')
router.register('permissions', PermissionViewSet, basename='permission')
router.register('roles', RolePermissionViewSet, basename='role')
router.register('users', UserManagementViewSet, basename='user')
router.register('backups', BackupRestoreViewSet, basename='backup')
router.register('integrations', IntegrationViewSet, basename='integration')
router.register('audit-logs', AuditLogViewSet, basename='audit-log')

# License management endpoints
router.register('licenses', LicenseSubscriptionViewSet, basename='license')
router.register('modules', ModuleActivationViewSet, basename='module')
router.register('license-history', LicenseHistoryViewSet, basename='license-history')

urlpatterns = [
    path('', include(router.urls)),
]
