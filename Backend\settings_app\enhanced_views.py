"""
Enhanced views for the settings module with comprehensive functionality.
"""
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.filters import SearchFilter, OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db import transaction
from django.core.cache import cache
from django.conf import settings
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import json
import logging

from .enhanced_models import (
    AdvancedSchoolProfile, SystemSettings, NotificationTemplate,
    IntegrationSettings, CustomField, AuditLog
)
from .enhanced_serializers import (
    AdvancedSchoolProfileSerializer, SystemSettingsSerializer,
    NotificationTemplateSerializer, IntegrationSettingsSerializer,
    CustomFieldSerializer, AuditLogSerializer
)
from .models import SchoolProfile, SystemConfiguration
from .serializers import SchoolProfileSerializer, SystemConfigurationSerializer
from core.permissions import IsSchoolAdminOrReadOnly, IsOwnerOrReadOnly
from core.audit_logger import SecurityAuditLogger

logger = logging.getLogger(__name__)


class AdvancedSchoolProfileViewSet(viewsets.ModelViewSet):
    """Enhanced viewset for school profiles with advanced features"""
    
    serializer_class = AdvancedSchoolProfileSerializer
    permission_classes = [permissions.IsAuthenticated, IsSchoolAdminOrReadOnly]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['school_type', 'is_active', 'is_verified', 'country']
    search_fields = ['school_branch__name', 'motto', 'vision', 'mission']
    ordering_fields = ['created_at', 'updated_at', 'establishment_date']
    ordering = ['-updated_at']
    
    def get_queryset(self):
        """Filter by user's school branch"""
        user = self.request.user
        if user.is_superuser:
            return AdvancedSchoolProfile.objects.all()
        return AdvancedSchoolProfile.objects.filter(school_branch=user.school_branch)
    
    def perform_create(self, serializer):
        """Create with audit logging"""
        instance = serializer.save()
        SecurityAuditLogger.log_data_modification(
            self.request, self.request.user, 'AdvancedSchoolProfile',
            instance.id, {'action': 'created'}
        )
    
    def perform_update(self, serializer):
        """Update with audit logging"""
        old_instance = self.get_object()
        instance = serializer.save()
        
        # Log changes
        changes = {}
        for field in serializer.validated_data:
            old_value = getattr(old_instance, field, None)
            new_value = getattr(instance, field, None)
            if old_value != new_value:
                changes[field] = {'old': old_value, 'new': new_value}
        
        SecurityAuditLogger.log_data_modification(
            self.request, self.request.user, 'AdvancedSchoolProfile',
            instance.id, changes
        )
    
    @action(detail=True, methods=['post'])
    def verify_school(self, request, pk=None):
        """Verify school profile (admin only)"""
        if not request.user.is_superuser:
            return Response(
                {'error': 'Only superusers can verify schools'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        profile = self.get_object()
        profile.is_verified = True
        profile.verification_date = timezone.now()
        profile.save()
        
        SecurityAuditLogger.log_data_modification(
            request, request.user, 'AdvancedSchoolProfile',
            profile.id, {'action': 'verified'}
        )
        
        return Response({'message': 'School verified successfully'})
    
    @action(detail=True, methods=['get'])
    def capacity_report(self, request, pk=None):
        """Get detailed capacity utilization report"""
        profile = self.get_object()
        utilization = profile.get_capacity_utilization()
        
        return Response({
            'current_students': profile.current_students,
            'max_students': profile.max_students,
            'current_staff': profile.current_staff,
            'max_staff': profile.max_staff,
            'utilization': utilization,
            'recommendations': self._get_capacity_recommendations(profile, utilization)
        })
    
    def _get_capacity_recommendations(self, profile, utilization):
        """Generate capacity recommendations"""
        recommendations = []
        
        if utilization['students'] > 90:
            recommendations.append({
                'type': 'warning',
                'message': 'Student capacity is near maximum. Consider expansion or enrollment limits.'
            })
        elif utilization['students'] > 80:
            recommendations.append({
                'type': 'info',
                'message': 'Student capacity is high. Monitor enrollment closely.'
            })
        
        if utilization['staff'] > 90:
            recommendations.append({
                'type': 'warning',
                'message': 'Staff capacity is near maximum. Consider hiring additional staff.'
            })
        
        return recommendations
    
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get school profile statistics"""
        queryset = self.get_queryset()
        
        stats = {
            'total_schools': queryset.count(),
            'verified_schools': queryset.filter(is_verified=True).count(),
            'active_schools': queryset.filter(is_active=True).count(),
            'by_type': {},
            'by_country': {},
        }
        
        # Group by school type
        for school_type, _ in AdvancedSchoolProfile.SCHOOL_TYPE_CHOICES:
            count = queryset.filter(school_type=school_type).count()
            stats['by_type'][school_type] = count
        
        # Group by country
        countries = queryset.values_list('country', flat=True).distinct()
        for country in countries:
            if country:
                count = queryset.filter(country=country).count()
                stats['by_country'][country] = count
        
        return Response(stats)


class SystemSettingsViewSet(viewsets.ModelViewSet):
    """Enhanced viewset for system settings"""
    
    serializer_class = SystemSettingsSerializer
    permission_classes = [permissions.IsAuthenticated, IsSchoolAdminOrReadOnly]
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_fields = ['environment', 'debug_mode', 'maintenance_mode']
    search_fields = ['school_branch__name']
    
    def get_queryset(self):
        """Filter by user's school branch"""
        user = self.request.user
        if user.is_superuser:
            return SystemSettings.objects.all()
        return SystemSettings.objects.filter(school_branch=user.school_branch)
    
    def perform_update(self, serializer):
        """Update with audit logging and cache invalidation"""
        old_instance = self.get_object()
        instance = serializer.save()
        
        # Log changes
        changes = {}
        for field in serializer.validated_data:
            old_value = getattr(old_instance, field, None)
            new_value = getattr(instance, field, None)
            if old_value != new_value:
                changes[field] = {'old': old_value, 'new': new_value}
        
        SecurityAuditLogger.log_data_modification(
            self.request, self.request.user, 'SystemSettings',
            instance.id, changes
        )
        
        # Invalidate relevant caches
        cache_keys = [
            f'system_settings_{instance.school_branch.id}',
            f'maintenance_mode_{instance.school_branch.id}',
        ]
        cache.delete_many(cache_keys)
    
    @action(detail=True, methods=['post'])
    def enable_maintenance(self, request, pk=None):
        """Enable maintenance mode"""
        settings_obj = self.get_object()
        message = request.data.get('message', 'System is under maintenance')
        
        settings_obj.maintenance_mode = True
        settings_obj.maintenance_message = message
        settings_obj.save()
        
        # Set cache flag for quick access
        cache.set(f'maintenance_mode_{settings_obj.school_branch.id}', True, 3600)
        
        SecurityAuditLogger.log_data_modification(
            request, request.user, 'SystemSettings',
            settings_obj.id, {'action': 'maintenance_enabled'}
        )
        
        return Response({'message': 'Maintenance mode enabled'})
    
    @action(detail=True, methods=['post'])
    def disable_maintenance(self, request, pk=None):
        """Disable maintenance mode"""
        settings_obj = self.get_object()
        
        settings_obj.maintenance_mode = False
        settings_obj.save()
        
        # Remove cache flag
        cache.delete(f'maintenance_mode_{settings_obj.school_branch.id}')
        
        SecurityAuditLogger.log_data_modification(
            request, request.user, 'SystemSettings',
            settings_obj.id, {'action': 'maintenance_disabled'}
        )
        
        return Response({'message': 'Maintenance mode disabled'})
    
    @action(detail=True, methods=['post'])
    def test_email_settings(self, request, pk=None):
        """Test email configuration"""
        settings_obj = self.get_object()
        
        if not settings_obj.email_notifications_enabled:
            return Response(
                {'error': 'Email notifications are disabled'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Test email configuration
            from django.core.mail import send_mail
            from django.conf import settings as django_settings
            
            send_mail(
                'Test Email from ShuleXcel',
                'This is a test email to verify email configuration.',
                django_settings.DEFAULT_FROM_EMAIL,
                [request.user.email],
                fail_silently=False,
            )
            
            return Response({'message': 'Test email sent successfully'})
            
        except Exception as e:
            logger.error(f"Email test failed: {str(e)}")
            return Response(
                {'error': f'Email test failed: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=False, methods=['get'])
    def system_health(self, request):
        """Get system health status"""
        try:
            # Check database
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            db_status = 'healthy'
        except Exception:
            db_status = 'unhealthy'
        
        # Check cache
        try:
            cache.set('health_check', 'test', 30)
            cache_status = 'healthy' if cache.get('health_check') == 'test' else 'unhealthy'
        except Exception:
            cache_status = 'unhealthy'
        
        # Check disk space
        import shutil
        try:
            total, used, free = shutil.disk_usage('/')
            disk_usage = {
                'total': total,
                'used': used,
                'free': free,
                'percentage_used': (used / total) * 100
            }
            disk_status = 'healthy' if disk_usage['percentage_used'] < 90 else 'warning'
        except Exception:
            disk_usage = None
            disk_status = 'unknown'
        
        return Response({
            'status': 'healthy' if all(s == 'healthy' for s in [db_status, cache_status, disk_status]) else 'warning',
            'components': {
                'database': db_status,
                'cache': cache_status,
                'disk': disk_status
            },
            'disk_usage': disk_usage,
            'timestamp': timezone.now()
        })


class NotificationTemplateViewSet(viewsets.ModelViewSet):
    """Viewset for notification templates"""
    
    serializer_class = NotificationTemplateSerializer
    permission_classes = [permissions.IsAuthenticated, IsSchoolAdminOrReadOnly]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['template_type', 'trigger_event', 'is_active']
    search_fields = ['name', 'subject', 'content']
    ordering_fields = ['name', 'created_at', 'updated_at']
    ordering = ['name']
    
    def get_queryset(self):
        """Filter by user's school branch"""
        user = self.request.user
        if user.is_superuser:
            return NotificationTemplate.objects.all()
        return NotificationTemplate.objects.filter(school_branch=user.school_branch)
    
    @action(detail=True, methods=['post'])
    def test_template(self, request, pk=None):
        """Test notification template with sample data"""
        template = self.get_object()
        sample_context = request.data.get('context', {
            'name': 'John Doe',
            'school_name': template.school_branch.name,
            'date': timezone.now().strftime('%Y-%m-%d'),
            'amount': '1000.00'
        })
        
        try:
            rendered_content = template.render_content(sample_context)
            return Response({
                'rendered_content': rendered_content,
                'context_used': sample_context
            })
        except Exception as e:
            return Response(
                {'error': f'Template rendering failed: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=False, methods=['get'])
    def available_variables(self, request):
        """Get available template variables by trigger event"""
        trigger_event = request.query_params.get('trigger_event')
        
        variable_mapping = {
            'user_registration': ['name', 'email', 'school_name', 'username', 'password'],
            'password_reset': ['name', 'reset_link', 'school_name', 'expiry_time'],
            'fee_payment': ['name', 'amount', 'payment_date', 'receipt_number', 'balance'],
            'grade_published': ['student_name', 'subject', 'grade', 'term', 'academic_year'],
            'attendance_alert': ['student_name', 'date', 'status', 'parent_name'],
            'library_overdue': ['student_name', 'book_title', 'due_date', 'fine_amount'],
            'event_reminder': ['event_name', 'event_date', 'event_time', 'location'],
            'system_maintenance': ['start_time', 'end_time', 'reason', 'contact_info'],
        }
        
        if trigger_event:
            variables = variable_mapping.get(trigger_event, [])
        else:
            variables = variable_mapping
        
        return Response({'variables': variables})


# Backward compatibility views
class SchoolProfileViewSet(viewsets.ModelViewSet):
    """Backward compatible viewset for existing school profiles"""
    
    serializer_class = SchoolProfileSerializer
    permission_classes = [permissions.IsAuthenticated, IsSchoolAdminOrReadOnly]
    
    def get_queryset(self):
        user = self.request.user
        if user.is_superuser:
            return SchoolProfile.objects.all()
        return SchoolProfile.objects.filter(school_branch=user.school_branch)


class SystemConfigurationViewSet(viewsets.ModelViewSet):
    """Backward compatible viewset for existing system configuration"""

    serializer_class = SystemConfigurationSerializer
    permission_classes = [permissions.IsAuthenticated, IsSchoolAdminOrReadOnly]

    def get_queryset(self):
        user = self.request.user
        if user.is_superuser:
            return SystemConfiguration.objects.all()
        return SystemConfiguration.objects.filter(school_branch=user.school_branch)


class IntegrationSettingsViewSet(viewsets.ModelViewSet):
    """Viewset for integration settings"""

    serializer_class = IntegrationSettingsSerializer
    permission_classes = [permissions.IsAuthenticated, IsSchoolAdminOrReadOnly]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['integration_type', 'provider', 'status', 'is_sandbox']
    search_fields = ['name', 'provider']
    ordering_fields = ['name', 'created_at', 'last_sync']
    ordering = ['name']

    def get_queryset(self):
        """Filter by user's school branch"""
        user = self.request.user
        if user.is_superuser:
            return IntegrationSettings.objects.all()
        return IntegrationSettings.objects.filter(school_branch=user.school_branch)

    def perform_create(self, serializer):
        """Create with audit logging"""
        instance = serializer.save()
        SecurityAuditLogger.log_data_modification(
            self.request, self.request.user, 'IntegrationSettings',
            instance.id, {'action': 'created', 'integration_type': instance.integration_type}
        )

    @action(detail=True, methods=['post'])
    def test_connection(self, request, pk=None):
        """Test integration connection"""
        integration = self.get_object()

        try:
            # This would be implemented based on integration type
            # For now, return a mock response
            result = self._test_integration_connection(integration)

            if result['success']:
                integration.status = 'active'
                integration.last_sync = timezone.now()
                integration.last_error = ''
            else:
                integration.status = 'error'
                integration.last_error = result['error']

            integration.save()

            SecurityAuditLogger.log_data_modification(
                request, request.user, 'IntegrationSettings',
                integration.id, {'action': 'connection_test', 'result': result}
            )

            return Response(result)

        except Exception as e:
            logger.error(f"Integration test failed: {str(e)}")
            return Response(
                {'success': False, 'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    def _test_integration_connection(self, integration):
        """Test specific integration connection"""
        # Mock implementation - would be replaced with actual integration tests
        import random

        if integration.integration_type == 'payment_gateway':
            return self._test_payment_gateway(integration)
        elif integration.integration_type == 'sms_provider':
            return self._test_sms_provider(integration)
        elif integration.integration_type == 'email_service':
            return self._test_email_service(integration)
        else:
            return {'success': True, 'message': 'Connection test not implemented for this integration type'}

    def _test_payment_gateway(self, integration):
        """Test payment gateway connection"""
        # Mock implementation
        return {
            'success': True,
            'message': 'Payment gateway connection successful',
            'response_time': '150ms',
            'api_version': '2.0'
        }

    def _test_sms_provider(self, integration):
        """Test SMS provider connection"""
        # Mock implementation
        return {
            'success': True,
            'message': 'SMS provider connection successful',
            'balance': '1000 credits',
            'rate_limit': '100/hour'
        }

    def _test_email_service(self, integration):
        """Test email service connection"""
        # Mock implementation
        return {
            'success': True,
            'message': 'Email service connection successful',
            'quota_remaining': '9500/10000',
            'smtp_status': 'active'
        }

    @action(detail=True, methods=['post'])
    def sync_data(self, request, pk=None):
        """Sync data with integration"""
        integration = self.get_object()

        if integration.status != 'active':
            return Response(
                {'error': 'Integration is not active'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Mock sync implementation
            result = {
                'success': True,
                'records_synced': 150,
                'sync_time': timezone.now(),
                'next_sync': timezone.now() + timezone.timedelta(hours=1)
            }

            integration.last_sync = timezone.now()
            integration.save()

            SecurityAuditLogger.log_data_modification(
                request, request.user, 'IntegrationSettings',
                integration.id, {'action': 'data_sync', 'result': result}
            )

            return Response(result)

        except Exception as e:
            logger.error(f"Data sync failed: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class CustomFieldViewSet(viewsets.ModelViewSet):
    """Viewset for custom fields"""

    serializer_class = CustomFieldSerializer
    permission_classes = [permissions.IsAuthenticated, IsSchoolAdminOrReadOnly]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['field_type', 'target_model', 'is_active', 'is_required']
    search_fields = ['name', 'label', 'help_text']
    ordering_fields = ['target_model', 'order', 'name', 'created_at']
    ordering = ['target_model', 'order', 'name']

    def get_queryset(self):
        """Filter by user's school branch"""
        user = self.request.user
        if user.is_superuser:
            return CustomField.objects.all()
        return CustomField.objects.filter(school_branch=user.school_branch)

    @action(detail=False, methods=['get'])
    def by_model(self, request):
        """Get custom fields grouped by target model"""
        target_model = request.query_params.get('model')

        if target_model:
            fields = self.get_queryset().filter(
                target_model=target_model,
                is_active=True
            ).order_by('order', 'name')
        else:
            fields = self.get_queryset().filter(is_active=True)

        serializer = self.get_serializer(fields, many=True)

        if target_model:
            return Response(serializer.data)

        # Group by model
        grouped_fields = {}
        for field_data in serializer.data:
            model = field_data['target_model']
            if model not in grouped_fields:
                grouped_fields[model] = []
            grouped_fields[model].append(field_data)

        return Response(grouped_fields)

    @action(detail=True, methods=['post'])
    def reorder(self, request, pk=None):
        """Reorder custom fields"""
        field = self.get_object()
        new_order = request.data.get('order')

        if new_order is None:
            return Response(
                {'error': 'Order value is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            with transaction.atomic():
                # Update the field order
                old_order = field.order
                field.order = new_order
                field.save()

                # Adjust other fields in the same model
                if new_order < old_order:
                    # Moving up - increment orders of fields in between
                    CustomField.objects.filter(
                        school_branch=field.school_branch,
                        target_model=field.target_model,
                        order__gte=new_order,
                        order__lt=old_order
                    ).exclude(pk=field.pk).update(order=models.F('order') + 1)
                else:
                    # Moving down - decrement orders of fields in between
                    CustomField.objects.filter(
                        school_branch=field.school_branch,
                        target_model=field.target_model,
                        order__gt=old_order,
                        order__lte=new_order
                    ).exclude(pk=field.pk).update(order=models.F('order') - 1)

            return Response({'message': 'Field reordered successfully'})

        except Exception as e:
            logger.error(f"Field reordering failed: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class AuditLogViewSet(viewsets.ReadOnlyModelViewSet):
    """Read-only viewset for audit logs"""

    serializer_class = AuditLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['action', 'severity', 'success', 'user']
    search_fields = ['description', 'user__email', 'ip_address']
    ordering_fields = ['timestamp', 'action', 'severity']
    ordering = ['-timestamp']

    def get_queryset(self):
        """Filter by user's school branch and permissions"""
        user = self.request.user

        if user.is_superuser:
            return AuditLog.objects.all()

        # School admins can see all logs for their school
        if hasattr(user, 'school_branch') and user.has_perm('settings_app.view_auditlog'):
            return AuditLog.objects.filter(school_branch=user.school_branch)

        # Regular users can only see their own logs
        return AuditLog.objects.filter(user=user)

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get audit log statistics"""
        queryset = self.get_queryset()

        # Date range filter
        from_date = request.query_params.get('from_date')
        to_date = request.query_params.get('to_date')

        if from_date:
            queryset = queryset.filter(timestamp__gte=from_date)
        if to_date:
            queryset = queryset.filter(timestamp__lte=to_date)

        stats = {
            'total_logs': queryset.count(),
            'by_action': {},
            'by_severity': {},
            'by_user': {},
            'success_rate': 0,
            'recent_activity': []
        }

        # Group by action
        for action, _ in AuditLog.ACTION_TYPES:
            count = queryset.filter(action=action).count()
            stats['by_action'][action] = count

        # Group by severity
        for severity, _ in AuditLog.SEVERITY_LEVELS:
            count = queryset.filter(severity=severity).count()
            stats['by_severity'][severity] = count

        # Success rate
        total = queryset.count()
        if total > 0:
            successful = queryset.filter(success=True).count()
            stats['success_rate'] = (successful / total) * 100

        # Recent activity (last 10 logs)
        recent_logs = queryset[:10]
        stats['recent_activity'] = AuditLogSerializer(recent_logs, many=True).data

        return Response(stats)
