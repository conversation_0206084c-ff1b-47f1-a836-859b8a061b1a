# ShuleXcel API Documentation

## Authentication
All endpoints require JWT authentication:
```http
Authorization: Bearer <token>
```

## Base URL
```
/api/v1/
```

## Analytics Endpoints

### Student Performance Profile
GET `/analytics/student/{id}/profile/`

**Permissions:** Teacher, Admin, HOD

**Response:**
```json
{
    "learning_velocity": {
        "rate": 0.85,
        "trend": "increasing",
        "comparison": "above_average"
    },
    "subject_affinities": [
        {
            "subject": "Mathematics",
            "affinity_score": 0.92,
            "strength_areas": []
        }
    ]
}
```

### Class Analytics
GET `/analytics/class/{id}/performance/`

**Permissions:** Teacher, Admin, HOD

**Query Parameters:**
- `term_id` (optional): Specific term ID
- `subject_id` (optional): Filter by subject

**Response:**
```json
{
    "class_average": 65.4,
    "performance_distribution": {
        "excellent": 15,
        "good": 25,
        "average": 40,
        "below_average": 20
    }
}
```

## Performance Tracking Endpoints

### Student Progress Tracking
GET `/performance/student/{id}/track/`

**Query Parameters:**
- `start_date`: Start of tracking period
- `end_date`: End of tracking period
- `subject_id` (optional): Filter by subject

**Response:**
```json
{
    "progress_metrics": {
        "improvement_rate": 0.15,
        "mastery_levels": {},
        "weak_areas": []
    }
}
```
