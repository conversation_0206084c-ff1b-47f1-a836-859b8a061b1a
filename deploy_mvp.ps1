# ShuleXcel MVP Deployment Script for Windows
# This script deploys the ShuleXcel MVP using Docker

param(
    [switch]$SkipEnvCheck,
    [switch]$Help
)

if ($Help) {
    Write-Host "ShuleXcel MVP Deployment Script" -ForegroundColor Cyan
    Write-Host "Usage: .\deploy_mvp.ps1 [-SkipEnvCheck] [-Help]" -ForegroundColor White
    Write-Host ""
    Write-Host "Parameters:" -ForegroundColor Yellow
    Write-Host "  -SkipEnvCheck    Skip environment file check" -ForegroundColor White
    Write-Host "  -Help           Show this help message" -ForegroundColor White
    exit 0
}

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if Docker is installed
function Test-Docker {
    Write-Status "Checking Docker installation..."
    
    try {
        $dockerVersion = docker --version
        $composeVersion = docker-compose --version
        Write-Success "Docker and Docker Compose are installed"
        Write-Host "Docker: $dockerVersion" -ForegroundColor Gray
        Write-Host "Compose: $composeVersion" -ForegroundColor Gray
        return $true
    }
    catch {
        Write-Error "Docker or Docker Compose is not installed or not in PATH"
        Write-Host "Please install Docker Desktop from: https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
        return $false
    }
}

# Setup environment file
function Set-Environment {
    Write-Status "Setting up environment configuration..."
    
    if (-not (Test-Path "Backend\.env") -and -not $SkipEnvCheck) {
        if (Test-Path "Backend\.env.example") {
            Write-Status "Creating .env file from template..."
            Copy-Item "Backend\.env.example" "Backend\.env"
            Write-Warning "Please edit Backend\.env file with your production settings"
            Write-Warning "Press Enter when you've updated the .env file..."
            Read-Host
        } else {
            Write-Error "Backend\.env.example file not found"
            return $false
        }
    } else {
        Write-Success "Environment file exists or check skipped"
    }
    return $true
}

# Create Docker Compose configuration
function New-DockerCompose {
    Write-Status "Creating Docker Compose configuration..."
    
    $dockerComposeContent = @"
version: '3.8'

services:
  backend:
    build: ./Backend
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - DJANGO_ENVIRONMENT=production
    volumes:
      - ./Backend:/app
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - logs_volume:/app/logs
    depends_on:
      - db
    restart: unless-stopped

  frontend:
    build: ./Frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: shulexcel_db
      POSTGRES_USER: shulexcel_user
      POSTGRES_PASSWORD: changeme
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    restart: unless-stopped

volumes:
  postgres_data:
  static_volume:
  media_volume:
  logs_volume:
"@

    $dockerComposeContent | Out-File -FilePath "docker-compose.yml" -Encoding UTF8
    Write-Success "Docker Compose configuration created"
    return $true
}

# Deploy the application
function Start-Deployment {
    Write-Status "Building and deploying ShuleXcel MVP..."
    
    try {
        # Build images
        Write-Status "Building Docker images..."
        docker-compose build
        if ($LASTEXITCODE -ne 0) { throw "Docker build failed" }
        
        # Start services
        Write-Status "Starting services..."
        docker-compose up -d
        if ($LASTEXITCODE -ne 0) { throw "Failed to start services" }
        
        # Wait for services
        Write-Status "Waiting for services to be ready..."
        Start-Sleep -Seconds 15
        
        # Run migrations
        Write-Status "Running database migrations..."
        docker-compose exec backend python manage.py migrate
        
        # Setup MVP data
        Write-Status "Setting up MVP data..."
        docker-compose exec backend python setup_mvp.py
        
        Write-Success "Deployment completed!"
        return $true
    }
    catch {
        Write-Error "Deployment failed: $_"
        return $false
    }
}

# Show deployment status
function Show-Status {
    Write-Status "Checking deployment status..."
    docker-compose ps
    
    Write-Host ""
    Write-Success "ShuleXcel MVP is now running! 🎉"
    Write-Host ""
    Write-Host "Access URLs:" -ForegroundColor Cyan
    Write-Host "  Frontend:    http://localhost" -ForegroundColor White
    Write-Host "  Backend API: http://localhost:8000" -ForegroundColor White
    Write-Host "  Admin Panel: http://localhost:8000/admin" -ForegroundColor White
    
    Write-Host ""
    Write-Host "Default Admin Credentials:" -ForegroundColor Cyan
    Write-Host "  Email:    <EMAIL>" -ForegroundColor White
    Write-Host "  Password: admin123" -ForegroundColor White
    
    Write-Host ""
    Write-Warning "Important Next Steps:"
    Write-Host "  1. Change default admin password" -ForegroundColor Yellow
    Write-Host "  2. Configure SSL/HTTPS for production" -ForegroundColor Yellow
    Write-Host "  3. Set up proper backup procedures" -ForegroundColor Yellow
    Write-Host "  4. Configure monitoring and logging" -ForegroundColor Yellow
}

# Main deployment process
function Start-MVPDeployment {
    Write-Host "🚀 ShuleXcel MVP Deployment Script" -ForegroundColor Cyan
    Write-Host "==================================" -ForegroundColor Cyan
    Write-Host ""
    
    if (-not (Test-Docker)) {
        exit 1
    }
    
    if (-not (Set-Environment)) {
        exit 1
    }
    
    if (-not (New-DockerCompose)) {
        exit 1
    }
    
    if (-not (Start-Deployment)) {
        Write-Error "Deployment failed. Check the logs above for details."
        exit 1
    }
    
    Show-Status
    Write-Success "MVP deployment completed successfully! 🎉"
}

# Run the deployment
Start-MVPDeployment
