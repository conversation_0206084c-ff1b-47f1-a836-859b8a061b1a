from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from django.contrib.auth import get_user_model
from django.conf import settings
from datetime import datetime, timedelta
import time
import hmac
import hashlib
import logging

User = get_user_model()
logger = logging.getLogger(__name__)

class ValidateTokenView(APIView):
    """
    API view to validate a password reset token
    """
    permission_classes = [permissions.AllowAny]

    def post(self, request, **_):
        """
        Validate a password reset token

        Expects:
        {
            "token": "user_id.token.timestamp.signature"
        }

        Returns:
        {
            "valid": true/false,
            "user_id": "user_id" (if valid),
            "message": "Token is valid" or error message
        }
        """
        token = request.data.get('token', '')

        if not token:
            return Response({
                'valid': False,
                'message': 'Token is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if it's a secure token (contains 4 parts separated by dots)
        if token.count('.') == 3:
            return self._validate_secure_token(token)
        else:
            # Try to validate as a django_rest_passwordreset token
            try:
                from django_rest_passwordreset.models import ResetPasswordToken
                reset_token = ResetPasswordToken.objects.get(key=token)

                # Check if the token has expired
                from django.utils import timezone
                from django.conf import settings

                # Get the expiry time from settings or use default (24 hours)
                expiry_time = getattr(settings, 'DJANGO_REST_PASSWORDRESET_TOKEN_EXPIRY_TIME', 24)

                # Calculate the expiry date
                expiry_date = reset_token.created_at + timezone.timedelta(hours=expiry_time)

                # Check if the token has expired
                if timezone.now() > expiry_date:
                    return Response({
                        'valid': False,
                        'message': 'Token has expired'
                    }, status=status.HTTP_400_BAD_REQUEST)

                # Token is valid
                return Response({
                    'valid': True,
                    'message': 'Token is valid'
                }, status=status.HTTP_200_OK)
            except Exception as e:
                logger.error(f"Error validating django_rest_passwordreset token: {e}")
                return Response({
                    'valid': False,
                    'message': 'Invalid token'
                }, status=status.HTTP_400_BAD_REQUEST)

    def _validate_secure_token(self, token):
        """
        Validate a secure token in the format: user_id.token.timestamp.signature
        """
        try:
            # Split the token into its components
            parts = token.split('.')
            if len(parts) != 4:
                return Response({
                    'valid': False,
                    'message': 'Invalid token format'
                }, status=status.HTTP_400_BAD_REQUEST)

            user_id, token_value, timestamp_str, signature = parts

            # Convert timestamp to int
            try:
                timestamp = int(timestamp_str)
            except ValueError:
                return Response({
                    'valid': False,
                    'message': 'Invalid timestamp in token'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Check if the token has expired (24 hours)
            current_time = int(time.time())
            if current_time - timestamp > 24 * 60 * 60:
                return Response({
                    'valid': False,
                    'message': 'Token has expired'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Verify the signature
            payload = f"{user_id}:{token_value}:{timestamp}"
            expected_signature = hmac.new(
                key=settings.SECRET_KEY.encode(),
                msg=payload.encode(),
                digestmod=hashlib.sha256
            ).hexdigest()

            if signature != expected_signature:
                return Response({
                    'valid': False,
                    'message': 'Invalid token signature'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Check if the user exists
            try:
                user = User.objects.get(id=user_id)
                # We don't need to use the user object here, just check if it exists
            except (User.DoesNotExist, ValueError):
                return Response({
                    'valid': False,
                    'message': 'User not found'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Token is valid
            return Response({
                'valid': True,
                'user_id': user_id,
                'message': 'Token is valid'
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error validating token: {e}")
            return Response({
                'valid': False,
                'message': 'Error validating token'
            }, status=status.HTTP_400_BAD_REQUEST)


class ResetPasswordWithTokenView(APIView):
    """
    API view to reset a password using a secure token
    """
    permission_classes = [permissions.AllowAny]

    def post(self, request, **_):
        """
        Reset a password using a secure token

        Expects:
        {
            "token": "user_id.token.timestamp.signature",
            "password": "new_password",
            "password_confirm": "new_password"
        }

        Returns:
        {
            "status": "OK",
            "message": "Password has been reset successfully"
        }
        """
        token = request.data.get('token', '')
        password = request.data.get('password', '')
        password_confirm = request.data.get('password_confirm', '')

        if not token:
            return Response({
                'status': 'error',
                'message': 'Token is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not password:
            return Response({
                'status': 'error',
                'message': 'Password is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if password != password_confirm:
            return Response({
                'status': 'error',
                'message': 'Passwords do not match'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate password strength
        if len(password) < 8:
            return Response({
                'status': 'error',
                'message': 'Password must be at least 8 characters long'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if it's a secure token (contains 4 parts separated by dots)
        if token.count('.') == 3:
            return self._reset_with_secure_token(token, password)
        else:
            # Try to reset with a django_rest_passwordreset token
            try:
                from django_rest_passwordreset.models import ResetPasswordToken
                reset_token = ResetPasswordToken.objects.get(key=token)

                # Check if the token has expired
                from django.utils import timezone
                from django.conf import settings

                # Get the expiry time from settings or use default (24 hours)
                expiry_time = getattr(settings, 'DJANGO_REST_PASSWORDRESET_TOKEN_EXPIRY_TIME', 24)

                # Calculate the expiry date
                expiry_date = reset_token.created_at + timezone.timedelta(hours=expiry_time)

                # Check if the token has expired
                if timezone.now() > expiry_date:
                    return Response({
                        'status': 'error',
                        'message': 'Token has expired'
                    }, status=status.HTTP_400_BAD_REQUEST)

                # Get the user
                user = reset_token.user

                # Set the new password
                user.set_password(password)
                user.save()

                # Delete the token
                reset_token.delete()

                logger.info(f"Password reset successfully for user: {user.email} using django_rest_passwordreset token")

                # Return success
                return Response({
                    'status': 'OK',
                    'message': 'Password has been reset successfully'
                }, status=status.HTTP_200_OK)
            except Exception as e:
                logger.error(f"Error resetting password with django_rest_passwordreset token: {e}")
                return Response({
                    'status': 'error',
                    'message': 'Invalid token'
                }, status=status.HTTP_400_BAD_REQUEST)

    def _reset_with_secure_token(self, token, password):
        """
        Reset a password using a secure token in the format: user_id.token.timestamp.signature
        """
        try:
            # Split the token into its components
            parts = token.split('.')
            if len(parts) != 4:
                logger.error(f"Invalid token format: {token}")
                return Response({
                    'status': 'error',
                    'message': 'Invalid token format'
                }, status=status.HTTP_400_BAD_REQUEST)

            user_id, token_value, timestamp_str, signature = parts

            # Convert timestamp to int
            try:
                timestamp = int(timestamp_str)
            except ValueError:
                logger.error(f"Invalid timestamp in token: {timestamp_str}")
                return Response({
                    'status': 'error',
                    'message': 'Invalid timestamp in token'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Check if the token has expired (24 hours)
            current_time = int(time.time())
            if current_time - timestamp > 24 * 60 * 60:
                logger.error(f"Token has expired. Current time: {current_time}, Token time: {timestamp}")
                return Response({
                    'status': 'error',
                    'message': 'Token has expired'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Verify the signature
            payload = f"{user_id}:{token_value}:{timestamp}"
            expected_signature = hmac.new(
                key=settings.SECRET_KEY.encode(),
                msg=payload.encode(),
                digestmod=hashlib.sha256
            ).hexdigest()

            if signature != expected_signature:
                logger.error(f"Invalid token signature. Expected: {expected_signature}, Got: {signature}")
                return Response({
                    'status': 'error',
                    'message': 'Invalid token signature'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Check if the user exists
            try:
                user = User.objects.get(id=user_id)
            except (User.DoesNotExist, ValueError):
                logger.error(f"User not found with ID: {user_id}")
                return Response({
                    'status': 'error',
                    'message': 'User not found'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Set the new password
            user.set_password(password)
            user.save()

            logger.info(f"Password reset successfully for user: {user.email}")

            # Return success
            return Response({
                'status': 'OK',
                'message': 'Password has been reset successfully'
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error resetting password: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return Response({
                'status': 'error',
                'message': f'Error resetting password: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
