import React, { useState } from 'react';
import { studentService } from '../services/studentService';
import { academicsApi } from '../api/academicsApi';
import { curriculumApi } from '../api/curriculumApi';
import { useAuth } from '../context/AuthContext';

const DebugApiTest: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [results, setResults] = useState<any>({});
  const [loading, setLoading] = useState<string>('');
  const [error, setError] = useState<string>('');

  const testApi = async (apiName: string, apiCall: () => Promise<any>) => {
    setLoading(apiName);
    setError('');
    try {
      const result = await apiCall();
      setResults(prev => ({ ...prev, [apiName]: result }));
      console.log(`${apiName} success:`, result);
    } catch (err: any) {
      const errorMsg = err.response?.data?.detail || err.message || 'Unknown error';
      setError(`${apiName}: ${errorMsg}`);
      console.error(`${apiName} error:`, err);
    } finally {
      setLoading('');
    }
  };

  const testStudents = () => testApi('students', () => 
    studentService.getAllStudents({ page: 1, page_size: 5 })
  );

  const testCurriculumSystems = () => testApi('curriculumSystems', () => 
    curriculumApi.getCurriculumSystems()
  );

  const testEducationLevels = () => testApi('educationLevels', () => 
    curriculumApi.getEducationLevels()
  );

  const testClasses = () => testApi('classes', () => 
    academicsApi.getClasses()
  );

  const testStreams = () => testApi('streams', () => 
    academicsApi.getStreams()
  );

  const testSubjects = () => testApi('subjects', () => 
    academicsApi.getSubjects()
  );

  const testDepartments = () => testApi('departments', () => 
    academicsApi.getDepartments()
  );

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">API Debug Test Page</h1>
      
      {/* Authentication Status */}
      <div className="bg-blue-50 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-2">Authentication Status</h2>
        <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
        <p><strong>User:</strong> {user ? `${user.first_name} ${user.last_name} (${user.user_type})` : 'None'}</p>
        <p><strong>User ID:</strong> {user?.id || 'None'}</p>
      </div>

      {/* Test Buttons */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <button
          onClick={testStudents}
          disabled={loading === 'students'}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading === 'students' ? 'Loading...' : 'Test Students'}
        </button>

        <button
          onClick={testCurriculumSystems}
          disabled={loading === 'curriculumSystems'}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
        >
          {loading === 'curriculumSystems' ? 'Loading...' : 'Test Curriculum'}
        </button>

        <button
          onClick={testEducationLevels}
          disabled={loading === 'educationLevels'}
          className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:opacity-50"
        >
          {loading === 'educationLevels' ? 'Loading...' : 'Test Ed Levels'}
        </button>

        <button
          onClick={testClasses}
          disabled={loading === 'classes'}
          className="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 disabled:opacity-50"
        >
          {loading === 'classes' ? 'Loading...' : 'Test Classes'}
        </button>

        <button
          onClick={testStreams}
          disabled={loading === 'streams'}
          className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 disabled:opacity-50"
        >
          {loading === 'streams' ? 'Loading...' : 'Test Streams'}
        </button>

        <button
          onClick={testSubjects}
          disabled={loading === 'subjects'}
          className="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600 disabled:opacity-50"
        >
          {loading === 'subjects' ? 'Loading...' : 'Test Subjects'}
        </button>

        <button
          onClick={testDepartments}
          disabled={loading === 'departments'}
          className="bg-pink-500 text-white px-4 py-2 rounded hover:bg-pink-600 disabled:opacity-50"
        >
          {loading === 'departments' ? 'Loading...' : 'Test Departments'}
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 p-4 rounded-lg mb-6">
          <h3 className="text-red-800 font-semibold">Error:</h3>
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {/* Results Display */}
      <div className="space-y-6">
        {Object.entries(results).map(([apiName, result]) => (
          <div key={apiName} className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-2 capitalize">{apiName} Results:</h3>
            <div className="bg-white p-3 rounded border overflow-auto max-h-96">
              <pre className="text-sm">{JSON.stringify(result, null, 2)}</pre>
            </div>
          </div>
        ))}
      </div>

      {/* Backend Status */}
      <div className="mt-8 bg-yellow-50 p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Backend Status</h3>
        <p>Backend should be running at: <code>http://localhost:8000</code></p>
        <p>Frontend is running at: <code>http://localhost:5173</code></p>
        <p>Check browser console for detailed API logs.</p>
      </div>

      {/* Instructions */}
      <div className="mt-6 bg-gray-50 p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Instructions</h3>
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Make sure you're logged in (authentication status should show "Yes")</li>
          <li>Click each test button to verify API endpoints</li>
          <li>Check the browser console for detailed request/response logs</li>
          <li>If you see authentication errors, try logging out and back in</li>
          <li>If you see 404 errors, check that the backend is running</li>
        </ol>
      </div>
    </div>
  );
};

export default DebugApiTest;
