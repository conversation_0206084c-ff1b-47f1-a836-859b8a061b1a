#!/usr/bin/env python
"""
Profile and Module Content Verification Script

This script tests if user profiles and modules contain actual content
rather than just empty placeholders or mock data.
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
    django.setup()

def test_user_profiles_content():
    """Test if user profiles contain actual data"""
    print("🔍 Testing User Profile Content...")
    
    try:
        from core.models import CustomUser
        from users.models import Teacher, Student, AdminProfile, Parent, Staff
        from schools.models import School, SchoolBranch
        
        # Check if we have any users with profiles
        users_with_profiles = 0
        empty_profiles = 0
        
        # Test Admin Profiles
        admin_profiles = AdminProfile.objects.all()
        print(f"  📊 Admin Profiles: {admin_profiles.count()}")
        
        for profile in admin_profiles[:3]:  # Check first 3
            if profile.user:
                users_with_profiles += 1
                fields_filled = 0
                total_fields = 0
                
                # Check key fields
                if profile.admin_number: fields_filled += 1
                total_fields += 1
                if profile.position: fields_filled += 1
                total_fields += 1
                if profile.date_hired: fields_filled += 1
                total_fields += 1
                if profile.phone_number: fields_filled += 1
                total_fields += 1
                
                completion = (fields_filled / total_fields) * 100 if total_fields > 0 else 0
                print(f"    - {profile.user.get_full_name()}: {completion:.0f}% complete")
                
                if completion < 25:
                    empty_profiles += 1
        
        # Test Teacher Profiles
        teacher_profiles = Teacher.objects.all()
        print(f"  📊 Teacher Profiles: {teacher_profiles.count()}")
        
        for profile in teacher_profiles[:3]:  # Check first 3
            if profile.user:
                users_with_profiles += 1
                fields_filled = 0
                total_fields = 0
                
                # Check key fields
                if profile.teacher_number: fields_filled += 1
                total_fields += 1
                if profile.email: fields_filled += 1
                total_fields += 1
                if profile.phone_number: fields_filled += 1
                total_fields += 1
                if profile.date_of_birth: fields_filled += 1
                total_fields += 1
                if profile.subjects_taught: fields_filled += 1
                total_fields += 1
                
                completion = (fields_filled / total_fields) * 100 if total_fields > 0 else 0
                print(f"    - {profile.user.get_full_name()}: {completion:.0f}% complete")
                
                if completion < 25:
                    empty_profiles += 1
        
        # Test Student Profiles
        student_profiles = Student.objects.all()
        print(f"  📊 Student Profiles: {student_profiles.count()}")
        
        for profile in student_profiles[:3]:  # Check first 3
            if profile.user:
                users_with_profiles += 1
                fields_filled = 0
                total_fields = 0
                
                # Check key fields
                if profile.admission_number: fields_filled += 1
                total_fields += 1
                if profile.current_class: fields_filled += 1
                total_fields += 1
                if profile.date_of_birth: fields_filled += 1
                total_fields += 1
                if profile.phone_number: fields_filled += 1
                total_fields += 1
                
                completion = (fields_filled / total_fields) * 100 if total_fields > 0 else 0
                print(f"    - {profile.user.get_full_name()}: {completion:.0f}% complete")
                
                if completion < 25:
                    empty_profiles += 1
        
        # Summary
        if users_with_profiles > 0:
            empty_percentage = (empty_profiles / users_with_profiles) * 100
            print(f"  📈 Profile Completion Summary:")
            print(f"    - Total profiles checked: {users_with_profiles}")
            print(f"    - Empty/incomplete profiles: {empty_profiles} ({empty_percentage:.0f}%)")
            
            if empty_percentage > 70:
                print("  ⚠️  Most profiles are empty - users will see blank profile pages")
                return False
            elif empty_percentage > 30:
                print("  ⚠️  Many profiles are incomplete - some users may see limited content")
                return True
            else:
                print("  ✅ Most profiles have content - users will see meaningful data")
                return True
        else:
            print("  ⚠️  No user profiles found - users will see empty profile pages")
            return False
            
    except Exception as e:
        print(f"  ❌ Profile Content Error: {e}")
        return False

def test_dashboard_content():
    """Test if dashboard APIs return actual data"""
    print("🔍 Testing Dashboard Content...")
    
    try:
        from django.test import Client
        from django.contrib.auth import get_user_model
        from core.models import CustomUser
        from schools.models import School, SchoolBranch
        from academics.models import AcademicYear, ClassRoom, Subject
        
        # Get a test user
        User = get_user_model()
        admin_user = User.objects.filter(is_superuser=True).first()
        
        if not admin_user:
            print("  ⚠️  No admin user found for testing")
            return False
        
        client = Client()
        client.force_login(admin_user)
        
        # Test dashboard metrics endpoint
        try:
            response = client.get('/api/core/dashboard/metrics/')
            if response.status_code == 200:
                data = response.json()
                
                # Check if data contains actual numbers
                users_total = data.get('users', {}).get('total', 0)
                print(f"    - Total users in system: {users_total}")
                
                if users_total > 0:
                    print("  ✅ Dashboard shows real user data")
                else:
                    print("  ⚠️  Dashboard shows no users - may appear empty")
                
                return users_total > 0
            else:
                print(f"  ⚠️  Dashboard API returned status {response.status_code}")
                return False
                
        except Exception as api_error:
            print(f"  ⚠️  Dashboard API error: {api_error}")
            return False
            
    except Exception as e:
        print(f"  ❌ Dashboard Content Error: {e}")
        return False

def test_academic_content():
    """Test if academic modules have actual content"""
    print("🔍 Testing Academic Module Content...")
    
    try:
        from academics.models import AcademicYear, ClassRoom, Subject, Department, Stream
        from schools.models import School
        
        # Check academic years
        academic_years = AcademicYear.objects.all()
        print(f"  📊 Academic Years: {academic_years.count()}")
        
        # Check classes
        classes = ClassRoom.objects.all()
        print(f"  📊 Classes: {classes.count()}")
        
        # Check subjects
        subjects = Subject.objects.all()
        print(f"  📊 Subjects: {subjects.count()}")
        
        # Check departments
        departments = Department.objects.all()
        print(f"  📊 Departments: {departments.count()}")
        
        # Check streams
        streams = Stream.objects.all()
        print(f"  📊 Streams: {streams.count()}")
        
        # Calculate content score
        content_items = [academic_years.count(), classes.count(), subjects.count(), 
                        departments.count(), streams.count()]
        total_content = sum(content_items)
        
        if total_content > 10:
            print("  ✅ Academic modules have substantial content")
            return True
        elif total_content > 5:
            print("  ⚠️  Academic modules have some content but may seem sparse")
            return True
        else:
            print("  ⚠️  Academic modules are mostly empty - users will see blank pages")
            return False
            
    except Exception as e:
        print(f"  ❌ Academic Content Error: {e}")
        return False

def test_module_navigation():
    """Test if users can navigate between modules"""
    print("🔍 Testing Module Navigation...")
    
    try:
        from django.test import Client
        from django.contrib.auth import get_user_model
        from django.conf import settings
        
        User = get_user_model()
        admin_user = User.objects.filter(is_superuser=True).first()
        
        if not admin_user:
            print("  ⚠️  No admin user found for testing")
            return False
        
        # Temporarily add testserver to ALLOWED_HOSTS
        original_allowed_hosts = settings.ALLOWED_HOSTS
        if 'testserver' not in settings.ALLOWED_HOSTS:
            settings.ALLOWED_HOSTS = list(settings.ALLOWED_HOSTS) + ['testserver']
        
        client = Client()
        client.force_login(admin_user)
        
        # Test key module endpoints
        endpoints_to_test = [
            ('/api/users/profile/', 'User Profile'),
            ('/api/academics/academic-years/', 'Academic Years'),
            ('/api/schools/schools/', 'Schools'),
        ]
        
        accessible_endpoints = 0
        
        for endpoint, name in endpoints_to_test:
            try:
                response = client.get(endpoint)
                if response.status_code in [200, 201]:
                    print(f"    ✅ {name}: Accessible")
                    accessible_endpoints += 1
                else:
                    print(f"    ⚠️  {name}: Status {response.status_code}")
            except Exception as e:
                print(f"    ❌ {name}: Error - {e}")
        
        # Restore original ALLOWED_HOSTS
        settings.ALLOWED_HOSTS = original_allowed_hosts
        
        success_rate = (accessible_endpoints / len(endpoints_to_test)) * 100
        print(f"  📈 Module accessibility: {success_rate:.0f}%")
        
        if success_rate >= 80:
            print("  ✅ Most modules are accessible")
            return True
        elif success_rate >= 50:
            print("  ⚠️  Some modules may not be accessible")
            return True
        else:
            print("  ❌ Many modules are not accessible")
            return False
            
    except Exception as e:
        print(f"  ❌ Module Navigation Error: {e}")
        return False

def main():
    """Run all profile and module content tests"""
    print("🚀 ShuleXcel Profile & Module Content Verification")
    print("=" * 60)
    
    setup_django()
    
    tests = [
        test_user_profiles_content,
        test_dashboard_content,
        test_academic_content,
        test_module_navigation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"  ❌ Test failed with exception: {e}")
        print()
    
    print("=" * 60)
    print(f"📊 Content Verification Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All content verification tests passed!")
        print("\n✅ Users will see meaningful content in:")
        print("   - User Profiles ✅")
        print("   - Dashboard Data ✅") 
        print("   - Academic Modules ✅")
        print("   - Module Navigation ✅")
        print("\n🚀 MVP is ready with proper content!")
    elif passed >= total * 0.75:
        print("⚠️  Most content tests passed - MVP is mostly ready")
        print("Some areas may need attention for better user experience")
    else:
        print(f"❌ Content verification failed - {total - passed} critical issues found")
        print("Users may see empty or incomplete content in profiles and modules")
    
    return passed >= total * 0.75

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
