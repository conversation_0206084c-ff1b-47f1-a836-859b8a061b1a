from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import MpesaTransaction
from fees.models import FeePayment, FeeBalance

@receiver(post_save, sender=MpesaTransaction)
def handle_mpesa_transaction_update(sender, instance, created, **kwargs):
    """
    Handle updates to MPESA transactions
    """
    # Only process completed transactions that have a fee payment linked
    if instance.status == 'COMPLETED' and instance.fee_payment:
        fee_payment = instance.fee_payment
        
        # Update fee payment if not already verified
        if not fee_payment.verified:
            fee_payment.verified = True
            fee_payment.reference_number = instance.mpesa_receipt_number or instance.transaction_id
            fee_payment.save()
            
            # Update fee balance
            try:
                balance = FeeBalance.objects.get(
                    student=fee_payment.student,
                    term=fee_payment.term
                )
                balance.amount_paid += fee_payment.amount_paid
                balance.balance = balance.total_amount - balance.amount_paid
                balance.save()
            except FeeBalance.DoesNotExist:
                pass
