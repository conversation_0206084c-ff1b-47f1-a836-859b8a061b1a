<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Confirmation - {{ company_name }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #28a745;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
        }
        .success-icon {
            background-color: #28a745;
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 24px;
        }
        .title {
            color: #28a745;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #6c757d;
            font-size: 16px;
        }
        .payment-details {
            background-color: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }
        .detail-label {
            font-weight: 600;
            color: #495057;
        }
        .detail-value {
            color: #212529;
            font-weight: 500;
        }
        .amount {
            font-size: 24px;
            font-weight: bold;
            color: #28a745;
        }
        .license-status {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        .license-status .icon {
            font-size: 20px;
            margin-right: 10px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 14px;
        }
        .button {
            display: inline-block;
            background-color: #2563eb;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 600;
            margin: 10px 0;
        }
        .button:hover {
            background-color: #1d4ed8;
        }
        @media (max-width: 600px) {
            .detail-row {
                flex-direction: column;
            }
            .detail-label {
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">{{ company_name }}</div>
            <div class="success-icon">✓</div>
            <h1 class="title">Payment Confirmed!</h1>
            <p class="subtitle">Your payment has been successfully processed</p>
        </div>

        <div class="content">
            <p>Dear {{ school_name }},</p>
            
            <p>We're pleased to confirm that your payment has been successfully processed and your license has been activated immediately.</p>

            <div class="payment-details">
                <h3 style="margin-top: 0; color: #495057;">Payment Details</h3>
                
                <div class="detail-row">
                    <span class="detail-label">Payment Reference:</span>
                    <span class="detail-value">{{ payment_reference }}</span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Amount Paid:</span>
                    <span class="detail-value amount">KES {{ amount|floatformat:2 }}</span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Payment Method:</span>
                    <span class="detail-value">{{ payment_method }}</span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Payment Date:</span>
                    <span class="detail-value">{{ payment_date|date:"F d, Y" }}</span>
                </div>
                
                {% if receipt_number %}
                <div class="detail-row">
                    <span class="detail-label">Receipt Number:</span>
                    <span class="detail-value">{{ receipt_number }}</span>
                </div>
                {% endif %}
            </div>

            {% if license_activated %}
            <div class="license-status">
                <span class="icon">🎉</span>
                <strong>License Activated!</strong><br>
                Your ShuleXcel license has been automatically activated and is now ready to use.
            </div>
            {% endif %}

            <div style="text-align: center; margin: 30px 0;">
                <a href="#" class="button">Access Your Dashboard</a>
            </div>

            <h3>What's Next?</h3>
            <ul>
                <li>Your license is now active and all features are available</li>
                <li>You can access your dashboard immediately</li>
                <li>A detailed receipt has been sent to your email</li>
                <li>Your next billing cycle will begin automatically</li>
            </ul>

            <p>If you have any questions about your payment or need assistance, please don't hesitate to contact our support team.</p>

            <p>Thank you for choosing {{ company_name }}!</p>

            <p>Best regards,<br>
            <strong>The {{ company_name }} Team</strong></p>
        </div>

        <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
            <p>© {{ current_year }} {{ company_name }}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
