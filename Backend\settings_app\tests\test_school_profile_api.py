from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from settings_app.models import SchoolProfile
from schools.models import School, SchoolBranch
from core.models import CustomUser

class SchoolProfileAPITest(TestCase):
    def setUp(self):
        # Create a test school
        self.school = School.objects.create(
            name="Test School",
            address="123 Test Street",
            phone="1234567890",
            email="<EMAIL>",
            registration_number="TEST-REG-001",
            established_date="2000-01-01"
        )

        # Create a test school branch
        self.school_branch = SchoolBranch.objects.create(
            school=self.school,
            name="Test School Branch",
            address="123 Test Street",
            phone="1234567890",
            email="<EMAIL>",
            registration_number="TEST-BR-REG-001",
            established_date="2000-01-01"
        )

        # Create a test admin user
        self.admin_user = CustomUser.objects.create_user(
            username="testadmin",
            email="<EMAIL>",
            password="testpassword",
            is_staff=True,
            is_superuser=True,
            school_branch=self.school_branch
        )

        # Create a test non-admin user
        self.regular_user = CustomUser.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpassword",
            school_branch=self.school_branch
        )

        # Create a test school profile
        self.school_profile = SchoolProfile.objects.create(
            school_branch=self.school_branch,
            logo="https://example.com/logo.png",
            mission="Test Mission",
            vision="Test Vision",
            core_values="Test Core Values",
            contact_email="<EMAIL>",
            contact_phone="9876543210",
            updated_by=self.admin_user
        )

        # Initialize the API client
        self.client = APIClient()

    def test_get_all_profiles(self):
        """Test retrieving all school profiles"""
        # Authenticate as admin
        self.client.force_authenticate(user=self.admin_user)

        # Make the request
        url = reverse('school-profile-list')
        response = self.client.get(url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Handle both paginated and non-paginated responses
        if isinstance(response.data, dict) and 'results' in response.data:
            # Paginated response
            self.assertEqual(len(response.data['results']), 1)
            self.assertEqual(response.data['results'][0]['mission'], 'Test Mission')
        else:
            # Non-paginated response
            self.assertEqual(len(response.data), 1)
            self.assertEqual(response.data[0]['mission'], 'Test Mission')

    def test_get_profile_detail(self):
        """Test retrieving a specific school profile"""
        # Authenticate as admin
        self.client.force_authenticate(user=self.admin_user)

        # Make the request
        url = reverse('school-profile-detail', args=[self.school_profile.id])
        response = self.client.get(url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['mission'], 'Test Mission')
        self.assertEqual(response.data['vision'], 'Test Vision')

    def test_update_profile(self):
        """Test updating a school profile"""
        # Authenticate as admin
        self.client.force_authenticate(user=self.admin_user)

        # Prepare the data
        data = {
            'mission': 'Updated Mission',
            'about': 'New About Section'
        }

        # Make the request
        url = reverse('school-profile-detail', args=[self.school_profile.id])
        response = self.client.patch(url, data, format='json')

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['mission'], 'Updated Mission')
        self.assertEqual(response.data['about'], 'New About Section')

        # Refresh the profile from the database
        self.school_profile.refresh_from_db()
        self.assertEqual(self.school_profile.mission, 'Updated Mission')

    def test_current_profile(self):
        """Test retrieving the current school profile"""
        # Authenticate as admin user (since regular users might not have permission)
        self.client.force_authenticate(user=self.admin_user)

        # Make the request
        url = reverse('school-profile-current')
        response = self.client.get(url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['mission'], 'Test Mission')

    def test_unauthorized_access(self):
        """Test that unauthenticated users cannot access the API"""
        # Make the request without authentication
        url = reverse('school-profile-list')
        response = self.client.get(url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_non_admin_cannot_update(self):
        """Test that non-admin users cannot update school profiles"""
        # Authenticate as regular user
        self.client.force_authenticate(user=self.regular_user)

        # Prepare the data
        data = {
            'mission': 'Unauthorized Update'
        }

        # Make the request
        url = reverse('school-profile-detail', args=[self.school_profile.id])
        response = self.client.patch(url, data, format='json')

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
