"""
Enhanced models for the settings module with comprehensive features.
"""
from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator, EmailValidator, RegexValidator
from django.core.exceptions import ValidationError
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from schools.models import SchoolBranch
from core.models import CustomUser
import json
import uuid
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
from django.conf import settings
import os


class AdvancedSchoolProfile(models.Model):
    """Enhanced school profile with comprehensive settings"""
    
    TIMEZONE_CHOICES = [
        ('UTC', 'UTC'),
        ('Africa/Nairobi', 'East Africa Time'),
        ('Africa/Lagos', 'West Africa Time'),
        ('Africa/Cairo', 'Egypt Time'),
        ('Africa/Johannesburg', 'South Africa Time'),
        ('America/New_York', 'Eastern Time'),
        ('Europe/London', 'GMT/BST'),
        ('Asia/Dubai', 'Gulf Time'),
    ]
    
    LANGUAGE_CHOICES = [
        ('en', 'English'),
        ('sw', 'Swahili'),
        ('fr', 'French'),
        ('ar', 'Arabic'),
        ('pt', 'Portuguese'),
    ]
    
    CURRENCY_CHOICES = [
        ('USD', 'US Dollar'),
        ('KES', 'Kenyan Shilling'),
        ('NGN', 'Nigerian Naira'),
        ('GHS', 'Ghanaian Cedi'),
        ('ZAR', 'South African Rand'),
        ('EUR', 'Euro'),
        ('GBP', 'British Pound'),
    ]
    
    SCHOOL_TYPE_CHOICES = [
        ('public', 'Public School'),
        ('private', 'Private School'),
        ('charter', 'Charter School'),
        ('international', 'International School'),
        ('religious', 'Religious School'),
    ]
    
    school_branch = models.OneToOneField(SchoolBranch, on_delete=models.CASCADE, related_name='advanced_profile')
    
    # Basic Information
    school_type = models.CharField(max_length=20, choices=SCHOOL_TYPE_CHOICES, default='private')
    establishment_date = models.DateField(null=True, blank=True)
    registration_number = models.CharField(max_length=100, blank=True, unique=True)
    accreditation_body = models.CharField(max_length=200, blank=True)
    
    # Branding
    logo = models.ImageField(upload_to='school_logos/', blank=True, null=True)
    favicon = models.ImageField(upload_to='school_favicons/', blank=True, null=True)
    banner_image = models.ImageField(upload_to='school_banners/', blank=True, null=True)
    letterhead = models.ImageField(upload_to='school_letterheads/', blank=True, null=True)
    
    # Color Scheme
    primary_color = models.CharField(
        max_length=7, 
        default='#1f2937', 
        validators=[RegexValidator(r'^#[0-9A-Fa-f]{6}$', 'Enter a valid hex color code')],
        help_text='Primary brand color (hex code)'
    )
    secondary_color = models.CharField(
        max_length=7, 
        default='#3b82f6',
        validators=[RegexValidator(r'^#[0-9A-Fa-f]{6}$', 'Enter a valid hex color code')],
        help_text='Secondary brand color (hex code)'
    )
    accent_color = models.CharField(
        max_length=7, 
        default='#10b981',
        validators=[RegexValidator(r'^#[0-9A-Fa-f]{6}$', 'Enter a valid hex color code')],
        help_text='Accent color (hex code)'
    )
    
    # School Information
    motto = models.CharField(max_length=255, blank=True)
    vision = models.TextField(blank=True)
    mission = models.TextField(blank=True)
    core_values = models.JSONField(default=list, blank=True, help_text='List of core values')
    history = models.TextField(blank=True)
    achievements = models.JSONField(default=list, blank=True, help_text='List of achievements')
    
    # Contact Information
    phone_validator = RegexValidator(r'^\+?1?\d{9,15}$', 'Enter a valid phone number')
    phone = models.CharField(max_length=20, blank=True, validators=[phone_validator])
    mobile = models.CharField(max_length=20, blank=True, validators=[phone_validator])
    fax = models.CharField(max_length=20, blank=True, validators=[phone_validator])
    email = models.EmailField(blank=True, validators=[EmailValidator()])
    admin_email = models.EmailField(blank=True, validators=[EmailValidator()])
    website = models.URLField(blank=True)
    
    # Address Information
    address_line_1 = models.CharField(max_length=255, blank=True)
    address_line_2 = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state_province = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)
    country = models.CharField(max_length=100, blank=True)
    
    # Geographic Information
    latitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True)
    longitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True)
    
    # Academic Configuration
    academic_year_start_month = models.IntegerField(
        default=1, 
        validators=[MinValueValidator(1), MaxValueValidator(12)],
        help_text="Month when academic year starts (1-12)"
    )
    academic_year_end_month = models.IntegerField(
        default=12, 
        validators=[MinValueValidator(1), MaxValueValidator(12)],
        help_text="Month when academic year ends (1-12)"
    )
    terms_per_year = models.IntegerField(
        default=3,
        validators=[MinValueValidator(1), MaxValueValidator(4)],
        help_text="Number of terms/semesters per academic year"
    )
    
    # System Localization
    timezone = models.CharField(max_length=50, choices=TIMEZONE_CHOICES, default='UTC')
    language = models.CharField(max_length=10, choices=LANGUAGE_CHOICES, default='en')
    currency = models.CharField(max_length=3, choices=CURRENCY_CHOICES, default='USD')
    
    # Date and Time Formats
    date_format = models.CharField(max_length=20, default='YYYY-MM-DD', choices=[
        ('YYYY-MM-DD', 'YYYY-MM-DD'),
        ('DD/MM/YYYY', 'DD/MM/YYYY'),
        ('MM/DD/YYYY', 'MM/DD/YYYY'),
        ('DD-MM-YYYY', 'DD-MM-YYYY'),
    ])
    time_format = models.CharField(max_length=10, default='24', choices=[
        ('24', '24 Hour'),
        ('12', '12 Hour'),
    ])
    
    # Social Media
    facebook_url = models.URLField(blank=True)
    twitter_url = models.URLField(blank=True)
    instagram_url = models.URLField(blank=True)
    linkedin_url = models.URLField(blank=True)
    youtube_url = models.URLField(blank=True)
    tiktok_url = models.URLField(blank=True)
    
    # Features Configuration
    enabled_features = models.JSONField(default=dict, blank=True)
    custom_fields = models.JSONField(default=dict, blank=True)
    notification_settings = models.JSONField(default=dict, blank=True)
    
    # Capacity Information
    max_students = models.PositiveIntegerField(null=True, blank=True)
    max_staff = models.PositiveIntegerField(null=True, blank=True)
    current_students = models.PositiveIntegerField(default=0)
    current_staff = models.PositiveIntegerField(default=0)
    
    # Status
    is_active = models.BooleanField(default=True)
    is_verified = models.BooleanField(default=False)
    verification_date = models.DateTimeField(null=True, blank=True)
    
    # Metadata
    updated_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Advanced Profile for {self.school_branch.name}"
    
    def clean(self):
        if self.academic_year_start_month == self.academic_year_end_month:
            raise ValidationError("Academic year start and end months cannot be the same")
        
        if self.max_students and self.current_students > self.max_students:
            raise ValidationError("Current students cannot exceed maximum capacity")
            
        if self.max_staff and self.current_staff > self.max_staff:
            raise ValidationError("Current staff cannot exceed maximum capacity")
    
    def get_academic_year_duration(self):
        """Calculate academic year duration in months"""
        if self.academic_year_end_month >= self.academic_year_start_month:
            return self.academic_year_end_month - self.academic_year_start_month + 1
        else:
            return (12 - self.academic_year_start_month) + self.academic_year_end_month + 1
    
    def get_full_address(self):
        """Get formatted full address"""
        address_parts = [
            self.address_line_1,
            self.address_line_2,
            self.city,
            self.state_province,
            self.postal_code,
            self.country
        ]
        return ', '.join([part for part in address_parts if part])
    
    def get_capacity_utilization(self):
        """Get capacity utilization percentages"""
        student_utilization = 0
        staff_utilization = 0
        
        if self.max_students and self.max_students > 0:
            student_utilization = (self.current_students / self.max_students) * 100
            
        if self.max_staff and self.max_staff > 0:
            staff_utilization = (self.current_staff / self.max_staff) * 100
            
        return {
            'students': round(student_utilization, 2),
            'staff': round(staff_utilization, 2)
        }

    class Meta:
        verbose_name = "Advanced School Profile"
        verbose_name_plural = "Advanced School Profiles"
        indexes = [
            models.Index(fields=['school_branch', 'is_active']),
            models.Index(fields=['registration_number']),
            models.Index(fields=['is_verified', 'verification_date']),
        ]


class SystemSettings(models.Model):
    """Advanced system settings and configuration"""
    
    ENVIRONMENT_CHOICES = [
        ('development', 'Development'),
        ('staging', 'Staging'),
        ('production', 'Production'),
    ]
    
    BACKUP_FREQUENCY_CHOICES = [
        ('hourly', 'Every Hour'),
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
    ]
    
    school_branch = models.OneToOneField(SchoolBranch, on_delete=models.CASCADE, related_name='system_settings')
    
    # Environment Configuration
    environment = models.CharField(max_length=20, choices=ENVIRONMENT_CHOICES, default='production')
    debug_mode = models.BooleanField(default=False)
    maintenance_mode = models.BooleanField(default=False)
    maintenance_message = models.TextField(blank=True, default="System is under maintenance. Please try again later.")
    
    # Security Settings
    session_timeout = models.PositiveIntegerField(default=3600, help_text="Session timeout in seconds")
    password_expiry_days = models.PositiveIntegerField(default=90, help_text="Password expiry in days")
    max_login_attempts = models.PositiveIntegerField(default=5)
    lockout_duration = models.PositiveIntegerField(default=1800, help_text="Account lockout duration in seconds")
    require_mfa = models.BooleanField(default=False, help_text="Require multi-factor authentication")
    
    # Backup Configuration
    auto_backup_enabled = models.BooleanField(default=True)
    backup_frequency = models.CharField(max_length=20, choices=BACKUP_FREQUENCY_CHOICES, default='daily')
    backup_retention_days = models.PositiveIntegerField(default=30)
    backup_location = models.CharField(max_length=255, blank=True)
    
    # Email Configuration
    email_notifications_enabled = models.BooleanField(default=True)
    smtp_host = models.CharField(max_length=255, blank=True)
    smtp_port = models.PositiveIntegerField(default=587)
    smtp_use_tls = models.BooleanField(default=True)
    smtp_username = models.CharField(max_length=255, blank=True)
    smtp_password = models.CharField(max_length=255, blank=True)  # Should be encrypted
    
    # SMS Configuration
    sms_notifications_enabled = models.BooleanField(default=False)
    sms_provider = models.CharField(max_length=50, blank=True)
    sms_api_key = models.CharField(max_length=255, blank=True)  # Should be encrypted
    sms_sender_id = models.CharField(max_length=20, blank=True)
    
    # File Upload Settings
    max_file_size = models.PositiveIntegerField(default=10485760, help_text="Maximum file size in bytes")
    allowed_file_types = models.JSONField(default=list, help_text="List of allowed file extensions")
    upload_path = models.CharField(max_length=255, default='uploads/')
    
    # Performance Settings
    cache_timeout = models.PositiveIntegerField(default=3600, help_text="Cache timeout in seconds")
    page_size = models.PositiveIntegerField(default=20, help_text="Default pagination size")
    max_export_records = models.PositiveIntegerField(default=10000)
    
    # Integration Settings
    api_rate_limit = models.PositiveIntegerField(default=1000, help_text="API requests per hour")
    webhook_timeout = models.PositiveIntegerField(default=30, help_text="Webhook timeout in seconds")
    
    # Custom Settings
    custom_settings = models.JSONField(default=dict, blank=True)
    
    # Metadata
    updated_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"System Settings for {self.school_branch.name}"
    
    def clean(self):
        if self.session_timeout < 300:  # Minimum 5 minutes
            raise ValidationError("Session timeout cannot be less than 5 minutes")
            
        if self.max_login_attempts < 3:
            raise ValidationError("Maximum login attempts cannot be less than 3")
    
    def get_allowed_file_types_display(self):
        """Get formatted list of allowed file types"""
        if not self.allowed_file_types:
            return "All file types allowed"
        return ", ".join(self.allowed_file_types)

    class Meta:
        verbose_name = "System Settings"
        verbose_name_plural = "System Settings"


class NotificationTemplate(models.Model):
    """Email and SMS notification templates"""

    TEMPLATE_TYPES = [
        ('email', 'Email Template'),
        ('sms', 'SMS Template'),
        ('push', 'Push Notification'),
        ('in_app', 'In-App Notification'),
    ]

    TRIGGER_EVENTS = [
        ('user_registration', 'User Registration'),
        ('password_reset', 'Password Reset'),
        ('fee_payment', 'Fee Payment'),
        ('grade_published', 'Grade Published'),
        ('attendance_alert', 'Attendance Alert'),
        ('library_overdue', 'Library Book Overdue'),
        ('event_reminder', 'Event Reminder'),
        ('system_maintenance', 'System Maintenance'),
        ('custom', 'Custom Event'),
    ]

    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='notification_templates')
    name = models.CharField(max_length=100)
    template_type = models.CharField(max_length=20, choices=TEMPLATE_TYPES)
    trigger_event = models.CharField(max_length=50, choices=TRIGGER_EVENTS)

    # Template Content
    subject = models.CharField(max_length=255, blank=True, help_text="For email templates")
    content = models.TextField(help_text="Template content with variables like {{name}}, {{school_name}}")
    html_content = models.TextField(blank=True, help_text="HTML version for email templates")

    # Configuration
    is_active = models.BooleanField(default=True)
    send_immediately = models.BooleanField(default=True)
    delay_minutes = models.PositiveIntegerField(default=0, help_text="Delay before sending")

    # Variables
    available_variables = models.JSONField(default=list, help_text="List of available template variables")

    # Metadata
    created_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True)
    updated_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True, related_name='updated_templates')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.template_type})"

    def render_content(self, context):
        """Render template content with provided context"""
        import re
        content = self.content
        for key, value in context.items():
            pattern = r'\{\{\s*' + re.escape(key) + r'\s*\}\}'
            content = re.sub(pattern, str(value), content)
        return content

    class Meta:
        verbose_name = "Notification Template"
        verbose_name_plural = "Notification Templates"
        unique_together = ['school_branch', 'name', 'template_type']


class IntegrationSettings(models.Model):
    """Third-party integration settings"""

    INTEGRATION_TYPES = [
        ('payment_gateway', 'Payment Gateway'),
        ('sms_provider', 'SMS Provider'),
        ('email_service', 'Email Service'),
        ('learning_management', 'Learning Management System'),
        ('government_reporting', 'Government Reporting'),
        ('analytics', 'Analytics Service'),
        ('cloud_storage', 'Cloud Storage'),
        ('video_conferencing', 'Video Conferencing'),
        ('custom', 'Custom Integration'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('testing', 'Testing'),
        ('error', 'Error'),
    ]

    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='integrations')
    name = models.CharField(max_length=100)
    integration_type = models.CharField(max_length=50, choices=INTEGRATION_TYPES)
    provider = models.CharField(max_length=100)

    # Configuration
    api_endpoint = models.URLField(blank=True)
    api_key = models.CharField(max_length=500, blank=True)  # Should be encrypted
    api_secret = models.CharField(max_length=500, blank=True)  # Should be encrypted
    webhook_url = models.URLField(blank=True)

    # Settings
    configuration = models.JSONField(default=dict, help_text="Integration-specific configuration")
    is_sandbox = models.BooleanField(default=True, help_text="Use sandbox/test environment")

    # Status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='inactive')
    last_sync = models.DateTimeField(null=True, blank=True)
    last_error = models.TextField(blank=True)

    # Metadata
    created_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True)
    updated_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True, related_name='updated_integrations')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.provider}"

    def test_connection(self):
        """Test the integration connection"""
        # Implementation would depend on the integration type
        pass

    def encrypt_sensitive_data(self):
        """Encrypt API keys and secrets"""
        # Implementation for encrypting sensitive data
        pass

    class Meta:
        verbose_name = "Integration Settings"
        verbose_name_plural = "Integration Settings"
        unique_together = ['school_branch', 'name']


class CustomField(models.Model):
    """Custom fields for extending models"""

    FIELD_TYPES = [
        ('text', 'Text'),
        ('number', 'Number'),
        ('email', 'Email'),
        ('url', 'URL'),
        ('date', 'Date'),
        ('datetime', 'Date & Time'),
        ('boolean', 'Yes/No'),
        ('choice', 'Single Choice'),
        ('multiple_choice', 'Multiple Choice'),
        ('file', 'File Upload'),
        ('textarea', 'Long Text'),
    ]

    MODEL_CHOICES = [
        ('student', 'Student'),
        ('teacher', 'Teacher'),
        ('parent', 'Parent'),
        ('class', 'Class'),
        ('subject', 'Subject'),
        ('school', 'School'),
        ('custom', 'Custom Model'),
    ]

    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='custom_fields')
    name = models.CharField(max_length=100)
    field_type = models.CharField(max_length=20, choices=FIELD_TYPES)
    target_model = models.CharField(max_length=50, choices=MODEL_CHOICES)

    # Field Configuration
    label = models.CharField(max_length=100)
    help_text = models.CharField(max_length=255, blank=True)
    placeholder = models.CharField(max_length=100, blank=True)
    default_value = models.TextField(blank=True)

    # Validation
    is_required = models.BooleanField(default=False)
    min_length = models.PositiveIntegerField(null=True, blank=True)
    max_length = models.PositiveIntegerField(null=True, blank=True)
    min_value = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    max_value = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    # Choices (for choice fields)
    choices = models.JSONField(default=list, blank=True, help_text="List of choices for choice fields")

    # Display
    order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    show_in_list = models.BooleanField(default=False)
    show_in_detail = models.BooleanField(default=True)

    # Metadata
    created_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.label} ({self.target_model})"

    def get_field_widget(self):
        """Get appropriate form widget for the field type"""
        widget_mapping = {
            'text': 'TextInput',
            'number': 'NumberInput',
            'email': 'EmailInput',
            'url': 'URLInput',
            'date': 'DateInput',
            'datetime': 'DateTimeInput',
            'boolean': 'CheckboxInput',
            'choice': 'Select',
            'multiple_choice': 'SelectMultiple',
            'file': 'FileInput',
            'textarea': 'Textarea',
        }
        return widget_mapping.get(self.field_type, 'TextInput')

    class Meta:
        verbose_name = "Custom Field"
        verbose_name_plural = "Custom Fields"
        unique_together = ['school_branch', 'name', 'target_model']
        ordering = ['target_model', 'order', 'name']


class AuditLog(models.Model):
    """Enhanced audit logging for system activities"""

    ACTION_TYPES = [
        ('create', 'Create'),
        ('update', 'Update'),
        ('delete', 'Delete'),
        ('view', 'View'),
        ('login', 'Login'),
        ('logout', 'Logout'),
        ('export', 'Export'),
        ('import', 'Import'),
        ('backup', 'Backup'),
        ('restore', 'Restore'),
        ('config_change', 'Configuration Change'),
    ]

    SEVERITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    # Basic Information
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='audit_logs')
    user = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True)
    action = models.CharField(max_length=20, choices=ACTION_TYPES)
    severity = models.CharField(max_length=10, choices=SEVERITY_LEVELS, default='low')

    # Target Object
    content_type = models.ForeignKey(ContentType, on_delete=models.SET_NULL, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')

    # Details
    description = models.TextField()
    changes = models.JSONField(default=dict, blank=True, help_text="Before/after values for updates")
    metadata = models.JSONField(default=dict, blank=True, help_text="Additional context data")

    # Request Information
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    request_path = models.CharField(max_length=500, blank=True)
    request_method = models.CharField(max_length=10, blank=True)

    # Timing
    timestamp = models.DateTimeField(auto_now_add=True)
    duration_ms = models.PositiveIntegerField(null=True, blank=True, help_text="Action duration in milliseconds")

    # Status
    success = models.BooleanField(default=True)
    error_message = models.TextField(blank=True)

    def __str__(self):
        return f"{self.user} - {self.action} - {self.timestamp}"

    def get_object_representation(self):
        """Get string representation of the target object"""
        if self.content_object:
            return str(self.content_object)
        return f"{self.content_type} (ID: {self.object_id})"

    class Meta:
        verbose_name = "Audit Log"
        verbose_name_plural = "Audit Logs"
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['school_branch', 'timestamp']),
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['action', 'timestamp']),
            models.Index(fields=['severity', 'timestamp']),
            models.Index(fields=['content_type', 'object_id']),
        ]
