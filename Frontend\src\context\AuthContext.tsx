import { createContext, useContext, ReactNode, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { login as loginAction, logout as logoutAction, refreshToken as refreshTokenAction } from '../store/slices/authSlice';
import type { RootState } from '../store/store';
import type { AppDispatch } from '../store/store';
import { authService } from '../services/authService';
import { secureStorage } from '../utils/secureStorage';

interface AuthContextType {
  isAuthenticated: boolean;
  user: any;
  loading: boolean;
  error: string | null;
  login: (credentials: { email: string; password: string }) => Promise<any>;
  logout: () => void;
  refreshToken: () => Promise<any>;
}

const AuthContext = createContext<AuthContextType | null>(null);

export function AuthProvider({ children }: { children: ReactNode }) {
  const dispatch = useDispatch<AppDispatch>();
  const { isAuthenticated, user, loading, error } = useSelector((state: RootState) => state.auth);

  const login = async (credentials: { email: string; password: string }) => {
    return dispatch(loginAction(credentials)).unwrap();
  };

  const logout = () => {
    dispatch(logoutAction());
  };

  const refreshToken = async () => {
    return dispatch(refreshTokenAction()).unwrap();
  };

  // Check token validity on component mount
  useEffect(() => {
    const checkTokenValidity = async () => {
      // Skip token refresh on auth pages to prevent loops
      const currentPath = window.location.pathname;
      const authPages = ['/signin', '/signup', '/forgot-password', '/register'];
      const isAuthPage = authPages.some(page => currentPath.includes(page));

      if (isAuthPage) {
        console.log('🚫 AuthContext: Skipping token refresh on auth page');
        return;
      }

      // Skip if user is already authenticated according to auth state
      if (isAuthenticated && user) {
        console.log('✅ AuthContext: User already authenticated, skipping token check');
        return;
      }

      console.log('🔍 AuthContext: Starting token validity check...');

      try {
        // Check if there's a token and it's not expired
        if (!secureStorage.isAuthenticated()) {
          console.log('❌ AuthContext: No valid token found');
          return;
        }

        console.log('✅ AuthContext: Valid token found, attempting refresh...');
        try {
          await refreshToken();
          console.log('✅ AuthContext: Token refresh successful');
        } catch (error) {
          console.log('❌ AuthContext: Token refresh failed, logging out');
          // If refresh fails, logout
          logout();
        }
      } catch (error) {
        console.log('❌ AuthContext: Error in token check:', error);
      }
    };

    checkTokenValidity();
  }, [isAuthenticated, user]); // Check when auth state changes

  return (
    <AuthContext.Provider value={{ isAuthenticated, user, loading, error, login, logout, refreshToken }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};