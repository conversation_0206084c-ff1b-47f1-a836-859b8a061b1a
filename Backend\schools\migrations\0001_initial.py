# Generated by Django 5.2.1 on 2025-06-01 13:25

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='School',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('code', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('address', models.TextField()),
                ('phone', models.CharField(max_length=20)),
                ('email', models.EmailField(max_length=254)),
                ('website', models.URLField(blank=True, null=True)),
                ('registration_number', models.Char<PERSON>ield(max_length=50, unique=True)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='school_logos/')),
                ('established_date', models.DateField()),
                ('school_type', models.CharField(choices=[('public', 'Public School'), ('private', 'Private School'), ('international', 'International School'), ('boarding', 'Boarding School'), ('day', 'Day School'), ('mixed', 'Mixed (Day & Boarding)')], default='public', max_length=20)),
                ('education_levels', models.CharField(choices=[('primary', 'Primary Only'), ('secondary', 'Secondary Only'), ('primary_secondary', 'Primary & Secondary'), ('pre_primary', 'Pre-Primary Only'), ('all_levels', 'All Levels')], default='primary_secondary', max_length=20)),
                ('student_capacity', models.PositiveIntegerField(blank=True, null=True)),
                ('current_enrollment', models.PositiveIntegerField(default=0)),
                ('motto', models.CharField(blank=True, max_length=200, null=True)),
                ('vision', models.TextField(blank=True, null=True)),
                ('mission', models.TextField(blank=True, null=True)),
                ('principal_name', models.CharField(blank=True, max_length=100, null=True)),
                ('principal_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('principal_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('county', models.CharField(blank=True, max_length=50, null=True)),
                ('sub_county', models.CharField(blank=True, max_length=50, null=True)),
                ('ward', models.CharField(blank=True, max_length=50, null=True)),
                ('postal_code', models.CharField(blank=True, max_length=10, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_archived', models.BooleanField(default=False)),
                ('archived_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='SchoolBranch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('code', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('address', models.TextField()),
                ('phone', models.CharField(max_length=20)),
                ('email', models.EmailField(max_length=254)),
                ('website', models.URLField(blank=True, null=True)),
                ('registration_number', models.CharField(max_length=50, unique=True)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='branch_logos/')),
                ('established_date', models.DateField()),
                ('branch_type', models.CharField(choices=[('public', 'Public School'), ('private', 'Private School'), ('international', 'International School'), ('boarding', 'Boarding School'), ('day', 'Day School'), ('mixed', 'Mixed (Day & Boarding)')], default='private', max_length=20)),
                ('student_capacity', models.PositiveIntegerField(blank=True, null=True)),
                ('current_enrollment', models.PositiveIntegerField(default=0)),
                ('branch_manager', models.CharField(blank=True, max_length=100, null=True)),
                ('manager_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('manager_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('county', models.CharField(blank=True, max_length=50, null=True)),
                ('sub_county', models.CharField(blank=True, max_length=50, null=True)),
                ('ward', models.CharField(blank=True, max_length=50, null=True)),
                ('postal_code', models.CharField(blank=True, max_length=10, null=True)),
                ('has_library', models.BooleanField(default=False)),
                ('has_laboratory', models.BooleanField(default=False)),
                ('has_computer_lab', models.BooleanField(default=False)),
                ('has_playground', models.BooleanField(default=False)),
                ('has_boarding_facilities', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('is_archived', models.BooleanField(default=False)),
                ('archived_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('school', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='branch', to='schools.school')),
                ('users', models.ManyToManyField(blank=True, related_name='branch', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='UserBranchRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(max_length=20)),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_roles', to='schools.schoolbranch')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='branch_roles', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'branch', 'role')},
            },
        ),
    ]
