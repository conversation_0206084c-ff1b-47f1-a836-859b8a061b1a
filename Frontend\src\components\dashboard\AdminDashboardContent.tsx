import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { useSchool } from '../../contexts/SchoolContext';
import { getAllDashboardData } from '../../services/dashboardService';

// Import Heroicons
import {
  UserGroupIcon,
  UserIcon,
  AcademicCapIcon,
  BookOpenIcon,
  CalendarIcon,
  MegaphoneIcon,
  ArrowRightIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';

// Define types for dashboard data
interface DashboardStats {
  totalStudents: number;
  totalTeachers: number;
  totalClasses: number;
  totalCourses: number;
  totalStreams: number;
  totalDepartments: number;
  totalEvents: number;
  totalAnnouncements: number;
}

interface Announcement {
  id: number;
  title: string;
  date: string;
  priority: string;
}

interface Event {
  id: number;
  title: string;
  date: string;
  location: string;
}

interface Activity {
  id: number;
  user: string;
  action: string;
  timestamp: string;
}

interface DashboardData {
  stats: DashboardStats;
  academicData?: {
    classes: any[];
    streams: any[];
    subjects: any[];
    departments: any[];
    academicYears: any[];
    currentAcademicYear: any;
  };
  recentAnnouncements: Announcement[];
  upcomingEvents: Event[];
  recentActivities: Activity[];
}

// Default dashboard data for when API calls fail
const defaultDashboardData: DashboardData = {
  stats: {
    totalStudents: 0,
    totalTeachers: 0,
    totalClasses: 0,
    totalCourses: 0,
    totalStreams: 0,
    totalDepartments: 0,
    totalEvents: 0,
    totalAnnouncements: 0
  },
  academicData: {
    classes: [],
    streams: [],
    subjects: [],
    departments: [],
    academicYears: [],
    currentAcademicYear: null
  },
  recentAnnouncements: [],
  upcomingEvents: [],
  recentActivities: []
};

const AdminDashboardContent: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData>(defaultDashboardData);
  const [loading, setLoading] = useState<boolean>(true);
  const navigate = useNavigate();

  // Get the selected school and branch from context
  const { selectedSchool, selectedBranch } = useSchool();

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Pass the selected school and branch IDs to the dashboard service
        const data = await getAllDashboardData({
          schoolId: selectedSchool?.id,
          branchId: selectedBranch?.id
        });

        if (data) {
          setDashboardData(data);
        } else {
          // If no data is returned, use default data
          setDashboardData(defaultDashboardData);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // If there's an error, use default data
        setDashboardData(defaultDashboardData);
      } finally {
        setLoading(false);
      }
    };

    // Only fetch data if we have a selected school and branch
    if (selectedSchool && selectedBranch) {
      setLoading(true);
      fetchData();
    } else {
      // If no school or branch is selected, use default data and stop loading
      setDashboardData(defaultDashboardData);
      setLoading(false);
    }
  }, [selectedSchool, selectedBranch]); // Re-fetch when school or branch changes

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 dark:border-blue-400"></div>
        <span className="sr-only">Loading dashboard data...</span>
      </div>
    );
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'LOW':
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300';
      case 'MEDIUM':
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300';
      case 'HIGH':
        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
      default:
        return 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300';
    }
  };

  const getPriorityIcon = (priority: string) => {
    return priority === 'HIGH' ? (
      <ChevronUpIcon className="h-4 w-4 text-red-600 dark:text-red-400" />
    ) : (
      <ChevronDownIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white">Admin Dashboard</h1>
        <div className="mt-4 md:mt-0 flex items-center space-x-3">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {selectedSchool && selectedBranch && (
              <span>
                Viewing data for <span className="font-medium text-gray-700 dark:text-gray-300">{selectedSchool.name}</span>
                {selectedBranch && ` - ${selectedBranch.name}`}
              </span>
            )}
          </div>
          <button
            type="button"
            onClick={() => {
              setLoading(true);
              getAllDashboardData({
                schoolId: selectedSchool?.id,
                branchId: selectedBranch?.id
              }).then(data => {
                if (data) setDashboardData(data);
                setLoading(false);
              });
            }}
            className="inline-flex items-center rounded-lg bg-indigo-600 dark:bg-indigo-700 px-3 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 dark:hover:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 dark:focus-visible:outline-indigo-500"
          >
            <ArrowPathIcon className="-ml-0.5 mr-1.5 h-5 w-5" />
            Refresh Data
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        {/* Summary Cards */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-blue-50 dark:bg-blue-900/20">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-blue-100 dark:bg-blue-800/50 rounded-lg">
                <UserGroupIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white ml-3">Students</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                {dashboardData.stats && dashboardData.stats.totalStudents > 0 ? (
                  <p className="text-3xl font-bold text-gray-800 dark:text-white">{dashboardData.stats.totalStudents}</p>
                ) : (
                  <p className="text-3xl font-bold text-gray-400 dark:text-gray-500">0</p>
                )}
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Total enrolled students</p>
              </div>
              {dashboardData.stats && dashboardData.stats.totalStudents > 0 ? (
                <div className="bg-blue-100 dark:bg-blue-800/50 text-blue-600 dark:text-blue-300 rounded-full px-2 py-1 text-xs font-medium">
                  +5.2%
                </div>
              ) : (
                <button
                  type="button"
                  onClick={() => navigate('/AllStudents/add')}
                  className="bg-blue-100 dark:bg-blue-800/50 text-blue-600 dark:text-blue-300 rounded-full px-2 py-1 text-xs font-medium hover:bg-blue-200 dark:hover:bg-blue-800/70"
                >
                  Add New
                </button>
              )}
            </div>
          </div>
          <div className="px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700">
            <button
              type="button"
              onClick={() => navigate('/AllStudents')}
              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium flex items-center"
            >
              View Students
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-green-50 dark:bg-green-900/20">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-green-100 dark:bg-green-800/50 rounded-lg">
                <UserIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white ml-3">Teachers</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                {dashboardData.stats && dashboardData.stats.totalTeachers > 0 ? (
                  <p className="text-3xl font-bold text-gray-800 dark:text-white">{dashboardData.stats.totalTeachers}</p>
                ) : (
                  <p className="text-3xl font-bold text-gray-400 dark:text-gray-500">0</p>
                )}
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Total teaching staff</p>
              </div>
              {dashboardData.stats && dashboardData.stats.totalTeachers > 0 ? (
                <div className="bg-green-100 dark:bg-green-800/50 text-green-600 dark:text-green-300 rounded-full px-2 py-1 text-xs font-medium">
                  +2.8%
                </div>
              ) : (
                <button
                  type="button"
                  onClick={() => navigate('/teachers/add')}
                  className="bg-green-100 dark:bg-green-800/50 text-green-600 dark:text-green-300 rounded-full px-2 py-1 text-xs font-medium hover:bg-green-200 dark:hover:bg-green-800/70"
                >
                  Add New
                </button>
              )}
            </div>
          </div>
          <div className="px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700">
            <button
              type="button"
              onClick={() => navigate('/teachers')}
              className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 text-sm font-medium flex items-center"
            >
              View Teachers
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-purple-50 dark:bg-purple-900/20">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-purple-100 dark:bg-purple-800/50 rounded-lg">
                <AcademicCapIcon className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white ml-3">Classes</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                {dashboardData.stats && dashboardData.stats.totalClasses > 0 ? (
                  <p className="text-3xl font-bold text-gray-800 dark:text-white">{dashboardData.stats.totalClasses}</p>
                ) : (
                  <p className="text-3xl font-bold text-gray-400 dark:text-gray-500">0</p>
                )}
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Active classes</p>
              </div>
              {dashboardData.stats && dashboardData.stats.totalClasses > 0 ? (
                <div className="bg-purple-100 dark:bg-purple-800/50 text-purple-600 dark:text-purple-300 rounded-full px-2 py-1 text-xs font-medium">
                  +4.3%
                </div>
              ) : (
                <button
                  type="button"
                  onClick={() => navigate('/classes/add')}
                  className="bg-purple-100 dark:bg-purple-800/50 text-purple-600 dark:text-purple-300 rounded-full px-2 py-1 text-xs font-medium hover:bg-purple-200 dark:hover:bg-purple-800/70"
                >
                  Add New
                </button>
              )}
            </div>
          </div>
          <div className="px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700">
            <button
              type="button"
              onClick={() => navigate('/classes')}
              className="text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 text-sm font-medium flex items-center"
            >
              View Classes
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-amber-50 dark:bg-amber-900/20">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-amber-100 dark:bg-amber-800/50 rounded-lg">
                <BookOpenIcon className="h-6 w-6 text-amber-600 dark:text-amber-400" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white ml-3">Courses</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                {dashboardData.stats && dashboardData.stats.totalCourses > 0 ? (
                  <p className="text-3xl font-bold text-gray-800 dark:text-white">{dashboardData.stats.totalCourses}</p>
                ) : (
                  <p className="text-3xl font-bold text-gray-400 dark:text-gray-500">0</p>
                )}
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Total courses offered</p>
              </div>
              {dashboardData.stats && dashboardData.stats.totalCourses > 0 ? (
                <div className="bg-amber-100 dark:bg-amber-800/50 text-amber-600 dark:text-amber-300 rounded-full px-2 py-1 text-xs font-medium">
                  +3.1%
                </div>
              ) : (
                <button
                  type="button"
                  onClick={() => navigate('/academics/subjects/add')}
                  className="bg-amber-100 dark:bg-amber-800/50 text-amber-600 dark:text-amber-300 rounded-full px-2 py-1 text-xs font-medium hover:bg-amber-200 dark:hover:bg-amber-800/70"
                >
                  Add New
                </button>
              )}
            </div>
          </div>
          <div className="px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700">
            <button
              type="button"
              onClick={() => navigate('/academics/subjects')}
              className="text-amber-600 dark:text-amber-400 hover:text-amber-800 dark:hover:text-amber-300 text-sm font-medium flex items-center"
            >
              View Courses
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        {/* Streams Card */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-indigo-50 dark:bg-indigo-900/20">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-indigo-100 dark:bg-indigo-800/50 rounded-lg">
                <AcademicCapIcon className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white ml-3">Streams</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                {dashboardData.stats && dashboardData.stats.totalStreams > 0 ? (
                  <p className="text-3xl font-bold text-gray-800 dark:text-white">{dashboardData.stats.totalStreams}</p>
                ) : (
                  <p className="text-3xl font-bold text-gray-400 dark:text-gray-500">0</p>
                )}
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Class streams</p>
              </div>
              {dashboardData.stats && dashboardData.stats.totalStreams > 0 ? (
                <div className="bg-indigo-100 dark:bg-indigo-800/50 text-indigo-600 dark:text-indigo-300 rounded-full px-2 py-1 text-xs font-medium">
                  Active
                </div>
              ) : (
                <button
                  type="button"
                  onClick={() => navigate('/academics/streams/add')}
                  className="bg-indigo-100 dark:bg-indigo-800/50 text-indigo-600 dark:text-indigo-300 rounded-full px-2 py-1 text-xs font-medium hover:bg-indigo-200 dark:hover:bg-indigo-800/70"
                >
                  Add New
                </button>
              )}
            </div>
          </div>
          <div className="px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700">
            <button
              type="button"
              onClick={() => navigate('/academics/streams')}
              className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 text-sm font-medium flex items-center"
            >
              View Streams
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        {/* Departments Card */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-teal-50 dark:bg-teal-900/20">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-teal-100 dark:bg-teal-800/50 rounded-lg">
                <BookOpenIcon className="h-6 w-6 text-teal-600 dark:text-teal-400" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white ml-3">Departments</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                {dashboardData.stats && dashboardData.stats.totalDepartments > 0 ? (
                  <p className="text-3xl font-bold text-gray-800 dark:text-white">{dashboardData.stats.totalDepartments}</p>
                ) : (
                  <p className="text-3xl font-bold text-gray-400 dark:text-gray-500">0</p>
                )}
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Academic departments</p>
              </div>
              {dashboardData.stats && dashboardData.stats.totalDepartments > 0 ? (
                <div className="bg-teal-100 dark:bg-teal-800/50 text-teal-600 dark:text-teal-300 rounded-full px-2 py-1 text-xs font-medium">
                  Active
                </div>
              ) : (
                <button
                  type="button"
                  onClick={() => navigate('/academics/departments/add')}
                  className="bg-teal-100 dark:bg-teal-800/50 text-teal-600 dark:text-teal-300 rounded-full px-2 py-1 text-xs font-medium hover:bg-teal-200 dark:hover:bg-teal-800/70"
                >
                  Add New
                </button>
              )}
            </div>
          </div>
          <div className="px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700">
            <button
              type="button"
              onClick={() => navigate('/academics/departments')}
              className="text-teal-600 dark:text-teal-400 hover:text-teal-800 dark:hover:text-teal-300 text-sm font-medium flex items-center"
            >
              View Departments
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>
      </div>

      {/* Academic Overview Section */}
      {dashboardData.academicData && dashboardData.academicData.currentAcademicYear && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden">
          <div className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">Academic Overview</h2>
                <div className="flex items-center space-x-6">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Current Academic Year</p>
                    <p className="text-lg font-medium text-gray-800 dark:text-white">{dashboardData.academicData.currentAcademicYear.year}</p>
                  </div>
                  <div className="h-8 w-px bg-gray-300 dark:bg-gray-600"></div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Total Classes</p>
                    <p className="text-lg font-medium text-gray-800 dark:text-white">{dashboardData.academicData.classes.length}</p>
                  </div>
                  <div className="h-8 w-px bg-gray-300 dark:bg-gray-600"></div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Active Subjects</p>
                    <p className="text-lg font-medium text-gray-800 dark:text-white">{dashboardData.academicData.subjects.length}</p>
                  </div>
                </div>
              </div>
              <button
                type="button"
                onClick={() => navigate('/academics')}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 flex items-center"
              >
                Manage Academics
                <ArrowRightIcon className="h-4 w-4 ml-2" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Second Row - Announcements and Events */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Announcements */}
        <div>
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 h-full">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold text-gray-800 dark:text-white">Recent Announcements</h2>
                <button
                  type="button"
                  onClick={() => navigate('/announcements')}
                  className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium flex items-center"
                >
                  View All
                  <ArrowRightIcon className="h-4 w-4 ml-1" />
                </button>
              </div>
              <div className="border-t border-gray-100 dark:border-gray-700 -mx-6 px-6 py-2"></div>
              <div className="space-y-4 mt-4">
                {dashboardData.recentAnnouncements && dashboardData.recentAnnouncements.length > 0 ? (
                  dashboardData.recentAnnouncements.map((announcement) => (
                    <div key={announcement.id} className="flex items-center justify-between py-3 border-b border-gray-100 dark:border-gray-700 last:border-0">
                      <div className="flex items-start space-x-3">
                        <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg mt-1">
                          <MegaphoneIcon className="h-5 w-5 text-blue-500 dark:text-blue-400" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-800 dark:text-white">{announcement.title}</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">{format(new Date(announcement.date), 'MMM dd, yyyy')}</p>
                        </div>
                      </div>
                      <div className={`flex items-center px-3 py-1 rounded-full text-xs font-medium ${getPriorityColor(announcement.priority)}`}>
                        {getPriorityIcon(announcement.priority)}
                        <span className="ml-1">{announcement.priority}</span>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-full mb-3">
                      <MegaphoneIcon className="h-8 w-8 text-blue-500 dark:text-blue-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-1">No announcements yet</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 max-w-xs">Create your first announcement to keep everyone informed about important updates.</p>
                    <button
                      type="button"
                      onClick={() => navigate('/announcements')}
                      className="mt-4 px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800/40 transition-colors duration-200 flex items-center"
                    >
                      <MegaphoneIcon className="h-4 w-4 mr-2" />
                      Create Announcement
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Upcoming Events */}
        <div className="md:col-span-1 lg:col-span-2">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 h-full">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold text-gray-800 dark:text-white">Upcoming Events</h2>
                <button
                  type="button"
                  onClick={() => navigate('/calendar')}
                  className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium flex items-center"
                >
                  View All
                  <ArrowRightIcon className="h-4 w-4 ml-1" />
                </button>
              </div>
              <div className="border-t border-gray-100 dark:border-gray-700 -mx-6 px-6 py-2"></div>
              <div className="space-y-4 mt-4">
                {dashboardData.upcomingEvents && dashboardData.upcomingEvents.length > 0 ? (
                  dashboardData.upcomingEvents.map((event) => (
                    <div key={event.id} className="flex items-center justify-between py-3 border-b border-gray-100 dark:border-gray-700 last:border-0">
                      <div className="flex items-start space-x-3">
                        <div className="p-2 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg mt-1">
                          <CalendarIcon className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-800 dark:text-white">{event.title}</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Location: {event.location}</p>
                        </div>
                      </div>
                      <div className="text-sm font-medium text-indigo-600 dark:text-indigo-300 bg-indigo-50 dark:bg-indigo-900/20 px-3 py-1 rounded-full">
                        {format(new Date(event.date), 'MMM dd')}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <div className="p-3 bg-indigo-50 dark:bg-indigo-900/20 rounded-full mb-3">
                      <CalendarIcon className="h-8 w-8 text-indigo-500 dark:text-indigo-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-1">No upcoming events</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 max-w-xs">Schedule your first event to keep everyone informed about important dates.</p>
                    <button
                      type="button"
                      onClick={() => navigate('/calendar')}
                      className="mt-4 px-4 py-2 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-300 rounded-lg hover:bg-indigo-200 dark:hover:bg-indigo-800/40 transition-colors duration-200 flex items-center"
                    >
                      <CalendarIcon className="h-4 w-4 mr-2" />
                      Schedule Event
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="col-span-1 md:col-span-2 lg:col-span-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
            <div className="p-6">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">Quick Actions</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                <button
                  type="button"
                  onClick={() => navigate('/AllStudents')}
                  className="flex items-center justify-center px-4 py-3 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-800/30 transition-colors duration-200"
                >
                  <UserGroupIcon className="h-5 w-5 mr-2" />
                  Manage Students
                </button>
                <button
                  type="button"
                  onClick={() => navigate('/announcements')}
                  className="flex items-center justify-center px-4 py-3 bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-800/30 transition-colors duration-200"
                >
                  <MegaphoneIcon className="h-5 w-5 mr-2" />
                  Post Announcement
                </button>
                <button
                  type="button"
                  onClick={() => navigate('/assessments')}
                  className="flex items-center justify-center px-4 py-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-100 dark:hover:bg-green-800/30 transition-colors duration-200"
                >
                  <AcademicCapIcon className="h-5 w-5 mr-2" />
                  Manage Assessments
                </button>
                <button
                  type="button"
                  onClick={() => navigate('/academics/subjects')}
                  className="flex items-center justify-center px-4 py-3 bg-indigo-50 dark:bg-indigo-900/20 text-indigo-700 dark:text-indigo-300 rounded-lg hover:bg-indigo-100 dark:hover:bg-indigo-800/30 transition-colors duration-200"
                >
                  <BookOpenIcon className="h-5 w-5 mr-2" />
                  Manage Subjects
                </button>
                <button
                  type="button"
                  onClick={() => navigate('/settings/school-profile')}
                  className="flex items-center justify-center px-4 py-3 bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300 rounded-lg hover:bg-amber-100 dark:hover:bg-amber-800/30 transition-colors duration-200"
                >
                  <UserIcon className="h-5 w-5 mr-2" />
                  School Settings
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboardContent;
