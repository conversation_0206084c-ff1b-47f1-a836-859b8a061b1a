from django.core.management.base import BaseCommand
from academics.curriculum_models import CurriculumSystem, EducationLevel

class Command(BaseCommand):
    help = 'Seed curriculum systems data'

    def handle(self, *args, **options):
        self.stdout.write('Seeding curriculum systems...')

        # CBC System
        cbc, created = CurriculumSystem.objects.update_or_create(
            code='CBC',
            defaults={
                'name': 'Competency Based Curriculum (2-6-3-3-3)',
                'description': 'Kenya\'s new curriculum system with 2 years pre-primary, 6 years primary, 3 years junior secondary, 3 years senior secondary, and 3 years university',
                'structure': {
                    'stages': [
                        {
                            'name': 'Pre-Primary',
                            'code': 'PP',
                            'years': 2,
                            'age_range': [4, 5]
                        },
                        {
                            'name': 'Primary',
                            'code': 'PRI',
                            'years': 6,
                            'age_range': [6, 11]
                        },
                        {
                            'name': 'Junior Secondary',
                            'code': 'JSS',
                            'years': 3,
                            'age_range': [12, 14]
                        },
                        {
                            'name': 'Senior Secondary',
                            'code': 'SSS',
                            'years': 3,
                            'age_range': [15, 17]
                        }
                    ]
                }
            }
        )

        if created:
            self.stdout.write(self.style.SUCCESS(f'Created CBC curriculum system'))
        else:
            self.stdout.write(f'Updated CBC curriculum system')

        # Create CBC education levels
        self._create_cbc_education_levels(cbc)

        # 8-4-4 System
        old_system, created = CurriculumSystem.objects.update_or_create(
            code='844',
            defaults={
                'name': '8-4-4 System',
                'description': 'Kenya\'s previous curriculum with 8 years primary, 4 years secondary, and 4 years university',
                'structure': {
                    'stages': [
                        {
                            'name': 'Primary',
                            'code': 'PRI',
                            'years': 8,
                            'age_range': [6, 13]
                        },
                        {
                            'name': 'Secondary',
                            'code': 'SEC',
                            'years': 4,
                            'age_range': [14, 17]
                        }
                    ]
                }
            }
        )

        if created:
            self.stdout.write(self.style.SUCCESS(f'Created 8-4-4 curriculum system'))
        else:
            self.stdout.write(f'Updated 8-4-4 curriculum system')

        # Create 8-4-4 education levels
        self._create_844_education_levels(old_system)

        # IGCSE/A-Levels
        igcse, created = CurriculumSystem.objects.update_or_create(
            code='IGCSE',
            defaults={
                'name': 'IGCSE/A-Levels',
                'description': 'International curriculum with IGCSE and A-Levels',
                'structure': {
                    'stages': [
                        {
                            'name': 'Primary',
                            'code': 'PRI',
                            'years': 6,
                            'age_range': [5, 10]
                        },
                        {
                            'name': 'Lower Secondary',
                            'code': 'LS',
                            'years': 3,
                            'age_range': [11, 13]
                        },
                        {
                            'name': 'IGCSE',
                            'code': 'IGCSE',
                            'years': 2,
                            'age_range': [14, 15]
                        },
                        {
                            'name': 'A-Levels',
                            'code': 'AL',
                            'years': 2,
                            'age_range': [16, 17]
                        }
                    ]
                }
            }
        )

        if created:
            self.stdout.write(self.style.SUCCESS(f'Created IGCSE curriculum system'))
        else:
            self.stdout.write(f'Updated IGCSE curriculum system')

        # Create IGCSE education levels
        self._create_igcse_education_levels(igcse)

        self.stdout.write(self.style.SUCCESS('Successfully seeded curriculum systems'))

    def _create_cbc_education_levels(self, cbc_system):
        """Create education levels for CBC system"""
        # Pre-Primary levels
        for i in range(1, 3):
            name = f"PP{i}"
            EducationLevel.objects.update_or_create(
                curriculum_system=cbc_system,
                code=name,
                defaults={
                    'name': f"Pre-Primary {i}",
                    'stage_code': 'PP',
                    'sequence': i,
                }
            )

        # Primary levels
        for i in range(1, 7):
            name = f"Grade {i}"
            EducationLevel.objects.update_or_create(
                curriculum_system=cbc_system,
                code=f"G{i}",
                defaults={
                    'name': name,
                    'stage_code': 'PRI',
                    'sequence': i + 2,  # After PP1, PP2
                }
            )

        # Junior Secondary levels
        for i in range(7, 10):
            name = f"Grade {i}"
            EducationLevel.objects.update_or_create(
                curriculum_system=cbc_system,
                code=f"G{i}",
                defaults={
                    'name': name,
                    'stage_code': 'JSS',
                    'sequence': i + 2,  # Continue sequence
                }
            )

        # Senior Secondary levels
        for i in range(10, 13):
            name = f"Grade {i}"
            EducationLevel.objects.update_or_create(
                curriculum_system=cbc_system,
                code=f"G{i}",
                defaults={
                    'name': name,
                    'stage_code': 'SSS',
                    'sequence': i + 2,  # Continue sequence
                }
            )

        self.stdout.write(f'Created CBC education levels')

    def _create_844_education_levels(self, system_844):
        """Create education levels for 8-4-4 system"""
        # Primary levels
        for i in range(1, 9):
            name = f"Standard {i}"
            EducationLevel.objects.update_or_create(
                curriculum_system=system_844,
                code=f"STD{i}",
                defaults={
                    'name': name,
                    'stage_code': 'PRI',
                    'sequence': i,
                }
            )

        # Secondary levels
        for i in range(1, 5):
            name = f"Form {i}"
            EducationLevel.objects.update_or_create(
                curriculum_system=system_844,
                code=f"F{i}",
                defaults={
                    'name': name,
                    'stage_code': 'SEC',
                    'sequence': i + 8,  # After Standard 1-8
                }
            )

        self.stdout.write(f'Created 8-4-4 education levels')

    def _create_igcse_education_levels(self, igcse_system):
        """Create education levels for IGCSE system"""
        # Primary levels
        for i in range(1, 7):
            name = f"Year {i}"
            EducationLevel.objects.update_or_create(
                curriculum_system=igcse_system,
                code=f"Y{i}",
                defaults={
                    'name': name,
                    'stage_code': 'PRI',
                    'sequence': i,
                }
            )

        # Lower Secondary levels
        for i in range(7, 10):
            name = f"Year {i}"
            EducationLevel.objects.update_or_create(
                curriculum_system=igcse_system,
                code=f"Y{i}",
                defaults={
                    'name': name,
                    'stage_code': 'LS',
                    'sequence': i,
                }
            )

        # IGCSE levels
        for i in range(10, 12):
            name = f"Year {i}"
            EducationLevel.objects.update_or_create(
                curriculum_system=igcse_system,
                code=f"Y{i}",
                defaults={
                    'name': name,
                    'stage_code': 'IGCSE',
                    'sequence': i,
                }
            )

        # A-Levels
        for i in range(12, 14):
            name = f"Year {i}"
            EducationLevel.objects.update_or_create(
                curriculum_system=igcse_system,
                code=f"Y{i}",
                defaults={
                    'name': name,
                    'stage_code': 'AL',
                    'sequence': i,
                }
            )

        self.stdout.write(f'Created IGCSE education levels')