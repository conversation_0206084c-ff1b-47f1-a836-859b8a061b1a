from django.core.management.base import BaseCommand
from django.db import transaction
from academics.models import ClassProgressionRule
from academics.curriculum_models import CurriculumSystem, EducationLevel
from schools.models import School

class Command(BaseCommand):
    help = 'Seeds class progression rules for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--school_id',
            type=int,
            help='School ID to create progression rules for',
        )

    @transaction.atomic
    def handle(self, *args, **options):
        school_id = options.get('school_id')

        if school_id:
            schools = School.objects.filter(id=school_id)
            if not schools.exists():
                self.stdout.write(self.style.ERROR(f'School with ID {school_id} not found'))
                return
        else:
            schools = School.objects.all()
            if not schools.exists():
                self.stdout.write(self.style.ERROR('No schools found in the database'))
                return

        # Get all curriculum systems
        curriculum_systems = CurriculumSystem.objects.all()
        if not curriculum_systems.exists():
            self.stdout.write(self.style.ERROR('No curriculum systems found. Run seed_curriculum_systems first.'))
            return

        # Process each school
        for school in schools:
            self.stdout.write(self.style.SUCCESS(f'Creating progression rules for school: {school.name}'))

            # Process each curriculum system
            for curriculum in curriculum_systems:
                self.stdout.write(self.style.SUCCESS(f'Processing curriculum: {curriculum.name}'))

                # Get education levels for this curriculum
                levels = EducationLevel.objects.filter(curriculum_system=curriculum).order_by('sequence')
                if not levels.exists():
                    self.stdout.write(self.style.WARNING(f'No education levels found for {curriculum.name}'))
                    continue

                # Create progression rules
                for i in range(len(levels) - 1):
                    current_level = levels[i]
                    next_level = levels[i + 1]

                    # Create default progression rule
                    try:
                        rule = ClassProgressionRule.objects.get(
                            current_level=current_level,
                            next_level=next_level,
                            school=school
                        )
                        created = False
                    except ClassProgressionRule.DoesNotExist:
                        rule = ClassProgressionRule.objects.create(
                            current_level=current_level,
                            next_level=next_level,
                            school=school,
                            is_default_progression=True,
                            requirements={
                                'min_grade': 'D',
                                'min_attendance': 75,
                                'required_subjects': []
                            }
                        )
                        created = True

                    action = 'Created' if created else 'Already exists'
                    self.stdout.write(
                        self.style.SUCCESS(f'{action}: {current_level.name} → {next_level.name}')
                    )

                # For CBC, create alternative paths at key transition points
                if curriculum.code == 'CBC':
                    # Find Grade 6 (end of primary)
                    try:
                        grade6 = levels.get(code='G6')
                        # Find JSS1 (start of junior secondary)
                        jss1 = levels.get(code='JSS1')

                        # Create alternative progression rule for vocational path
                        try:
                            rule = ClassProgressionRule.objects.get(
                                current_level=grade6,
                                next_level=jss1,
                                school=school,
                                is_default_progression=False
                            )
                            created = False
                        except ClassProgressionRule.DoesNotExist:
                            rule = ClassProgressionRule.objects.create(
                                current_level=grade6,
                                next_level=jss1,
                                school=school,
                                is_default_progression=False,
                                requirements={
                                    'min_grade': 'C-',
                                    'min_attendance': 80,
                                    'required_subjects': ['Mathematics', 'English', 'Science']
                                }
                            )
                            created = True

                        action = 'Created' if created else 'Already exists'
                        self.stdout.write(
                            self.style.SUCCESS(f'{action} alternative path: {grade6.name} → {jss1.name}')
                        )
                    except EducationLevel.DoesNotExist:
                        self.stdout.write(self.style.WARNING('Could not find Grade 6 or JSS1 for CBC'))

                # For 8-4-4, create alternative paths at key transition points
                if curriculum.code == '844':
                    # Find Class 8 (end of primary)
                    try:
                        class8 = levels.get(code='8')
                        # Find Form 1 (start of secondary)
                        form1 = levels.get(code='F1')

                        # Create alternative progression rule
                        try:
                            rule = ClassProgressionRule.objects.get(
                                current_level=class8,
                                next_level=form1,
                                school=school,
                                is_default_progression=False
                            )
                            created = False
                        except ClassProgressionRule.DoesNotExist:
                            rule = ClassProgressionRule.objects.create(
                                current_level=class8,
                                next_level=form1,
                                school=school,
                                is_default_progression=False,
                                requirements={
                                    'min_grade': 250,  # KCPE score
                                    'min_attendance': 90,
                                    'required_subjects': ['Mathematics', 'English', 'Kiswahili', 'Science', 'Social Studies']
                                }
                            )
                            created = True

                        action = 'Created' if created else 'Already exists'
                        self.stdout.write(
                            self.style.SUCCESS(f'{action} alternative path: {class8.name} → {form1.name}')
                        )
                    except EducationLevel.DoesNotExist:
                        self.stdout.write(self.style.WARNING('Could not find Class 8 or Form 1 for 8-4-4'))

                # For IGCSE, create alternative paths
                if curriculum.code == 'IGCSE':
                    # Find Year 11 (end of IGCSE)
                    try:
                        year11 = levels.get(code='Y11')
                        # Find Year 12 (start of A-Levels)
                        year12 = levels.get(code='Y12')

                        # Create alternative progression rule
                        try:
                            rule = ClassProgressionRule.objects.get(
                                current_level=year11,
                                next_level=year12,
                                school=school,
                                is_default_progression=False
                            )
                            created = False
                        except ClassProgressionRule.DoesNotExist:
                            rule = ClassProgressionRule.objects.create(
                                current_level=year11,
                                next_level=year12,
                                school=school,
                                is_default_progression=False,
                                requirements={
                                    'min_grade': 'B',
                                    'min_attendance': 85,
                                    'required_subjects': ['Mathematics', 'English', 'Science']
                                }
                            )
                            created = True

                        action = 'Created' if created else 'Already exists'
                        self.stdout.write(
                            self.style.SUCCESS(f'{action} alternative path: {year11.name} → {year12.name}')
                        )
                    except EducationLevel.DoesNotExist:
                        self.stdout.write(self.style.WARNING('Could not find Year 11 or Year 12 for IGCSE'))

        self.stdout.write(self.style.SUCCESS('Successfully created class progression rules'))
