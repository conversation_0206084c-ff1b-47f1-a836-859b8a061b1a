from django.contrib import admin
from django.utils.html import format_html
from .models import (
    SchoolProfile, SystemConfiguration, Role, UserRole,
    BackupConfiguration, Backup, Integration, AuditLog
)
from .license_models import LicenseSubscription, ModuleActivation, LicenseHistory
from .modules import AVAILABLE_MODULES, PACKAGE_TIERS

@admin.register(SchoolProfile)
class SchoolProfileAdmin(admin.ModelAdmin):
    list_display = ('school_branch', 'contact_email', 'contact_phone', 'updated_at')
    search_fields = ('school_branch__name', 'contact_email', 'contact_phone')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(SystemConfiguration)
class SystemConfigurationAdmin(admin.ModelAdmin):
    list_display = ('school_branch', 'current_academic_year', 'current_term', 'updated_at')
    search_fields = ('school_branch__name', 'current_academic_year', 'current_term')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('School Information', {
            'fields': ('school_branch',)
        }),
        ('Academic Settings', {
            'fields': ('academic_year_start_month', 'academic_year_end_month',
                      'current_academic_year', 'current_term')
        }),
        ('Grading System', {
            'fields': ('grading_system', 'grading_scale')
        }),
        ('System Settings', {
            'fields': ('attendance_tracking_method', 'enable_online_payments',
                      'enable_sms_notifications', 'enable_email_notifications',
                      'default_language', 'timezone', 'date_format', 'time_format',
                      'system_maintenance_mode')
        }),
        ('Audit Information', {
            'fields': ('updated_by', 'created_at', 'updated_at')
        }),
    )

@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = ('name', 'school_branch', 'is_active', 'is_system_role', 'created_at')
    list_filter = ('is_active', 'is_system_role', 'school_branch')
    search_fields = ('name', 'description', 'school_branch__name')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    list_display = ('user', 'role', 'school_branch', 'is_primary', 'assigned_at')
    list_filter = ('is_primary', 'role', 'school_branch')
    search_fields = ('user__email', 'user__first_name', 'user__last_name', 'role__name')
    readonly_fields = ('assigned_at',)

@admin.register(BackupConfiguration)
class BackupConfigurationAdmin(admin.ModelAdmin):
    list_display = ('school_branch', 'is_enabled', 'backup_frequency', 'backup_storage', 'updated_at')
    list_filter = ('is_enabled', 'backup_frequency', 'backup_storage')
    search_fields = ('school_branch__name',)
    readonly_fields = ('created_at', 'updated_at')

@admin.register(Backup)
class BackupAdmin(admin.ModelAdmin):
    list_display = ('backup_name', 'school_branch', 'backup_type', 'status', 'started_at', 'completed_at')
    list_filter = ('status', 'backup_type', 'school_branch')
    search_fields = ('backup_name', 'school_branch__name')
    readonly_fields = ('started_at',)

@admin.register(Integration)
class IntegrationAdmin(admin.ModelAdmin):
    list_display = ('name', 'integration_type', 'provider', 'school_branch', 'status', 'is_enabled')
    list_filter = ('integration_type', 'provider', 'status', 'is_enabled')
    search_fields = ('name', 'provider', 'school_branch__name')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Integration Information', {
            'fields': ('name', 'integration_type', 'provider', 'description', 'school_branch')
        }),
        ('API Configuration', {
            'fields': ('api_key', 'api_secret', 'configuration', 'webhook_url', 'callback_url')
        }),
        ('Status', {
            'fields': ('status', 'is_enabled', 'last_sync', 'error_message')
        }),
        ('Audit Information', {
            'fields': ('created_by', 'created_at', 'updated_at')
        }),
    )

@admin.register(AuditLog)
class AuditLogAdmin(admin.ModelAdmin):
    list_display = ('user', 'action', 'entity_type', 'entity_name', 'timestamp', 'school_branch')
    list_filter = ('action', 'entity_type', 'school_branch')
    search_fields = ('user__email', 'entity_type', 'entity_name', 'ip_address')
    readonly_fields = ('user', 'action', 'entity_type', 'entity_id', 'entity_name',
                      'action_details', 'ip_address', 'user_agent', 'timestamp', 'school_branch')

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

@admin.register(LicenseSubscription)
class LicenseSubscriptionAdmin(admin.ModelAdmin):
    list_display = ('school', 'package_display', 'subscription_status', 'is_active_display', 'start_date', 'expiry_date')
    list_filter = ('package_type', 'subscription_status')
    search_fields = ('school__name', 'license_key')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('School Information', {
            'fields': ('school',)
        }),
        ('Subscription Details', {
            'fields': ('package_type', 'subscription_status', 'start_date', 'expiry_date')
        }),
        ('Limits', {
            'fields': ('max_students', 'max_staff', 'max_branches')
        }),
        ('Module Configuration', {
            'fields': ('custom_modules',)
        }),
        ('License Information', {
            'fields': ('license_key', 'created_at', 'updated_at')
        }),
    )

    def package_display(self, obj):
        package_info = PACKAGE_TIERS.get(obj.package_type, {})
        return package_info.get('name', obj.package_type)
    package_display.short_description = 'Package'

    def is_active_display(self, obj):
        is_active = obj.is_active()
        if is_active:
            return format_html('<span style="color: green;">Active</span>')
        return format_html('<span style="color: red;">Inactive</span>')
    is_active_display.short_description = 'Status'

@admin.register(ModuleActivation)
class ModuleActivationAdmin(admin.ModelAdmin):
    list_display = ('school_branch', 'enabled_modules_display', 'updated_at')
    search_fields = ('school_branch__name',)
    readonly_fields = ('created_at', 'updated_at')

    def enabled_modules_display(self, obj):
        modules = []
        for module_code in obj.enabled_modules:
            module_info = AVAILABLE_MODULES.get(module_code, {})
            modules.append(module_info.get('name', module_code))
        return ', '.join(modules)
    enabled_modules_display.short_description = 'Enabled Modules'

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        if obj:
            # Get available modules for this school branch
            try:
                available_modules = obj.school_branch.school.license.get_enabled_modules()
            except:
                available_modules = [code for code, info in AVAILABLE_MODULES.items() if info.get('is_core', False)]

            # Add help text to the enabled_modules field
            form.base_fields['enabled_modules'].help_text = f"Available modules: {', '.join(available_modules)}"
        return form


@admin.register(LicenseHistory)
class LicenseHistoryAdmin(admin.ModelAdmin):
    list_display = ('license_school', 'action_display', 'user_display', 'timestamp')
    list_filter = ('action', 'timestamp')
    search_fields = ('license__school__name', 'notes')
    readonly_fields = ('license', 'action', 'user', 'timestamp', 'previous_state', 'new_state', 'notes')

    def license_school(self, obj):
        return obj.license.school.name
    license_school.short_description = 'School'

    def action_display(self, obj):
        return obj.get_action_display()
    action_display.short_description = 'Action'

    def user_display(self, obj):
        if obj.user:
            return f"{obj.user.first_name} {obj.user.last_name}".strip() or obj.user.username
        return "System"
    user_display.short_description = 'User'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False