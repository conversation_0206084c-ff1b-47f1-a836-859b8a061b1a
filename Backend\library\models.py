from django.db import models
from django.utils import timezone
from schools.models import SchoolBranch
from core.models import CustomUser

class Book(models.Model):
    BOOK_STATUS_CHOICES = [
        ('AVAILABLE', 'Available'),
        ('BORROWED', 'Borrowed'),
        ('RESERVED', 'Reserved'),
        ('MAINTENANCE', 'Under Maintenance'),
        ('LOST', 'Lost'),
    ]

    title = models.CharField(max_length=255)
    author = models.CharField(max_length=255)
    isbn = models.CharField(max_length=20, unique=True)
    publisher = models.CharField(max_length=255)
    publication_year = models.PositiveIntegerField()
    edition = models.CharField(max_length=50, blank=True, null=True)
    category = models.CharField(max_length=100)
    subject = models.CharField(max_length=100, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    pages = models.PositiveIntegerField()
    cover_image = models.ImageField(upload_to='book_covers/', blank=True, null=True)
    quantity = models.PositiveIntegerField(default=1)
    available_quantity = models.PositiveIntegerField(default=1)
    location = models.CharField(max_length=100, help_text="Shelf/Section location")
    status = models.CharField(max_length=20, choices=BOOK_STATUS_CHOICES, default='AVAILABLE')
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='books')
    added_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='added_books')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title} by {self.author}"

    class Meta:
        ordering = ['title', 'author']
        indexes = [
            models.Index(fields=['isbn']),
            models.Index(fields=['title']),
            models.Index(fields=['author']),
            models.Index(fields=['category']),
        ]

class EResource(models.Model):
    RESOURCE_TYPE_CHOICES = [
        ('EBOOK', 'E-Book'),
        ('JOURNAL', 'Journal'),
        ('ARTICLE', 'Article'),
        ('VIDEO', 'Video'),
        ('AUDIO', 'Audio'),
        ('DOCUMENT', 'Document'),
        ('OTHER', 'Other'),
    ]

    title = models.CharField(max_length=255)
    author = models.CharField(max_length=255)
    resource_type = models.CharField(max_length=20, choices=RESOURCE_TYPE_CHOICES)
    publisher = models.CharField(max_length=255, blank=True, null=True)
    publication_year = models.PositiveIntegerField(blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    category = models.CharField(max_length=100)
    subject = models.CharField(max_length=100, blank=True, null=True)
    url = models.URLField(blank=True, null=True)
    file = models.FileField(upload_to='e_resources/', blank=True, null=True)
    thumbnail = models.ImageField(upload_to='e_resource_thumbnails/', blank=True, null=True)
    access_level = models.CharField(max_length=50, default='ALL')  # ALL, TEACHERS, STUDENTS, ADMIN
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='e_resources')
    added_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='added_e_resources')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title} ({self.get_resource_type_display()})"

    class Meta:
        ordering = ['title', 'resource_type']
        indexes = [
            models.Index(fields=['title']),
            models.Index(fields=['resource_type']),
            models.Index(fields=['category']),
        ]

class Borrowing(models.Model):
    book = models.ForeignKey(Book, on_delete=models.CASCADE, related_name='borrowings')
    borrower = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='borrowings')
    borrow_date = models.DateField(default=timezone.now)
    due_date = models.DateField()
    return_date = models.DateField(blank=True, null=True)
    is_returned = models.BooleanField(default=False)
    is_renewed = models.BooleanField(default=False)
    renewal_count = models.PositiveIntegerField(default=0)
    fine_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    fine_paid = models.BooleanField(default=False)
    notes = models.TextField(blank=True, null=True)
    issued_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='issued_borrowings')
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='borrowings')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.book.title} borrowed by {self.borrower.get_full_name()}"

    def is_overdue(self):
        if self.is_returned:
            return False
        return timezone.now().date() > self.due_date

    def days_overdue(self):
        if not self.is_overdue():
            return 0
        return (timezone.now().date() - self.due_date).days

    def calculate_fine(self, fine_rate=1.00):
        """Calculate fine based on days overdue and fine rate per day"""
        if self.is_returned:
            if self.return_date > self.due_date:
                return (self.return_date - self.due_date).days * fine_rate
            return 0
        elif self.is_overdue():
            return self.days_overdue() * fine_rate
        return 0

    def save(self, *args, **kwargs):
        # Update fine amount before saving
        if not self.fine_paid:
            self.fine_amount = self.calculate_fine()

        # Update book available quantity
        if not self.pk:  # New borrowing
            self.book.available_quantity -= 1
            if self.book.available_quantity == 0:
                self.book.status = 'BORROWED'
            self.book.save()

        super().save(*args, **kwargs)

    class Meta:
        ordering = ['-borrow_date']
        indexes = [
            models.Index(fields=['borrow_date']),
            models.Index(fields=['due_date']),
            models.Index(fields=['is_returned']),
        ]

class Return(models.Model):
    borrowing = models.OneToOneField(Borrowing, on_delete=models.CASCADE, related_name='return_record')
    return_date = models.DateField(default=timezone.now)
    condition = models.CharField(max_length=50, default='GOOD')
    fine_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    fine_paid = models.BooleanField(default=False)
    payment_date = models.DateField(blank=True, null=True)
    payment_method = models.CharField(max_length=50, blank=True, null=True)
    received_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='received_returns')
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Return of {self.borrowing.book.title} by {self.borrowing.borrower.get_full_name()}"

    def save(self, *args, **kwargs):
        # Update borrowing record
        if not self.pk:  # New return
            self.borrowing.is_returned = True
            self.borrowing.return_date = self.return_date
            self.borrowing.fine_amount = self.fine_amount
            self.borrowing.fine_paid = self.fine_paid
            self.borrowing.save()

            # Update book available quantity
            book = self.borrowing.book
            book.available_quantity += 1
            if book.status == 'BORROWED' and book.available_quantity > 0:
                book.status = 'AVAILABLE'
            book.save()

        super().save(*args, **kwargs)

    class Meta:
        ordering = ['-return_date']
        indexes = [
            models.Index(fields=['return_date']),
            models.Index(fields=['fine_paid']),
        ]

class LibraryReport(models.Model):
    REPORT_TYPE_CHOICES = [
        ('BORROWING', 'Borrowing Report'),
        ('OVERDUE', 'Overdue Report'),
        ('INVENTORY', 'Inventory Report'),
        ('FINE', 'Fine Collection Report'),
        ('USAGE', 'Usage Statistics Report'),
    ]

    title = models.CharField(max_length=255)
    report_type = models.CharField(max_length=20, choices=REPORT_TYPE_CHOICES)
    start_date = models.DateField()
    end_date = models.DateField()
    generated_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='generated_library_reports')
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='library_reports')
    report_data = models.JSONField(blank=True, null=True)
    file = models.FileField(upload_to='library_reports/', blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title} ({self.start_date} to {self.end_date})"

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['report_type']),
            models.Index(fields=['start_date']),
            models.Index(fields=['end_date']),
        ]