from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group
from core.models import CustomUser
from schools.models import School, SchoolBranch
from django.db.models.signals import post_save
from users.signals import save_user_role_profile
import random
from datetime import date

class Command(BaseCommand):
    help = 'Create sample users for testing dashboard'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample users...')
        
        # Get or create a school and branch
        school, created = School.objects.get_or_create(
            name="Sample School",
            defaults={
                'address': '123 Sample Street',
                'phone': '0123456789',
                'email': '<EMAIL>',
                'registration_number': 'SS-001',
                'established_date': '2000-01-01'
            }
        )
        
        branch, created = SchoolBranch.objects.get_or_create(
            school=school,
            name="Main Campus",
            defaults={
                'address': '123 Sample Street',
                'phone': '0123456789',
                'email': '<EMAIL>',
                'registration_number': 'SS-BR-001',
                'established_date': '2000-01-01'
            }
        )
        
        # Temporarily disconnect the signal to avoid profile creation issues
        post_save.disconnect(save_user_role_profile, sender=CustomUser)

        try:
            # Create sample teachers
            teacher_data = [
                {'first_name': '<PERSON>', 'last_name': 'Doe', 'email': '<EMAIL>'},
                {'first_name': 'Jane', 'last_name': '<PERSON>', 'email': '<EMAIL>'},
                {'first_name': 'Mike', 'last_name': '<PERSON>', 'email': '<EMAIL>'},
                {'first_name': 'Sarah', 'last_name': 'Wilson', 'email': '<EMAIL>'},
                {'first_name': 'David', 'last_name': 'Brown', 'email': '<EMAIL>'},
            ]

            for data in teacher_data:
                user, created = CustomUser.objects.get_or_create(
                    email=data['email'],
                    defaults={
                        'username': data['email'],
                        'first_name': data['first_name'],
                        'last_name': data['last_name'],
                        'user_type': 'teacher',
                        'phone': f'+2547{random.randint(10000000, 99999999)}'
                    }
                )
                if created:
                    user.set_password('teacher123')
                    user.save()
                    self.stdout.write(f'  Created teacher: {data["first_name"]} {data["last_name"]}')

            # Create sample students
            student_data = [
                {'first_name': 'Alice', 'last_name': 'Johnson', 'email': '<EMAIL>'},
                {'first_name': 'Bob', 'last_name': 'Williams', 'email': '<EMAIL>'},
                {'first_name': 'Carol', 'last_name': 'Davis', 'email': '<EMAIL>'},
                {'first_name': 'Daniel', 'last_name': 'Miller', 'email': '<EMAIL>'},
                {'first_name': 'Emma', 'last_name': 'Wilson', 'email': '<EMAIL>'},
                {'first_name': 'Frank', 'last_name': 'Moore', 'email': '<EMAIL>'},
                {'first_name': 'Grace', 'last_name': 'Taylor', 'email': '<EMAIL>'},
                {'first_name': 'Henry', 'last_name': 'Anderson', 'email': '<EMAIL>'},
                {'first_name': 'Ivy', 'last_name': 'Thomas', 'email': '<EMAIL>'},
                {'first_name': 'Jack', 'last_name': 'Jackson', 'email': '<EMAIL>'},
            ]

            for data in student_data:
                user, created = CustomUser.objects.get_or_create(
                    email=data['email'],
                    defaults={
                        'username': data['email'],
                        'first_name': data['first_name'],
                        'last_name': data['last_name'],
                        'user_type': 'student',
                        'phone': f'+2547{random.randint(10000000, 99999999)}'
                    }
                )
                if created:
                    user.set_password('student123')
                    user.save()
                    self.stdout.write(f'  Created student: {data["first_name"]} {data["last_name"]}')

        finally:
            # Reconnect the signal
            post_save.connect(save_user_role_profile, sender=CustomUser)

        # Count users by type
        teacher_count = CustomUser.objects.filter(user_type='teacher').count()
        student_count = CustomUser.objects.filter(user_type='student').count()

        self.stdout.write(self.style.SUCCESS(f'Sample users created successfully!'))
        self.stdout.write(f'  Teachers: {teacher_count}')
        self.stdout.write(f'  Students: {student_count}')
