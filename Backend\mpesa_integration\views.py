import requests
import base64
import json
from datetime import datetime
import logging
from django.conf import settings
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from rest_framework import viewsets, status, views
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny

from .models import MpesaCredential, MpesaTransaction, MpesaCallback
from .serializers import (
    MpesaCredentialSerializer, MpesaTransactionSerializer,
    MpesaCallbackSerializer, STKPushSerializer, TransactionStatusSerializer
)
from fees.models import FeePayment, FeeBalance

# Import automated payment processor for school billing
try:
    from fees.automated_payment_processor import payment_verification_engine
    from fees.school_billing_models import SchoolPayment
    AUTO_VERIFICATION_AVAILABLE = True
except ImportError:
    AUTO_VERIFICATION_AVAILABLE = False
    logger.warning("Automated payment verification not available")

logger = logging.getLogger(__name__)

class MpesaCredentialViewSet(viewsets.ModelViewSet):
    """
    API endpoint for MPESA credentials
    """
    queryset = MpesaCredential.objects.all()
    serializer_class = MpesaCredentialSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter credentials by school if user has permission"""
        user = self.request.user
        if user.is_superuser:
            return self.queryset
        return self.queryset.filter(school=user.school_branch.school)

class MpesaTransactionViewSet(viewsets.ModelViewSet):
    """
    API endpoint for MPESA transactions
    """
    queryset = MpesaTransaction.objects.all()
    serializer_class = MpesaTransactionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter transactions by school if user has permission"""
        user = self.request.user
        if user.is_superuser:
            return self.queryset
        # Filter by fee_payment's student's school
        return self.queryset.filter(fee_payment__student__school_branch__school=user.school_branch.school)

class MpesaAPIView(views.APIView):
    """
    Base class for MPESA API views
    """
    permission_classes = [IsAuthenticated]

    def get_mpesa_credentials(self, school_id):
        """Get active MPESA credentials for a school"""
        try:
            return MpesaCredential.objects.get(school_id=school_id, is_active=True)
        except MpesaCredential.DoesNotExist:
            return None

    def generate_access_token(self, credentials):
        """Generate MPESA API access token"""
        consumer_key = credentials.consumer_key
        consumer_secret = credentials.consumer_secret
        api_url = "https://sandbox.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials"
        if credentials.environment == 'production':
            api_url = "https://api.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials"

        auth = base64.b64encode(f"{consumer_key}:{consumer_secret}".encode()).decode('utf-8')
        headers = {
            'Authorization': f'Basic {auth}'
        }

        try:
            response = requests.get(api_url, headers=headers)
            response_data = response.json()
            return response_data.get('access_token')
        except Exception as e:
            logger.error(f"Error generating access token: {str(e)}")
            return None

    def format_phone_number(self, phone_number):
        """Format phone number to required format (254XXXXXXXXX)"""
        if phone_number.startswith('+'):
            phone_number = phone_number[1:]
        if phone_number.startswith('0'):
            phone_number = '254' + phone_number[1:]
        if not phone_number.startswith('254'):
            phone_number = '254' + phone_number
        return phone_number

class STKPushView(MpesaAPIView):
    """
    Initiate STK Push payment
    """
    def post(self, request, *args, **kwargs):
        serializer = STKPushSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        school_id = request.user.school_branch.school.id
        credentials = self.get_mpesa_credentials(school_id)
        if not credentials:
            return Response(
                {"error": "MPESA credentials not found for this school"},
                status=status.HTTP_404_NOT_FOUND
            )

        access_token = self.generate_access_token(credentials)
        if not access_token:
            return Response(
                {"error": "Failed to generate MPESA access token"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        # Format phone number
        phone_number = self.format_phone_number(serializer.validated_data['phone_number'])

        # Generate timestamp
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')

        # Generate password
        password_string = f"{credentials.shortcode}{credentials.passkey}{timestamp}"
        password = base64.b64encode(password_string.encode()).decode('utf-8')

        # Prepare API URL
        api_url = "https://sandbox.safaricom.co.ke/mpesa/stkpush/v1/processrequest"
        if credentials.environment == 'production':
            api_url = "https://api.safaricom.co.ke/mpesa/stkpush/v1/processrequest"

        # Prepare callback URL
        callback_url = f"{request.build_absolute_uri('/').rstrip('/')}/api/mpesa/callback/stk/"

        # Prepare request data
        amount = int(float(serializer.validated_data['amount']))
        reference = serializer.validated_data['reference']
        description = serializer.validated_data.get('description', 'Fee Payment')

        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }

        payload = {
            "BusinessShortCode": credentials.shortcode,
            "Password": password,
            "Timestamp": timestamp,
            "TransactionType": "CustomerPayBillOnline",
            "Amount": amount,
            "PartyA": phone_number,
            "PartyB": credentials.shortcode,
            "PhoneNumber": phone_number,
            "CallBackURL": callback_url,
            "AccountReference": reference,
            "TransactionDesc": description
        }

        try:
            response = requests.post(api_url, json=payload, headers=headers)
            response_data = response.json()

            # Create transaction record
            transaction = MpesaTransaction.objects.create(
                transaction_type="STK_PUSH",
                phone_number=phone_number,
                amount=amount,
                reference=reference,
                description=description,
                checkout_request_id=response_data.get('CheckoutRequestID'),
                merchant_request_id=response_data.get('MerchantRequestID')
            )

            # Link to fee payment if provided
            fee_payment_id = serializer.validated_data.get('fee_payment_id')
            if fee_payment_id:
                try:
                    fee_payment = FeePayment.objects.get(id=fee_payment_id)
                    transaction.fee_payment = fee_payment
                    transaction.save()
                except FeePayment.DoesNotExist:
                    pass

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"STK Push error: {str(e)}")
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class STKPushCallbackView(views.APIView):
    """
    Handle STK Push callback from MPESA
    """
    permission_classes = [AllowAny]  # Allow MPESA to call this endpoint

    def post(self, request, *args, **kwargs):
        # Log the callback data
        callback_data = request.data
        logger.info(f"STK Push callback received: {callback_data}")

        # Save callback data
        callback = MpesaCallback.objects.create(
            callback_data=callback_data
        )

        # Process the callback
        try:
            body = callback_data.get('Body', {})
            stkCallback = body.get('stkCallback', {})
            checkout_request_id = stkCallback.get('CheckoutRequestID')

            # Find the transaction
            try:
                transaction = MpesaTransaction.objects.get(checkout_request_id=checkout_request_id)
                callback.transaction = transaction
                callback.save()

                # Update transaction status
                result_code = stkCallback.get('ResultCode')
                transaction.result_code = result_code
                transaction.result_description = stkCallback.get('ResultDesc')

                if result_code == '0':  # Success
                    transaction.status = 'COMPLETED'

                    # Extract MPESA receipt number
                    call_back_metadata = stkCallback.get('CallbackMetadata', {})
                    items = call_back_metadata.get('Item', [])
                    for item in items:
                        if item.get('Name') == 'MpesaReceiptNumber':
                            transaction.mpesa_receipt_number = item.get('Value')
                            break

                    # Update fee payment if linked
                    if transaction.fee_payment:
                        fee_payment = transaction.fee_payment
                        fee_payment.verified = True
                        fee_payment.reference_number = transaction.mpesa_receipt_number
                        fee_payment.save()

                        # Update fee balance
                        try:
                            balance = FeeBalance.objects.get(
                                student=fee_payment.student,
                                term=fee_payment.term
                            )
                            balance.amount_paid += fee_payment.amount_paid
                            balance.balance = balance.total_amount - balance.amount_paid
                            balance.save()
                        except FeeBalance.DoesNotExist:
                            pass

                    # Trigger automated verification for school billing payments
                    if AUTO_VERIFICATION_AVAILABLE:
                        self._trigger_auto_verification(transaction)
                else:
                    transaction.status = 'FAILED'

                transaction.save()
                callback.processed = True
                callback.save()

            except MpesaTransaction.DoesNotExist:
                logger.error(f"Transaction not found for checkout request ID: {checkout_request_id}")

        except Exception as e:
            logger.error(f"Error processing STK Push callback: {str(e)}")

        # Always return success to MPESA
        return Response({"ResultCode": 0, "ResultDesc": "Accepted"})

    def _trigger_auto_verification(self, mpesa_transaction):
        """
        Trigger automated verification for school billing payments
        """
        try:
            # Look for matching school billing payments
            school_payments = SchoolPayment.objects.filter(
                external_reference=mpesa_transaction.mpesa_receipt_number,
                payment_method='MOBILE_MONEY',
                status='PENDING'
            )

            for payment in school_payments:
                # Check if amounts match (allowing for small discrepancies)
                amount_diff = abs(payment.amount - mpesa_transaction.amount)
                if amount_diff <= 1:  # Allow 1 KES difference for rounding
                    logger.info(f"Triggering auto-verification for payment {payment.payment_reference}")

                    # Trigger automated verification
                    payment_verification_engine.process_payment_verification(
                        payment.id,
                        verification_source='MPESA_CALLBACK'
                    )
                    break

        except Exception as e:
            logger.error(f"Error triggering auto-verification: {e}")

class TransactionStatusView(MpesaAPIView):
    """
    Check transaction status
    """
    def post(self, request, *args, **kwargs):
        serializer = TransactionStatusSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        school_id = request.user.school_branch.school.id
        credentials = self.get_mpesa_credentials(school_id)
        if not credentials:
            return Response(
                {"error": "MPESA credentials not found for this school"},
                status=status.HTTP_404_NOT_FOUND
            )

        access_token = self.generate_access_token(credentials)
        if not access_token:
            return Response(
                {"error": "Failed to generate MPESA access token"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        # Generate timestamp
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')

        # Generate password
        password_string = f"{credentials.shortcode}{credentials.passkey}{timestamp}"
        password = base64.b64encode(password_string.encode()).decode('utf-8')

        # Prepare API URL
        api_url = "https://sandbox.safaricom.co.ke/mpesa/transactionstatus/v1/query"
        if credentials.environment == 'production':
            api_url = "https://api.safaricom.co.ke/mpesa/transactionstatus/v1/query"

        # Prepare callback URL
        callback_url = f"{request.build_absolute_uri('/').rstrip('/')}/api/mpesa/callback/transaction-status/"

        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }

        payload = {
            "BusinessShortCode": credentials.shortcode,
            "Password": password,
            "Timestamp": timestamp,
            "TransactionID": serializer.validated_data['transaction_id'],
            "PartyA": credentials.shortcode,
            "IdentifierType": "4",  # Shortcode
            "ResultURL": callback_url,
            "QueueTimeOutURL": callback_url,
            "Remarks": "Transaction status query",
            "Occasion": ""
        }

        try:
            response = requests.post(api_url, json=payload, headers=headers)
            response_data = response.json()
            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Transaction status error: {str(e)}")
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )