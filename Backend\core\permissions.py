from rest_framework import permissions
from django.contrib.auth.models import Group

# Import role-based permissions
from .role_permissions import (
    IsSystemAdmin, IsSchoolAdmin as RoleIsSchoolAdmin, IsDeputyPrincipal, IsBranchAdmin,
    IsDepartmentHead, IsICTAdmin, <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IsLibrarian, IsCounselor,
    IsAccountant, IsSecretary, IsNurse, IsMaintenance, IsSecurity, IsDriver,
    IsStudent as RoleIsStudent, IsParent, IsAnyAdmin, IsAcademicStaff, IsSupportStaff,
    IsAnyStaff, IsSchoolMember, IsBranchMember, HasRequiredGroups, IsParentOfStudent,
    IsTeacherOfStudent
)

class BranchBasedPermission(permissions.BasePermission):
    """
    Permission class to handle school branch based access control.
    """
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False

        # Allow superusers full access
        if request.user.is_superuser:
            return True

        # Check if user has a school branch
        return hasattr(request.user, 'school_branch')

    def has_object_permission(self, request, view, obj):
        if request.user.is_superuser:
            return True

        # Get the appropriate branch field name
        branch_field = 'branch' if hasattr(obj, 'branch') else 'school_branch'

        # Check if object has branch field
        if not hasattr(obj, branch_field):
            return False

        # Verify user's school branch matches object's branch
        return getattr(obj, branch_field) == request.user.school_branch

class IsStudent(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and hasattr(request.user, 'student')

    def has_object_permission(self, request, view, obj):
        return obj.student == request.user.student

class IsTeacher(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and hasattr(request.user, 'teacher')

    def has_object_permission(self, request, view, obj):
        # Check if teacher is assigned to the class/subject
        return obj.teacher == request.user.teacher or \
               obj.class_name in request.user.teacher.assigned_classes.all()

class IsHOD(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and \
               hasattr(request.user, 'teacher') and \
               request.user.teacher.is_hod

    def has_object_permission(self, request, view, obj):
        return obj.department == request.user.teacher.department

class IsSchoolAdmin(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and \
               hasattr(request.user, 'staff') and \
               request.user.staff.role == 'admin'

class IsBursar(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and \
               hasattr(request.user, 'staff') and \
               request.user.staff.role == 'BURSAR'

class IsRegistrar(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and \
               hasattr(request.user, 'staff') and \
               request.user.staff.role == 'REGISTRAR'

class IsSchoolBranchAdmin(permissions.BasePermission):
    """
    Permission class to check if user is a school branch admin.
    """
    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and
            hasattr(request.user, 'staff_profile') and
            request.user.staff_profile and
            request.user.staff_profile.is_admin
        )

class IsSchoolBranchStaff(permissions.BasePermission):
    """
    Permission class to check if user is a school branch staff member.
    """
    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and
            hasattr(request.user, 'staff_profile') and
            request.user.staff_profile is not None
        )

class IsAdminOrSuperUser(permissions.BasePermission):
    """
    Permission class to check if user is an admin or superuser.
    """
    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and
            (request.user.is_superuser or request.user.is_staff)
        )

class IsSuperUserOrSchoolAdmin(permissions.BasePermission):
    """
    Permission class to check if user is a superuser or school admin.
    """
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False

        # Allow superusers full access
        if request.user.is_superuser:
            return True

        # Check if user is a school admin
        return (
            hasattr(request.user, 'staff_profile') and
            request.user.staff_profile and
            request.user.staff_profile.is_admin
        )

    def has_object_permission(self, request, view, obj):
        if request.user.is_superuser:
            return True

        # For school-specific objects
        if hasattr(obj, 'school'):
            return hasattr(request.user, 'school_branch') and obj.school == request.user.school_branch.school

        # For branch-specific objects
        if hasattr(obj, 'school_branch'):
            return hasattr(request.user, 'school_branch') and obj.school_branch == request.user.school_branch

        return False


class RoleBasedPermissionMixin:
    """Mixin to check permissions based on user groups"""

    required_groups = []  # List of group names required to access this view

    def get_permissions(self):
        """Add HasRequiredGroups to the permission classes."""
        permissions = super().get_permissions()
        permissions.append(HasRequiredGroups())
        return permissions


class IsInGroup(permissions.BasePermission):
    """Permission class to check if user is in a specific group"""

    def __init__(self, group_name):
        self.group_name = group_name

    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False

        # Allow superusers full access
        if request.user.is_superuser:
            return True

        # Check if user is in the specified group
        return request.user.groups.filter(name=self.group_name).exists()
