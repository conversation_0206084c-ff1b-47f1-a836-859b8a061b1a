from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.db.models import Count
from core.models import CustomUser
from schools.models import SchoolBranch
from library.models import Book, EResource
from inventory.models import Asset, Supply
from communication.models import Announcement, Message

class DashboardMetricsView(APIView):
    """
    API view to get dashboard metrics
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        # Get the user's school branch
        school_branch = request.user.school_branch
        
        if not school_branch:
            return Response(
                {"error": "User is not associated with any school branch"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get metrics based on the user's school branch
        metrics = {
            "users": {
                "total": CustomUser.objects.filter(school_branch=school_branch).count(),
                "active": CustomUser.objects.filter(school_branch=school_branch, is_active=True).count(),
                "staff": CustomUser.objects.filter(school_branch=school_branch, is_staff=True).count(),
            },
            "library": {
                "books": Book.objects.filter(school_branch=school_branch).count(),
                "e_resources": EResource.objects.filter(school_branch=school_branch).count(),
            },
            "inventory": {
                "assets": Asset.objects.filter(school_branch=school_branch).count(),
                "supplies": Supply.objects.filter(school_branch=school_branch).count(),
            },
            "communication": {
                "announcements": Announcement.objects.filter(school_branch=school_branch).count(),
                "messages": Message.objects.filter(school_branch=school_branch).count(),
            }
        }
        
        return Response(metrics, status=status.HTTP_200_OK)
