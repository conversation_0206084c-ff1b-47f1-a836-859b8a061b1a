from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from core.models import CustomUser
from users.models import Student, Teacher, Parent, Staff
from academics.models import Class, Stream, Subject
from schools.models import School, SchoolBranch

class Command(BaseCommand):
    help = 'Set up default groups and permissions for the application'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up default groups and permissions...'))
        
        # Create default groups
        self.create_default_groups()
        
        # Assign permissions to groups
        self.assign_permissions()
        
        self.stdout.write(self.style.SUCCESS('Successfully set up default groups and permissions!'))
    
    def create_default_groups(self):
        """Create the default groups for the application"""
        default_groups = [
            # Top-level groups
            'Administration',
            'Academic Staff',
            'Support Staff',
            'Students',
            'Parents',
            'Finance',
            'Logistics',
            
            # Sub-groups
            'School Administrators',
            'Department Heads',
            'System Administrators',
            'Teachers',
            'Librarians',
            'Counselors',
            'IT Support',
            'Administrative Assistants',
            'Maintenance',
            'Accountants',
            'Bursars',
            'Fee Collectors',
            'Transport Managers',
            'Inventory Managers',
            'Facility Managers',
        ]
        
        for group_name in default_groups:
            Group.objects.get_or_create(name=group_name)
            self.stdout.write(self.style.SUCCESS(f'Created group: {group_name}'))
    
    def assign_permissions(self):
        """Assign permissions to the default groups"""
        # Get content types for models
        student_ct = ContentType.objects.get_for_model(Student)
        teacher_ct = ContentType.objects.get_for_model(Teacher)
        parent_ct = ContentType.objects.get_for_model(Parent)
        staff_ct = ContentType.objects.get_for_model(Staff)
        class_ct = ContentType.objects.get_for_model(Class)
        stream_ct = ContentType.objects.get_for_model(Stream)
        subject_ct = ContentType.objects.get_for_model(Subject)
        school_ct = ContentType.objects.get_for_model(School)
        branch_ct = ContentType.objects.get_for_model(SchoolBranch)
        user_ct = ContentType.objects.get_for_model(CustomUser)
        
        # Administration group permissions
        admin_group = Group.objects.get(name='Administration')
        admin_permissions = Permission.objects.filter(
            content_type__in=[
                student_ct, teacher_ct, parent_ct, staff_ct, 
                class_ct, stream_ct, subject_ct, school_ct, branch_ct, user_ct
            ]
        )
        admin_group.permissions.set(admin_permissions)
        self.stdout.write(self.style.SUCCESS(f'Assigned {admin_permissions.count()} permissions to Administration group'))
        
        # School Administrators group permissions
        school_admin_group = Group.objects.get(name='School Administrators')
        school_admin_permissions = Permission.objects.filter(
            content_type__in=[
                student_ct, teacher_ct, parent_ct, staff_ct, 
                class_ct, stream_ct, subject_ct, branch_ct
            ]
        )
        school_admin_group.permissions.set(school_admin_permissions)
        self.stdout.write(self.style.SUCCESS(f'Assigned {school_admin_permissions.count()} permissions to School Administrators group'))
        
        # Academic Staff group permissions
        academic_group = Group.objects.get(name='Academic Staff')
        academic_permissions = Permission.objects.filter(
            content_type__in=[student_ct, class_ct, stream_ct, subject_ct]
        ).exclude(codename__startswith='delete')
        academic_group.permissions.set(academic_permissions)
        self.stdout.write(self.style.SUCCESS(f'Assigned {academic_permissions.count()} permissions to Academic Staff group'))
        
        # Teachers group permissions
        teachers_group = Group.objects.get(name='Teachers')
        teacher_permissions = Permission.objects.filter(
            content_type__in=[student_ct, class_ct, stream_ct, subject_ct]
        ).filter(codename__startswith='view') | Permission.objects.filter(
            content_type__in=[student_ct, class_ct, stream_ct, subject_ct]
        ).filter(codename__startswith='change')
        teachers_group.permissions.set(teacher_permissions)
        self.stdout.write(self.style.SUCCESS(f'Assigned {teacher_permissions.count()} permissions to Teachers group'))
        
        # Students group permissions
        students_group = Group.objects.get(name='Students')
        student_permissions = Permission.objects.filter(
            content_type__in=[student_ct, class_ct, stream_ct, subject_ct]
        ).filter(codename__startswith='view')
        students_group.permissions.set(student_permissions)
        self.stdout.write(self.style.SUCCESS(f'Assigned {student_permissions.count()} permissions to Students group'))
        
        # Parents group permissions
        parents_group = Group.objects.get(name='Parents')
        parent_permissions = Permission.objects.filter(
            content_type__in=[student_ct, class_ct, stream_ct, subject_ct]
        ).filter(codename__startswith='view')
        parents_group.permissions.set(parent_permissions)
        self.stdout.write(self.style.SUCCESS(f'Assigned {parent_permissions.count()} permissions to Parents group'))
        
        # Finance group permissions
        finance_group = Group.objects.get(name='Finance')
        finance_permissions = Permission.objects.filter(
            content_type__in=[student_ct]
        ).filter(codename__startswith='view') | Permission.objects.filter(
            content_type__in=[student_ct]
        ).filter(codename__startswith='change')
        finance_group.permissions.set(finance_permissions)
        self.stdout.write(self.style.SUCCESS(f'Assigned {finance_permissions.count()} permissions to Finance group'))
        
        # Support Staff group permissions
        support_group = Group.objects.get(name='Support Staff')
        support_permissions = Permission.objects.filter(
            content_type__in=[student_ct, teacher_ct, parent_ct]
        ).filter(codename__startswith='view')
        support_group.permissions.set(support_permissions)
        self.stdout.write(self.style.SUCCESS(f'Assigned {support_permissions.count()} permissions to Support Staff group'))
