from django.core.management.base import BaseCommand
from academics.curriculum_models import CurriculumSystem, EducationLevel

class Command(BaseCommand):
    help = 'Creates education levels for the 8-4-4 curriculum system'

    def handle(self, *args, **options):
        # Get the 8-4-4 curriculum system
        try:
            curriculum_system = CurriculumSystem.objects.get(code='844')
            self.stdout.write(self.style.SUCCESS(f'Found 8-4-4 curriculum system: {curriculum_system.name}'))
        except CurriculumSystem.DoesNotExist:
            self.stdout.write(self.style.ERROR('8-4-4 curriculum system not found. Please create it first.'))
            return

        # Define the education levels for 8-4-4
        education_levels = [
            # Primary School (8 years)
            {'name': 'Standard 1', 'code': '844_STD1', 'sequence': 1, 'stage_code': 'PRIMARY'},
            {'name': 'Standard 2', 'code': '844_STD2', 'sequence': 2, 'stage_code': 'PRIMARY'},
            {'name': 'Standard 3', 'code': '844_STD3', 'sequence': 3, 'stage_code': 'PRIMARY'},
            {'name': 'Standard 4', 'code': '844_STD4', 'sequence': 4, 'stage_code': 'PRIMARY'},
            {'name': 'Standard 5', 'code': '844_STD5', 'sequence': 5, 'stage_code': 'PRIMARY'},
            {'name': 'Standard 6', 'code': '844_STD6', 'sequence': 6, 'stage_code': 'PRIMARY'},
            {'name': 'Standard 7', 'code': '844_STD7', 'sequence': 7, 'stage_code': 'PRIMARY'},
            {'name': 'Standard 8', 'code': '844_STD8', 'sequence': 8, 'stage_code': 'PRIMARY'},

            # Secondary School (4 years)
            {'name': 'Form 1', 'code': '844_FORM1', 'sequence': 9, 'stage_code': 'SECONDARY'},
            {'name': 'Form 2', 'code': '844_FORM2', 'sequence': 10, 'stage_code': 'SECONDARY'},
            {'name': 'Form 3', 'code': '844_FORM3', 'sequence': 11, 'stage_code': 'SECONDARY'},
            {'name': 'Form 4', 'code': '844_FORM4', 'sequence': 12, 'stage_code': 'SECONDARY'},
        ]

        # Create the education levels
        created_count = 0
        for level_data in education_levels:
            # Check if the level already exists
            existing = EducationLevel.objects.filter(
                code=level_data['code'],
                curriculum_system=curriculum_system
            ).exists()

            if not existing:
                try:
                    EducationLevel.objects.create(
                        name=level_data['name'],
                        code=level_data['code'],
                        sequence=level_data['sequence'],
                        stage_code=level_data['stage_code'],
                        curriculum_system=curriculum_system
                    )
                    created_count += 1
                    self.stdout.write(self.style.SUCCESS(f'Created education level: {level_data["name"]}'))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Error creating {level_data["name"]}: {str(e)}'))
            else:
                self.stdout.write(self.style.WARNING(f'Education level already exists: {level_data["name"]}'))

        self.stdout.write(self.style.SUCCESS(f'Successfully created {created_count} education levels for 8-4-4 curriculum system'))
