#!/usr/bin/env python
"""
ShuleXcel MVP Setup Script

This script sets up the minimum viable product (MVP) data for ShuleXcel.
It creates essential data needed for the MVP deployment.
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Backend.settings')
    django.setup()

def run_command(command):
    """Run a Django management command"""
    print(f"Running: {command}")
    try:
        execute_from_command_line(command.split())
        print(f"✅ Successfully completed: {command}")
        return True
    except Exception as e:
        print(f"❌ Error running {command}: {e}")
        return False

def setup_mvp():
    """Setup MVP data"""
    print("🚀 Setting up ShuleXcel MVP...")
    print("=" * 50)
    
    # List of commands to run for MVP setup
    commands = [
        "python manage.py migrate",
        "python manage.py collectstatic --noinput",
        "python manage.py seed_basic_data",
        "python manage.py seed_academic_data",
        "python manage.py seed_curriculum_systems",
        "python manage.py seed_curriculum_subjects",
    ]
    
    success_count = 0
    total_commands = len(commands)
    
    for command in commands:
        if run_command(command):
            success_count += 1
        print("-" * 30)
    
    print("\n" + "=" * 50)
    print(f"MVP Setup Complete: {success_count}/{total_commands} commands successful")
    
    if success_count == total_commands:
        print("🎉 MVP setup completed successfully!")
        print("\nDefault Admin Credentials:")
        print("Email: <EMAIL>")
        print("Password: admin123")
        print("\nYou can now start the server with:")
        print("python manage.py runserver")
    else:
        print("⚠️  Some commands failed. Please check the errors above.")
        return False
    
    return True

if __name__ == "__main__":
    setup_django()
    setup_mvp()
