import random
from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group
from core.models import CustomUser
from schools.models import School, SchoolBranch
from academics.models import AcademicYear, GradingSystem, GradeScale, Term, Stream, Class
from datetime import date, timedelta

class Command(BaseCommand):
    help = 'Seeds the database with academic data for development'

    def add_arguments(self, parser):
        parser.add_argument(
            '--flush',
            action='store_true',
            help='Flush existing academic data before seeding',
        )

    def handle(self, *args, **options):
        if options['flush']:
            self.stdout.write(self.style.WARNING('Flushing existing academic data...'))
            # Be careful with this in production!
            # This is only for development purposes
            AcademicYear.objects.all().delete()
            GradingSystem.objects.all().delete()
            GradeScale.objects.all().delete()
            Term.objects.all().delete()
            Stream.objects.all().delete()
            Class.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Academic data flushed successfully'))

        # Get or create basic data first
        school = self.get_or_create_school()
        admin_user = self.get_admin_user()

        # Create a teacher for the grading system
        teacher = self.create_teacher(admin_user)

        # Create academic data
        academic_year = self.create_academic_year(school)
        terms = self.create_terms(academic_year)
        grading_system = self.create_grading_system(school, academic_year, terms, teacher)
        streams = self.create_streams(school)
        classes = self.create_classes(school, streams, admin_user)

        # Associate classes with grading system
        for class_obj in classes:
            grading_system.classes.add(class_obj)

        self.stdout.write(self.style.SUCCESS('Academic database seeded successfully'))

    def get_or_create_school(self):
        """Get or create the main school"""
        self.stdout.write('Getting or creating school...')

        school, created = School.objects.get_or_create(
            name='ShuleXcel Academy',
            defaults={
                'code': 'SXA',
                'address': '123 Education Lane, Nairobi',
                'phone': '+254700000000',
                'email': '<EMAIL>',
                'registration_number': 'REG12345',
                'established_date': date(2020, 1, 1),
            }
        )

        if created:
            self.stdout.write(f'  Created school: {school.name}')
        else:
            self.stdout.write(f'  Using existing school: {school.name}')

        return school

    def get_admin_user(self):
        """Get the admin user or create one if it doesn't exist"""
        self.stdout.write('Getting admin user...')

        try:
            admin_user = CustomUser.objects.get(email='<EMAIL>')
            self.stdout.write(f'  Using existing admin user: {admin_user.email}')
        except CustomUser.DoesNotExist:
            # Get a branch
            branch = SchoolBranch.objects.first()
            if not branch:
                self.stdout.write(self.style.ERROR('No school branch found. Please run seed_basic_data first.'))
                return None

            # Temporarily disconnect the signal
            from django.db.models.signals import post_save
            from users.signals import save_user_role_profile

            # Disconnect the signal
            post_save.disconnect(save_user_role_profile, sender=CustomUser)

            # Create admin user
            admin_user = CustomUser(
                username='<EMAIL>',
                email='<EMAIL>',
                first_name='Admin',
                last_name='User',
                is_staff=True,
                is_superuser=True,
                phone='+254700000000',
                user_type='admin',
                school_branch=branch
            )
            admin_user.set_password('admin123')
            admin_user.save()

            # Reconnect the signal
            post_save.connect(save_user_role_profile, sender=CustomUser)

            self.stdout.write(f'  Created admin user: {admin_user.email}')

        return admin_user

    def create_teacher(self, admin_user):
        """Create a teacher for the grading system"""
        self.stdout.write('Creating teacher for grading system...')

        # Import the necessary models
        from users.models import Teacher
        from academics.models import Department
        from datetime import date

        # Get the school
        school = admin_user.school_branch.school

        # Get or create a department
        department, created = Department.objects.get_or_create(
            name='Academic Department',
            school=school,
            defaults={
                'code': 'ACAD',
                'description': 'Academic Department for Teachers'
            }
        )

        if created:
            self.stdout.write(f'  Created department: {department.name}')
        else:
            self.stdout.write(f'  Using existing department: {department.name}')

        # Check if the teacher already exists
        try:
            teacher = Teacher.objects.get(email='<EMAIL>')
            self.stdout.write(f'  Using existing teacher: {teacher.email}')
        except Teacher.DoesNotExist:
            # Create a new teacher
            teacher = Teacher.objects.create(
                user=admin_user,  # Use the admin user for simplicity
                first_name='Head',
                last_name='Teacher',
                school_branch=admin_user.school_branch,
                department=department,
                is_hod=True,
                is_class_teacher=True,
                teacher_number='TCH001',
                date_of_birth=date(1980, 1, 1),
                national_id='12345678',
                phone_number='+254700000001',
                email='<EMAIL>',
                gender='M'
            )
            self.stdout.write(f'  Created teacher: {teacher.email}')

        return teacher

    def create_academic_year(self, school):
        """Create an academic year"""
        self.stdout.write('Creating academic year...')

        current_year = date.today().year
        academic_year, created = AcademicYear.objects.get_or_create(
            school_name=school,
            year=f"{current_year}-{current_year + 1}",
            defaults={
                'start_date': date(current_year, 1, 1),
                'end_date': date(current_year + 1, 12, 31)
            }
        )

        if created:
            self.stdout.write(f'  Created academic year: {academic_year.year}')
        else:
            self.stdout.write(f'  Academic year already exists: {academic_year.year}')

        return academic_year

    def create_terms(self, academic_year):
        """Create terms for the academic year"""
        self.stdout.write('Creating terms...')

        # Get the school from the academic year
        school = academic_year.school_name

        terms = []
        term_data = [
            {
                'name': 'Term 1',
                'start_date': date(academic_year.start_date.year, 1, 1),
                'end_date': date(academic_year.start_date.year, 4, 30)
            },
            {
                'name': 'Term 2',
                'start_date': date(academic_year.start_date.year, 5, 1),
                'end_date': date(academic_year.start_date.year, 8, 31)
            },
            {
                'name': 'Term 3',
                'start_date': date(academic_year.start_date.year, 9, 1),
                'end_date': date(academic_year.start_date.year, 12, 31)
            }
        ]

        for term_info in term_data:
            term, created = Term.objects.get_or_create(
                name=term_info['name'],
                academic_year=academic_year,
                school=school,
                defaults={
                    'start_date': term_info['start_date'],
                    'end_date': term_info['end_date']
                }
            )

            terms.append(term)

            if created:
                self.stdout.write(f'  Created term: {term.name}')
            else:
                self.stdout.write(f'  Term already exists: {term.name}')

        return terms

    def create_grading_system(self, school, academic_year, terms, teacher):
        """Create a grading system with grade scales"""
        self.stdout.write('Creating grading system...')

        grading_system, created = GradingSystem.objects.get_or_create(
            name='Standard Grading',
            school=school,
            AcademicYear=academic_year,
            created_by=teacher,
            defaults={
                'description': 'Standard grading system for all classes',
                'is_active': True
            }
        )

        if created:
            self.stdout.write(f'  Created grading system: {grading_system.name}')

            # Add terms to the grading system
            for term in terms:
                grading_system.terms.add(term)

            # Create grade scales
            grade_scales = [
                {'grade': 'A', 'min_score': 80, 'max_score': 100, 'points': 12.0, 'remarks': 'Excellent'},
                {'grade': 'A-', 'min_score': 75, 'max_score': 79.99, 'points': 11.0, 'remarks': 'Very Good'},
                {'grade': 'B+', 'min_score': 70, 'max_score': 74.99, 'points': 10.0, 'remarks': 'Good'},
                {'grade': 'B', 'min_score': 65, 'max_score': 69.99, 'points': 9.0, 'remarks': 'Good'},
                {'grade': 'B-', 'min_score': 60, 'max_score': 64.99, 'points': 8.0, 'remarks': 'Above Average'},
                {'grade': 'C+', 'min_score': 55, 'max_score': 59.99, 'points': 7.0, 'remarks': 'Average'},
                {'grade': 'C', 'min_score': 50, 'max_score': 54.99, 'points': 6.0, 'remarks': 'Average'},
                {'grade': 'C-', 'min_score': 45, 'max_score': 49.99, 'points': 5.0, 'remarks': 'Below Average'},
                {'grade': 'D+', 'min_score': 40, 'max_score': 44.99, 'points': 4.0, 'remarks': 'Below Average'},
                {'grade': 'D', 'min_score': 35, 'max_score': 39.99, 'points': 3.0, 'remarks': 'Poor'},
                {'grade': 'D-', 'min_score': 30, 'max_score': 34.99, 'points': 2.0, 'remarks': 'Poor'},
                {'grade': 'E', 'min_score': 0, 'max_score': 29.99, 'points': 1.0, 'remarks': 'Very Poor'}
            ]

            for scale in grade_scales:
                GradeScale.objects.create(
                    grading_system=grading_system,
                    grade=scale['grade'],
                    min_score=scale['min_score'],
                    max_score=scale['max_score'],
                    points=scale['points'],
                    remarks=scale['remarks']
                )
                self.stdout.write(f'    Created grade scale: {scale["grade"]}')
        else:
            self.stdout.write(f'  Grading system already exists: {grading_system.name}')

        return grading_system

    def create_streams(self, school):
        """Create streams for the school"""
        self.stdout.write('Creating streams...')

        streams = []
        stream_data = [
            {'name': 'Stream A', 'code': 'SA'},
            {'name': 'Stream B', 'code': 'SB'},
            {'name': 'Stream C', 'code': 'SC'},
            {'name': 'Stream D', 'code': 'SD'}
        ]

        for stream_info in stream_data:
            stream, created = Stream.objects.get_or_create(
                name=stream_info['name'],
                code=stream_info['code'],
                school=school,
                defaults={
                    'description': f'Standard {stream_info["name"]}'
                }
            )

            streams.append(stream)

            if created:
                self.stdout.write(f'  Created stream: {stream.name}')
            else:
                self.stdout.write(f'  Stream already exists: {stream.name}')

        return streams

    def create_classes(self, school, streams, admin_user):
        """Create classes for the school"""
        self.stdout.write('Creating classes...')

        # Get the academic year
        academic_year = AcademicYear.objects.filter(school_name=school).first()
        if not academic_year:
            self.stdout.write(self.style.ERROR('No academic year found. Skipping class creation.'))
            return []

        # Get the teacher
        from users.models import Teacher
        try:
            teacher = Teacher.objects.get(email='<EMAIL>')
            self.stdout.write(f'  Using existing teacher: {teacher.email}')
        except Teacher.DoesNotExist:
            self.stdout.write(self.style.ERROR('No teacher found. Skipping class creation.'))
            return []

        classes = []
        class_data = [
            {'name': 'Grade 1', 'grade_level': 1},
            {'name': 'Grade 2', 'grade_level': 2},
            {'name': 'Grade 3', 'grade_level': 3},
            {'name': 'Grade 4', 'grade_level': 4},
            {'name': 'Grade 5', 'grade_level': 5},
            {'name': 'Grade 6', 'grade_level': 6},
            {'name': 'Form 1', 'grade_level': 7},
            {'name': 'Form 2', 'grade_level': 8},
            {'name': 'Form 3', 'grade_level': 9},
            {'name': 'Form 4', 'grade_level': 10}
        ]

        for class_info in class_data:
            # Use the first stream for simplicity
            stream = streams[0] if streams else None

            if not stream:
                self.stdout.write(self.style.ERROR('No streams found. Skipping class creation.'))
                break

            try:
                class_obj, created = Class.objects.get_or_create(
                    name=class_info['name'],
                    school=school,
                    stream=stream,
                    academic_year=academic_year,
                    defaults={
                        'grade_level': class_info['grade_level'],
                        'class_teacher': teacher
                    }
                )

                classes.append(class_obj)

                if created:
                    self.stdout.write(f'  Created class: {class_obj.name}')
                else:
                    self.stdout.write(f'  Class already exists: {class_obj.name}')
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'  Error creating class {class_info["name"]}: {str(e)}'))

        return classes
