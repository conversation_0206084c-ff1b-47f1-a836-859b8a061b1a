from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework_simplejwt.views import TokenRefreshView, TokenVerifyView
from .views import UserViewSet, AuthViewSet, TeacherViewset, NoticeViewset, DashboardViewSet
from .simple_password_reset_views import SimplePasswordResetView
from .token_validation_views import ValidateTokenView, ResetPasswordWithTokenView

router = DefaultRouter()
router.register('users', UserViewSet, basename='user')
router.register('auth', AuthViewSet, basename='auth')
router.register('teachers', TeacherViewset, basename='teacher')
router.register('notices', NoticeViewset, basename='notice')
router.register('dashboard', DashboardViewSet, basename='dashboard')

urlpatterns = [
    path('', include(router.urls)),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('token/verify/', TokenVerifyView.as_view(), name='token_verify'),
    # Add auth/token endpoints for compatibility with frontend
    path('auth/token/refresh/', TokenRefreshView.as_view(), name='auth_token_refresh'),
    path('auth/token/verify/', TokenVerifyView.as_view(), name='auth_token_verify'),
    # Direct login endpoint for frontend compatibility
    path('auth/login/', AuthViewSet.as_view({'post': 'login'}), name='auth_login'),

    # Password reset endpoints
    path('password-reset-direct/', SimplePasswordResetView.as_view(), name='password_reset_direct'),
    path('password-reset/validate-token/', ValidateTokenView.as_view(), name='validate_reset_token'),
    path('password-reset/reset-with-token/', ResetPasswordWithTokenView.as_view(), name='reset_with_token'),

    # Include the API URLs
    path('api/', include('core.api.urls')),
]
