from django.db import models
from django.utils import timezone
from schools.models import SchoolBranch
from core.models import CustomUser

class Announcement(models.Model):
    ANNOUNCEMENT_TYPE_CHOICES = [
        ('GENERAL', 'General'),
        ('ACADEMIC', 'Academic'),
        ('EVENT', 'Event'),
        ('EMERGENCY', 'Emergency'),
        ('HOLIDAY', 'Holiday'),
    ]

    PRIORITY_CHOICES = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('URGENT', 'Urgent'),
    ]

    TARGET_AUDIENCE_CHOICES = [
        ('ALL', 'All'),
        ('STAFF', 'Staff'),
        ('TEACHERS', 'Teachers'),
        ('STUDENTS', 'Students'),
        ('PARENTS', 'Parents'),
        ('SPECIFIC_CLASS', 'Specific Class'),
    ]

    title = models.CharField(max_length=255)
    content = models.TextField()
    announcement_type = models.CharField(max_length=20, choices=ANNOUNCEMENT_TYPE_CHOICES, default='GENERAL')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='MEDIUM')
    target_audience = models.CharField(max_length=20, choices=TARGET_AUDIENCE_CHOICES, default='ALL')
    target_class = models.ForeignKey('academics.ClassRoom', on_delete=models.SET_NULL, null=True, blank=True, related_name='announcements')
    target_stream = models.ForeignKey('academics.Stream', on_delete=models.SET_NULL, null=True, blank=True, related_name='announcements')
    publish_date = models.DateTimeField(default=timezone.now)
    expiry_date = models.DateTimeField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    attachment = models.FileField(upload_to='announcements/', blank=True, null=True)
    created_by = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='created_announcements')
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='announcements')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

    class Meta:
        ordering = ['-publish_date']
        indexes = [
            models.Index(fields=['announcement_type']),
            models.Index(fields=['priority']),
            models.Index(fields=['target_audience']),
            models.Index(fields=['publish_date']),
            models.Index(fields=['is_active']),
        ]

class Message(models.Model):
    MESSAGE_STATUS_CHOICES = [
        ('SENT', 'Sent'),
        ('DELIVERED', 'Delivered'),
        ('READ', 'Read'),
        ('FAILED', 'Failed'),
    ]

    sender = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='sent_messages')
    recipient = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='received_messages')
    subject = models.CharField(max_length=255, blank=True, null=True)
    content = models.TextField()
    attachment = models.FileField(upload_to='messages/', blank=True, null=True)
    status = models.CharField(max_length=10, choices=MESSAGE_STATUS_CHOICES, default='SENT')
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(blank=True, null=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='messages')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Message from {self.sender} to {self.recipient}"

    def mark_as_read(self):
        self.is_read = True
        self.read_at = timezone.now()
        self.status = 'READ'
        self.save()

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['sender']),
            models.Index(fields=['recipient']),
            models.Index(fields=['is_read']),
            models.Index(fields=['created_at']),
        ]

class Email(models.Model):
    EMAIL_STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('QUEUED', 'Queued'),
        ('SENT', 'Sent'),
        ('FAILED', 'Failed'),
    ]

    subject = models.CharField(max_length=255)
    content = models.TextField()
    sender = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='sent_emails')
    recipients = models.TextField(help_text="Comma-separated list of email addresses")
    cc = models.TextField(blank=True, null=True, help_text="Comma-separated list of CC email addresses")
    bcc = models.TextField(blank=True, null=True, help_text="Comma-separated list of BCC email addresses")
    attachments = models.FileField(upload_to='email_attachments/', blank=True, null=True)
    status = models.CharField(max_length=10, choices=EMAIL_STATUS_CHOICES, default='DRAFT')
    sent_at = models.DateTimeField(blank=True, null=True)
    error_message = models.TextField(blank=True, null=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='emails')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.subject

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['sender']),
            models.Index(fields=['status']),
            models.Index(fields=['sent_at']),
        ]

class SMS(models.Model):
    SMS_STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('QUEUED', 'Queued'),
        ('SENT', 'Sent'),
        ('FAILED', 'Failed'),
    ]

    content = models.TextField()
    sender = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='sent_sms')
    recipients = models.TextField(help_text="Comma-separated list of phone numbers")
    status = models.CharField(max_length=10, choices=SMS_STATUS_CHOICES, default='DRAFT')
    sent_at = models.DateTimeField(blank=True, null=True)
    error_message = models.TextField(blank=True, null=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='sms')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"SMS from {self.sender} to {len(self.recipients.split(','))} recipients"

    class Meta:
        verbose_name_plural = 'SMS'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['sender']),
            models.Index(fields=['status']),
            models.Index(fields=['sent_at']),
        ]

class Newsletter(models.Model):
    NEWSLETTER_STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('PUBLISHED', 'Published'),
        ('ARCHIVED', 'Archived'),
    ]

    title = models.CharField(max_length=255)
    content = models.TextField()
    issue_number = models.PositiveIntegerField(blank=True, null=True)
    publish_date = models.DateField(default=timezone.now)
    status = models.CharField(max_length=10, choices=NEWSLETTER_STATUS_CHOICES, default='DRAFT')
    cover_image = models.ImageField(upload_to='newsletter_covers/', blank=True, null=True)
    attachment = models.FileField(upload_to='newsletters/', blank=True, null=True)
    created_by = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='created_newsletters')
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='newsletters')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

    class Meta:
        ordering = ['-publish_date']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['publish_date']),
        ]

class Event(models.Model):
    EVENT_TYPE_CHOICES = [
        ('ACADEMIC', 'Academic'),
        ('SPORTS', 'Sports'),
        ('CULTURAL', 'Cultural'),
        ('MEETING', 'Meeting'),
        ('HOLIDAY', 'Holiday'),
        ('OTHER', 'Other'),
    ]

    title = models.CharField(max_length=255)
    description = models.TextField()
    event_type = models.CharField(max_length=20, choices=EVENT_TYPE_CHOICES, default='OTHER')
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    location = models.CharField(max_length=255, blank=True, null=True)
    is_all_day = models.BooleanField(default=False)
    is_recurring = models.BooleanField(default=False)
    recurrence_pattern = models.CharField(max_length=255, blank=True, null=True, help_text="e.g., 'RRULE:FREQ=WEEKLY;BYDAY=MO,WE,FR'")
    organizer = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='organized_events')
    attendees = models.ManyToManyField(CustomUser, related_name='attending_events', blank=True)
    is_public = models.BooleanField(default=True)
    attachment = models.FileField(upload_to='events/', blank=True, null=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='events')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

    class Meta:
        ordering = ['start_date']
        indexes = [
            models.Index(fields=['event_type']),
            models.Index(fields=['is_recurring']),
            models.Index(fields=['is_all_day']),
            models.Index(fields=['start_date']),
            models.Index(fields=['end_date']),
            models.Index(fields=['is_public']),
        ]

class ParentTeacherMeeting(models.Model):
    MEETING_STATUS_CHOICES = [
        ('SCHEDULED', 'Scheduled'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
        ('RESCHEDULED', 'Rescheduled'),
    ]

    title = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    teacher = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='teacher_meetings')
    parent = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='parent_meetings')
    # Temporarily use CharField for student until the Student model is available
    student_name = models.CharField(max_length=255)
    # student = models.ForeignKey('users.Student', on_delete=models.CASCADE, related_name='parent_teacher_meetings')
    scheduled_date = models.DateTimeField()
    duration_minutes = models.PositiveIntegerField(default=30)
    location = models.CharField(max_length=255, blank=True, null=True)
    is_virtual = models.BooleanField(default=False)
    meeting_link = models.URLField(blank=True, null=True)
    status = models.CharField(max_length=15, choices=MEETING_STATUS_CHOICES, default='SCHEDULED')
    notes = models.TextField(blank=True, null=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='parent_teacher_meetings')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Meeting between {self.teacher.get_full_name()} and {self.parent.get_full_name()} about {self.student_name}"

    class Meta:
        ordering = ['-scheduled_date']
        indexes = [
            models.Index(fields=['teacher']),
            models.Index(fields=['parent']),
            models.Index(fields=['student_name']),
            models.Index(fields=['scheduled_date']),
            models.Index(fields=['status']),
        ]

class Notice(models.Model):
    title = models.CharField(max_length=200)
    content = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title