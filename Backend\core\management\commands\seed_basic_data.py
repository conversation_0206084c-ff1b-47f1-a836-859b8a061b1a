import random
from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group
from core.models import CustomUser
from schools.models import SchoolBranch, School
from datetime import date

class Command(BaseCommand):
    help = 'Seeds the database with basic data for development'

    def add_arguments(self, parser):
        parser.add_argument(
            '--flush',
            action='store_true',
            help='Flush existing data before seeding',
        )

    def handle(self, *args, **options):
        if options['flush']:
            self.stdout.write(self.style.WARNING('Flushing existing data...'))
            # Be careful with this in production!
            # This is only for development purposes
            CustomUser.objects.all().delete()
            Group.objects.all().delete()
            SchoolBranch.objects.all().delete()
            School.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Data flushed successfully'))

        self.create_groups()
        self.create_school_branches()
        self.create_admin_user()

        self.stdout.write(self.style.SUCCESS('Basic database seeded successfully'))

    def create_groups(self):
        self.stdout.write('Creating user groups...')
        groups = [
            'Administration',
            'Teachers',
            'Students',
            'Parents',
            'Finance',
            'Support',
            'Logistics'
        ]

        created_groups = []
        for group_name in groups:
            group, created = Group.objects.get_or_create(name=group_name)
            created_groups.append(group)
            if created:
                self.stdout.write(f'  Created group: {group_name}')
            else:
                self.stdout.write(f'  Group already exists: {group_name}')

        return created_groups

    def create_school_branches(self):
        self.stdout.write('Creating school branches...')

        # First, create a school
        school, created = School.objects.get_or_create(
            name='ShuleXcel Academy',
            defaults={
                'code': 'SXA',
                'address': '123 Education Lane, Nairobi',
                'phone': '+254700000000',
                'email': '<EMAIL>',
                'registration_number': 'REG12345',
                'established_date': date(2020, 1, 1),
            }
        )

        if created:
            self.stdout.write(f'  Created school: {school.name}')
        else:
            self.stdout.write(f'  School already exists: {school.name}')

        branches = [
            {
                'name': 'Main Campus',
                'code': 'MC',
                'address': '123 Main St, Nairobi',
                'phone': '+254700000001',
                'email': '<EMAIL>',
                'registration_number': 'BR001',
                'established_date': date(2020, 1, 1),
                'school': school,
            },
            {
                'name': 'South Campus',
                'code': 'SC',
                'address': '456 South Ave, Nairobi',
                'phone': '+254700000002',
                'email': '<EMAIL>',
                'registration_number': 'BR002',
                'established_date': date(2021, 6, 15),
                'school': school,
            },
            {
                'name': 'East Campus',
                'code': 'EC',
                'address': '789 East Blvd, Nairobi',
                'phone': '+254700000003',
                'email': '<EMAIL>',
                'registration_number': 'BR003',
                'established_date': date(2022, 9, 1),
                'school': school,
            }
        ]

        created_branches = []
        for branch_data in branches:
            branch, created = SchoolBranch.objects.get_or_create(
                name=branch_data['name'],
                defaults=branch_data
            )
            created_branches.append(branch)
            if created:
                self.stdout.write(f'  Created branch: {branch.name}')
            else:
                self.stdout.write(f'  Branch already exists: {branch.name}')

        return created_branches

    def create_admin_user(self):
        self.stdout.write('Creating admin user...')

        # Get admin group
        admin_group = Group.objects.get(name='Administration')

        # Get a branch
        branch = SchoolBranch.objects.first()

        # Check if admin user already exists
        if CustomUser.objects.filter(email='<EMAIL>').exists():
            admin_user = CustomUser.objects.get(email='<EMAIL>')
            self.stdout.write(f'  Admin user already exists: {admin_user.email}')
        else:
            # Temporarily disconnect the signal
            from django.db.models.signals import post_save
            from users.signals import save_user_role_profile

            # Disconnect the signal
            post_save.disconnect(save_user_role_profile, sender=CustomUser)

            # Create admin user
            admin_user = CustomUser(
                username='<EMAIL>',
                email='<EMAIL>',
                first_name='Admin',
                last_name='User',
                is_staff=True,
                is_superuser=True,
                phone='+254700000000',
                user_type='admin',
                school_branch=branch
            )
            admin_user.set_password('admin123')
            admin_user.save()

            # Reconnect the signal
            post_save.connect(save_user_role_profile, sender=CustomUser)

            self.stdout.write(f'  Created admin user: {admin_user.email}')

        # Add to admin group
        admin_user.groups.add(admin_group)

        self.stdout.write(f'  Created admin user: {admin_user.email}')
