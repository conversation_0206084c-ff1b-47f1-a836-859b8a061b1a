"""
Custom authentication backends for ShuleXcel.
"""
from django.contrib.auth.backends import ModelBackend
from django.contrib.auth import get_user_model
from django.db.models import Q
import logging

logger = logging.getLogger(__name__)
UserModel = get_user_model()


class EmailAuthBackend(ModelBackend):
    """
    Custom authentication backend that allows users to log in using their email address.
    """
    
    def authenticate(self, request, username=None, password=None, email=None, **kwargs):
        """
        Authenticate a user using email and password.
        
        Args:
            request: The HTTP request object
            username: Username (can be email)
            password: User's password
            email: User's email address
            **kwargs: Additional keyword arguments
            
        Returns:
            User object if authentication successful, None otherwise
        """
        if email is None:
            email = username
            
        if email is None or password is None:
            return None
            
        try:
            # Try to find user by email or username
            user = UserModel.objects.get(
                Q(email__iexact=email) | Q(username__iexact=email)
            )
        except UserModel.DoesNotExist:
            # Run the default password hasher once to reduce the timing
            # difference between an existing and a nonexistent user
            UserModel().set_password(password)
            logger.warning(f"Authentication failed: User with email {email} does not exist")
            return None
        except UserModel.MultipleObjectsReturned:
            logger.error(f"Multiple users found with email {email}")
            return None
            
        # Check if the user is active
        if not user.is_active:
            logger.warning(f"Authentication failed: User {email} is inactive")
            return None
            
        # Check password
        if user.check_password(password):
            logger.info(f"User {email} authenticated successfully")
            return user
        else:
            logger.warning(f"Authentication failed: Invalid password for user {email}")
            return None

    def get_user(self, user_id):
        """
        Get user by ID.
        
        Args:
            user_id: The user's ID
            
        Returns:
            User object if found, None otherwise
        """
        try:
            return UserModel.objects.get(pk=user_id)
        except UserModel.DoesNotExist:
            return None


class SchoolBranchAuthBackend(ModelBackend):
    """
    Authentication backend that also validates school branch access.
    """
    
    def authenticate(self, request, username=None, password=None, school_branch=None, **kwargs):
        """
        Authenticate user and validate school branch access.
        
        Args:
            request: The HTTP request object
            username: Username or email
            password: User's password
            school_branch: School branch ID or code
            **kwargs: Additional keyword arguments
            
        Returns:
            User object if authentication and branch validation successful, None otherwise
        """
        # First authenticate using the email backend
        email_backend = EmailAuthBackend()
        user = email_backend.authenticate(request, username=username, password=password, **kwargs)
        
        if user is None:
            return None
            
        # If school_branch is specified, validate access
        if school_branch:
            if not user.school_branch:
                logger.warning(f"User {user.email} has no school branch assigned")
                return None
                
            # Check if user belongs to the specified branch
            if (str(user.school_branch.id) != str(school_branch) and 
                user.school_branch.code != school_branch):
                logger.warning(f"User {user.email} does not belong to branch {school_branch}")
                return None
                
        return user


class TokenAuthBackend(ModelBackend):
    """
    Authentication backend for API token-based authentication.
    """
    
    def authenticate(self, request, token=None, **kwargs):
        """
        Authenticate user using API token.
        
        Args:
            request: The HTTP request object
            token: API token
            **kwargs: Additional keyword arguments
            
        Returns:
            User object if token is valid, None otherwise
        """
        if not token:
            return None
            
        try:
            # Import here to avoid circular imports
            from rest_framework_simplejwt.tokens import AccessToken
            from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
            
            # Validate the token
            access_token = AccessToken(token)
            user_id = access_token['user_id']
            
            # Get the user
            user = UserModel.objects.get(pk=user_id)
            
            if not user.is_active:
                logger.warning(f"Token authentication failed: User {user.email} is inactive")
                return None
                
            return user
            
        except (InvalidToken, TokenError, UserModel.DoesNotExist) as e:
            logger.warning(f"Token authentication failed: {str(e)}")
            return None
