"""
Custom middleware for ShuleXcel security and functionality.
"""
import logging
import time
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from django.core.cache import cache
from django.conf import settings
from django.contrib.auth import get_user_model
from django_ratelimit.decorators import ratelimit
from django_ratelimit.exceptions import Ratelimited

logger = logging.getLogger(__name__)
User = get_user_model()


class SecurityHeadersMiddleware(MiddlewareMixin):
    """
    Middleware to add security headers to all responses.
    """
    
    def process_response(self, request, response):
        """
        Add security headers to the response.
        """
        # Content Security Policy
        if not settings.DEBUG:
            response['Content-Security-Policy'] = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self' https:; "
                "connect-src 'self'; "
                "frame-ancestors 'none';"
            )
        
        # Additional security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response['Permissions-Policy'] = (
            "geolocation=(), microphone=(), camera=(), "
            "payment=(), usb=(), magnetometer=(), gyroscope=()"
        )
        
        # Remove server information
        if 'Server' in response:
            del response['Server']
            
        return response


class RequestLoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log requests for security monitoring.
    """
    
    def process_request(self, request):
        """
        Log incoming requests.
        """
        # Store request start time
        request._start_time = time.time()
        
        # Log security-relevant requests
        if any(path in request.path for path in ['/admin/', '/api/auth/', '/api/users/']):
            logger.info(
                f"Security request: {request.method} {request.path} "
                f"from {self.get_client_ip(request)} "
                f"User-Agent: {request.META.get('HTTP_USER_AGENT', 'Unknown')}"
            )
    
    def process_response(self, request, response):
        """
        Log response information.
        """
        if hasattr(request, '_start_time'):
            duration = time.time() - request._start_time
            
            # Log slow requests
            if duration > 2.0:  # Log requests taking more than 2 seconds
                logger.warning(
                    f"Slow request: {request.method} {request.path} "
                    f"took {duration:.2f}s from {self.get_client_ip(request)}"
                )
            
            # Log failed authentication attempts
            if response.status_code == 401:
                logger.warning(
                    f"Authentication failed: {request.method} {request.path} "
                    f"from {self.get_client_ip(request)}"
                )
            
            # Log permission denied
            if response.status_code == 403:
                logger.warning(
                    f"Permission denied: {request.method} {request.path} "
                    f"from {self.get_client_ip(request)} "
                    f"User: {getattr(request, 'user', 'Anonymous')}"
                )
        
        return response
    
    def get_client_ip(self, request):
        """
        Get the client's IP address.
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class RateLimitMiddleware(MiddlewareMixin):
    """
    Custom rate limiting middleware for API endpoints.
    """
    
    def process_request(self, request):
        """
        Apply rate limiting to requests.
        """
        # Skip rate limiting in debug mode
        if settings.DEBUG:
            return None
            
        # Get client IP
        client_ip = self.get_client_ip(request)
        
        # Apply different rate limits based on endpoint
        if request.path.startswith('/api/auth/login/'):
            # Strict rate limiting for login attempts
            cache_key = f"login_attempts:{client_ip}"
            attempts = cache.get(cache_key, 0)
            
            if attempts >= 5:  # Max 5 login attempts per hour
                logger.warning(f"Rate limit exceeded for login from {client_ip}")
                return JsonResponse(
                    {'error': 'Too many login attempts. Please try again later.'},
                    status=429
                )
        
        elif request.path.startswith('/api/'):
            # General API rate limiting
            cache_key = f"api_requests:{client_ip}"
            requests_count = cache.get(cache_key, 0)
            
            if requests_count >= 1000:  # Max 1000 API requests per hour
                logger.warning(f"Rate limit exceeded for API from {client_ip}")
                return JsonResponse(
                    {'error': 'Rate limit exceeded. Please try again later.'},
                    status=429
                )
        
        return None
    
    def process_response(self, request, response):
        """
        Update rate limiting counters.
        """
        if settings.DEBUG:
            return response
            
        client_ip = self.get_client_ip(request)
        
        # Update login attempt counter
        if request.path.startswith('/api/auth/login/'):
            cache_key = f"login_attempts:{client_ip}"
            if response.status_code == 401:  # Failed login
                attempts = cache.get(cache_key, 0)
                cache.set(cache_key, attempts + 1, 3600)  # 1 hour timeout
            elif response.status_code == 200:  # Successful login
                cache.delete(cache_key)  # Reset counter on successful login
        
        # Update API request counter
        elif request.path.startswith('/api/'):
            cache_key = f"api_requests:{client_ip}"
            requests_count = cache.get(cache_key, 0)
            cache.set(cache_key, requests_count + 1, 3600)  # 1 hour timeout
        
        return response
    
    def get_client_ip(self, request):
        """
        Get the client's IP address.
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class SchoolBranchMiddleware(MiddlewareMixin):
    """
    Middleware to ensure users can only access data from their school branch.
    """
    
    def process_request(self, request):
        """
        Add school branch context to the request.
        """
        if hasattr(request, 'user') and request.user.is_authenticated:
            # Add school branch to request for easy access
            request.school_branch = getattr(request.user, 'school_branch', None)
            request.school = getattr(request.user.school_branch, 'school', None) if request.school_branch else None
        
        return None


class MaintenanceModeMiddleware(MiddlewareMixin):
    """
    Middleware to enable maintenance mode.
    """
    
    def process_request(self, request):
        """
        Check if maintenance mode is enabled.
        """
        # Check if maintenance mode is enabled
        maintenance_mode = cache.get('maintenance_mode', False)
        
        if maintenance_mode:
            # Allow superusers to access during maintenance
            if hasattr(request, 'user') and request.user.is_authenticated and request.user.is_superuser:
                return None
            
            # Allow access to admin and maintenance endpoints
            if request.path.startswith('/admin/') or request.path.startswith('/api/maintenance/'):
                return None
            
            # Return maintenance response for all other requests
            return JsonResponse(
                {
                    'error': 'System is currently under maintenance. Please try again later.',
                    'maintenance': True
                },
                status=503
            )
        
        return None
