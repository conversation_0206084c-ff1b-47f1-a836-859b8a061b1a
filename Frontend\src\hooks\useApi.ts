import { useState, useEffect, useCallback } from 'react';
import { useNotification } from '../context/NotificationContext';

interface ApiState<T> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
}

/**
 * Custom hook for API data fetching with automatic error handling
 * 
 * @param fetchFn - The API fetch function to call
 * @param dependencies - Dependencies array for the useEffect hook
 * @param initialData - Optional initial data
 * @returns ApiState object with data, loading state, and error
 */
export function useApi<T>(
  fetchFn: () => Promise<T>,
  dependencies: any[] = [],
  initialData: T | null = null
): ApiState<T> & { refetch: () => Promise<void> } {
  const [state, setState] = useState<ApiState<T>>({
    data: initialData,
    isLoading: true,
    error: null
  });
  
  const { showNotification } = useNotification();

  const fetchData = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const data = await fetchFn();
      setState({ data, isLoading: false, error: null });
      return data;
    } catch (error) {
      console.error('API Error:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      
      setState({ data: initialData, isLoading: false, error: error instanceof Error ? error : new Error(errorMessage) });
      
      showNotification({
        type: 'error',
        message: `Failed to fetch data: ${errorMessage}`
      });
      
      return null;
    }
  }, [fetchFn, initialData, showNotification]);

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, dependencies); // Removed fetchData from dependencies to prevent infinite loops

  return {
    ...state,
    refetch: fetchData
  };
}
