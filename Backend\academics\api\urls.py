from django.urls import path
from rest_framework.routers import Default<PERSON>outer
from .views import (
    AcademicAnalyticsViewSet,
    LearningPathViewSet,
    PerformanceTrackingViewSet,
    DashboardMetricsViewSet,
    TeacherPerformanceViewSet
)
from .curriculum_propagation_views import CurriculumPropagationViewSet

# Create router and register viewsets
router = DefaultRouter()
router.register('analytics', AcademicAnalyticsViewSet, basename='analytics')
router.register('learning-paths', LearningPathViewSet, basename='learning-paths')
router.register('performance', PerformanceTrackingViewSet, basename='performance')
router.register('curriculum-propagation', CurriculumPropagationViewSet, basename='curriculum-propagation')
router.register('teacher-performance', TeacherPerformanceViewSet, basename='teacher-performance')

# Create URL patterns
urlpatterns = router.urls + [
    # Dashboard metrics endpoint (directly expose this)
    path('metrics/', DashboardMetricsViewSet.as_view({'get': 'metrics'}), name='dashboard-metrics'),

    # Other endpoints
    path('student/<int:pk>/performance/',
         AcademicAnalyticsViewSet.as_view({'get': 'student_performance'})),
    path('learning-paths/<int:pk>/generate/',
         LearningPathViewSet.as_view({'post': 'generate_path'})),
    path('performance/<int:pk>/metrics/',
         PerformanceTrackingViewSet.as_view({'get': 'progress_metrics'})),
    path('performance/<int:pk>/achievements/',
         PerformanceTrackingViewSet.as_view({'get': 'achievement_tracking'})),
]
