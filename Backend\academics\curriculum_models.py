from django.db import models
from django.utils import timezone
from schools.models import School, SchoolBranch

class CurriculumSystem(models.Model):
    """Base model for different curriculum systems"""
    name = models.CharField(max_length=100)  # e.g., "CBC", "8-4-4", "IGCSE"
    code = models.CharField(max_length=20, unique=True)
    description = models.TextField()
    is_active = models.BooleanField(default=True)

    # System structure
    structure = models.JSONField(help_text="JSON defining the education structure")

    # For international systems that might have different academic calendars
    academic_year_start_month = models.IntegerField(default=1)  # January
    academic_year_end_month = models.IntegerField(default=12)  # December

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

class SchoolCurriculumConfig(models.Model):
    """School-specific curriculum configuration"""
    school = models.ForeignKey('schools.School', on_delete=models.CASCADE)
    primary_curriculum = models.ForeignKey(CurriculumSystem, on_delete=models.PROTECT, related_name='primary_schools')
    secondary_curriculum = models.ForeignKey(CurriculumSystem, on_delete=models.PROTECT, related_name='secondary_schools')

    # For schools that might be transitioning between systems
    is_transition_period = models.BooleanField(default=False)
    transition_details = models.JSONField(null=True, blank=True)

    # Flag to indicate if this configuration was auto-created and needs review
    is_provisional = models.BooleanField(default=False, help_text="Indicates if this configuration was auto-created and needs review")

    # Custom school-specific curriculum modifications
    curriculum_modifications = models.JSONField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.school.name} Curriculum Configuration"

class EducationLevel(models.Model):
    """Represents an education level within a curriculum system"""
    curriculum_system = models.ForeignKey(CurriculumSystem, on_delete=models.CASCADE)
    name = models.CharField(max_length=100)  # e.g., "Grade 4", "Form 2", "Year 10"
    code = models.CharField(max_length=20)
    stage_code = models.CharField(max_length=10)  # e.g., "PRI", "JSS"
    sequence = models.IntegerField(help_text="Ordering within the curriculum")

    # For CBC-specific or system-specific attributes
    system_specific_data = models.JSONField(null=True, blank=True)

    class Meta:
        unique_together = ['curriculum_system', 'code']
        ordering = ['curriculum_system', 'sequence']

    def __str__(self):
        return f"{self.name} ({self.curriculum_system.code})"


class CurriculumConfigHistory(models.Model):
    """History of changes to curriculum configurations"""
    # The school configuration this history entry is for
    school_config = models.ForeignKey('SchoolCurriculumConfig', on_delete=models.CASCADE, related_name='history')

    # The user who made the change
    changed_by = models.ForeignKey('core.CustomUser', on_delete=models.SET_NULL, null=True, related_name='curriculum_config_changes')

    # The curriculum systems at the time of the change
    primary_curriculum = models.ForeignKey(
        CurriculumSystem,
        on_delete=models.PROTECT,
        related_name='primary_history'
    )
    secondary_curriculum = models.ForeignKey(
        CurriculumSystem,
        on_delete=models.PROTECT,
        related_name='secondary_history'
    )

    # Configuration state at the time of the change
    is_transition_period = models.BooleanField(default=False)
    transition_details = models.JSONField(null=True, blank=True)
    curriculum_modifications = models.JSONField(null=True, blank=True)
    is_provisional = models.BooleanField(default=False)

    # Change metadata
    change_type = models.CharField(max_length=20, choices=[
        ('created', 'Created'),
        ('updated', 'Updated'),
        ('applied_template', 'Applied Template')
    ])
    template_used = models.ForeignKey('CurriculumConfigTemplate', on_delete=models.SET_NULL, null=True, blank=True, related_name='applications')
    change_comment = models.TextField(blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"History entry for {self.school_config} - {self.created_at}"

    class Meta:
        verbose_name = "Curriculum Configuration History"
        verbose_name_plural = "Curriculum Configuration History"
        ordering = ['-created_at']


class CurriculumConfigTemplate(models.Model):
    """Template for curriculum configurations that can be applied to multiple schools"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    primary_curriculum = models.ForeignKey(
        CurriculumSystem,
        on_delete=models.PROTECT,
        related_name='primary_templates'
    )
    secondary_curriculum = models.ForeignKey(
        CurriculumSystem,
        on_delete=models.PROTECT,
        related_name='secondary_templates'
    )
    is_transition_period = models.BooleanField(default=False)
    transition_details = models.JSONField(null=True, blank=True)
    curriculum_modifications = models.JSONField(null=True, blank=True)

    # Who created this template
    created_by = models.ForeignKey('core.CustomUser', on_delete=models.SET_NULL, null=True, related_name='created_curriculum_templates')

    # Is this a system-wide template or specific to a school/organization
    is_system_template = models.BooleanField(default=False, help_text="If true, this template is available to all schools")
    school = models.ForeignKey('schools.School', on_delete=models.CASCADE, null=True, blank=True, related_name='curriculum_templates')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Curriculum Configuration Template"
        verbose_name_plural = "Curriculum Configuration Templates"


class BranchCurriculumConfig(models.Model):
    """Branch-specific curriculum configuration"""
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='curriculum_config')
    primary_curriculum = models.ForeignKey(
        CurriculumSystem,
        on_delete=models.PROTECT,
        related_name='primary_branches'
    )
    secondary_curriculum = models.ForeignKey(
        CurriculumSystem,
        on_delete=models.PROTECT,
        related_name='secondary_branches'
    )

    # For branches that might be transitioning between systems
    is_transition_period = models.BooleanField(default=False)
    transition_details = models.TextField(blank=True, null=True)

    # Track if this config is inherited from the school or customized for the branch
    is_inherited_from_school = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Curriculum Config for {self.school_branch.name}"

    class Meta:
        verbose_name = "Branch Curriculum Configuration"
        verbose_name_plural = "Branch Curriculum Configurations"


class CurriculumPropagationHistory(models.Model):
    """
    Tracks history of curriculum propagation from schools to branches
    """
    school = models.ForeignKey(School, on_delete=models.CASCADE, related_name='propagation_history')
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='propagation_history')
    user = models.ForeignKey('core.CustomUser', on_delete=models.SET_NULL, null=True, related_name='propagation_history')
    propagated_at = models.DateTimeField(auto_now_add=True)

    # Store the configuration details at the time of propagation
    primary_curriculum_id = models.IntegerField()
    primary_curriculum_name = models.CharField(max_length=100)
    secondary_curriculum_id = models.IntegerField(null=True, blank=True)
    secondary_curriculum_name = models.CharField(max_length=100, null=True, blank=True)
    is_transition_period = models.BooleanField(default=False)
    transition_details = models.TextField(null=True, blank=True)

    # Status of the propagation
    status = models.CharField(max_length=20, choices=[
        ('success', 'Success'),
        ('failed', 'Failed')
    ])
    error_message = models.TextField(null=True, blank=True)

    def __str__(self):
        return f"Propagation: {self.school.name} to {self.school_branch.name} on {self.propagated_at}"

    class Meta:
        verbose_name = "Curriculum Propagation History"
        verbose_name_plural = "Curriculum Propagation History"
        ordering = ['-propagated_at']


class CurriculumComplianceCheck(models.Model):
    """Scheduled compliance check for curriculum configurations"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)

    # The template to check against
    template = models.ForeignKey(
        CurriculumConfigTemplate,
        on_delete=models.CASCADE,
        related_name='compliance_checks'
    )

    # Compliance threshold (0-100)
    compliance_threshold = models.IntegerField(
        default=80,
        help_text="Minimum compliance score (0-100) required to pass the check"
    )

    # Frequency of checks
    FREQUENCY_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
    ]
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES, default='weekly')

    # Scheduling
    is_active = models.BooleanField(default=True)
    last_check_at = models.DateTimeField(null=True, blank=True)
    next_check_at = models.DateTimeField()

    # Filtering
    school = models.ForeignKey('schools.School', on_delete=models.SET_NULL, null=True, blank=True, related_name='compliance_checks')
    school_name_filter = models.CharField(max_length=100, blank=True, null=True)

    # Notification settings
    notify_missing_configs = models.BooleanField(default=True, help_text="Notify about schools with no configuration")
    notify_school_admins = models.BooleanField(default=False, help_text="Send notifications to school administrators")
    send_email_notifications = models.BooleanField(default=True)

    # Who created this check
    created_by = models.ForeignKey('core.CustomUser', on_delete=models.SET_NULL, null=True, related_name='created_compliance_checks')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        # Set next_check_at if it's not set
        if not self.next_check_at:
            self.next_check_at = timezone.now()
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = "Curriculum Compliance Check"
        verbose_name_plural = "Curriculum Compliance Checks"


class CurriculumComplianceNotification(models.Model):
    """Notification for curriculum compliance issues"""
    # The school with compliance issues
    school = models.ForeignKey('schools.School', on_delete=models.CASCADE, related_name='compliance_notifications')

    # The template that was checked against
    template = models.ForeignKey(
        CurriculumConfigTemplate,
        on_delete=models.CASCADE,
        related_name='compliance_notifications'
    )

    # The scheduled check that generated this notification
    scheduled_check = models.ForeignKey(
        CurriculumComplianceCheck,
        on_delete=models.SET_NULL,
        null=True,
        related_name='notifications'
    )

    # Compliance details
    compliance_score = models.IntegerField()
    compliance_status = models.CharField(
        max_length=20,
        choices=[
            ('compliant', 'Compliant'),
            ('mostly_compliant', 'Mostly Compliant'),
            ('partially_compliant', 'Partially Compliant'),
            ('non_compliant', 'Non-Compliant'),
            ('no_config', 'No Configuration')
        ]
    )
    differences = models.JSONField(default=list)

    # Resolution status
    is_resolved = models.BooleanField(default=False)
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolved_by = models.ForeignKey(
        'core.CustomUser',
        on_delete=models.SET_NULL,
        null=True,
        related_name='resolved_compliance_notifications'
    )
    resolution_comment = models.TextField(blank=True, null=True)

    # Who created this notification
    created_by = models.ForeignKey(
        'core.CustomUser',
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_compliance_notifications'
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Compliance Notification for {self.school.name} - {self.compliance_status}"

    def resolve(self, user, comment=None):
        """Mark this notification as resolved"""
        self.is_resolved = True
        self.resolved_at = timezone.now()
        self.resolved_by = user
        if comment:
            self.resolution_comment = comment
        self.save()

    class Meta:
        verbose_name = "Curriculum Compliance Notification"
        verbose_name_plural = "Curriculum Compliance Notifications"
        ordering = ['-created_at']