from rest_framework import serializers
from .curriculum_models import CurriculumSystem, SchoolCurriculumConfig, EducationLevel
from schools.serializers import SchoolSerializer
from schools.models import School

class CurriculumSystemSerializer(serializers.ModelSerializer):
    class Meta:
        model = CurriculumSystem
        fields = ['id', 'name', 'code', 'description', 'structure', 'is_active',
                 'academic_year_start_month', 'academic_year_end_month']

class EducationLevelSerializer(serializers.ModelSerializer):
    curriculum_system = CurriculumSystemSerializer(read_only=True)
    curriculum_system_id = serializers.PrimaryKeyRelatedField(
        queryset=CurriculumSystem.objects.all(),
        source='curriculum_system',
        write_only=True
    )

    class Meta:
        model = EducationLevel
        fields = ['id', 'name', 'code', 'stage_code', 'sequence',
                 'system_specific_data', 'curriculum_system', 'curriculum_system_id']

class SchoolCurriculumConfigSerializer(serializers.ModelSerializer):
    school = SchoolSerializer(read_only=True)
    school_id = serializers.PrimaryKeyRelatedField(
        queryset=School.objects.all(),
        source='school',
        write_only=True
    )
    primary_curriculum = CurriculumSystemSerializer(read_only=True)
    primary_curriculum_id = serializers.PrimaryKeyRelatedField(
        queryset=CurriculumSystem.objects.all(),
        source='primary_curriculum',
        write_only=True
    )
    secondary_curriculum = CurriculumSystemSerializer(read_only=True)
    secondary_curriculum_id = serializers.PrimaryKeyRelatedField(
        queryset=CurriculumSystem.objects.all(),
        source='secondary_curriculum',
        write_only=True
    )

    class Meta:
        model = SchoolCurriculumConfig
        fields = ['id', 'school', 'school_id', 'primary_curriculum', 'primary_curriculum_id',
                 'secondary_curriculum', 'secondary_curriculum_id', 'is_transition_period',
                 'transition_details', 'curriculum_modifications', 'is_provisional']