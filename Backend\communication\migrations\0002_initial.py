# Generated by Django 5.2.1 on 2025-06-01 13:25

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('academics', '0001_initial'),
        ('communication', '0001_initial'),
        ('schools', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='announcement',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_announcements', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='announcement',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='announcements', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='announcement',
            name='target_class',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='announcements', to='academics.classroom'),
        ),
        migrations.AddField(
            model_name='announcement',
            name='target_stream',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='announcements', to='academics.stream'),
        ),
        migrations.AddField(
            model_name='email',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='emails', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='email',
            name='sender',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_emails', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='event',
            name='attendees',
            field=models.ManyToManyField(blank=True, related_name='attending_events', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='event',
            name='organizer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='organized_events', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='event',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='events', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='message',
            name='recipient',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_messages', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='message',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='message',
            name='sender',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_messages', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='newsletter',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_newsletters', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='newsletter',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='newsletters', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='parentteachermeeting',
            name='parent',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='parent_meetings', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='parentteachermeeting',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='parent_teacher_meetings', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='parentteachermeeting',
            name='teacher',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='teacher_meetings', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='sms',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sms', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='sms',
            name='sender',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_sms', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='announcement',
            index=models.Index(fields=['announcement_type'], name='communicati_announc_76f701_idx'),
        ),
        migrations.AddIndex(
            model_name='announcement',
            index=models.Index(fields=['priority'], name='communicati_priorit_50c09c_idx'),
        ),
        migrations.AddIndex(
            model_name='announcement',
            index=models.Index(fields=['target_audience'], name='communicati_target__ea4f28_idx'),
        ),
        migrations.AddIndex(
            model_name='announcement',
            index=models.Index(fields=['publish_date'], name='communicati_publish_1687a6_idx'),
        ),
        migrations.AddIndex(
            model_name='announcement',
            index=models.Index(fields=['is_active'], name='communicati_is_acti_eb12d9_idx'),
        ),
        migrations.AddIndex(
            model_name='email',
            index=models.Index(fields=['sender'], name='communicati_sender__79d010_idx'),
        ),
        migrations.AddIndex(
            model_name='email',
            index=models.Index(fields=['status'], name='communicati_status_c25156_idx'),
        ),
        migrations.AddIndex(
            model_name='email',
            index=models.Index(fields=['sent_at'], name='communicati_sent_at_e03e96_idx'),
        ),
        migrations.AddIndex(
            model_name='event',
            index=models.Index(fields=['event_type'], name='communicati_event_t_21ff24_idx'),
        ),
        migrations.AddIndex(
            model_name='event',
            index=models.Index(fields=['is_recurring'], name='communicati_is_recu_96f042_idx'),
        ),
        migrations.AddIndex(
            model_name='event',
            index=models.Index(fields=['is_all_day'], name='communicati_is_all__d2b1f5_idx'),
        ),
        migrations.AddIndex(
            model_name='event',
            index=models.Index(fields=['start_date'], name='communicati_start_d_da44cf_idx'),
        ),
        migrations.AddIndex(
            model_name='event',
            index=models.Index(fields=['end_date'], name='communicati_end_dat_6a37f7_idx'),
        ),
        migrations.AddIndex(
            model_name='event',
            index=models.Index(fields=['is_public'], name='communicati_is_publ_4c0e3f_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['sender'], name='communicati_sender__0e7b4b_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['recipient'], name='communicati_recipie_7147cc_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['is_read'], name='communicati_is_read_d3b61c_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['created_at'], name='communicati_created_544711_idx'),
        ),
        migrations.AddIndex(
            model_name='newsletter',
            index=models.Index(fields=['status'], name='communicati_status_477642_idx'),
        ),
        migrations.AddIndex(
            model_name='newsletter',
            index=models.Index(fields=['publish_date'], name='communicati_publish_924335_idx'),
        ),
        migrations.AddIndex(
            model_name='parentteachermeeting',
            index=models.Index(fields=['teacher'], name='communicati_teacher_da05a6_idx'),
        ),
        migrations.AddIndex(
            model_name='parentteachermeeting',
            index=models.Index(fields=['parent'], name='communicati_parent__a989d4_idx'),
        ),
        migrations.AddIndex(
            model_name='parentteachermeeting',
            index=models.Index(fields=['student_name'], name='communicati_student_64ab6e_idx'),
        ),
        migrations.AddIndex(
            model_name='parentteachermeeting',
            index=models.Index(fields=['scheduled_date'], name='communicati_schedul_4b0051_idx'),
        ),
        migrations.AddIndex(
            model_name='parentteachermeeting',
            index=models.Index(fields=['status'], name='communicati_status_cb0fcc_idx'),
        ),
        migrations.AddIndex(
            model_name='sms',
            index=models.Index(fields=['sender'], name='communicati_sender__9f3aa3_idx'),
        ),
        migrations.AddIndex(
            model_name='sms',
            index=models.Index(fields=['status'], name='communicati_status_01b1c2_idx'),
        ),
        migrations.AddIndex(
            model_name='sms',
            index=models.Index(fields=['sent_at'], name='communicati_sent_at_dc4919_idx'),
        ),
    ]
