"""
Management command to process pending payments automatically
Can be run via cron job for continuous automation
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.db.models import Q
from datetime import timedelta
import logging

from fees.school_billing_models import SchoolPayment, AutomatedPaymentSettings
from fees.automated_payment_processor import payment_verification_engine

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Process pending payments for automatic verification'

    def add_arguments(self, parser):
        parser.add_argument(
            '--max-age-hours',
            type=int,
            default=24,
            help='Maximum age of payments to process (in hours)'
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=50,
            help='Number of payments to process in each batch'
        )
        parser.add_argument(
            '--school-id',
            type=int,
            help='Process payments for specific school only'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be processed without actually processing'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Process payments even if automation is disabled'
        )

    def handle(self, *args, **options):
        max_age_hours = options['max_age_hours']
        batch_size = options['batch_size']
        school_id = options.get('school_id')
        dry_run = options['dry_run']
        force = options['force']

        self.stdout.write(
            self.style.SUCCESS(
                f'Starting payment processing (max age: {max_age_hours}h, batch size: {batch_size})'
            )
        )

        # Calculate cutoff time
        cutoff_time = timezone.now() - timedelta(hours=max_age_hours)

        # Build query for pending payments
        query = Q(status='PENDING', created_at__gte=cutoff_time)
        
        if school_id:
            query &= Q(school_id=school_id)

        # Get pending payments
        pending_payments = SchoolPayment.objects.filter(query).select_related(
            'school', 'invoice'
        ).order_by('created_at')[:batch_size]

        if not pending_payments:
            self.stdout.write(
                self.style.WARNING('No pending payments found to process')
            )
            return

        self.stdout.write(f'Found {len(pending_payments)} pending payments to process')

        # Process each payment
        results = {
            'total': 0,
            'processed': 0,
            'auto_verified': 0,
            'flagged': 0,
            'failed': 0,
            'skipped': 0,
            'errors': []
        }

        for payment in pending_payments:
            results['total'] += 1
            
            try:
                # Check if automation is enabled for this school
                if not force:
                    try:
                        settings = AutomatedPaymentSettings.objects.get(school=payment.school)
                        if not settings.auto_verification_enabled:
                            self.stdout.write(
                                self.style.WARNING(
                                    f'Skipping {payment.payment_reference} - automation disabled for {payment.school.name}'
                                )
                            )
                            results['skipped'] += 1
                            continue
                    except AutomatedPaymentSettings.DoesNotExist:
                        self.stdout.write(
                            self.style.WARNING(
                                f'Skipping {payment.payment_reference} - no automation settings for {payment.school.name}'
                            )
                        )
                        results['skipped'] += 1
                        continue

                if dry_run:
                    self.stdout.write(
                        f'[DRY RUN] Would process: {payment.payment_reference} - '
                        f'{payment.school.name} - KES {payment.amount}'
                    )
                    results['processed'] += 1
                    continue

                # Process the payment
                self.stdout.write(
                    f'Processing: {payment.payment_reference} - {payment.school.name} - KES {payment.amount}'
                )

                success = payment_verification_engine.process_payment_verification(
                    payment.id,
                    verification_source='MANAGEMENT_COMMAND'
                )

                results['processed'] += 1

                if success:
                    self.stdout.write(
                        self.style.SUCCESS(f'✓ Auto-verified: {payment.payment_reference}')
                    )
                    results['auto_verified'] += 1
                else:
                    # Check if it was flagged or failed
                    payment.refresh_from_db()
                    if payment.status == 'PENDING':
                        self.stdout.write(
                            self.style.WARNING(f'⚠ Flagged for review: {payment.payment_reference}')
                        )
                        results['flagged'] += 1
                    else:
                        self.stdout.write(
                            self.style.ERROR(f'✗ Failed: {payment.payment_reference}')
                        )
                        results['failed'] += 1

            except Exception as e:
                error_msg = f'Error processing {payment.payment_reference}: {str(e)}'
                self.stdout.write(self.style.ERROR(error_msg))
                results['errors'].append(error_msg)
                results['failed'] += 1
                logger.error(error_msg)

        # Print summary
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS('PROCESSING SUMMARY'))
        self.stdout.write('='*50)
        self.stdout.write(f'Total payments found: {results["total"]}')
        self.stdout.write(f'Processed: {results["processed"]}')
        self.stdout.write(f'Auto-verified: {results["auto_verified"]}')
        self.stdout.write(f'Flagged for review: {results["flagged"]}')
        self.stdout.write(f'Failed: {results["failed"]}')
        self.stdout.write(f'Skipped: {results["skipped"]}')

        if results['errors']:
            self.stdout.write('\nERRORS:')
            for error in results['errors']:
                self.stdout.write(self.style.ERROR(f'  - {error}'))

        # Calculate success rate
        if results['processed'] > 0:
            success_rate = (results['auto_verified'] / results['processed']) * 100
            self.stdout.write(f'\nAutomation success rate: {success_rate:.1f}%')

        if dry_run:
            self.stdout.write(
                self.style.WARNING('\nThis was a dry run. No payments were actually processed.')
            )

        self.stdout.write(
            self.style.SUCCESS('\nPayment processing completed!')
        )
