"""
Serializers for Super Admin School Billing System
"""

from rest_framework import serializers
from .school_billing_models import (
    SchoolBillingAccount, SchoolSubscriptionPlan, SchoolInvoice,
    SchoolInvoiceLineItem, SchoolPayment
)
from schools.models import School
from settings_app.license_models import LicenseSubscription


class SchoolBasicSerializer(serializers.ModelSerializer):
    """Basic school information for billing"""
    
    class Meta:
        model = School
        fields = ['id', 'name', 'email', 'phone', 'is_active']


class LicenseSubscriptionBasicSerializer(serializers.ModelSerializer):
    """Basic license information"""
    
    class Meta:
        model = LicenseSubscription
        fields = [
            'package_type', 'subscription_status', 'start_date', 
            'expiry_date', 'max_students', 'max_staff'
        ]


class SchoolBillingAccountSerializer(serializers.ModelSerializer):
    """Serializer for school billing accounts"""
    
    school = SchoolBasicSerializer(read_only=True)
    school_id = serializers.IntegerField(write_only=True)
    is_overdue = serializers.SerializerMethodField()
    license_info = serializers.SerializerMethodField()
    
    class Meta:
        model = SchoolBillingAccount
        fields = [
            'id', 'school', 'school_id', 'billing_status', 'billing_cycle',
            'billing_contact_name', 'billing_contact_email', 'billing_contact_phone',
            'billing_address', 'billing_city', 'billing_country', 'billing_postal_code',
            'tax_id', 'tax_rate', 'auto_billing_enabled', 'payment_grace_period_days',
            'current_balance', 'credit_limit', 'created_at', 'updated_at',
            'last_billing_date', 'next_billing_date', 'is_overdue', 'license_info'
        ]
        read_only_fields = ['created_at', 'updated_at', 'current_balance']

    def get_is_overdue(self, obj):
        return obj.is_overdue()

    def get_license_info(self, obj):
        try:
            license_sub = obj.school.license
            return LicenseSubscriptionBasicSerializer(license_sub).data
        except LicenseSubscription.DoesNotExist:
            return None

    def validate_school_id(self, value):
        try:
            school = School.objects.get(id=value)
            # Check if billing account already exists
            if SchoolBillingAccount.objects.filter(school=school).exists():
                raise serializers.ValidationError("Billing account already exists for this school")
            return value
        except School.DoesNotExist:
            raise serializers.ValidationError("School not found")


class SchoolSubscriptionPlanSerializer(serializers.ModelSerializer):
    """Serializer for subscription plans"""
    
    class Meta:
        model = SchoolSubscriptionPlan
        fields = [
            'id', 'name', 'plan_type', 'description', 'monthly_price',
            'quarterly_price', 'annual_price', 'max_students', 'max_staff',
            'max_branches', 'storage_limit_gb', 'included_modules', 'features',
            'is_active', 'is_featured', 'sort_order', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def validate_included_modules(self, value):
        """Validate that included modules are valid"""
        if not isinstance(value, list):
            raise serializers.ValidationError("Included modules must be a list")
        
        # Here you could validate against available modules
        # from settings_app.modules import AVAILABLE_MODULES
        # for module in value:
        #     if module not in AVAILABLE_MODULES:
        #         raise serializers.ValidationError(f"Invalid module: {module}")
        
        return value


class SchoolInvoiceLineItemSerializer(serializers.ModelSerializer):
    """Serializer for invoice line items"""
    
    subscription_plan_name = serializers.CharField(source='subscription_plan.name', read_only=True)
    
    class Meta:
        model = SchoolInvoiceLineItem
        fields = [
            'id', 'description', 'quantity', 'unit_price', 'total_amount',
            'billing_period_start', 'billing_period_end', 'subscription_plan',
            'subscription_plan_name', 'created_at'
        ]
        read_only_fields = ['total_amount', 'created_at']


class SchoolInvoiceSerializer(serializers.ModelSerializer):
    """Serializer for school invoices"""
    
    school = SchoolBasicSerializer(read_only=True)
    school_id = serializers.IntegerField(write_only=True)
    line_items = SchoolInvoiceLineItemSerializer(many=True, read_only=True)
    days_overdue = serializers.SerializerMethodField()
    
    class Meta:
        model = SchoolInvoice
        fields = [
            'id', 'invoice_number', 'school', 'school_id', 'invoice_date',
            'due_date', 'status', 'subtotal', 'tax_amount', 'discount_amount',
            'total_amount', 'paid_amount', 'billing_period_start',
            'billing_period_end', 'notes', 'internal_notes', 'created_at',
            'updated_at', 'sent_at', 'paid_at', 'line_items', 'days_overdue'
        ]
        read_only_fields = [
            'invoice_number', 'created_at', 'updated_at', 'sent_at', 'paid_at'
        ]

    def get_days_overdue(self, obj):
        if obj.status == 'OVERDUE' and obj.due_date:
            from django.utils import timezone
            return (timezone.now().date() - obj.due_date).days
        return 0

    def validate_school_id(self, value):
        try:
            School.objects.get(id=value)
            return value
        except School.DoesNotExist:
            raise serializers.ValidationError("School not found")


class SchoolPaymentSerializer(serializers.ModelSerializer):
    """Serializer for school payments"""
    
    school = SchoolBasicSerializer(read_only=True)
    school_id = serializers.IntegerField(write_only=True)
    invoice_number = serializers.CharField(source='invoice.invoice_number', read_only=True)
    verified_by_name = serializers.CharField(source='verified_by.get_full_name', read_only=True)
    
    class Meta:
        model = SchoolPayment
        fields = [
            'id', 'payment_reference', 'school', 'school_id', 'invoice',
            'invoice_number', 'amount', 'payment_method', 'payment_date',
            'status', 'external_reference', 'transaction_id', 'notes',
            'verified_by', 'verified_by_name', 'verified_at', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'payment_reference', 'verified_by', 'verified_at', 'created_at', 'updated_at'
        ]

    def validate_school_id(self, value):
        try:
            School.objects.get(id=value)
            return value
        except School.DoesNotExist:
            raise serializers.ValidationError("School not found")


class BillingDashboardSerializer(serializers.Serializer):
    """Serializer for billing dashboard statistics"""
    
    total_schools = serializers.IntegerField()
    active_accounts = serializers.IntegerField()
    overdue_accounts = serializers.IntegerField()
    monthly_revenue = serializers.FloatField()
    outstanding_amount = serializers.FloatField()
    collection_rate = serializers.FloatField()


class SchoolBillingOverviewSerializer(serializers.Serializer):
    """Serializer for school billing overview"""
    
    school = SchoolBasicSerializer()
    billing_account = SchoolBillingAccountSerializer()
    license_info = LicenseSubscriptionBasicSerializer()
    current_invoice = SchoolInvoiceSerializer()
    recent_payments = SchoolPaymentSerializer(many=True)
    total_paid_this_year = serializers.FloatField()
    outstanding_balance = serializers.FloatField()


class BulkInvoiceGenerationSerializer(serializers.Serializer):
    """Serializer for bulk invoice generation"""
    
    billing_cycle = serializers.ChoiceField(
        choices=['MONTHLY', 'QUARTERLY', 'ANNUALLY'],
        default='MONTHLY'
    )
    school_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        help_text="List of school IDs to generate invoices for. If empty, generates for all active schools."
    )
    due_date_offset_days = serializers.IntegerField(
        default=30,
        help_text="Number of days from invoice date to set as due date"
    )
    send_immediately = serializers.BooleanField(
        default=False,
        help_text="Whether to send invoices immediately after generation"
    )

    def validate_school_ids(self, value):
        if value:
            # Validate that all school IDs exist
            existing_schools = School.objects.filter(id__in=value).values_list('id', flat=True)
            invalid_ids = set(value) - set(existing_schools)
            if invalid_ids:
                raise serializers.ValidationError(f"Invalid school IDs: {list(invalid_ids)}")
        return value


class PaymentVerificationSerializer(serializers.Serializer):
    """Serializer for payment verification"""
    
    payment_id = serializers.IntegerField()
    verification_notes = serializers.CharField(max_length=500, required=False)
    
    def validate_payment_id(self, value):
        try:
            payment = SchoolPayment.objects.get(id=value)
            if payment.status != 'PENDING':
                raise serializers.ValidationError("Payment is not in pending status")
            return value
        except SchoolPayment.DoesNotExist:
            raise serializers.ValidationError("Payment not found")


class LicenseActivationSerializer(serializers.Serializer):
    """Serializer for manual license activation"""
    
    school_id = serializers.IntegerField()
    package_type = serializers.ChoiceField(
        choices=['basic', 'standard', 'premium', 'enterprise', 'custom']
    )
    duration_days = serializers.IntegerField(min_value=1, max_value=3650)  # Max 10 years
    max_students = serializers.IntegerField(min_value=1)
    max_staff = serializers.IntegerField(min_value=1)
    max_branches = serializers.IntegerField(min_value=1, default=1)
    custom_modules = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        help_text="List of custom modules for custom package type"
    )
    activation_reason = serializers.CharField(
        max_length=200,
        help_text="Reason for manual activation"
    )

    def validate_school_id(self, value):
        try:
            School.objects.get(id=value)
            return value
        except School.DoesNotExist:
            raise serializers.ValidationError("School not found")

    def validate(self, data):
        # If package type is custom, custom_modules should be provided
        if data['package_type'] == 'custom' and not data.get('custom_modules'):
            raise serializers.ValidationError({
                'custom_modules': 'Custom modules are required for custom package type'
            })
        return data
