from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from inventory.models import Asset, AssetCategory
from schools.models import School, SchoolBranch
from core.models import CustomUser

class AssetAPITest(TestCase):
    def setUp(self):
        # Create a test school
        self.school = School.objects.create(
            name="Test School",
            address="123 Test Street",
            phone="1234567890",
            email="<EMAIL>",
            registration_number="TEST-REG-001",
            established_date="2000-01-01"
        )

        # Create a test school branch
        self.school_branch = SchoolBranch.objects.create(
            school=self.school,
            name="Test School Branch",
            address="123 Test Street",
            phone="1234567890",
            email="<EMAIL>",
            registration_number="TEST-BR-REG-001",
            established_date="2000-01-01"
        )

        # Create a test admin user
        self.admin_user = CustomUser.objects.create_user(
            username="testadmin",
            email="<EMAIL>",
            password="testpassword",
            is_staff=True,
            school_branch=self.school_branch
        )

        # Create a test inventory manager user
        self.inventory_manager = CustomUser.objects.create_user(
            username="testmanager",
            email="<EMAIL>",
            password="testpassword",
            school_branch=self.school_branch
        )

        # Create a test asset category
        self.category = AssetCategory.objects.create(
            name="Test Category",
            description="Test Category Description",
            school_branch=self.school_branch
        )

        # Create a test asset
        self.asset = Asset.objects.create(
            name="Test Asset",
            asset_number="ASSET-001",
            category=self.category,
            purchase_date="2023-01-01",
            purchase_price=1000.00,
            location="Room 101",
            status="AVAILABLE",
            school_branch=self.school_branch,
            added_by=self.admin_user
        )

        # Initialize the API client
        self.client = APIClient()

    def test_get_all_assets(self):
        """Test retrieving all assets"""
        # Authenticate as admin
        self.client.force_authenticate(user=self.admin_user)

        # Make the request
        url = reverse('asset-list')
        response = self.client.get(url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Handle both paginated and non-paginated responses
        if isinstance(response.data, dict) and 'results' in response.data:
            # Paginated response
            self.assertEqual(len(response.data['results']), 1)
            self.assertEqual(response.data['results'][0]['name'], 'Test Asset')
        else:
            # Non-paginated response
            self.assertEqual(len(response.data), 1)
            self.assertEqual(response.data[0]['name'], 'Test Asset')

    def test_get_asset_detail(self):
        """Test retrieving a specific asset"""
        # Authenticate as admin
        self.client.force_authenticate(user=self.admin_user)

        # Make the request
        url = reverse('asset-detail', args=[self.asset.id])
        response = self.client.get(url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Test Asset')
        self.assertEqual(response.data['asset_number'], 'ASSET-001')

    def test_create_asset(self):
        """Test creating a new asset"""
        # Authenticate as admin
        self.client.force_authenticate(user=self.admin_user)

        # Prepare the data
        data = {
            'name': 'New Test Asset',
            'asset_number': 'ASSET-002',
            'category': self.category.id,
            'purchase_date': '2023-02-01',
            'purchase_price': 2000.00,
            'location': 'Room 102',
            'status': 'AVAILABLE',
            'school_branch': self.school_branch.id
        }

        # Make the request
        url = reverse('asset-list')
        response = self.client.post(url, data, format='json')

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'New Test Asset')

        # Check that the asset was created in the database
        self.assertEqual(Asset.objects.count(), 2)

    def test_update_asset(self):
        """Test updating an asset"""
        # Authenticate as admin
        self.client.force_authenticate(user=self.admin_user)

        # Prepare the data
        data = {
            'name': 'Updated Test Asset',
            'status': 'IN_USE'
        }

        # Make the request
        url = reverse('asset-detail', args=[self.asset.id])
        response = self.client.patch(url, data, format='json')

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Updated Test Asset')
        self.assertEqual(response.data['status'], 'IN_USE')

        # Refresh the asset from the database
        self.asset.refresh_from_db()
        self.assertEqual(self.asset.name, 'Updated Test Asset')

    def test_delete_asset(self):
        """Test deleting an asset"""
        # Authenticate as admin
        self.client.force_authenticate(user=self.admin_user)

        # Make the request
        url = reverse('asset-detail', args=[self.asset.id])
        response = self.client.delete(url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # Check that the asset was deleted from the database
        self.assertEqual(Asset.objects.count(), 0)

    def test_unauthorized_access(self):
        """Test that unauthenticated users cannot access the API"""
        # Make the request without authentication
        url = reverse('asset-list')
        response = self.client.get(url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
