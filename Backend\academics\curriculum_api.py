from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.db.models import Q
from django.utils import timezone

from .curriculum_models import CurriculumSystem, SchoolCurriculumConfig, BranchCurriculumConfig, EducationLevel, CurriculumConfigTemplate, CurriculumConfigHistory, CurriculumComplianceCheck, CurriculumComplianceNotification, CurriculumPropagationHistory
from schools.models import School, SchoolBranch
from .curriculum_serializers import CurriculumSystemSerializer, EducationLevelSerializer
from .serializers.curriculum_serializers import (
    SchoolCurriculumConfigSerializer,
    BranchCurriculumConfigSerializer,
    CurriculumConfigTemplateSerializer,
    CurriculumConfigHistorySerializer,
    CurriculumComplianceCheckSerializer,
    CurriculumComplianceNotificationSerializer,
    CurriculumPropagationHistorySerializer
)
from core.permissions import IsAdminOrSuperUser, IsSchoolAdmin

class CurriculumSystemViewSet(viewsets.ModelViewSet):
    """
    API endpoint for curriculum systems
    """
    queryset = CurriculumSystem.objects.all()
    serializer_class = CurriculumSystemSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = CurriculumSystem.objects.all()

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            is_active = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active)

        return queryset

class EducationLevelViewSet(viewsets.ModelViewSet):
    """
    API endpoint for education levels
    """
    queryset = EducationLevel.objects.all()
    serializer_class = EducationLevelSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = EducationLevel.objects.all()

        # Debug logging
        print("Education Levels API - Query params:", self.request.query_params)
        print("Education Levels API - Initial queryset count:", queryset.count())

        # Filter by curriculum system
        curriculum_system = self.request.query_params.get('curriculum_system')
        if curriculum_system:
            print(f"Education Levels API - Filtering by curriculum_system: {curriculum_system}")

            # Check if the curriculum_system parameter is a code or an ID
            if curriculum_system.isdigit():
                # If it's a number, filter by ID
                print(f"Education Levels API - Treating as ID: {curriculum_system}")
                queryset = queryset.filter(curriculum_system_id=curriculum_system)
            else:
                # If it's not a number, filter by code
                print(f"Education Levels API - Treating as code: {curriculum_system}")
                queryset = queryset.filter(curriculum_system__code=curriculum_system)

                # Debug: List all curriculum systems for reference
                from .curriculum_models import CurriculumSystem
                all_systems = CurriculumSystem.objects.all()
                print("Education Levels API - Available curriculum systems:")
                for system in all_systems:
                    print(f"  - ID: {system.id}, Code: {system.code}, Name: {system.name}")

            print(f"Education Levels API - After curriculum filter: {queryset.count()}")

            # List first few education levels for debugging
            for level in queryset[:10]:  # Limit to 10 for brevity
                print(f"  Level: {level.name}, System: {level.curriculum_system.code if level.curriculum_system else 'None'}")

        # Order by sequence
        queryset = queryset.order_by('sequence')

        return queryset


def create_config_history_entry(config, user, change_type, template=None, comment=None):
    """
    Create a history entry for a curriculum configuration change

    Args:
        config: The SchoolCurriculumConfig instance
        user: The user making the change
        change_type: Type of change ('created', 'updated', or 'applied_template')
        template: Optional CurriculumConfigTemplate that was applied
        comment: Optional comment about the change
    """
    try:
        CurriculumConfigHistory.objects.create(
            school_config=config,
            changed_by=user,
            primary_curriculum=config.primary_curriculum,
            secondary_curriculum=config.secondary_curriculum,
            is_transition_period=config.is_transition_period,
            transition_details=config.transition_details,
            curriculum_modifications=config.curriculum_modifications,
            is_provisional=config.is_provisional,
            change_type=change_type,
            template_used=template,
            change_comment=comment
        )
    except Exception as e:
        # Log the error but don't fail the operation
        print(f"Error creating history entry: {str(e)}")


class SchoolCurriculumConfigViewSet(viewsets.ModelViewSet):
    """
    API endpoint for school curriculum configurations
    """
    queryset = SchoolCurriculumConfig.objects.all()
    serializer_class = SchoolCurriculumConfigSerializer
    permission_classes = [IsAuthenticated, IsAdminOrSuperUser]

    def get_queryset(self):
        """Filter queryset based on query parameters"""
        queryset = SchoolCurriculumConfig.objects.all()

        # Filter by school
        school_id = self.request.query_params.get('school_id')
        if school_id:
            queryset = queryset.filter(school_id=school_id)

        # If user is not a superuser or admin, restrict to their school
        user = self.request.user
        if not (user.is_superuser or user.groups.filter(name='System Administrators').exists()):
            # Get the user's school
            try:
                school = user.school
                queryset = queryset.filter(school=school)
            except:
                return SchoolCurriculumConfig.objects.none()

        return queryset

    def perform_create(self, serializer):
        """Create a new curriculum configuration and record history"""
        config = serializer.save()
        create_config_history_entry(
            config=config,
            user=self.request.user,
            change_type='created'
        )

    def perform_update(self, serializer):
        """Update a curriculum configuration and record history"""
        config = serializer.save()
        create_config_history_entry(
            config=config,
            user=self.request.user,
            change_type='updated'
        )

    @action(detail=True, methods=['get'])
    def history(self, request, pk=None):
        """Get the history of changes for a curriculum configuration"""
        config = self.get_object()
        history_entries = CurriculumConfigHistory.objects.filter(school_config=config).order_by('-created_at')

        # Pagination
        page = self.paginate_queryset(history_entries)
        if page is not None:
            serializer = CurriculumConfigHistorySerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = CurriculumConfigHistorySerializer(history_entries, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def rollback(self, request, pk=None):
        """Roll back to a previous configuration state"""
        config = self.get_object()

        # Get the history entry ID to roll back to
        history_id = request.data.get('history_id')
        if not history_id:
            return Response(
                {'error': 'History entry ID is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            history_entry = CurriculumConfigHistory.objects.get(id=history_id, school_config=config)
        except CurriculumConfigHistory.DoesNotExist:
            return Response(
                {'error': 'History entry not found or does not belong to this configuration'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if user has permission to modify this configuration
        user = request.user
        if not (user.is_superuser or user.groups.filter(name='System Administrators').exists()):
            if user.school != config.school:
                return Response(
                    {'error': 'You do not have permission to modify this school\'s configuration'},
                    status=status.HTTP_403_FORBIDDEN
                )

        # Update the configuration with the historical values
        try:
            config.primary_curriculum = history_entry.primary_curriculum
            config.secondary_curriculum = history_entry.secondary_curriculum
            config.is_transition_period = history_entry.is_transition_period
            config.transition_details = history_entry.transition_details
            config.curriculum_modifications = history_entry.curriculum_modifications
            config.is_provisional = False  # Never roll back to a provisional state
            config.save()

            # Record this rollback in the history
            create_config_history_entry(
                config=config,
                user=request.user,
                change_type='updated',
                comment=f'Rolled back to configuration from {history_entry.created_at}'
            )

            serializer = self.get_serializer(config)
            return Response({
                'success': True,
                'message': f'Configuration rolled back to state from {history_entry.created_at}',
                'config': serializer.data
            })
        except Exception as e:
            return Response(
                {'error': f'Failed to roll back configuration: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def export(self, request, pk=None):
        """Export a curriculum configuration"""
        config = self.get_object()

        # Check if user has permission to view this configuration
        user = request.user
        if not (user.is_superuser or user.groups.filter(name='System Administrators').exists()):
            if user.school != config.school:
                return Response(
                    {'error': 'You do not have permission to export this school\'s configuration'},
                    status=status.HTTP_403_FORBIDDEN
                )

        # Prepare the export data
        export_data = {
            'primary_curriculum': {
                'id': config.primary_curriculum.id,
                'name': config.primary_curriculum.name,
                'code': config.primary_curriculum.code
            },
            'secondary_curriculum': {
                'id': config.secondary_curriculum.id,
                'name': config.secondary_curriculum.name,
                'code': config.secondary_curriculum.code
            },
            'is_transition_period': config.is_transition_period,
            'transition_details': config.transition_details,
            'curriculum_modifications': config.curriculum_modifications,
            'school': {
                'id': config.school.id,
                'name': config.school.name
            },
            'exported_at': timezone.now().isoformat(),
            'exported_by': {
                'id': request.user.id,
                'username': request.user.username,
                'name': f'{request.user.first_name} {request.user.last_name}'
            }
        }

        return Response(export_data)

    @action(detail=False, methods=['post'])
    def import_config(self, request):
        """Import a curriculum configuration"""
        # Get the import data and target school ID
        import_data = request.data.get('config_data')
        target_school_id = request.data.get('school_id')

        if not import_data or not target_school_id:
            return Response(
                {'error': 'Both config_data and school_id are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            target_school = School.objects.get(id=target_school_id)
        except School.DoesNotExist:
            return Response(
                {'error': f'School with ID {target_school_id} not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if user has permission to modify this school
        user = request.user
        if not (user.is_superuser or user.groups.filter(name='System Administrators').exists()):
            if user.school != target_school:
                return Response(
                    {'error': 'You do not have permission to modify this school\'s configuration'},
                    status=status.HTTP_403_FORBIDDEN
                )

        # Validate the import data
        required_fields = ['primary_curriculum', 'secondary_curriculum', 'is_transition_period']
        for field in required_fields:
            if field not in import_data:
                return Response(
                    {'error': f'Import data is missing required field: {field}'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Get the curriculum systems by code
        try:
            primary_curriculum = CurriculumSystem.objects.get(code=import_data['primary_curriculum']['code'])
            secondary_curriculum = CurriculumSystem.objects.get(code=import_data['secondary_curriculum']['code'])
        except CurriculumSystem.DoesNotExist:
            return Response(
                {'error': 'One or both curriculum systems not found. Please ensure the curriculum systems exist in this instance.'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Create or update the configuration
        try:
            config, created = SchoolCurriculumConfig.objects.update_or_create(
                school=target_school,
                defaults={
                    'primary_curriculum': primary_curriculum,
                    'secondary_curriculum': secondary_curriculum,
                    'is_transition_period': import_data['is_transition_period'],
                    'transition_details': import_data.get('transition_details'),
                    'curriculum_modifications': import_data.get('curriculum_modifications'),
                    'is_provisional': False  # Imported configurations are never provisional
                }
            )

            # Record this import in the history
            create_config_history_entry(
                config=config,
                user=request.user,
                change_type='updated',
                comment=f'Imported configuration from external source'
            )

            serializer = self.get_serializer(config)
            return Response({
                'success': True,
                'message': f'Configuration successfully imported for {target_school.name}',
                'config': serializer.data,
                'created': created
            })
        except Exception as e:
            return Response(
                {'error': f'Failed to import configuration: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def batch_import(self, request):
        """Import curriculum configurations for multiple schools at once"""
        # Get the import data and mappings
        import_data = request.data.get('config_data')
        school_mappings = request.data.get('school_mappings', [])

        if not import_data or not school_mappings or not isinstance(school_mappings, list):
            return Response(
                {'error': 'Both config_data and school_mappings (as a list) are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if user has permission to perform bulk operations
        user = request.user
        if not (user.is_superuser or user.groups.filter(name='System Administrators').exists()):
            return Response(
                {'error': 'You do not have permission to perform bulk operations'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Validate the import data
        required_fields = ['primary_curriculum', 'secondary_curriculum', 'is_transition_period']
        for field in required_fields:
            if field not in import_data:
                return Response(
                    {'error': f'Import data is missing required field: {field}'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Get the curriculum systems by code
        try:
            primary_curriculum = CurriculumSystem.objects.get(code=import_data['primary_curriculum']['code'])
            secondary_curriculum = CurriculumSystem.objects.get(code=import_data['secondary_curriculum']['code'])
        except CurriculumSystem.DoesNotExist:
            return Response(
                {'error': 'One or both curriculum systems not found. Please ensure the curriculum systems exist in this instance.'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Process each school mapping
        results = {
            'total': len(school_mappings),
            'successful': 0,
            'failed': 0,
            'details': []
        }

        for mapping in school_mappings:
            school_id = mapping.get('school_id')
            if not school_id:
                results['failed'] += 1
                results['details'].append({
                    'school_id': None,
                    'status': 'failed',
                    'error': 'School ID is required'
                })
                continue

            try:
                school = School.objects.get(id=school_id)

                # Create or update the configuration
                config, created = SchoolCurriculumConfig.objects.update_or_create(
                    school=school,
                    defaults={
                        'primary_curriculum': primary_curriculum,
                        'secondary_curriculum': secondary_curriculum,
                        'is_transition_period': import_data['is_transition_period'],
                        'transition_details': import_data.get('transition_details'),
                        'curriculum_modifications': import_data.get('curriculum_modifications'),
                        'is_provisional': False  # Imported configurations are never provisional
                    }
                )

                # Record this import in the history
                create_config_history_entry(
                    config=config,
                    user=request.user,
                    change_type='updated',
                    comment=f'Imported configuration from batch import'
                )

                results['successful'] += 1
                results['details'].append({
                    'school_id': school_id,
                    'school_name': school.name,
                    'status': 'success',
                    'created': created
                })
            except School.DoesNotExist:
                results['failed'] += 1
                results['details'].append({
                    'school_id': school_id,
                    'status': 'failed',
                    'error': f'School with ID {school_id} not found'
                })
            except Exception as e:
                results['failed'] += 1
                results['details'].append({
                    'school_id': school_id,
                    'status': 'failed',
                    'error': str(e)
                })

        return Response({
            'success': results['failed'] == 0,
            'message': f'Configuration imported for {results["successful"]} of {results["total"]} schools',
            'results': results
        })

    @action(detail=False, methods=['get'])
    def compare(self, request):
        """Compare curriculum configurations between schools"""
        # Get the school IDs to compare
        source_school_id = request.query_params.get('source_school_id')
        target_school_id = request.query_params.get('target_school_id')

        if not source_school_id or not target_school_id:
            return Response(
                {'error': 'Both source_school_id and target_school_id are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            source_school = School.objects.get(id=source_school_id)
            target_school = School.objects.get(id=target_school_id)
        except School.DoesNotExist:
            return Response(
                {'error': 'One or both schools not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if user has permission to view these schools
        user = request.user
        if not (user.is_superuser or user.groups.filter(name='System Administrators').exists()):
            # Regular users can only compare their own school
            if user.school.id != int(source_school_id) and user.school.id != int(target_school_id):
                return Response(
                    {'error': 'You do not have permission to compare these schools'},
                    status=status.HTTP_403_FORBIDDEN
                )

        # Get the configurations
        source_config = SchoolCurriculumConfig.objects.filter(school=source_school).first()
        target_config = SchoolCurriculumConfig.objects.filter(school=target_school).first()

        if not source_config:
            return Response(
                {'error': f'No configuration found for source school {source_school.name}'},
                status=status.HTTP_404_NOT_FOUND
            )

        if not target_config:
            return Response(
                {'error': f'No configuration found for target school {target_school.name}'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Serialize the configurations
        source_serializer = self.get_serializer(source_config)
        target_serializer = self.get_serializer(target_config)

        # Prepare the comparison result
        comparison = {
            'source_school': {
                'id': source_school.id,
                'name': source_school.name,
                'config': source_serializer.data
            },
            'target_school': {
                'id': target_school.id,
                'name': target_school.name,
                'config': target_serializer.data
            },
            'differences': []
        }

        # Compare the configurations
        source_data = source_serializer.data
        target_data = target_serializer.data

        # Compare primary curriculum
        if source_data['primary_curriculum']['id'] != target_data['primary_curriculum']['id']:
            comparison['differences'].append({
                'field': 'primary_curriculum',
                'source_value': source_data['primary_curriculum']['name'],
                'target_value': target_data['primary_curriculum']['name']
            })

        # Compare secondary curriculum
        if source_data['secondary_curriculum']['id'] != target_data['secondary_curriculum']['id']:
            comparison['differences'].append({
                'field': 'secondary_curriculum',
                'source_value': source_data['secondary_curriculum']['name'],
                'target_value': target_data['secondary_curriculum']['name']
            })

        # Compare transition period
        if source_data['is_transition_period'] != target_data['is_transition_period']:
            comparison['differences'].append({
                'field': 'is_transition_period',
                'source_value': 'Yes' if source_data['is_transition_period'] else 'No',
                'target_value': 'Yes' if target_data['is_transition_period'] else 'No'
            })

        # Compare transition details if both are in transition
        if source_data['is_transition_period'] and target_data['is_transition_period']:
            if source_data['transition_details'] != target_data['transition_details']:
                comparison['differences'].append({
                    'field': 'transition_details',
                    'source_value': source_data['transition_details'] or 'Not specified',
                    'target_value': target_data['transition_details'] or 'Not specified'
                })

        # Compare curriculum modifications
        if source_data['curriculum_modifications'] != target_data['curriculum_modifications']:
            comparison['differences'].append({
                'field': 'curriculum_modifications',
                'source_value': 'Has modifications' if source_data['curriculum_modifications'] else 'No modifications',
                'target_value': 'Has modifications' if target_data['curriculum_modifications'] else 'No modifications'
            })

        # Compare provisional status
        if source_data['is_provisional'] != target_data['is_provisional']:
            comparison['differences'].append({
                'field': 'is_provisional',
                'source_value': 'Yes' if source_data['is_provisional'] else 'No',
                'target_value': 'Yes' if target_data['is_provisional'] else 'No'
            })

        return Response(comparison)

    @action(detail=False, methods=['get'])
    def check_provisional(self, request):
        """
        Check if the current user's school has a provisional curriculum configuration

        Returns:
            - has_provisional: True if the school has a provisional configuration
            - config_id: ID of the provisional configuration (if exists)
        """
        user = request.user

        try:
            school = user.school

            # Get the most recent configuration for the school
            config = SchoolCurriculumConfig.objects.filter(school=school).order_by('-updated_at').first()

            if config and config.is_provisional:
                return Response({
                    'has_provisional': True,
                    'config_id': config.id,
                    'school_name': school.name
                })
            else:
                return Response({
                    'has_provisional': False
                })
        except Exception as e:
            return Response(
                {'error': f'Could not check provisional configurations: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get the current school's curriculum configuration

        If no configuration exists, a provisional one will be auto-created
        """
        user = request.user

        # Get the user's school
        try:
            school = user.school
            config = SchoolCurriculumConfig.objects.filter(school=school).first()

            if config:
                serializer = self.get_serializer(config)
                return Response(serializer.data)
            else:
                # Auto-create a default configuration
                try:
                    # Get the default curriculum system (preferably CBC, or the first active one)
                    default_system = CurriculumSystem.objects.filter(code='CBC', is_active=True).first()
                    if not default_system:
                        default_system = CurriculumSystem.objects.filter(is_active=True).first()

                    if not default_system:
                        return Response(
                            {'error': 'No active curriculum systems found to create a default configuration'},
                            status=status.HTTP_404_NOT_FOUND
                        )

                    # Create a provisional configuration
                    config = SchoolCurriculumConfig.objects.create(
                        school=school,
                        primary_curriculum=default_system,
                        secondary_curriculum=default_system,
                        is_provisional=True
                    )

                    # Record history
                    create_config_history_entry(
                        config=config,
                        user=request.user,
                        change_type='created',
                        comment='Auto-created provisional configuration'
                    )

                    serializer = self.get_serializer(config)
                    return Response(serializer.data)
                except Exception as e:
                    return Response(
                        {'error': f'Failed to create default curriculum configuration: {str(e)}'},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )
        except Exception as e:
            return Response(
                {'error': f'Could not determine your school: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )


class BranchCurriculumConfigViewSet(viewsets.ModelViewSet):
    """
    API endpoint for branch curriculum configurations
    """
    queryset = BranchCurriculumConfig.objects.all()
    serializer_class = BranchCurriculumConfigSerializer
    permission_classes = [IsAuthenticated, IsAdminOrSuperUser]

    @action(detail=False, methods=['get'])
    def propagation_history(self, request):
        """
        Get the history of curriculum propagations

        Query parameters:
        - school_id: Filter by school ID
        - branch_id: Filter by branch ID
        - status: Filter by status (success, failed)
        """
        queryset = CurriculumPropagationHistory.objects.all().order_by('-propagated_at')

        # Filter by school
        school_id = request.query_params.get('school_id')
        if school_id:
            queryset = queryset.filter(school_id=school_id)

        # Filter by branch
        branch_id = request.query_params.get('branch_id')
        if branch_id:
            queryset = queryset.filter(school_branch_id=branch_id)

        # Filter by status
        status_filter = request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Paginate the results
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = CurriculumPropagationHistorySerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = CurriculumPropagationHistorySerializer(queryset, many=True)
        return Response(serializer.data)

    def get_queryset(self):
        """Filter queryset based on query parameters"""
        queryset = BranchCurriculumConfig.objects.all()

        # Filter by branch
        branch_id = self.request.query_params.get('branch_id')
        if branch_id:
            queryset = queryset.filter(school_branch_id=branch_id)

        # Filter by school
        school_id = self.request.query_params.get('school_id')
        if school_id:
            queryset = queryset.filter(school_branch__school_id=school_id)

        # If user is not a superuser or admin, restrict to their school/branch
        user = self.request.user
        if not (user.is_superuser or user.groups.filter(name='System Administrators').exists()):
            # Get the user's school and branch
            try:
                school = user.school
                branch = user.school_branch

                if branch:
                    queryset = queryset.filter(school_branch=branch)
                else:
                    queryset = queryset.filter(school_branch__school=school)
            except:
                return BranchCurriculumConfig.objects.none()

        return queryset

    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get the current branch's curriculum configuration"""
        user = request.user

        # Get the user's branch
        try:
            branch = user.school_branch
            if not branch:
                return Response(
                    {'error': 'You are not associated with any branch'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            config = BranchCurriculumConfig.objects.filter(school_branch=branch).first()

            if config:
                serializer = self.get_serializer(config)
                return Response(serializer.data)
            else:
                # Check if there's a school-level configuration
                try:
                    school_config = SchoolCurriculumConfig.objects.filter(school=branch.school).first()
                    if school_config:
                        # Create a branch config based on the school config
                        branch_config = BranchCurriculumConfig.objects.create(
                            school_branch=branch,
                            primary_curriculum=school_config.primary_curriculum,
                            secondary_curriculum=school_config.secondary_curriculum,
                            is_transition_period=school_config.is_transition_period,
                            transition_details=school_config.transition_details,
                            is_inherited_from_school=True
                        )
                        serializer = self.get_serializer(branch_config)
                        return Response(serializer.data)
                except:
                    pass

                return Response(
                    {'error': 'No curriculum configuration found for your branch'},
                    status=status.HTTP_404_NOT_FOUND
                )
        except:
            return Response(
                {'error': 'Could not determine your branch'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['post'])
    def propagate(self, request):
        """Propagate school curriculum configuration to branches"""
        # Check if user has permission
        if not (request.user.is_superuser or request.user.groups.filter(name='System Administrators').exists()):
            return Response(
                {'error': 'You do not have permission to propagate curriculum configurations'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get parameters
        school_id = request.data.get('school_id')
        branch_ids = request.data.get('branch_ids', [])

        if not school_id:
            return Response(
                {'error': 'School ID is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the school configuration
        try:
            school = School.objects.get(id=school_id)
            # Get the most recent configuration for the school
            school_config = SchoolCurriculumConfig.objects.filter(school=school).order_by('-updated_at').first()

            if not school_config:
                return Response(
                    {'error': f'Curriculum configuration for school with ID {school_id} not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
        except School.DoesNotExist:
            return Response(
                {'error': f'School with ID {school_id} not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Get branches to propagate to
        if branch_ids:
            branches = SchoolBranch.objects.filter(school=school, id__in=branch_ids)
        else:
            branches = SchoolBranch.objects.filter(school=school)

        if not branches.exists():
            return Response(
                {'error': 'No branches found for the specified school'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Propagate configuration to branches
        results = {
            'total_branches': branches.count(),
            'updated': 0,
            'created': 0,
            'failed': 0,
            'skipped': 0,
            'details': []
        }

        for branch in branches:
            try:
                # Make sure the school_config has the required fields
                if not hasattr(school_config, 'primary_curriculum') or not school_config.primary_curriculum:
                    results['failed'] += 1
                    results['details'].append({
                        'branch_id': branch.id,
                        'branch_name': branch.name,
                        'status': 'failed',
                        'error': 'School configuration is missing primary curriculum'
                    })
                    continue

                if not hasattr(school_config, 'secondary_curriculum') or not school_config.secondary_curriculum:
                    results['failed'] += 1
                    results['details'].append({
                        'branch_id': branch.id,
                        'branch_name': branch.name,
                        'status': 'failed',
                        'error': 'School configuration is missing secondary curriculum'
                    })
                    continue

                branch_config, created = BranchCurriculumConfig.objects.update_or_create(
                    school_branch=branch,
                    defaults={
                        'primary_curriculum': school_config.primary_curriculum,
                        'secondary_curriculum': school_config.secondary_curriculum,
                        'is_transition_period': school_config.is_transition_period,
                        'transition_details': school_config.transition_details,
                        'is_inherited_from_school': True
                    }
                )

                # Record the propagation history
                CurriculumPropagationHistory.objects.create(
                    school=school,
                    school_branch=branch,
                    user=request.user,
                    primary_curriculum_id=school_config.primary_curriculum.id,
                    primary_curriculum_name=school_config.primary_curriculum.name,
                    secondary_curriculum_id=school_config.secondary_curriculum.id if school_config.secondary_curriculum else None,
                    secondary_curriculum_name=school_config.secondary_curriculum.name if school_config.secondary_curriculum else None,
                    is_transition_period=school_config.is_transition_period,
                    transition_details=school_config.transition_details,
                    status='success'
                )

                if created:
                    results['created'] += 1
                    results['details'].append({
                        'branch_id': branch.id,
                        'branch_name': branch.name,
                        'status': 'created'
                    })
                else:
                    # Skip update if the branch has customized its config
                    if not branch_config.is_inherited_from_school:
                        results['skipped'] += 1
                        results['details'].append({
                            'branch_id': branch.id,
                            'branch_name': branch.name,
                            'status': 'skipped',
                            'reason': 'Branch has customized configuration'
                        })
                        continue

                    results['updated'] += 1
                    results['details'].append({
                        'branch_id': branch.id,
                        'branch_name': branch.name,
                        'status': 'updated'
                    })
            except Exception as e:
                results['failed'] += 1
                error_message = str(e)

                # Add more detailed error information
                if "primary_curriculum" in error_message:
                    error_message = "Error with primary curriculum: " + error_message
                elif "secondary_curriculum" in error_message:
                    error_message = "Error with secondary curriculum: " + error_message

                # Log the error for debugging
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Error propagating curriculum to branch {branch.id} ({branch.name}): {error_message}")

                # Record the failed propagation history
                CurriculumPropagationHistory.objects.create(
                    school=school,
                    school_branch=branch,
                    user=request.user,
                    primary_curriculum_id=school_config.primary_curriculum.id if hasattr(school_config, 'primary_curriculum') and school_config.primary_curriculum else 0,
                    primary_curriculum_name=school_config.primary_curriculum.name if hasattr(school_config, 'primary_curriculum') and school_config.primary_curriculum else 'Unknown',
                    secondary_curriculum_id=school_config.secondary_curriculum.id if hasattr(school_config, 'secondary_curriculum') and school_config.secondary_curriculum else None,
                    secondary_curriculum_name=school_config.secondary_curriculum.name if hasattr(school_config, 'secondary_curriculum') and school_config.secondary_curriculum else None,
                    is_transition_period=school_config.is_transition_period if hasattr(school_config, 'is_transition_period') else False,
                    transition_details=school_config.transition_details if hasattr(school_config, 'transition_details') else None,
                    status='failed',
                    error_message=error_message
                )

                results['details'].append({
                    'branch_id': branch.id,
                    'branch_name': branch.name,
                    'status': 'failed',
                    'error': error_message
                })

        return Response(results)


class CurriculumConfigTemplateViewSet(viewsets.ModelViewSet):
    """
    API endpoint for curriculum configuration templates
    """
    queryset = CurriculumConfigTemplate.objects.all()
    serializer_class = CurriculumConfigTemplateSerializer
    permission_classes = [IsAuthenticated, IsAdminOrSuperUser]

    def get_queryset(self):
        """Filter queryset based on query parameters"""
        queryset = CurriculumConfigTemplate.objects.all()

        # Filter by system templates or school-specific templates
        is_system = self.request.query_params.get('is_system')
        if is_system is not None:
            is_system_bool = is_system.lower() == 'true'
            queryset = queryset.filter(is_system_template=is_system_bool)

        # Filter by school
        school_id = self.request.query_params.get('school_id')
        if school_id:
            queryset = queryset.filter(school_id=school_id)

        # If user is not a superuser or admin, restrict to their school's templates and system templates
        user = self.request.user
        if not (user.is_superuser or user.groups.filter(name='System Administrators').exists()):
            # Get the user's school
            try:
                school = user.school
                queryset = queryset.filter(Q(school=school) | Q(is_system_template=True))
            except:
                return CurriculumConfigTemplate.objects.filter(is_system_template=True)

        return queryset

    def perform_create(self, serializer):
        """Set the created_by field to the current user"""
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def apply_to_school(self, request, pk=None):
        """Apply this template to a school"""
        template = self.get_object()

        # Get the school to apply to
        school_id = request.data.get('school_id')
        if not school_id:
            return Response(
                {'error': 'School ID is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            school = School.objects.get(id=school_id)
        except School.DoesNotExist:
            return Response(
                {'error': f'School with ID {school_id} not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if user has permission to modify this school
        user = request.user
        if not (user.is_superuser or user.groups.filter(name='System Administrators').exists()):
            if user.school != school:
                return Response(
                    {'error': 'You do not have permission to modify this school\'s configuration'},
                    status=status.HTTP_403_FORBIDDEN
                )

        # Create or update the school's curriculum configuration
        try:
            config, created = SchoolCurriculumConfig.objects.update_or_create(
                school=school,
                defaults={
                    'primary_curriculum': template.primary_curriculum,
                    'secondary_curriculum': template.secondary_curriculum,
                    'is_transition_period': template.is_transition_period,
                    'transition_details': template.transition_details,
                    'curriculum_modifications': template.curriculum_modifications,
                    'is_provisional': False  # Not provisional since it's explicitly applied
                }
            )

            # Record history
            create_config_history_entry(
                config=config,
                user=request.user,
                change_type='applied_template',
                template=template,
                comment=f'Applied template "{template.name}" to {school.name}'
            )

            serializer = SchoolCurriculumConfigSerializer(config)
            return Response({
                'success': True,
                'message': f'Template "{template.name}" applied to {school.name}',
                'config': serializer.data,
                'created': created
            })
        except Exception as e:
            return Response(
                {'error': f'Failed to apply template: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def apply_to_schools(self, request, pk=None):
        """Apply this template to multiple schools at once"""
        template = self.get_object()

        # Get the schools to apply to
        school_ids = request.data.get('school_ids', [])
        if not school_ids or not isinstance(school_ids, list):
            return Response(
                {'error': 'A list of school IDs is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if user has permission to perform bulk operations
        user = request.user
        if not (user.is_superuser or user.groups.filter(name='System Administrators').exists()):
            return Response(
                {'error': 'You do not have permission to perform bulk operations'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Apply template to each school
        results = {
            'total': len(school_ids),
            'successful': 0,
            'failed': 0,
            'details': []
        }

        for school_id in school_ids:
            try:
                school = School.objects.get(id=school_id)

                # Create or update the school's curriculum configuration
                config, created = SchoolCurriculumConfig.objects.update_or_create(
                    school=school,
                    defaults={
                        'primary_curriculum': template.primary_curriculum,
                        'secondary_curriculum': template.secondary_curriculum,
                        'is_transition_period': template.is_transition_period,
                        'transition_details': template.transition_details,
                        'curriculum_modifications': template.curriculum_modifications,
                        'is_provisional': False  # Not provisional since it's explicitly applied
                    }
                )

                # Record history
                create_config_history_entry(
                    config=config,
                    user=request.user,
                    change_type='applied_template',
                    template=template,
                    comment=f'Applied template "{template.name}" to {school.name} (bulk operation)'
                )

                results['successful'] += 1
                results['details'].append({
                    'school_id': school_id,
                    'school_name': school.name,
                    'status': 'success',
                    'created': created
                })
            except School.DoesNotExist:
                results['failed'] += 1
                results['details'].append({
                    'school_id': school_id,
                    'status': 'failed',
                    'error': f'School with ID {school_id} not found'
                })
            except Exception as e:
                results['failed'] += 1
                results['details'].append({
                    'school_id': school_id,
                    'status': 'failed',
                    'error': str(e)
                })

        return Response({
            'success': results['failed'] == 0,
            'message': f'Template "{template.name}" applied to {results["successful"]} of {results["total"]} schools',
            'results': results
        })

    @action(detail=True, methods=['post'])
    def compare_with_school(self, request, pk=None):
        """Compare this template with a school's configuration"""
        template = self.get_object()

        # Get the school to compare with
        school_id = request.data.get('school_id')
        if not school_id:
            return Response(
                {'error': 'School ID is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            school = School.objects.get(id=school_id)
        except School.DoesNotExist:
            return Response(
                {'error': f'School with ID {school_id} not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if user has permission to view this school
        user = request.user
        if not (user.is_superuser or user.groups.filter(name='System Administrators').exists()):
            if user.school != school:
                return Response(
                    {'error': 'You do not have permission to view this school\'s configuration'},
                    status=status.HTTP_403_FORBIDDEN
                )

        # Get the school's configuration
        # Since there might be multiple configurations, get the most recent one
        configs = SchoolCurriculumConfig.objects.filter(school=school).order_by('-updated_at')

        if not configs.exists():
            return Response(
                {'error': f'No configuration found for school {school.name}'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Use the most recent configuration
        config = configs.first()

        # Get the comparison result
        comparison = self._compare_config_with_template(config, template)

        return Response(comparison)

    def _compare_config_with_template(self, config, template):
        """Helper method to compare a school config with a template"""
        # Prepare the comparison result
        comparison = {
            'template': {
                'id': template.id,
                'name': template.name,
                'primary_curriculum': {
                    'id': template.primary_curriculum.id,
                    'name': template.primary_curriculum.name,
                    'code': template.primary_curriculum.code
                },
                'secondary_curriculum': {
                    'id': template.secondary_curriculum.id,
                    'name': template.secondary_curriculum.name,
                    'code': template.secondary_curriculum.code
                },
                'is_transition_period': template.is_transition_period,
                'transition_details': template.transition_details,
                'curriculum_modifications': template.curriculum_modifications,
                'is_system_template': template.is_system_template
            },
            'school': {
                'id': config.school.id,
                'name': config.school.name,
                'config': {
                    'id': config.id,
                    'primary_curriculum': {
                        'id': config.primary_curriculum.id,
                        'name': config.primary_curriculum.name,
                        'code': config.primary_curriculum.code
                    },
                    'secondary_curriculum': {
                        'id': config.secondary_curriculum.id,
                        'name': config.secondary_curriculum.name,
                        'code': config.secondary_curriculum.code
                    },
                    'is_transition_period': config.is_transition_period,
                    'transition_details': config.transition_details,
                    'curriculum_modifications': config.curriculum_modifications,
                    'is_provisional': config.is_provisional
                }
            },
            'differences': [],
            'compliance_score': 100  # Start with 100% compliance
        }

        # Compare primary curriculum
        if template.primary_curriculum.id != config.primary_curriculum.id:
            comparison['differences'].append({
                'field': 'primary_curriculum',
                'template_value': template.primary_curriculum.name,
                'school_value': config.primary_curriculum.name,
                'severity': 'high'
            })
            comparison['compliance_score'] -= 20  # Major difference

        # Compare secondary curriculum
        if template.secondary_curriculum.id != config.secondary_curriculum.id:
            comparison['differences'].append({
                'field': 'secondary_curriculum',
                'template_value': template.secondary_curriculum.name,
                'school_value': config.secondary_curriculum.name,
                'severity': 'high'
            })
            comparison['compliance_score'] -= 20  # Major difference

        # Compare transition period
        if template.is_transition_period != config.is_transition_period:
            comparison['differences'].append({
                'field': 'is_transition_period',
                'template_value': 'Yes' if template.is_transition_period else 'No',
                'school_value': 'Yes' if config.is_transition_period else 'No',
                'severity': 'medium'
            })
            comparison['compliance_score'] -= 15  # Medium difference

        # Compare transition details if both are in transition
        if template.is_transition_period and config.is_transition_period:
            if template.transition_details != config.transition_details:
                comparison['differences'].append({
                    'field': 'transition_details',
                    'template_value': template.transition_details or 'Not specified',
                    'school_value': config.transition_details or 'Not specified',
                    'severity': 'low'
                })
                comparison['compliance_score'] -= 10  # Minor difference

        # Compare curriculum modifications
        if template.curriculum_modifications != config.curriculum_modifications:
            comparison['differences'].append({
                'field': 'curriculum_modifications',
                'template_value': 'Has modifications' if template.curriculum_modifications else 'No modifications',
                'school_value': 'Has modifications' if config.curriculum_modifications else 'No modifications',
                'severity': 'medium'
            })
            comparison['compliance_score'] -= 15  # Medium difference

        # Ensure compliance score doesn't go below 0
        comparison['compliance_score'] = max(0, comparison['compliance_score'])

        # Determine compliance status
        if comparison['compliance_score'] == 100:
            comparison['compliance_status'] = 'compliant'
        elif comparison['compliance_score'] >= 80:
            comparison['compliance_status'] = 'mostly_compliant'
        elif comparison['compliance_score'] >= 50:
            comparison['compliance_status'] = 'partially_compliant'
        else:
            comparison['compliance_status'] = 'non_compliant'

        return comparison

    @action(detail=True, methods=['get'])
    def compliance_report(self, request, pk=None):
        """Generate a compliance report for all schools against this template"""
        template = self.get_object()

        # Check if user has permission to generate reports
        user = request.user
        if not (user.is_superuser or user.groups.filter(name='System Administrators').exists()):
            return Response(
                {'error': 'You do not have permission to generate compliance reports'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get all schools
        schools = School.objects.all()

        # Filter by school name if provided
        school_name = request.query_params.get('school_name')
        if school_name:
            schools = schools.filter(name__icontains=school_name)

        # Generate the report
        report = {
            'template': {
                'id': template.id,
                'name': template.name,
                'primary_curriculum': {
                    'id': template.primary_curriculum.id,
                    'name': template.primary_curriculum.name,
                    'code': template.primary_curriculum.code
                },
                'secondary_curriculum': {
                    'id': template.secondary_curriculum.id,
                    'name': template.secondary_curriculum.name,
                    'code': template.secondary_curriculum.code
                },
                'is_transition_period': template.is_transition_period,
                'is_system_template': template.is_system_template
            },
            'generated_at': timezone.now().isoformat(),
            'generated_by': {
                'id': user.id,
                'username': user.username,
                'name': f'{user.first_name} {user.last_name}'
            },
            'total_schools': schools.count(),
            'schools_with_config': 0,
            'schools_without_config': 0,
            'compliance_summary': {
                'compliant': 0,
                'mostly_compliant': 0,
                'partially_compliant': 0,
                'non_compliant': 0
            },
            'average_compliance_score': 0,
            'school_results': []
        }

        total_compliance_score = 0

        # Check each school
        for school in schools:
            # Get the most recent configuration for the school
            configs = SchoolCurriculumConfig.objects.filter(school=school).order_by('-updated_at')

            if configs.exists():
                config = configs.first()
                report['schools_with_config'] += 1

                # Compare with template
                comparison = self._compare_config_with_template(config, template)

                # Update compliance summary
                report['compliance_summary'][comparison['compliance_status']] += 1
                total_compliance_score += comparison['compliance_score']

                # Add to school results
                report['school_results'].append({
                    'school': {
                        'id': school.id,
                        'name': school.name
                    },
                    'compliance_score': comparison['compliance_score'],
                    'compliance_status': comparison['compliance_status'],
                    'differences': comparison['differences'],
                    'config_id': config.id,
                    'is_provisional': config.is_provisional
                })
            else:
                report['schools_without_config'] += 1

                # Add to school results
                report['school_results'].append({
                    'school': {
                        'id': school.id,
                        'name': school.name
                    },
                    'compliance_score': 0,
                    'compliance_status': 'no_config',
                    'differences': [],
                    'config_id': None,
                    'is_provisional': None
                })

        # Calculate average compliance score
        if report['schools_with_config'] > 0:
            report['average_compliance_score'] = round(total_compliance_score / report['schools_with_config'])

        return Response(report)


class CurriculumComplianceCheckViewSet(viewsets.ModelViewSet):
    """
    API endpoint for curriculum compliance checks
    """
    queryset = CurriculumComplianceCheck.objects.all()
    serializer_class = CurriculumComplianceCheckSerializer
    permission_classes = [IsAuthenticated, IsAdminOrSuperUser]

    def get_queryset(self):
        """Filter queryset based on query parameters"""
        queryset = CurriculumComplianceCheck.objects.all()

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            is_active = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active)

        # Filter by template
        template_id = self.request.query_params.get('template_id')
        if template_id:
            queryset = queryset.filter(template_id=template_id)

        # If user is not a superuser or admin, restrict to their created checks
        user = self.request.user
        if not (user.is_superuser or user.groups.filter(name='System Administrators').exists()):
            queryset = queryset.filter(created_by=user)

        return queryset

    def perform_create(self, serializer):
        """Set the created_by field to the current user"""
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def run_now(self, request, pk=None):
        """Run a compliance check immediately"""
        check = self.get_object()

        # Import here to avoid circular imports
        from .curriculum_tasks import run_compliance_check

        try:
            summary = run_compliance_check(check)

            # Update the last check time
            check.last_check_at = timezone.now()
            check.save()

            return Response({
                'success': True,
                'message': f'Compliance check "{check.name}" executed successfully',
                'summary': summary
            })
        except Exception as e:
            return Response(
                {'error': f'Failed to run compliance check: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CurriculumComplianceNotificationViewSet(viewsets.ModelViewSet):
    """
    API endpoint for curriculum compliance notifications
    """
    queryset = CurriculumComplianceNotification.objects.all()
    serializer_class = CurriculumComplianceNotificationSerializer
    permission_classes = [IsAuthenticated, IsAdminOrSuperUser]

    def get_queryset(self):
        """Filter queryset based on query parameters"""
        queryset = CurriculumComplianceNotification.objects.all()

        # Filter by resolved status
        is_resolved = self.request.query_params.get('is_resolved')
        if is_resolved is not None:
            is_resolved = is_resolved.lower() == 'true'
            queryset = queryset.filter(is_resolved=is_resolved)

        # Filter by school
        school_id = self.request.query_params.get('school_id')
        if school_id:
            queryset = queryset.filter(school_id=school_id)

        # Filter by template
        template_id = self.request.query_params.get('template_id')
        if template_id:
            queryset = queryset.filter(template_id=template_id)

        # Filter by scheduled check
        check_id = self.request.query_params.get('check_id')
        if check_id:
            queryset = queryset.filter(scheduled_check_id=check_id)

        # If user is not a superuser or admin, restrict to their school
        user = self.request.user
        if not (user.is_superuser or user.groups.filter(name='System Administrators').exists()):
            # Get the user's school
            try:
                school = user.school
                queryset = queryset.filter(school=school)
            except:
                return CurriculumComplianceNotification.objects.none()

        return queryset

    @action(detail=True, methods=['post'])
    def resolve(self, request, pk=None):
        """Mark a notification as resolved"""
        notification = self.get_object()

        # Check if already resolved
        if notification.is_resolved:
            return Response({
                'success': False,
                'message': 'Notification is already resolved'
            })

        # Get the resolution comment
        comment = request.data.get('comment')

        # Resolve the notification
        notification.resolve(request.user, comment)

        return Response({
            'success': True,
            'message': 'Notification marked as resolved'
        })

    @action(detail=False, methods=['post'])
    def bulk_resolve(self, request):
        """Resolve multiple notifications at once"""
        # Get the notification IDs to resolve
        notification_ids = request.data.get('notification_ids', [])
        if not notification_ids or not isinstance(notification_ids, list):
            return Response(
                {'error': 'A list of notification IDs is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the resolution comment
        comment = request.data.get('comment')

        # Get the notifications
        notifications = self.get_queryset().filter(id__in=notification_ids, is_resolved=False)

        # Resolve each notification
        count = 0
        for notification in notifications:
            notification.resolve(request.user, comment)
            count += 1

        return Response({
            'success': True,
            'message': f'{count} notifications marked as resolved'
        })
