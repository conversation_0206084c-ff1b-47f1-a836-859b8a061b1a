<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Refresh Issue</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .debug-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .log-entry {
            padding: 8px;
            margin: 4px 0;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .info {
            border-left-color: #17a2b8;
            background: #d1ecf1;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .clear-btn {
            background: #6c757d;
        }
        .clear-btn:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="debug-panel">
        <h2>🔍 Frontend Refresh Issue Debug Tool</h2>
        <p>This tool helps identify what's causing the app to refresh continuously.</p>
        
        <div>
            <button onclick="checkLocalStorage()">Check LocalStorage</button>
            <button onclick="checkAuthState()">Check Auth State</button>
            <button onclick="checkNetworkRequests()">Monitor Network</button>
            <button onclick="checkConsoleErrors()">Check Console</button>
            <button onclick="clearLogs()" class="clear-btn">Clear Logs</button>
        </div>
    </div>

    <div class="debug-panel">
        <h3>📊 Debug Logs</h3>
        <div id="logs"></div>
    </div>

    <div class="debug-panel">
        <h3>🌐 Network Monitor</h3>
        <div id="network-logs"></div>
    </div>

    <script>
        let logCount = 0;
        let networkRequests = [];

        function addLog(message, type = 'info') {
            const logs = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logs.appendChild(logEntry);
            logs.scrollTop = logs.scrollHeight;
            logCount++;
            
            if (logCount > 100) {
                logs.removeChild(logs.firstChild);
                logCount--;
            }
        }

        function checkLocalStorage() {
            addLog('🔍 Checking LocalStorage...', 'info');
            
            const keys = [
                'access_token', 'refresh_token', 'token', 'refreshToken',
                'user_data', 'user_type', 'selectedSchoolId', 'selectedBranchId',
                'persist:root', 'redux-persist'
            ];
            
            keys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    if (key.includes('token')) {
                        addLog(`✅ ${key}: ${value.substring(0, 20)}...`, 'info');
                    } else {
                        addLog(`✅ ${key}: ${value}`, 'info');
                    }
                } else {
                    addLog(`❌ ${key}: Not found`, 'warning');
                }
            });
        }

        function checkAuthState() {
            addLog('🔍 Checking Authentication State...', 'info');
            
            // Check if we can access the React app's state
            if (window.React) {
                addLog('✅ React is loaded', 'info');
            } else {
                addLog('❌ React not found', 'error');
            }
            
            // Check for Redux store
            if (window.__REDUX_DEVTOOLS_EXTENSION__) {
                addLog('✅ Redux DevTools available', 'info');
            }
            
            // Check current URL
            addLog(`📍 Current URL: ${window.location.href}`, 'info');
            addLog(`📍 Pathname: ${window.location.pathname}`, 'info');
            addLog(`📍 Hash: ${window.location.hash}`, 'info');
        }

        function checkNetworkRequests() {
            addLog('🌐 Starting network monitoring...', 'info');
            
            // Override fetch to monitor requests
            const originalFetch = window.fetch;
            window.fetch = function(...args) {
                const url = args[0];
                addLog(`🌐 FETCH: ${url}`, 'info');
                
                return originalFetch.apply(this, args)
                    .then(response => {
                        addLog(`✅ FETCH SUCCESS: ${url} (${response.status})`, 'info');
                        return response;
                    })
                    .catch(error => {
                        addLog(`❌ FETCH ERROR: ${url} - ${error.message}`, 'error');
                        throw error;
                    });
            };
            
            // Override XMLHttpRequest
            const originalXHR = window.XMLHttpRequest;
            window.XMLHttpRequest = function() {
                const xhr = new originalXHR();
                const originalOpen = xhr.open;
                
                xhr.open = function(method, url) {
                    addLog(`🌐 XHR: ${method} ${url}`, 'info');
                    return originalOpen.apply(this, arguments);
                };
                
                return xhr;
            };
        }

        function checkConsoleErrors() {
            addLog('🔍 Monitoring console errors...', 'info');
            
            // Override console methods
            const originalError = console.error;
            const originalWarn = console.warn;
            const originalLog = console.log;
            
            console.error = function(...args) {
                addLog(`❌ CONSOLE ERROR: ${args.join(' ')}`, 'error');
                return originalError.apply(this, args);
            };
            
            console.warn = function(...args) {
                addLog(`⚠️ CONSOLE WARN: ${args.join(' ')}`, 'warning');
                return originalWarn.apply(this, args);
            };
            
            // Listen for unhandled errors
            window.addEventListener('error', function(event) {
                addLog(`❌ UNHANDLED ERROR: ${event.error?.message || event.message}`, 'error');
                addLog(`📍 File: ${event.filename}:${event.lineno}:${event.colno}`, 'error');
            });
            
            // Listen for unhandled promise rejections
            window.addEventListener('unhandledrejection', function(event) {
                addLog(`❌ UNHANDLED PROMISE REJECTION: ${event.reason}`, 'error');
            });
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
            document.getElementById('network-logs').innerHTML = '';
            logCount = 0;
            addLog('🧹 Logs cleared', 'info');
        }

        // Auto-start monitoring
        window.addEventListener('load', function() {
            addLog('🚀 Debug tool loaded', 'info');
            checkLocalStorage();
            checkAuthState();
            checkNetworkRequests();
            checkConsoleErrors();
            
            // Monitor for page refreshes
            let refreshCount = 0;
            const startTime = Date.now();
            
            setInterval(() => {
                const currentTime = Date.now();
                const elapsed = currentTime - startTime;
                
                if (elapsed > 5000) { // After 5 seconds
                    addLog(`⏱️ App has been stable for ${Math.floor(elapsed/1000)} seconds`, 'info');
                }
            }, 5000);
        });

        // Monitor for navigation changes
        let lastPath = window.location.pathname;
        setInterval(() => {
            if (window.location.pathname !== lastPath) {
                addLog(`🔄 Navigation: ${lastPath} → ${window.location.pathname}`, 'warning');
                lastPath = window.location.pathname;
            }
        }, 100);
    </script>
</body>
</html>
