from django.contrib import admin
from .models import School, SchoolBranch

@admin.register(School)
class SchoolAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'registration_number', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'code', 'registration_number')

    def get_queryset(self, request):
        return super().get_queryset(request).order_by('name')


@admin.register(SchoolBranch)
class SchoolBranchAdmin(admin.ModelAdmin):
    list_display = ('name', 'school', 'code', 'is_active')
    list_filter = ('school', 'is_active')
    search_fields = ('name', 'school__name')

    def save_model(self, request, obj, form, change):
        if not obj.code:
            obj.code = obj._generate_branch_code()  
        super().save_model(request, obj, form, change)