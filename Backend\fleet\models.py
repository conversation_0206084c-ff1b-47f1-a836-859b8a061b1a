from django.db import models
from django.utils import timezone
from schools.models import SchoolBranch
from core.models import CustomUser

class Vehicle(models.Model):
    VEHICLE_TYPE_CHOICES = [
        ('BUS', 'Bus'),
        ('VAN', 'Van'),
        ('CAR', 'Car'),
        ('TRUCK', 'Truck'),
        ('OTHER', 'Other'),
    ]

    FUEL_TYPE_CHOICES = [
        ('PETROL', 'Petrol'),
        ('DIESEL', 'Diesel'),
        ('ELECTRIC', 'Electric'),
        ('HYBRID', 'Hybrid'),
        ('OTHER', 'Other'),
    ]

    STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('MAINTENANCE', 'Under Maintenance'),
        ('INACTIVE', 'Inactive'),
        ('RETIRED', 'Retired'),
    ]

    name = models.CharField(max_length=100)
    vehicle_type = models.CharField(max_length=10, choices=VEHICLE_TYPE_CHOICES)
    registration_number = models.CharField(max_length=20, unique=True)
    model = models.CharField(max_length=100)
    manufacturer = models.CharField(max_length=100)
    year_of_manufacture = models.PositiveIntegerField()
    seating_capacity = models.PositiveIntegerField()
    fuel_type = models.CharField(max_length=10, choices=FUEL_TYPE_CHOICES)
    purchase_date = models.DateField()
    purchase_price = models.DecimalField(max_digits=12, decimal_places=2)
    insurance_expiry = models.DateField()
    license_expiry = models.DateField()
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='ACTIVE')
    current_mileage = models.PositiveIntegerField(default=0)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='vehicles')
    image = models.ImageField(upload_to='vehicle_images/', blank=True, null=True)
    documents = models.FileField(upload_to='vehicle_documents/', blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.registration_number})"

    class Meta:
        ordering = ['name']
        indexes = [
            models.Index(fields=['registration_number']),
            models.Index(fields=['status']),
        ]

class Driver(models.Model):
    STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('ON_LEAVE', 'On Leave'),
        ('INACTIVE', 'Inactive'),
        ('TERMINATED', 'Terminated'),
    ]

    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE, related_name='driver_profile')
    license_number = models.CharField(max_length=50, unique=True)
    license_class = models.CharField(max_length=20)
    license_expiry = models.DateField()
    experience_years = models.PositiveIntegerField()
    date_of_birth = models.DateField()
    address = models.TextField()
    emergency_contact_name = models.CharField(max_length=100)
    emergency_contact_phone = models.CharField(max_length=20)
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='ACTIVE')
    assigned_vehicle = models.ForeignKey(Vehicle, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_drivers')
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='drivers')
    documents = models.FileField(upload_to='driver_documents/', blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.first_name} {self.user.last_name} ({self.license_number})"

    class Meta:
        ordering = ['user__first_name', 'user__last_name']
        indexes = [
            models.Index(fields=['license_number']),
            models.Index(fields=['status']),
        ]

class Route(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    start_location = models.CharField(max_length=255)
    end_location = models.CharField(max_length=255)
    distance = models.DecimalField(max_digits=8, decimal_places=2, help_text="Distance in kilometers")
    estimated_time = models.PositiveIntegerField(help_text="Estimated time in minutes")
    stops = models.JSONField(blank=True, null=True, help_text="JSON array of stop locations")
    is_active = models.BooleanField(default=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='routes')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']
        indexes = [
            models.Index(fields=['is_active']),
        ]

class Schedule(models.Model):
    SCHEDULE_TYPE_CHOICES = [
        ('REGULAR', 'Regular'),
        ('SPECIAL', 'Special Event'),
        ('FIELD_TRIP', 'Field Trip'),
    ]

    DAY_CHOICES = [
        ('MON', 'Monday'),
        ('TUE', 'Tuesday'),
        ('WED', 'Wednesday'),
        ('THU', 'Thursday'),
        ('FRI', 'Friday'),
        ('SAT', 'Saturday'),
        ('SUN', 'Sunday'),
    ]

    name = models.CharField(max_length=100)
    route = models.ForeignKey(Route, on_delete=models.CASCADE, related_name='schedules')
    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE, related_name='schedules')
    driver = models.ForeignKey(Driver, on_delete=models.CASCADE, related_name='schedules')
    schedule_type = models.CharField(max_length=15, choices=SCHEDULE_TYPE_CHOICES, default='REGULAR')
    days_of_week = models.JSONField(blank=True, null=True, help_text="JSON array of days (for regular schedules)")
    departure_time = models.TimeField()
    arrival_time = models.TimeField()
    start_date = models.DateField(default=timezone.now)
    end_date = models.DateField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    notes = models.TextField(blank=True, null=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='schedules')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.route.name}"

    class Meta:
        ordering = ['departure_time']
        indexes = [
            models.Index(fields=['is_active']),
            models.Index(fields=['schedule_type']),
            models.Index(fields=['start_date']),
        ]

class VehicleMaintenance(models.Model):
    MAINTENANCE_TYPE_CHOICES = [
        ('ROUTINE', 'Routine Service'),
        ('REPAIR', 'Repair'),
        ('INSPECTION', 'Inspection'),
        ('EMERGENCY', 'Emergency'),
        ('OTHER', 'Other'),
    ]

    STATUS_CHOICES = [
        ('SCHEDULED', 'Scheduled'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
    ]

    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE, related_name='maintenance_records')
    maintenance_type = models.CharField(max_length=15, choices=MAINTENANCE_TYPE_CHOICES)
    description = models.TextField()
    service_provider = models.CharField(max_length=100, blank=True, null=True)
    scheduled_date = models.DateField()
    completion_date = models.DateField(blank=True, null=True)
    cost = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    mileage_at_service = models.PositiveIntegerField(blank=True, null=True)
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='SCHEDULED')
    notes = models.TextField(blank=True, null=True)
    documents = models.FileField(upload_to='maintenance_documents/', blank=True, null=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='vehicle_maintenance')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.vehicle.name} - {self.maintenance_type} ({self.scheduled_date})"

    class Meta:
        ordering = ['-scheduled_date']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['scheduled_date']),
        ]

class StudentTransport(models.Model):
    USAGE_TYPE_CHOICES = [
        ('FULL', 'Full (Both Ways, All Days)'),
        ('MORNING_ONLY', 'Morning Only (Pickup Only)'),
        ('AFTERNOON_ONLY', 'Afternoon Only (Dropoff Only)'),
        ('PARTIAL_DAYS', 'Partial Days (Selected Days Only)'),
        ('CUSTOM', 'Custom Schedule')
    ]

    PAYMENT_FREQUENCY_CHOICES = [
        ('TERMLY', 'Once Per Term'),
        ('MONTHLY', 'Monthly'),
        ('WEEKLY', 'Weekly'),
        ('DAILY', 'Daily (Pay As You Go)')
    ]

    student = models.ForeignKey('users.Student', on_delete=models.CASCADE, related_name='transport')
    route = models.ForeignKey(Route, on_delete=models.CASCADE, related_name='students')
    pickup_point = models.CharField(max_length=255)
    dropoff_point = models.CharField(max_length=255)
    is_active = models.BooleanField(default=True)
    start_date = models.DateField(default=timezone.now)
    end_date = models.DateField(blank=True, null=True)

    # Enhanced transport usage fields
    usage_type = models.CharField(max_length=20, choices=USAGE_TYPE_CHOICES, default='FULL')
    days_per_week = models.PositiveSmallIntegerField(default=5, help_text="Number of days per week the student uses transport")
    selected_days = models.JSONField(blank=True, null=True, help_text="JSON array of weekdays (e.g., ['MON', 'WED', 'FRI'])")

    # Fee related fields
    fee_amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_frequency = models.CharField(max_length=20, choices=PAYMENT_FREQUENCY_CHOICES, default='MONTHLY')
    apply_transport_fee = models.BooleanField(default=True, help_text="Whether to include transport fee in student's fee structure")

    notes = models.TextField(blank=True, null=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='student_transport')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.student} - {self.route.name}"

    class Meta:
        ordering = ['student']
        indexes = [
            models.Index(fields=['is_active']),
            models.Index(fields=['start_date']),
        ]

class TransportAttendance(models.Model):
    student_transport = models.ForeignKey(StudentTransport, on_delete=models.CASCADE, related_name='attendance')
    schedule = models.ForeignKey(Schedule, on_delete=models.CASCADE, related_name='attendance')
    date = models.DateField()
    picked_up = models.BooleanField(default=False)
    dropped_off = models.BooleanField(default=False)
    pickup_time = models.TimeField(blank=True, null=True)
    dropoff_time = models.TimeField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    recorded_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='recorded_transport_attendance')
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='transport_attendance')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.student_transport.student} - {self.date}"

    class Meta:
        ordering = ['-date', 'student_transport__student']
        indexes = [
            models.Index(fields=['date']),
        ]
        unique_together = ['student_transport', 'schedule', 'date']

class FuelConsumption(models.Model):
    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE, related_name='fuel_records')
    date = models.DateField()
    fuel_amount = models.DecimalField(max_digits=8, decimal_places=2, help_text="Amount in liters")
    cost = models.DecimalField(max_digits=10, decimal_places=2)
    odometer_reading = models.PositiveIntegerField()
    fuel_station = models.CharField(max_length=100, blank=True, null=True)
    filled_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='fuel_records')
    receipt = models.FileField(upload_to='fuel_receipts/', blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    school_branch = models.ForeignKey(SchoolBranch, on_delete=models.CASCADE, related_name='fuel_records')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.vehicle.name} - {self.date}"

    class Meta:
        ordering = ['-date']
        indexes = [
            models.Index(fields=['date']),
        ]
