from django.http import JsonResponse
from django.urls import resolve
from .utils import is_module_enabled
from .modules import AVAILABLE_MODULES

class ModuleAccessMiddleware:
    """
    Middleware to check if a user has access to a module based on URL patterns
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
        # Map URL prefixes to module codes
        self.url_module_map = {
            'api/fleet/': 'fleet',
            'api/library/': 'library',
            'api/inventory/': 'inventory',
            'api/communication/': 'communication',
            'api/community/': 'community',
            'api/fees/': 'fees',
            'api/exams/': 'exams',
            'api/attendance/': 'attendance',
            'api/timetable/': 'timetable',
            'api/hostel/': 'hostel',
            'api/hr/': 'hr',
            'api/payroll/': 'hr',
            'api/mpesa/': 'mpesa_integration',
        }
        
    def __call__(self, request):
        # Skip middleware for non-API requests or unauthenticated users
        if not request.path.startswith('/api/') or not hasattr(request, 'user') or not request.user.is_authenticated:
            return self.get_response(request)
            
        # Skip for superusers
        if request.user.is_superuser:
            return self.get_response(request)
            
        # Check if the request is for a module-specific endpoint
        for url_prefix, module_code in self.url_module_map.items():
            if request.path.startswith(f'/{url_prefix}'):
                # Check if the user has access to this module
                if not hasattr(request.user, 'school_branch') or not request.user.school_branch:
                    return JsonResponse({
                        'error': 'You are not associated with any school branch'
                    }, status=403)
                    
                if not is_module_enabled(module_code, request.user.school_branch):
                    module_name = AVAILABLE_MODULES.get(module_code, {}).get('name', module_code.title())
                    return JsonResponse({
                        'error': f'The {module_name} module is not enabled for your school'
                    }, status=403)
                    
                break
                
        return self.get_response(request)
