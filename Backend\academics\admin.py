from django.contrib import admin
from .models import (
    AcademicYear, Department, Stream, ClassRoom, Term, TimeSlot,
    GradingSystem, GradeScale, StreamGradingSystem, Subject,
    TimetableTemplate, EnhancedTimeSlot, EnhancedTimetable, TimetableEntry,
    TeacherTimetable, TimetableConflict, TimetableSubstitution,
    LessonPlan, LessonDelivery, LessonResource, StudentLessonFeedback,
    LessonObservation, LessonSeries, LessonSeriesLesson,
    ExamType, ExamSession, EnhancedExam, ExamRegistration, EnhancedExamResult,
    ExamMalpractice,
    LiveStreamingPlatform, LiveSession, SessionParticipant, SessionAttendance,
    SessionRecording, SessionChat, ResultPublication, PublicationAccess,
    ClassProgressionRule, StudentProgression,
    Assessment, AssessmentResult
)
from .curriculum_models import CurriculumSystem, SchoolCurriculumConfig, EducationLevel
from .academic_templates import AcademicYearTemplate, TermTemplate

# Register models with admin
admin.site.register(AcademicYear)

@admin.register(GradingSystem)
class GradingSystemAdmin(admin.ModelAdmin):
    list_display = ('name', 'school', 'is_active')
    list_filter = ('school', 'is_active')
    search_fields = ('name', 'school__name')

@admin.register(StreamGradingSystem)
class StreamGradingSystemAdmin(admin.ModelAdmin):
    list_display = ('stream', 'grading_system', 'term', 'min_average', 'target_average')
    list_filter = ('stream', 'term')
    search_fields = ('stream__name', 'grading_system__name')

@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'school', 'head_of_department')
    list_filter = ('school',)
    search_fields = ('name', 'code', 'school__name')

@admin.register(Subject)
class SubjectAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'department')
    list_filter = ('department',)
    search_fields = ('name', 'code')

@admin.register(ClassRoom)
class ClassRoomAdmin(admin.ModelAdmin):
    list_display = ('name', 'numeric_level', 'academic_year', 'stream', 'school', 'education_level')
    list_filter = ('stream', 'school', 'academic_year', 'education_level')
    search_fields = ('name',)
    autocomplete_fields = ['education_level']

@admin.register(Stream)
class StreamAdmin(admin.ModelAdmin):
    list_display = ('name', 'code')
    search_fields = ('name', 'code')

@admin.register(Term)
class TermAdmin(admin.ModelAdmin):
    list_display = ('name', 'start_date', 'end_date', 'school', 'academic_year', 'is_current')
    list_filter = ('school', 'academic_year', 'is_current')
    search_fields = ('name', 'academic_year')

@admin.register(TimeSlot)
class TimeSlotAdmin(admin.ModelAdmin):
    list_display = ('day_of_week', 'start_time', 'end_time')
    list_filter = ('day_of_week',)

@admin.register(TimetableTemplate)
class TimetableTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'school', 'is_active')
    list_filter = ('school', 'is_active')
    search_fields = ('name', 'school__name')

@admin.register(EnhancedTimeSlot)
class EnhancedTimeSlotAdmin(admin.ModelAdmin):
    list_display = ('day_of_week', 'period_number', 'start_time', 'end_time', 'period_type')
    list_filter = ('day_of_week', 'period_type')

@admin.register(EnhancedTimetable)
class EnhancedTimetableAdmin(admin.ModelAdmin):
    list_display = ('class_name', 'academic_year', 'term', 'is_active', 'is_published')
    list_filter = ('class_name', 'academic_year', 'term', 'is_active', 'is_published')

@admin.register(TimetableEntry)
class TimetableEntryAdmin(admin.ModelAdmin):
    list_display = ('timetable', 'subject', 'teacher', 'time_slot', 'venue_type', 'is_double_period')
    list_filter = ('timetable', 'subject', 'teacher', 'venue_type', 'is_double_period')

@admin.register(TeacherTimetable)
class TeacherTimetableAdmin(admin.ModelAdmin):
    list_display = ('teacher', 'academic_year', 'term', 'total_periods_per_week', 'is_complete', 'conflicts_detected')
    list_filter = ('teacher', 'academic_year', 'term', 'is_complete', 'conflicts_detected')

@admin.register(TimetableConflict)
class TimetableConflictAdmin(admin.ModelAdmin):
    list_display = ('timetable', 'conflict_type', 'severity', 'is_resolved', 'detected_at')
    list_filter = ('timetable', 'conflict_type', 'severity', 'is_resolved')

@admin.register(TimetableSubstitution)
class TimetableSubstitutionAdmin(admin.ModelAdmin):
    list_display = ('original_entry', 'substitute_teacher', 'date', 'is_approved')
    list_filter = ('substitute_teacher', 'date', 'is_approved')

@admin.register(LessonPlan)
class LessonPlanAdmin(admin.ModelAdmin):
    list_display = ('title', 'subject', 'class_name', 'teacher', 'date_created', 'is_approved')
    list_filter = ('subject', 'class_name', 'teacher', 'is_approved')
    search_fields = ('title', 'subject__name', 'class_name__name', 'teacher__first_name', 'teacher__last_name')

@admin.register(LessonDelivery)
class LessonDeliveryAdmin(admin.ModelAdmin):
    list_display = ('lesson_plan', 'scheduled_date', 'actual_date', 'is_completed')
    list_filter = ('lesson_plan', 'scheduled_date', 'is_completed')
    search_fields = ('lesson_plan__title', 'scheduled_date')

@admin.register(LessonResource)
class LessonResourceAdmin(admin.ModelAdmin):
    list_display = ('title', 'lesson_plan', 'resource_type', 'uploaded_by', 'uploaded_at')
    list_filter = ('lesson_plan', 'resource_type', 'uploaded_by')
    search_fields = ('title', 'lesson_plan__title', 'uploaded_by__first_name', 'uploaded_by__last_name')

@admin.register(StudentLessonFeedback)
class StudentLessonFeedbackAdmin(admin.ModelAdmin):
    list_display = ('student', 'lesson_delivery', 'content_clarity', 'submitted_at')
    list_filter = ('student', 'lesson_delivery', 'content_clarity')
    search_fields = ('student__first_name', 'student__last_name', 'lesson_delivery__lesson_plan__title')

@admin.register(LessonObservation)
class LessonObservationAdmin(admin.ModelAdmin):
    list_display = ('lesson_delivery', 'observer', 'overall_rating', 'observation_date')
    list_filter = ('lesson_delivery', 'observer', 'overall_rating')
    search_fields = ('lesson_delivery__lesson_plan__title', 'observer__first_name', 'observer__last_name')

@admin.register(LessonSeries)
class LessonSeriesAdmin(admin.ModelAdmin):
    list_display = ('title', 'subject', 'class_name', 'start_date', 'end_date', 'is_active')
    list_filter = ('subject', 'class_name', 'is_active', 'start_date', 'end_date')
    search_fields = ('title', 'subject__name', 'class_name__name')

@admin.register(LessonSeriesLesson)
class LessonSeriesLessonAdmin(admin.ModelAdmin):
    list_display = ('series', 'lesson_plan', 'order', 'is_mandatory')
    list_filter = ('series', 'lesson_plan', 'is_mandatory')
    search_fields = ('series__title', 'lesson_plan__title')

@admin.register(ExamType)
class ExamTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'weight_percentage', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'code')

@admin.register(ExamSession)
class ExamSessionAdmin(admin.ModelAdmin):
    list_display = ('name', 'academic_year', 'term', 'exam_type', 'start_date', 'end_date', 'is_active', 'is_published')
    list_filter = ('academic_year', 'term', 'exam_type', 'is_active', 'is_published')
    search_fields = ('name',)

@admin.register(EnhancedExam)
class EnhancedExamAdmin(admin.ModelAdmin):
    list_display = ('exam_session', 'subject', 'class_name', 'exam_date', 'venue', 'is_published', 'results_published')
    list_filter = ('exam_session', 'subject', 'class_name', 'is_published', 'results_published')
    search_fields = ('exam_session__name', 'subject__name', 'class_name__name')

@admin.register(ExamRegistration)
class ExamRegistrationAdmin(admin.ModelAdmin):
    list_display = ('student', 'exam', 'registration_date', 'is_registered', 'is_present')
    list_filter = ('student', 'exam', 'is_registered', 'is_present')
    search_fields = ('student__first_name', 'student__last_name', 'exam__exam_session__name')

@admin.register(EnhancedExamResult)
class EnhancedExamResultAdmin(admin.ModelAdmin):
    list_display = ('student', 'exam', 'raw_score', 'percentage', 'grade', 'points', 'position_in_class', 'is_verified', 'created_at')
    list_filter = ('exam', 'grade', 'is_verified')
    search_fields = ('student__first_name', 'student__last_name', 'exam__subject__name')

@admin.register(ExamMalpractice)
class ExamMalpracticeAdmin(admin.ModelAdmin):
    list_display = ('student', 'exam', 'malpractice_type', 'reported_by', 'report_date', 'is_resolved')
    list_filter = ('exam', 'malpractice_type', 'is_resolved')
    search_fields = ('student__first_name', 'student__last_name', 'exam__subject__name')

@admin.register(LiveStreamingPlatform)
class LiveStreamingPlatformAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'is_active')
    search_fields = ('name', 'code')

@admin.register(LiveSession)
class LiveSessionAdmin(admin.ModelAdmin):
    list_display = ('title', 'platform', 'class_name', 'subject', 'teacher', 'term', 'scheduled_start', 'scheduled_end', 'status')
    list_filter = ('platform', 'class_name', 'subject', 'teacher', 'term', 'status')
    search_fields = ('title', 'platform__name', 'class_name__name', 'subject__name', 'teacher__first_name', 'teacher__last_name')

@admin.register(SessionParticipant)
class SessionParticipantAdmin(admin.ModelAdmin):
    list_display = ('session', 'user', 'participant_type', 'joined_at', 'left_at', 'is_present')
    list_filter = ('session', 'participant_type', 'is_present')
    search_fields = ('session__title', 'user__first_name', 'user__last_name')

@admin.register(SessionAttendance)
class SessionAttendanceAdmin(admin.ModelAdmin):
    list_display = ('session', 'student', 'is_present', 'attendance_percentage', 'questions_asked', 'chat_messages', 'engagement_score')
    list_filter = ('session', 'is_present')
    search_fields = ('session__title', 'student__first_name', 'student__last_name')

@admin.register(SessionRecording)
class SessionRecordingAdmin(admin.ModelAdmin):
    list_display = ('session', 'recording_url', 'is_processed', 'is_public')
    list_filter = ('session', 'is_processed', 'is_public')
    search_fields = ('session__title', 'recording_url')

@admin.register(SessionChat)
class SessionChatAdmin(admin.ModelAdmin):
    list_display = ('session', 'user', 'message_type', 'timestamp', 'is_flagged', 'is_deleted')
    list_filter = ('session', 'message_type', 'is_flagged', 'is_deleted')
    search_fields = ('session__title', 'user__first_name', 'user__last_name', 'message')

@admin.register(ResultPublication)
class ResultPublicationAdmin(admin.ModelAdmin):
    list_display = ('title', 'publication_type', 'academic_year', 'term', 'publish_date', 'status', 'is_public')
    list_filter = ('publication_type', 'academic_year', 'term', 'status', 'is_public')
    search_fields = ('title',)

@admin.register(PublicationAccess)
class PublicationAccessAdmin(admin.ModelAdmin):
    list_display = ('publication', 'user', 'first_accessed', 'last_accessed', 'access_count', 'ip_address')
    list_filter = ('publication', 'user')
    search_fields = ('publication__title', 'user__first_name', 'user__last_name', 'ip_address')

@admin.register(ClassProgressionRule)
class ClassProgressionRuleAdmin(admin.ModelAdmin):
    list_display = ('id', 'current_level', 'next_level', 'school', 'is_default_progression', 'created_at')
    list_filter = ('school', 'is_default_progression')
    search_fields = ('school__name',)

@admin.register(StudentProgression)
class StudentProgressionAdmin(admin.ModelAdmin):
    list_display = ('student', 'current_class', 'next_class', 'term', 'status', 'created_at')
    list_filter = ('status', 'term')
    search_fields = ('student__first_name', 'student__last_name', 'current_class__name', 'next_class__name')

@admin.register(AcademicYearTemplate)
class AcademicYearTemplateAdmin(admin.ModelAdmin):
    list_display = ('year', 'school', 'start_date', 'end_date', 'is_active', 'is_archived')
    list_filter = ('school', 'is_active', 'is_archived')
    search_fields = ('year', 'school__name')

@admin.register(TermTemplate)
class TermTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'academic_year_template', 'start_date', 'end_date')
    list_filter = ('academic_year_template__school', 'academic_year_template')
    search_fields = ('name', 'academic_year_template__year')

@admin.register(CurriculumSystem)
class CurriculumSystemAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'is_active')
    search_fields = ('name', 'code')
    list_filter = ('is_active',)

@admin.register(SchoolCurriculumConfig)
class SchoolCurriculumConfigAdmin(admin.ModelAdmin):
    list_display = ('school', 'primary_curriculum', 'secondary_curriculum', 'is_transition_period')
    list_filter = ('primary_curriculum', 'secondary_curriculum', 'is_transition_period')
    search_fields = ('school__name',)

@admin.register(EducationLevel)
class EducationLevelAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'curriculum_system', 'stage_code', 'sequence')
    list_filter = ('curriculum_system', 'stage_code')
    search_fields = ('name', 'code')
    ordering = ('curriculum_system', 'sequence')

@admin.register(Assessment)
class AssessmentAdmin(admin.ModelAdmin):
    list_display = ('title', 'assessment_type', 'subject', 'class_room', 'term', 'scheduled_date', 'is_published', 'is_graded')
    list_filter = ('assessment_type', 'is_published', 'is_graded', 'term', 'subject', 'class_room')
    search_fields = ('title', 'description', 'curriculum_strand')
    date_hierarchy = 'scheduled_date'
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'assessment_type')
        }),
        ('Context', {
            'fields': ('subject', 'class_room', 'term')
        }),
        ('Assessment Details', {
            'fields': ('total_marks', 'passing_marks', 'duration')
        }),
        ('Curriculum Alignment', {
            'fields': ('curriculum_strand', 'learning_outcomes', 'assessment_criteria')
        }),
        ('Scheduling', {
            'fields': ('scheduled_date', 'submission_deadline')
        }),
        ('Status', {
            'fields': ('is_published', 'is_graded')
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at')
        })
    )

@admin.register(AssessmentResult)
class AssessmentResultAdmin(admin.ModelAdmin):
    list_display = ('student', 'assessment', 'score', 'percentage', 'grade', 'is_submitted', 'is_graded')
    list_filter = ('is_submitted', 'is_graded', 'assessment__term', 'assessment__subject')
    search_fields = ('student__username', 'student__email', 'assessment__title')
    readonly_fields = ('created_at', 'updated_at', 'submitted_at', 'graded_at')
    fieldsets = (
        ('Basic Information', {
            'fields': ('assessment', 'student')
        }),
        ('Scores', {
            'fields': ('score', 'percentage', 'grade')
        }),
        ('Feedback', {
            'fields': ('teacher_feedback', 'student_feedback')
        }),
        ('Status', {
            'fields': ('is_submitted', 'is_graded')
        }),
        ('Metadata', {
            'fields': ('submitted_at', 'graded_at', 'graded_by', 'created_at', 'updated_at')
        })
    )