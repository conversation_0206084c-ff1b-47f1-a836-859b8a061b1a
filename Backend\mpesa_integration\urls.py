from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    MpesaCredentialViewSet, MpesaTransactionViewSet,
    STKPushView, STKPushCallbackView, TransactionStatusView
)

router = DefaultRouter()
router.register(r'credentials', MpesaCredentialViewSet, basename='mpesa-credentials')
router.register(r'transactions', MpesaTransactionViewSet, basename='mpesa-transactions')

urlpatterns = [
    path('', include(router.urls)),
    path('stk-push/', STKPushView.as_view(), name='mpesa-stk-push'),
    path('callback/stk/', STKPushCallbackView.as_view(), name='mpesa-stk-callback'),
    path('transaction-status/', TransactionStatusView.as_view(), name='mpesa-transaction-status'),
]
