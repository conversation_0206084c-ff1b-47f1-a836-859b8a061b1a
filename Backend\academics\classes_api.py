from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from .models import ClassRoom
from .serializers.class_serializer import ClassRoomSerializer

class ClassRoomViewSet(viewsets.ModelViewSet):
    """
    API endpoint for class rooms
    """
    queryset = ClassRoom.objects.all()
    serializer_class = ClassRoomSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = ClassRoom.objects.all()

        # Filter by education level
        education_level = self.request.query_params.get('education_level')
        if education_level:
            queryset = queryset.filter(education_level=education_level)

        # Filter by curriculum system
        curriculum_system = self.request.query_params.get('curriculum_system')
        if curriculum_system:
            queryset = queryset.filter(curriculum_system=curriculum_system)

        # Filter by school
        school = self.request.query_params.get('school')
        if school:
            queryset = queryset.filter(school=school)

        # Filter by school branch
        school_branch = self.request.query_params.get('school_branch')
        if school_branch:
            queryset = queryset.filter(school_branch=school_branch)

        return queryset
