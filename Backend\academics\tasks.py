from celery import shared_task
from django.utils import timezone
from .models import SmartIntervention, PerformancePredictor, AutomatedAlert
from django.db.models import Avg, StdDev
from datetime import datetime, timedelta
from .models import (AssessmentResult, PerformanceAnalytics, SmartIntervention, 
                    AutomatedAlert, PerformancePredictor)
from .ml_models import StudentPerformancePredictor, LearningStyleAnalyzer, AdaptiveDifficultyAdjuster

@shared_task
def run_daily_intervention_checks():
    """Daily task to check and create interventions"""
    SmartIntervention.check_and_create_interventions()

@shared_task
def update_performance_predictions():
    """Weekly task to update performance predictions"""
    predictors = PerformancePredictor.objects.filter(
        term__is_current=True
    )
    for predictor in predictors:
        predictor.predict_performance()

@shared_task
def check_intervention_effectiveness():
    """Weekly task to evaluate intervention effectiveness"""
    active_interventions = SmartIntervention.objects.filter(
        is_active=True,
        term__is_current=True
    )
    
    for intervention in active_interventions:
        # Check if student performance has improved
        recent_results = intervention.student.assessmentresult_set.filter(
            assessment__subject=intervention.subject,
            date_submitted__gte=intervention.created_at
        ).order_by('-date_submitted')
        
        if recent_results.exists():
            latest_score = recent_results.first().score
            previous_score = intervention.student.assessmentresult_set.filter(
                assessment__subject=intervention.subject,
                date_submitted__lt=intervention.created_at
            ).order_by('-date_submitted').first()
            
            if previous_score and latest_score > previous_score.score:
                # Create positive progress alert
                AutomatedAlert.objects.create(
                    student=intervention.student,
                    alert_type='POSITIVE_PROGRESS',
                    subject=intervention.subject,
                    description=f"Improvement noted after intervention: {latest_score - previous_score.score}%",
                    severity=1
                )

@shared_task
def analyze_learning_patterns():
    """Analyze student learning patterns and create intelligent interventions"""
    current_term = Term.objects.filter(is_current=True).first()
    if not current_term:
        return

    # Get all students with recent assessments
    recent_date = datetime.now() - timedelta(days=30)
    students = Student.objects.filter(
        assessmentresult__assessment__date__gte=recent_date
    ).distinct()

    for student in students:
        # Analyze per subject
        for subject in student.subjects.all():
            results = AssessmentResult.objects.filter(
                student=student,
                assessment__subject=subject,
                assessment__term=current_term
            ).order_by('assessment__date')

            if results.exists():
                # Pattern Analysis
                pattern_data = analyze_student_patterns(student, subject, results)
                
                # Create interventions based on patterns
                if pattern_data['needs_intervention']:
                    create_smart_intervention(student, subject, pattern_data)

def analyze_student_patterns(student, subject, results):
    """Analyze individual student's learning patterns"""
    scores = list(results.values_list('score', flat=True))
    dates = list(results.values_list('assessment__date', flat=True))
    
    pattern_data = {
        'needs_intervention': False,
        'pattern_type': None,
        'confidence': 0.0,
        'suggested_actions': []
    }

    # Time-based Analysis
    time_patterns = analyze_time_patterns(scores, dates)
    
    # Performance Variance
    variance = calculate_performance_variance(scores)
    
    # Learning Style Analysis
    learning_style = analyze_learning_style(student, subject)
    
    # Topic Difficulty Analysis
    topic_difficulties = analyze_topic_difficulties(results)

    # Combine analyses
    if time_patterns['declining']:
        pattern_data['needs_intervention'] = True
        pattern_data['pattern_type'] = 'DECLINING_PERFORMANCE'
        pattern_data['suggested_actions'].extend([
            'Schedule immediate review session',
            'Assess foundational concepts',
            'Increase practice frequency'
        ])
    
    if variance > 20:  # High performance variance
        pattern_data['needs_intervention'] = True
        pattern_data['pattern_type'] = 'INCONSISTENT_PERFORMANCE'
        pattern_data['suggested_actions'].extend([
            'Implement structured study routine',
            'Regular progress checks',
            'Stress management techniques'
        ])

    if topic_difficulties['difficult_topics']:
        pattern_data['suggested_actions'].extend([
            f"Focus review on topics: {', '.join(topic_difficulties['difficult_topics'])}",
            'Consider alternative teaching methods',
            'Provide additional resources'
        ])

    pattern_data['learning_style'] = learning_style
    pattern_data['confidence'] = calculate_confidence_level(time_patterns, variance)
    
    return pattern_data

def analyze_time_patterns(scores, dates):
    """Analyze time-based performance patterns"""
    if len(scores) < 2:
        return {'declining': False, 'trend': 'INSUFFICIENT_DATA'}
        
    trend = sum(scores[i] - scores[i-1] for i in range(1, len(scores))) / (len(scores)-1)
    
    return {
        'declining': trend < -5,  # Significant decline threshold
        'trend': 'DECLINING' if trend < -5 else 'IMPROVING' if trend > 5 else 'STABLE',
        'trend_value': trend
    }

def calculate_performance_variance(scores):
    """Calculate performance consistency"""
    if not scores:
        return 0
    mean = sum(scores) / len(scores)
    variance = sum((x - mean) ** 2 for x in scores) / len(scores)
    return variance ** 0.5  # Standard deviation

def analyze_learning_style(student, subject):
    """Analyze student's learning style based on performance patterns"""
    # Get performance across different assessment types
    assessment_performance = {}
    for assessment_type in ['QUIZ', 'ASSIGNMENT', 'PROJECT', 'EXAM']:
        avg_score = AssessmentResult.objects.filter(
            student=student,
            assessment__subject=subject,
            assessment__assessment_type__name=assessment_type
        ).aggregate(Avg('score'))['score__avg'] or 0
        
        assessment_performance[assessment_type] = avg_score

    # Determine dominant learning style
    if assessment_performance['PROJECT'] > assessment_performance['EXAM']:
        return 'PRACTICAL_LEARNER'
    elif assessment_performance['QUIZ'] > assessment_performance['ASSIGNMENT']:
        return 'ACTIVE_LEARNER'
    else:
        return 'THEORETICAL_LEARNER'

def analyze_topic_difficulties(results):
    """Analyze which topics are challenging for the student"""
    topic_scores = {}
    difficult_topics = []
    
    for result in results:
        topic = result.assessment.title  # Assuming title contains topic info
        if topic not in topic_scores:
            topic_scores[topic] = []
        topic_scores[topic].append(result.score)
    
    for topic, scores in topic_scores.items():
        avg_score = sum(scores) / len(scores)
        if avg_score < 50:  # Below passing threshold
            difficult_topics.append(topic)
            
    return {
        'difficult_topics': difficult_topics,
        'topic_scores': topic_scores
    }

def calculate_confidence_level(time_patterns, variance):
    """Calculate confidence level in the analysis"""
    confidence = 0.0
    
    # More data points = higher confidence
    if time_patterns.get('trend') != 'INSUFFICIENT_DATA':
        confidence += 0.4
    
    # Lower variance = higher confidence
    if variance < 10:
        confidence += 0.3
    elif variance < 20:
        confidence += 0.2
        
    # Strong trend = higher confidence
    if abs(time_patterns.get('trend_value', 0)) > 10:
        confidence += 0.3
        
    return min(confidence, 1.0)  # Cap at 100%

def create_smart_intervention(student, subject, pattern_data):
    """Create intelligent intervention based on pattern analysis"""
    intervention = SmartIntervention.objects.create(
        student=student,
        subject=subject,
        term=Term.objects.filter(is_current=True).first(),
        trigger_type=pattern_data['pattern_type'],
        risk_level=calculate_risk_level(pattern_data),
        automated_suggestions={
            'actions': pattern_data['suggested_actions'],
            'learning_style': pattern_data['learning_style'],
            'confidence': pattern_data['confidence']
        }
    )

    # Create corresponding alert
    AutomatedAlert.objects.create(
        student=student,
        alert_type='SMART_INTERVENTION',
        subject=subject,
        description=f"Smart intervention created: {pattern_data['pattern_type']}",
        severity=intervention.risk_level
    )

def calculate_risk_level(pattern_data):
    """Calculate risk level based on pattern analysis"""
    risk = 1  # Base risk level
    
    if pattern_data['pattern_type'] == 'DECLINING_PERFORMANCE':
        risk += 2
    if pattern_data['pattern_type'] == 'INCONSISTENT_PERFORMANCE':
        risk += 1
    if len(pattern_data['suggested_actions']) > 3:
        risk += 1
    if pattern_data['confidence'] > 0.8:
        risk += 1
        
    return min(risk, 5)  # Cap at 5

@shared_task
def update_learning_paths():
    """Weekly task to update personalized learning paths"""
    current_term = Term.objects.filter(is_current=True).first()
    if not current_term:
        return

    predictor = StudentPerformancePredictor()
    style_analyzer = LearningStyleAnalyzer()
    difficulty_adjuster = AdaptiveDifficultyAdjuster()

    for learning_path in LearningPath.objects.filter(
        term=current_term,
        is_completed=False
    ):
        # Update predictions
        predictions = predictor.predict_final_grade(
            learning_path.student,
            learning_path.subject,
            current_term
        )
        
        # Analyze learning style
        learning_style = style_analyzer.analyze_learning_style(
            learning_path.student,
            current_term
        )
        
        # Adjust difficulty
        difficulty_adjustments = difficulty_adjuster.adjust_difficulty(
            learning_path.student,
            learning_path.subject
        )
        
        # Update milestones based on new data
        update_milestones(learning_path, predictions, learning_style, difficulty_adjustments)
