from rest_framework import viewsets, views, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import render, get_object_or_404
from django.http import HttpResponse
from django.template.loader import get_template
from django.db import models, transaction
from django.core.exceptions import ValidationError as DjangoValidationError
from django.utils import timezone
import logging

# Import custom exceptions and validators
from .exceptions import (
    FeeException, InsufficientBalanceException, PaymentVerificationException,
    DuplicatePaymentException, FeeStructureNotFoundException,
    StudentNotEnrolledException, PaymentValidator, ValidationError
)

# Try to import WeasyPrint, but don't fail if it's not available
try:
    from weasyprint import HTML
    WEASYPRINT_AVAILABLE = True
except OSError:
    # WeasyPrint dependencies are not installed
    HTML = None
    WEASYPRINT_AVAILABLE = False
    
import matplotlib.pyplot as plt
import io
import base64
from datetime import datetime
from .models import (
    Fee<PERSON>tructure, FeePayment, FeeBalance,
    PaymentReminder, Lease, LeasePayment,
    StudentFeeAccount, FeeStatement, PaymentAnalytics,
    Invoice, Receipt
)
# Import required models for reports
from academics.models import TermResult, StudentAttendance, TeacherPerformanceMetrics
from .serializers import (
    FeeStructureSerializer, FeePaymentSerializer,
    FeeBalanceSerializer, PaymentReminderSerializer,
    StudentFeeAccountSerializer, FeeStatementSerializer,
    PaymentAnalyticsSerializer, ParentChildrenFeesSerializer,
    StudentFeeSummarySerializer, PaymentAnalyticsDetailSerializer,
    ReceiptSerializer, InvoiceSerializer
)
from .analytics import (
    get_payment_analytics, get_student_fee_summary,
    get_parent_children_fees, update_payment_analytics,
    generate_collection_report
)
from schools.models import School
from users.models import Student, Teacher
from academics.models import Term

# Setup logging
logger = logging.getLogger(__name__)

class FeeManagementViewSet(viewsets.ModelViewSet):
    """
    A viewset for managing fee-related operations.
    """
    queryset = FeeStructure.objects.all()
    serializer_class = FeeStructureSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """
        Filter queryset based on school if specified
        """
        queryset = super().get_queryset()
        school_id = self.request.query_params.get('school', None)
        if (school_id is not None):
            queryset = queryset.filter(school_id=school_id)
        return queryset

    @action(detail=True, methods=['get'])
    def student_balance(self, request, pk=None):
        """
        Get student's fee balance.
        """
        student = self.get_object()
        balance = FeeBalance.objects.filter(student=student).latest('term')
        return Response(FeeBalanceSerializer(balance).data)

    @action(detail=True, methods=['post'])
    def record_payment(self, request, pk=None):
        """
        Record new fee payment.
        """
        student = self.get_object()
        serializer = FeePaymentSerializer(data=request.data)
        if serializer.is_valid():
            payment = serializer.save(student=student)
            # Update balance
            balance = FeeBalance.objects.get(
                student=student,
                term=payment.term
            )
            balance.amount_paid += payment.amount
            balance.calculate_balance()
            return Response(serializer.data)
        return Response(serializer.errors, status=400)

    @action(detail=True, methods=['get'])
    def payment_history(self, request, pk=None):
        """
        Get student's payment history.
        """
        student = self.get_object()
        payments = FeePayment.objects.filter(student=student)
        return Response(FeePaymentSerializer(payments, many=True).data)

    @action(detail=True, methods=['post'])
    def send_reminder(self, request, pk=None):
        """
        Send fee reminder to parent.
        """
        student = self.get_object()
        balance = FeeBalance.objects.get(
            student=student,
            term_id=request.data['term']
        )
        if balance.balance > 0:
            reminder = PaymentReminder.objects.create(  # Changed from FeeReminder
                student=student,
                term_id=request.data['term'],
                due_amount=balance.balance,
                reminder_type=request.data.get('type', 'EMAIL')
            )
            # Send actual reminder
            self._send_fee_reminder(reminder)
            return Response({'status': 'Reminder sent'})
        return Response({'status': 'No balance due'})

class PaymentCallbackView(views.APIView):
    """
    Handle payment callbacks from payment providers
    """
    def post(self, request, *args, **kwargs):
        payment_provider = request.query_params.get('provider', '').lower()
        
        if payment_provider == 'mpesa':
            return self.handle_mpesa_callback(request.data)
        elif payment_provider == 'paypal':
            return self.handle_paypal_callback(request.data)
        
        return Response({'error': 'Invalid payment provider'}, status=400)
    
    def handle_mpesa_callback(self, data):
        """Handle M-Pesa payment callback"""
        # Implement M-Pesa specific callback handling
        try:
            # Extract payment details from callback data
            reference = data.get('reference')
            amount = data.get('amount')
            
            # Update payment record
            payment = FeePayment.objects.get(reference_number=reference)
            payment.verified = True
            payment.save()
            
            return Response({'status': 'success'})
        except Exception as e:
            return Response({'error': str(e)}, status=400)
    
    def handle_paypal_callback(self, data):
        """Handle PayPal payment callback"""
        # Implement PayPal specific callback handling
        try:
            # Extract payment details from callback data
            reference = data.get('reference')
            amount = data.get('amount')
            
            # Update payment record
            payment = FeePayment.objects.get(reference_number=reference)
            payment.verified = True
            payment.save()
            
            return Response({'status': 'success'})
        except Exception as e:
            return Response({'error': str(e)}, status=400)

def render_to_pdf(template_src, context_dict):
    """
    Render a template to a PDF file.
    """
    template = get_template(template_src)
    html = template.render(context_dict)
    
    if not WEASYPRINT_AVAILABLE:
        # Return a simple response with the HTML content if WeasyPrint is not available
        return html.encode('utf-8')
        
    pdf_file = HTML(string=html).write_pdf()
    return pdf_file

def generate_performance_chart(student):
    """
    Generate a performance chart for a student.
    """
    results = TermResult.objects.filter(student=student).order_by('term__start_date')
    terms = [result.term.name for result in results]
    scores = [result.total_score for result in results]

    plt.figure(figsize=(10, 5))
    plt.plot(terms, scores, marker='o')
    plt.title('Performance Over Terms')
    plt.xlabel('Term')
    plt.ylabel('Total Score')
    plt.grid(True)

    buffer = io.BytesIO()
    plt.savefig(buffer, format='png')
    buffer.seek(0)
    image_png = buffer.getvalue()
    buffer.close()

    graphic = base64.b64encode(image_png)
    graphic = graphic.decode('utf-8')
    return graphic

def ifrs16_report(request, school_id):
    """
    Generate IFRS 16 Lease Report for a school.
    """
    school = School.objects.get(id=school_id)
    leases = Lease.objects.filter(school=school)
    lease_payments = LeasePayment.objects.filter(lease__school=school)
    reporting_period = "2025"  # Example reporting period
    
    context = {
        'school': school,
        'leases': leases,
        'lease_payments': lease_payments,
        'reporting_period': reporting_period,
        'current_date': datetime.now().strftime("%Y-%m-%d"),
    }
    pdf = render_to_pdf('reports/ifrs16_report.html', context)
    return handle_pdf_response(pdf, f"IFRS16_Report_{school.name}.pdf")

def fee_collection_report(request, school_id):
    """
    Generate Fee Collection Report for a school.
    """
    school = School.objects.get(id=school_id)
    
    # Get term from request parameters, default to current term
    term_id = request.GET.get('term')
    try:
        term = Term.objects.get(id=term_id) if term_id else Term.objects.filter(
            school=school, 
            is_current=True  # Changed from is_active to is_current
        ).first()
        
        if not term:
            return HttpResponse("No active term found. Please specify a term.", status=400)
            
    except Term.DoesNotExist:
        return HttpResponse("Invalid term selected", status=400)
    
    # Filter payments to=by school and term
    payments = FeePayment.objects.filter(
        student__school=school,
        term=term
    )
    
    # Calculate total expected fees for the term
    total_expected = FeeStructure.objects.filter(
        school=school,
        term=term,
        is_active=True
    ).aggregate(total=models.Sum('total_amount'))['total'] or 0
    
    # Calculate total collected
    total_collected = payments.aggregate(
        total_amount=models.Sum('amount_paid')
    )['total_amount'] or 0
    
    # Calculate collection rate
    collection_rate = (total_collected / total_expected * 100) if total_expected > 0 else 0
    
    # Get payment method summary
    payment_methods = payments.values('payment_method').annotate(
        count=models.Count('id'),
        total=models.Sum('amount_paid')
    ).order_by('-total')
      # Calculate percentages for payment methods
    for method in payment_methods:
        method['name'] = dict(FeePayment.PAYMENT_METHODS)[method['payment_method']]
        method['percentage'] = (method['total'] / total_collected * 100) if total_collected > 0 else 0

    # Get finance officer details (you'll need to implement this based on your user model)
    finance_officer = request.user if request.user.is_authenticated else None
    
    context = {
        'school': school,
        'term': term,
        'payments': payments,
        'total_expected': total_expected,
        'total_collected': total_collected,
        'collection_rate': collection_rate,
        'payment_methods': payment_methods,
        'finance_officer': finance_officer,
        'reporting_period': f"{term.name} {term.academic_year}",
        'current_date': datetime.now().strftime("%Y-%m-%d"),
    }
    pdf = render_to_pdf('reports/fee_collection_report.html', context)
    return handle_pdf_response(pdf, f"Fee_Collection_Report_{school.name}.pdf")

def outstanding_fees_report(request, school_id):
    """
    Generate Outstanding Fees Report for a school.
    """
    school = School.objects.get(id=school_id)
    outstanding_balances = FeeBalance.objects.filter(student__school=school, balance__gt=0)
    reporting_period = "2025"  # Example reporting period
    
    context = {
        'school': school,
        'outstanding_balances': outstanding_balances,
        'reporting_period': reporting_period,
        'current_date': datetime.now().strftime("%Y-%m-%d"),
    }
    pdf = render_to_pdf('reports/outstanding_fees_report.html', context)
    return handle_pdf_response(pdf, f"Outstanding_Fees_Report_{school.name}.pdf")

def student_performance_report(request, school_id):
    """
    Generate Student Performance Report for a school.
    """
    school = School.objects.get(id=school_id)
    results = TermResult.objects.filter(student__school=school)
    reporting_period = "2025"  # Example reporting period
    
    context = {
        'school': school,
        'results': results,
        'reporting_period': reporting_period,
        'current_date': datetime.now().strftime("%Y-%m-%d"),
    }
    pdf = render_to_pdf('reports/student_performance_report.html', context)
    return handle_pdf_response(pdf, f"Student_Performance_Report_{school.name}.pdf")

def attendance_report(request, school_id):
    """
    Generate Attendance Report for a school.
    """
    school = School.objects.get(id=school_id)
    attendances = StudentAttendance.objects.filter(student__school=school)
    reporting_period = "2025"  # Example reporting period
    
    context = {
        'school': school,
        'attendances': attendances,
        'reporting_period': reporting_period,
        'current_date': datetime.now().strftime("%Y-%m-%d"),
    }
    pdf = render_to_pdf('reports/attendance_report.html', context)
    return handle_pdf_response(pdf, f"Attendance_Report_{school.name}.pdf")

def teacher_performance_report(request, school_id):
    """
    Generate Teacher Performance Report for a school.
    """
    school = School.objects.get(id=school_id)
    teacher_metrics = TeacherPerformanceMetrics.objects.filter(teacher__school=school)
    reporting_period = "2025"  # Example reporting period
    
    context = {
        'school': school,
        'teacher_metrics': teacher_metrics,
        'reporting_period': reporting_period,
        'current_date': datetime.now().strftime("%Y-%m-%d"),
    }
    pdf = render_to_pdf('reports/teacher_performance_report.html', context)
    return handle_pdf_response(pdf, f"Teacher_Performance_Report_{school.name}.pdf")

def individual_student_report(request, student_id):
    """
    Generate Individual Student Performance Report.
    """
    student = get_object_or_404(Student, id=student_id)
    results = TermResult.objects.filter(student=student)
    reporting_period = "2025"  # Example reporting period
    
    # Generate a performance chart for visual representation
    performance_chart = generate_performance_chart(student)
    
    context = {
        'student': student,
        'results': results,
        'performance_chart': performance_chart,
        'reporting_period': reporting_period,
        'current_date': datetime.now().strftime("%Y-%m-%d"),
    }
    pdf = render_to_pdf('reports/individual_student_report.html', context)
    return handle_pdf_response(pdf, f"Student_Report_{student.id}.pdf")

def individual_teacher_report(request, teacher_id):
    """
    Generate Individual Teacher Performance Report.
    """
    teacher = get_object_or_404(Teacher, id=teacher_id)
    teacher_metrics = TeacherPerformanceMetrics.objects.filter(teacher=teacher)
    reporting_period = "2025"  # Example reporting period

    context = {
        'teacher': teacher,
        'teacher_metrics': teacher_metrics,
        'reporting_period': reporting_period,
        'current_date': datetime.now().strftime("%Y-%m-%d"),
    }
    pdf = render_to_pdf('reports/individual_teacher_report.html', context)
    return handle_pdf_response(pdf, f"Teacher_Report_{teacher.id}.pdf")

def handle_pdf_response(pdf, filename):
    """
    Helper function to handle PDF response with graceful degradation 
    when WeasyPrint dependencies are not available
    """
    if not WEASYPRINT_AVAILABLE:
        # Return a message explaining that PDF generation requires GTK installation
        return HttpResponse(
            "PDF generation requires GTK libraries to be installed. "
            "Please follow the installation instructions at "
            "https://doc.courtbouillon.org/weasyprint/stable/first_steps.html#windows",
            content_type='text/plain'
        )
    
    response = HttpResponse(pdf, content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    return response


class ParentFeeViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for parents to view their children's fee information
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # Only return data for the authenticated parent's children
        if hasattr(self.request.user, 'parent_profile'):
            return StudentFeeAccount.objects.filter(
                student__in=self.request.user.parent_profile.children.all()
            )
        return StudentFeeAccount.objects.none()

    @action(detail=False, methods=['get'])
    def children_fees(self, request):
        """
        Get fee information for all children of the authenticated parent
        """
        try:
            # Validate user is a parent
            if not hasattr(request.user, 'parent_profile'):
                logger.warning(f"Non-parent user {request.user.username} attempted to access children fees")
                return Response({
                    'error': {
                        'message': 'Access denied. User is not a parent.',
                        'code': 'NOT_A_PARENT'
                    },
                    'success': False
                }, status=status.HTTP_403_FORBIDDEN)

            parent = request.user.parent_profile

            # Check if parent has any children
            if not parent.children.exists():
                return Response({
                    'data': [],
                    'message': 'No children found for this parent',
                    'success': True
                }, status=status.HTTP_200_OK)

            children_fees = get_parent_children_fees(parent)

            # Format data for serialization
            formatted_data = []
            for child_fee in children_fees:
                if child_fee:
                    try:
                        formatted_data.append({
                            'student_id': child_fee['student'].id,
                            'student_name': f"{child_fee['student'].first_name} {child_fee['student'].last_name}",
                            'class_name': child_fee['student'].current_class.name if child_fee['student'].current_class else 'N/A',
                            'term_name': child_fee['term'].name,
                            'total_fees': float(child_fee['fee_account'].total_fees),
                            'total_paid': float(child_fee['fee_account'].total_paid),
                            'balance': float(child_fee['fee_account'].balance),
                            'last_payment_date': child_fee['fee_account'].last_payment_date,
                            'is_fully_paid': child_fee['fee_account'].is_fully_paid
                        })
                    except (AttributeError, KeyError) as e:
                        logger.error(f"Error formatting child fee data: {e}")
                        continue

            serializer = ParentChildrenFeesSerializer(formatted_data, many=True)
            return Response({
                'data': serializer.data,
                'success': True,
                'count': len(formatted_data)
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error fetching children fees for parent {request.user.username}: {e}")
            return Response({
                'error': {
                    'message': 'Failed to fetch children fee information',
                    'code': 'FETCH_ERROR'
                },
                'success': False
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def child_fee_detail(self, request):
        """
        Get detailed fee information for a specific child
        """
        student_id = request.query_params.get('student_id')
        if not student_id:
            return Response({'error': 'student_id is required'}, status=400)

        if not hasattr(request.user, 'parent_profile'):
            return Response({'error': 'User is not a parent'}, status=400)

        parent = request.user.parent_profile

        # Verify the student is a child of this parent
        try:
            student = parent.children.get(id=student_id)
        except Student.DoesNotExist:
            return Response({'error': 'Student not found or not your child'}, status=404)

        fee_summary = get_student_fee_summary(student)
        if not fee_summary:
            return Response({'error': 'No fee information found'}, status=404)

        # Format the response
        response_data = {
            'student': f"{student.first_name} {student.last_name}",
            'term': fee_summary['term'].name,
            'total_fees': fee_summary['fee_account'].total_fees,
            'total_paid': fee_summary['fee_account'].total_paid,
            'balance': fee_summary['fee_account'].balance,
            'payment_history': fee_summary['payment_history'],
            'recent_payments': FeePaymentSerializer(fee_summary['payments'][:5], many=True).data,
            'invoices': InvoiceSerializer(fee_summary['invoices'], many=True).data
        }

        serializer = StudentFeeSummarySerializer(response_data)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def payment_history(self, request):
        """
        Get payment history for a specific child
        """
        student_id = request.query_params.get('student_id')
        if not student_id:
            return Response({'error': 'student_id is required'}, status=400)

        if not hasattr(request.user, 'parent_profile'):
            return Response({'error': 'User is not a parent'}, status=400)

        parent = request.user.parent_profile

        try:
            student = parent.children.get(id=student_id)
        except Student.DoesNotExist:
            return Response({'error': 'Student not found or not your child'}, status=404)

        payments = FeePayment.objects.filter(student=student).order_by('-payment_date')
        serializer = FeePaymentSerializer(payments, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def receipts(self, request):
        """
        Get receipts for a specific child
        """
        student_id = request.query_params.get('student_id')
        if not student_id:
            return Response({'error': 'student_id is required'}, status=400)

        if not hasattr(request.user, 'parent_profile'):
            return Response({'error': 'User is not a parent'}, status=400)

        parent = request.user.parent_profile

        try:
            student = parent.children.get(id=student_id)
        except Student.DoesNotExist:
            return Response({'error': 'Student not found or not your child'}, status=404)

        receipts = Receipt.objects.filter(
            payment__student=student
        ).order_by('-date_issued')

        serializer = ReceiptSerializer(receipts, many=True)
        return Response(serializer.data)


class PaymentAnalyticsViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for payment analytics (admin/finance staff only)
    """
    queryset = PaymentAnalytics.objects.all()
    serializer_class = PaymentAnalyticsSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        school_id = self.request.query_params.get('school', None)
        if school_id:
            queryset = queryset.filter(school_id=school_id)
        return queryset

    @action(detail=False, methods=['get'])
    def dashboard_analytics(self, request):
        """
        Get comprehensive analytics for the dashboard
        """
        school_id = request.query_params.get('school')
        if not school_id:
            return Response({'error': 'school parameter is required'}, status=400)

        try:
            school = School.objects.get(id=school_id)
        except School.DoesNotExist:
            return Response({'error': 'School not found'}, status=404)

        # Get analytics data
        analytics_data = get_payment_analytics(school)
        serializer = PaymentAnalyticsDetailSerializer(analytics_data)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def collection_report(self, request):
        """
        Generate collection report
        """
        school_id = request.query_params.get('school')
        term_id = request.query_params.get('term')

        if not school_id:
            return Response({'error': 'school parameter is required'}, status=400)

        try:
            school = School.objects.get(id=school_id)
            term = Term.objects.get(id=term_id) if term_id else None
        except (School.DoesNotExist, Term.DoesNotExist):
            return Response({'error': 'School or Term not found'}, status=404)

        report_data = generate_collection_report(school, term)
        return Response(report_data)

    @action(detail=False, methods=['post'])
    def update_analytics(self, request):
        """
        Manually update analytics for a school
        """
        school_id = request.data.get('school')
        if not school_id:
            return Response({'error': 'school parameter is required'}, status=400)

        try:
            school = School.objects.get(id=school_id)
        except School.DoesNotExist:
            return Response({'error': 'School not found'}, status=404)

        analytics = update_payment_analytics(school)
        serializer = PaymentAnalyticsSerializer(analytics)
        return Response(serializer.data)


class ReceiptViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing receipts
    """
    queryset = Receipt.objects.all()
    serializer_class = ReceiptSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        student_id = self.request.query_params.get('student')
        if student_id:
            queryset = queryset.filter(payment__student_id=student_id)
        return queryset

    @action(detail=True, methods=['get'])
    def download_pdf(self, request, pk=None):
        """
        Download receipt as PDF
        """
        receipt = self.get_object()
        if receipt.pdf_file:
            response = HttpResponse(receipt.pdf_file.read(), content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="receipt_{receipt.receipt_number}.pdf"'
            return response
        else:
            return Response({'error': 'PDF not available'}, status=404)

    @action(detail=False, methods=['post'])
    def generate_receipt(self, request):
        """
        Generate a new receipt for a payment
        """
        payment_id = request.data.get('payment_id')
        if not payment_id:
            return Response({'error': 'payment_id is required'}, status=400)

        try:
            payment = FeePayment.objects.get(id=payment_id)
        except FeePayment.DoesNotExist:
            return Response({'error': 'Payment not found'}, status=404)

        # Check if receipt already exists
        if hasattr(payment, 'receipt'):
            return Response({'error': 'Receipt already exists for this payment'}, status=400)

        # Create receipt
        receipt = Receipt.objects.create(
            payment=payment,
            amount=payment.amount_paid,
            issued_by=request.user.staff_profile if hasattr(request.user, 'staff_profile') else None
        )

        serializer = ReceiptSerializer(receipt)
        return Response(serializer.data)
