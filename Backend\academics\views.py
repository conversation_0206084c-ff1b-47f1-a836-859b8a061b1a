from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import PermissionDenied
from .models import (
    Department, Stream, ClassRoom, Subject, SubjectMaterial,
    Exam, ExamResult, Assignment, AssignmentSubmission,
    Notice, Announcement, Term, TimeSlot, Timetable,
    StreamPerformance, SubjectPerformance, TopStudent,
    StreamGradingSystem, PerformanceAnalytics,
    AcademicTarget, AcademicIntervention, ImprovementPlan, CalendarEvent,
    ClassProgressionRule, AcademicYear, GradingSystem, GradeScale
)
from .serializers import (
    StreamSerializer, ClassRoomSerializer, SubjectSerializer,
    SubjectMaterialSerializer, ExamSerializer, ExamResultSerializer,
    AssignmentSerializer, AssignmentSubmissionSerializer,
    NoticeSerializer, AnnouncementSerializer, TermSerializer,
    TimeSlotSerializer, TimetableSerializer,
    StreamPerformanceSerializer, SubjectPerformanceSerializer,
    TopStudentSerializer, StreamGradingSystemSerializer,
    PerformanceAnalyticsSerializer, TeacherPerformanceMetricsSerializer,
    AcademicTargetSerializer, AcademicInterventionSerializer, ImprovementPlanSerializer,
    InterventionSessionSerializer, ImprovementStrategySerializer, CalendarEventSerializer, DashboardMetricsSerializer,
    ClassProgressionRuleSerializer, AcademicYearSerializer, DepartmentSerializer,
    GradingSystemSerializer, GradeScaleSerializer, GradeSerializer
)
from .permissions import IsTeacherOrAdmin
from core.permissions import IsSchoolBranchStaff, RoleBasedPermissionMixin
from rest_framework.views import APIView
from django.utils import timezone
from dateutil.relativedelta import relativedelta
from django.db.models import Count
from users.models import Parent, Student, Teacher
from .serializers import DashboardMetricsSerializer

class AcademicYearViewSet(viewsets.ModelViewSet):
    queryset = AcademicYear.objects.all()
    serializer_class = AcademicYearSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Return all academic years for now
        return AcademicYear.objects.all()

    def create(self, request, *args, **kwargs):
        # Print the request data for debugging
        print(f"Request data: {request.data}")

        # Try to create the academic year and print any validation errors
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            print(f"Validation errors: {serializer.errors}")

        return super().create(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        instance = self.get_object()

        # Check if trying to archive a current academic year
        if instance.is_current and request.data.get('is_archived', False):
            return Response(
                {'error': 'Cannot archive a current academic year. Please set it as not current first.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        return super().update(request, *args, **kwargs)

    @action(detail=True, methods=['post'], url_path='archive')
    def archive(self, request, pk=None):
        """
        Archive an academic year

        This endpoint archives an academic year, making it inactive but preserving its data.
        Current academic years cannot be archived.

        Args:
            request: The HTTP request object
            pk: The primary key of the academic year to archive

        Returns:
            200 OK: Academic year archived successfully
            400 Bad Request: Cannot archive a current academic year
            404 Not Found: Academic year not found
        """
        academic_year = self.get_object()

        # Check if the academic year is current
        if academic_year.is_current:
            return Response(
                {'error': 'Cannot archive a current academic year. Please set it as not current first.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Archive the academic year
        academic_year.is_archived = True
        academic_year.save()

        return Response(
            {'message': f'Academic year {academic_year.year} has been archived successfully.'},
            status=status.HTTP_200_OK
        )

    @action(detail=True, methods=['post'], url_path='unarchive')
    def unarchive(self, request, pk=None):
        """
        Unarchive an academic year

        This endpoint unarchives an academic year, making it active again.

        Args:
            request: The HTTP request object
            pk: The primary key of the academic year to unarchive

        Returns:
            200 OK: Academic year unarchived successfully
            404 Not Found: Academic year not found
        """
        academic_year = self.get_object()

        # Unarchive the academic year
        academic_year.is_archived = False
        academic_year.save()

        return Response(
            {'message': f'Academic year {academic_year.year} has been unarchived successfully.'},
            status=status.HTTP_200_OK
        )

    @action(detail=True, methods=['post'], url_path='copy_to_template')
    def copy_to_template(self, request, pk=None):
        """
        Copy an academic year to a template

        This endpoint creates a new academic year template based on an existing
        academic year. It copies all terms and their date structures.

        Args:
            request: The HTTP request object
            pk: The primary key of the academic year to copy

        Request Parameters:
            template_name: The name for the new template

        Returns:
            201 Created: Template created successfully with template data
            400 Bad Request: Missing required parameters
            404 Not Found: Academic year not found
            500 Internal Server Error: Error during template creation
        """
        from .academic_templates import AcademicYearTemplate, TermTemplate
        from .template_serializers import AcademicYearTemplateSerializer
        from django.db import transaction
        import logging

        # Get logger
        logger = logging.getLogger('academics')

        # Get the academic year to copy
        academic_year = self.get_object()
        template_name = request.data.get('template_name') or request.data.get('templateName')

        if not template_name:
            return Response(
                {
                    'error': 'Template name is required',
                    'detail': 'Please provide a name for the new template in the "template_name" field.'
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            with transaction.atomic():
                # Create the template
                template = AcademicYearTemplate.objects.create(
                    school=academic_year.school_name,
                    year=template_name,
                    start_date=academic_year.start_date,
                    end_date=academic_year.end_date,
                    is_active=True,
                    is_archived=False
                )

                logger.info(f"Created template {template.id} from academic year {academic_year.id}")

                # Copy terms to term templates
                for term in academic_year.terms.all():
                    term_template = TermTemplate.objects.create(
                        academic_year_template=template,
                        name=term.name,
                        start_date=term.start_date,
                        end_date=term.end_date
                    )
                    logger.info(f"Created term template {term_template.id} from term {term.id}")

                # Return the created template with its terms
                serializer = AcademicYearTemplateSerializer(template)
                return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"Error copying academic year to template: {str(e)}\n{error_details}")
            return Response(
                {
                    'error': 'Failed to create template',
                    'detail': str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class GradingSystemViewSet(viewsets.ModelViewSet):
    queryset = GradingSystem.objects.all()
    serializer_class = GradingSystemSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Filter by school if provided
        school_id = self.request.GET.get('school', None)
        if school_id:
            return GradingSystem.objects.filter(school_id=school_id)
        return GradingSystem.objects.all()

class GradeViewSet(RoleBasedPermissionMixin, viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for education levels as grades
    This provides a simplified view of education levels for the frontend
    """
    serializer_class = GradeSerializer
    permission_classes = [IsAuthenticated]
    required_groups = ['Administration', 'School Administrators', 'Academic Staff', 'Teachers']

    from typing import Any
    from django.db.models.query import QuerySet

    from django.db.models.query import QuerySet

    def get_queryset(self):
        from .curriculum_models import EducationLevel, CurriculumSystem

        # Get the user's school
        user = self.request.user

        def get_user_school_branch(user):
            if hasattr(user, 'staff_profile') and user.staff_profile:
                return user.staff_profile.school_branch
            elif hasattr(user, 'teacher_profile') and user.teacher_profile:
                return user.teacher_profile.school_branch
            elif hasattr(user, 'student_profile') and user.student_profile:
                return user.student_profile.school_branch
            elif hasattr(user, 'teacher') and user.teacher:
                return user.teacher.school_branch
            elif hasattr(user, 'student') and user.student:
                return user.student.school_branch
            return None

        school_branch = get_user_school_branch(user)
        if not school_branch:
            return EducationLevel.objects.none()

        school = school_branch.school

        # Try to get the school's curriculum config
        from .curriculum_models import SchoolCurriculumConfig
        try:
            config = SchoolCurriculumConfig.objects.get(school=school)
            # Get education levels for both primary and secondary curricula
            queryset = EducationLevel.objects.filter(
                curriculum_system__in=[config.primary_curriculum, config.secondary_curriculum]
            ).order_by('curriculum_system', 'sequence')
            return queryset
        except SchoolCurriculumConfig.DoesNotExist:
            # If no config exists, return all education levels from all active curriculum systems
            curriculum_systems = CurriculumSystem.objects.filter(is_active=True)
            return EducationLevel.objects.filter(curriculum_system__in=curriculum_systems).order_by('curriculum_system', 'sequence')

class GradeScaleViewSet(viewsets.ModelViewSet):
    queryset = GradeScale.objects.all()
    serializer_class = GradeScaleSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Filter by grading system if provided
        grading_system_id = self.request.query_params.get('grading_system', None)
        if grading_system_id:
            return GradeScale.objects.filter(grading_system_id=grading_system_id)
        return GradeScale.objects.all()

class StreamViewSet(RoleBasedPermissionMixin, viewsets.ModelViewSet):
    queryset = Stream.objects.all()
    serializer_class = StreamSerializer
    permission_classes = [IsAuthenticated]
    required_groups = ['Administration', 'School Administrators', 'Academic Staff', 'Teachers']

    def get_queryset(self):
        # Return all streams for now
        return Stream.objects.all()

class ClassRoomViewSet(RoleBasedPermissionMixin, viewsets.ModelViewSet):
    queryset = ClassRoom.objects.all()
    serializer_class = ClassRoomSerializer
    permission_classes = [IsAuthenticated]
    required_groups = ['Administration', 'School Administrators', 'Academic Staff', 'Teachers']

    def get_queryset(self):
        queryset = ClassRoom.objects.all()

        # Filter by curriculum system
        curriculum_system = self.request.query_params.get('curriculum_system')
        if curriculum_system:
            queryset = queryset.filter(education_level__curriculum_system__code=curriculum_system)

        # Filter by education level
        education_level = self.request.query_params.get('education_level')
        if education_level:
            queryset = queryset.filter(education_level=education_level)

        # Filter by grade level
        grade_level = self.request.query_params.get('grade_level')
        if grade_level:
            queryset = queryset.filter(grade_level=grade_level)

        return queryset

    def perform_create(self, serializer):
        # Validate education level if provided
        # Use getattr to support both DRF Request and Django HttpRequest
        data = getattr(self.request, 'data', None) or self.request.POST
        education_level_id = data.get('education_level')
        if education_level_id:
            from .curriculum_models import EducationLevel
            try:
                education_level = EducationLevel.objects.get(id=education_level_id)
                # Set grade_level based on education_level sequence if not provided
                if not data.get('grade_level'):
                    serializer.validated_data['grade_level'] = education_level.sequence
            except EducationLevel.DoesNotExist:
                from rest_framework.exceptions import ValidationError
                raise ValidationError({'education_level': 'Invalid education level ID'})

        serializer.save()

class DepartmentViewSet(viewsets.ModelViewSet):
    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Return all departments for now
        return Department.objects.all()

class SubjectViewSet(RoleBasedPermissionMixin, viewsets.ModelViewSet):
    queryset = Subject.objects.all()
    serializer_class = SubjectSerializer
    permission_classes = [IsAuthenticated]
    required_groups = ['Administration', 'School Administrators', 'Academic Staff', 'Teachers']

    def get_queryset(self):
        queryset = Subject.objects.all()

        # Filter by class
        class_name = self.request.query_params.get('class_name')
        if class_name:
            queryset = queryset.filter(class_name=class_name)

        # Filter by stream
        stream = self.request.query_params.get('stream')
        if stream:
            queryset = queryset.filter(stream=stream)

        # Filter by department
        department = self.request.query_params.get('department')
        if department:
            queryset = queryset.filter(department=department)

        return queryset

    @action(detail=True, methods=['post'])
    def ensure_all_students_enrolled(self, request, pk=None):
        """Ensure all students in the class are enrolled in this subject if it's compulsory"""
        subject = self.get_object()

        if not subject.is_compulsory:
            return Response(
                {"detail": "This action is only available for compulsory subjects."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Call the method to ensure all students are enrolled
        subject.ensure_all_students_enrolled()

        # Return the updated subject
        serializer = self.get_serializer(subject)
        return Response(serializer.data)

class SubjectMaterialViewSet(viewsets.ModelViewSet):
    queryset = SubjectMaterial.objects.all()
    serializer_class = SubjectMaterialSerializer

class ExamViewSet(viewsets.ModelViewSet):
    queryset = Exam.objects.all()
    serializer_class = ExamSerializer

class ExamResultViewSet(viewsets.ModelViewSet):
    queryset = ExamResult.objects.all()
    serializer_class = ExamResultSerializer

class AssignmentViewSet(viewsets.ModelViewSet):
    queryset = Assignment.objects.all()
    serializer_class = AssignmentSerializer

class AssignmentSubmissionViewSet(viewsets.ModelViewSet):
    queryset = AssignmentSubmission.objects.all()
    serializer_class = AssignmentSubmissionSerializer

class NoticeViewSet(viewsets.ModelViewSet):
    queryset = Notice.objects.all()
    serializer_class = NoticeSerializer

class AnnouncementViewSet(viewsets.ModelViewSet):
    queryset = Announcement.objects.all()
    serializer_class = AnnouncementSerializer

class TermViewSet(viewsets.ModelViewSet):
    queryset = Term.objects.all()
    serializer_class = TermSerializer

    def get_queryset(self):
        queryset = Term.objects.all()

        # Filter by school
        school = self.request.query_params.get('school', None)
        if school:
            queryset = queryset.filter(school_id=school)

        # Filter by school branch
        branch = self.request.query_params.get('branch', None)
        if branch:
            queryset = queryset.filter(school_branch_id=branch)

        # Filter by academic year
        academic_year = self.request.query_params.get('academic_year', None)
        if academic_year:
            queryset = queryset.filter(academic_year_id=academic_year)

        # Filter by current status
        is_current = self.request.query_params.get('is_current', None)
        if is_current is not None:
            is_current_bool = is_current.lower() == 'true'
            queryset = queryset.filter(is_current=is_current_bool)

        # Filter by archived status
        is_archived = self.request.query_params.get('is_archived', None)
        if is_archived is not None:
            is_archived_bool = is_archived.lower() == 'true'
            queryset = queryset.filter(is_archived=is_archived_bool)

        return queryset

    @action(detail=True, methods=['patch'])
    def set_current(self, request, pk=None):
        term = self.get_object()
        term.is_current = True
        term.save()
        return Response({'status': 'success', 'message': f'{term.name} set as current term'})

    @action(detail=True, methods=['patch'])
    def archive(self, request, pk=None):
        term = self.get_object()
        term.is_archived = True
        term.save()
        return Response({'status': 'success', 'message': f'{term.name} archived successfully'})

    @action(detail=True, methods=['patch'])
    def unarchive(self, request, pk=None):
        term = self.get_object()
        term.is_archived = False
        term.save()
        return Response({'status': 'success', 'message': f'{term.name} unarchived successfully'})

class TimeSlotViewSet(viewsets.ModelViewSet):
    queryset = TimeSlot.objects.all()
    serializer_class = TimeSlotSerializer

class TimetableViewSet(viewsets.ModelViewSet):
    queryset = Timetable.objects.all()
    serializer_class = TimetableSerializer

    def get_queryset(self):
        queryset = Timetable.objects.all()
        class_name = self.request.query_params.get('class', None)
        term = self.request.query_params.get('term', None)
        school = self.request.query_params.get('school', None)

        if class_name:
            queryset = queryset.filter(class_name_id=class_name)
        if term:
            queryset = queryset.filter(term_id=term)
        if school:
            queryset = queryset.filter(school_id=school)

        return queryset

class StreamPerformanceViewSet(viewsets.ModelViewSet):
    queryset = StreamPerformance.objects.all()
    serializer_class = StreamPerformanceSerializer

    @action(detail=False, methods=['get'])
    def current_term(self, request):
        current_term = Term.objects.filter(is_current=True).first()
        if (current_term):
            performances = self.queryset.filter(term=current_term)
            serializer = self.get_serializer(performances, many=True)
            return Response(serializer.data)
        return Response({'error': 'No current term found'}, status=400)

class SubjectPerformanceViewSet(viewsets.ModelViewSet):
    queryset = SubjectPerformance.objects.all()
    serializer_class = SubjectPerformanceSerializer

    @action(detail=False, methods=['get'])
    def best_performing(self, request):
        term_id = request.query_params.get('term')
        if not term_id:
            return Response({'error': 'Term ID is required'}, status=400)

        top_subjects = self.queryset.filter(term_id=term_id)\
            .order_by('-average_score')[:10]
        serializer = self.get_serializer(top_subjects, many=True)
        return Response(serializer.data)

class TopStudentViewSet(viewsets.ModelViewSet):
    queryset = TopStudent.objects.all()
    serializer_class = TopStudentSerializer

    @action(detail=False, methods=['get'])
    def rankings(self, request):
        term_id = request.query_params.get('term')
        stream_id = request.query_params.get('stream')
        class_id = request.query_params.get('class')

        if not term_id:
            return Response({'error': 'Term ID is required'}, status=400)

        # Calculate rankings for the specified scope
        TopStudent.calculate_rankings(
            term_id=term_id,
            stream_id=stream_id,
            class_id=class_id
        )

        # Return top 10 students
        top_students = self.queryset.filter(term_id=term_id)
        if stream_id:
            top_students = top_students.filter(stream_id=stream_id)
        if class_id:
            top_students = top_students.filter(class_id=class_id)

        top_students = top_students.order_by('-average_score')[:10]
        serializer = self.get_serializer(top_students, many=True)
        return Response(serializer.data)

class PerformanceAnalyticsViewSet(viewsets.ModelViewSet):
    queryset = PerformanceAnalytics.objects.all()
    serializer_class = PerformanceAnalyticsSerializer

    @action(detail=False, methods=['get'])
    def student_progress(self, request):
        student_id = request.query_params.get('student')
        term_id = request.query_params.get('term')

        queryset = self.queryset
        if student_id:
            queryset = queryset.filter(student_id=student_id)
        if term_id:
            queryset = queryset.filter(term_id=term_id)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def intervention_needed(self, request):
        term_id = request.query_params.get('term')
        if not term_id:
            return Response({'error': 'Term ID is required'}, status=400)

        students_needing_help = self.queryset.filter(
            term_id=term_id,
            intervention_needed=True
        )
        serializer = self.get_serializer(students_needing_help, many=True)
        return Response(serializer.data)

class TeacherPerformanceMetricsViewSet(viewsets.ModelViewSet):
    queryset = TeacherPerformanceMetrics.objects.all()
    serializer_class = TeacherPerformanceMetricsSerializer

    @action(detail=False, methods=['get'])
    def department_performance(self, request):
        term_id = request.query_params.get('term')
        subject_id = request.query_params.get('subject')

        queryset = self.queryset
        if term_id:
            queryset = queryset.filter(term_id=term_id)
        if subject_id:
            queryset = queryset.filter(subject_id=subject_id)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

class AcademicTargetViewSet(viewsets.ModelViewSet):
    queryset = AcademicTarget.objects.all()
    serializer_class = AcademicTargetSerializer

    @action(detail=False, methods=['get'])
    def student_targets(self, request):
        student_id = request.query_params.get('student')
        term_id = request.query_params.get('term')

        if not student_id:
            return Response({'error': 'Student ID is required'}, status=400)

        queryset = self.queryset.filter(student_id=student_id)
        if term_id:
            queryset = queryset.filter(term_id=term_id)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

class AcademicInterventionViewSet(viewsets.ModelViewSet):
    queryset = AcademicIntervention.objects.all()
    serializer_class = AcademicInterventionSerializer

    @action(detail=True, methods=['post'])
    def add_session(self, request, pk=None):
        intervention = self.get_object()
        serializer = InterventionSessionSerializer(data=request.data)

        if serializer.is_valid():
            serializer.save(intervention=intervention)
            return Response(serializer.data, status=201)
        return Response(serializer.errors, status=400)

    @action(detail=False, methods=['get'])
    def active_interventions(self, request):
        queryset = self.queryset.filter(
            status='IN_PROGRESS',
            end_date__gte=timezone.now().date()
        )
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

class ImprovementPlanViewSet(viewsets.ModelViewSet):
    queryset = ImprovementPlan.objects.all()
    serializer_class = ImprovementPlanSerializer

    @action(detail=True, methods=['post'])
    def add_strategy(self, request, pk=None):
        plan = self.get_object()
        serializer = ImprovementStrategySerializer(data=request.data)

        if serializer.is_valid():
            serializer.save(plan=plan)
            return Response(serializer.data, status=201)
        return Response(serializer.errors, status=400)

    @action(detail=True, methods=['get'])
    def progress_report(self, request, pk=None):
        plan = self.get_object()
        strategies = plan.strategies.all()
        achieved = strategies.filter(achieved=True).count()
        total = strategies.count()

        return Response({
            'total_strategies': total,
            'achieved': achieved,
            'progress_percentage': (achieved / total * 100) if total > 0 else 0,
            'strategies': ImprovementStrategySerializer(strategies, many=True).data
        })

class AcademicAnalyticsViewSet(viewsets.ViewSet):
    permission_classes = [IsTeacherOrAdmin]

    @action(detail=True, methods=['get'])
    def student_profile(self, request, pk=None):
        """Get student's academic profile"""
        student = self.get_student(pk)
        return Response({
            'profile': 'Implementation pending'
        })

class PerformanceTrackingViewSet(viewsets.ViewSet):
    permission_classes = [IsTeacherOrAdmin]

    @action(detail=True, methods=['get'])
    def progress_metrics(self, request, pk=None):
        """Get student's progress metrics"""
        student = self.get_student(pk)
        return Response({
            'metrics': 'Implementation pending'
        })

    def get_student(self, pk):
        # TODO: Implement student retrieval
        pass

class CalendarEventViewSet(viewsets.ModelViewSet):
    serializer_class = CalendarEventSerializer
    permission_classes = [IsAuthenticated]

    def get_user_school_branch(self, user):
        """Get school branch based on user type"""
        if hasattr(user, 'staff_profile') and user.staff_profile:
            return user.staff_profile.school_branch
        elif hasattr(user, 'teacher_profile') and user.teacher_profile:
            return user.teacher_profile.school_branch
        elif hasattr(user, 'student_profile') and user.student_profile:
            return user.student_profile.school_branch
        elif hasattr(user, 'teacher') and user.teacher:
            return user.teacher.school_branch
        elif hasattr(user, 'student') and user.student:
            return user.student.school_branch
        return None

    def get_queryset(self):
        school_branch = self.get_user_school_branch(self.request.user)
        if not school_branch:
            return CalendarEvent.objects.none()
        return CalendarEvent.objects.filter(school_branch=school_branch)

    def perform_create(self, serializer):
        school_branch = self.get_user_school_branch(self.request.user)
        if not school_branch:
            raise PermissionDenied(
                detail={
                    "message": "User must be associated with a school branch",
                    "user_id": self.request.user.id,
                    "profiles": [attr for attr in dir(self.request.user) if not attr.startswith('_')]
                }
            )
        serializer.save(
            created_by=self.request.user,
            school_branch=school_branch
        )

class DashboardMetricsView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        school_branch = self.request.user.school_branch

        now = timezone.now()
        current_month = now.replace(day=1)
        previous_month = current_month - relativedelta(months=1)

        # Fetch current metrics
        current_metrics = {
            'students': Student.objects.filter(school_branch=school_branch).count(),
            'male': Student.objects.filter(school_branch=school_branch, gender='M').count(),
            'female': Student.objects.filter(school_branch=school_branch, gender='F').count(),
            'classes': Class.objects.filter(school_branch=school_branch).count(),
            'teachers': Teacher.objects.filter(school_branch=school_branch).count(),
            'subjects': Subject.objects.filter(school_branch=school_branch).count(),
            'parents': Parent.objects.filter(school_branch=school_branch).count(),
            'departments': Department.objects.filter(school_branch=school_branch).count(),
        }

        # Fetch previous month's metrics
        previous_metrics = {
            'students': Student.objects.filter(school_branch=school_branch, created_at__lt=current_month).count(),
            'male': Student.objects.filter(school_branch=school_branch, gender='M', created_at__lt=current_month).count(),
            'female': Student.objects.filter(school_branch=school_branch, gender='F', created_at__lt=current_month).count(),
            'classes': Class.objects.filter(school_branch=school_branch, created_at__lt=current_month).count(),
            'teachers': Teacher.objects.filter(school_branch=school_branch, created_at__lt=current_month).count(),
            'subjects': Subject.objects.filter(school_branch=school_branch, created_at__lt=current_month).count(),
            'parents': Parent.objects.filter(school_branch=school_branch, created_at__lt=current_month).count(),
            'departments': Department.objects.filter(school_branch=school_branch, created_at__lt=current_month).count(),
        }

        # Compute percentage change
        def compute_percentage_change(current, previous):
            if previous == 0:  # If no data exists for the previous month
                return 0
            return round(((current - previous) / previous) * 100, 2)

        metrics_data = {
            key: {
                'label': key.capitalize(),
                'value': current_metrics[key],
                'percentageChange': compute_percentage_change(current_metrics[key], previous_metrics[key]),
                'icon': 'group' if key in ['students', 'male', 'female'] else 'box'
            }
            for key in current_metrics
        }

        serializer = DashboardMetricsSerializer(metrics_data)
        return Response(serializer.data)