from django.db import models
from django.utils import timezone
import uuid
import json
from schools.models import School, SchoolBranch
from django.contrib.auth import get_user_model
from .modules import AVAILABLE_MODULES, PACKAGE_TIERS, MODULE_STATUS, get_module_status, is_module_live

User = get_user_model()


def generate_license_key(school, expiry_date=None):
    """
    Generate a meaningful license key for a school with format: SX-SCHOOLCODE-RANDOM-YEAR

    Args:
        school: School object
        expiry_date: Optional expiry date, defaults to one year from now

    Returns:
        A string license key
    """
    # Get up to 3 initials from school name
    school_code = ''.join(word[0] for word in school.name.upper().split()[:3])

    # Generate 8 random characters for uniqueness and security
    random_chars = uuid.uuid4().hex[:8].upper()

    # Get expiry year
    if not expiry_date:
        expiry_date = timezone.now().date() + timezone.timedelta(days=365)
    expiry_year = expiry_date.year

    # Create the license key
    return f"SX-{school_code}-{random_chars}-{expiry_year}"

class LicenseSubscription(models.Model):
    """Subscription and licensing information for a school"""
    SUBSCRIPTION_STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('TRIAL', 'Trial'),
        ('EXPIRED', 'Expired'),
        ('CANCELLED', 'Cancelled'),
    ]

    school = models.OneToOneField(School, on_delete=models.CASCADE, related_name='license')
    package_type = models.CharField(max_length=20, choices=[(k, v['name']) for k, v in PACKAGE_TIERS.items()], default='basic')
    subscription_status = models.CharField(max_length=20, choices=SUBSCRIPTION_STATUS_CHOICES, default='TRIAL')
    start_date = models.DateField(default=timezone.now)
    expiry_date = models.DateField()
    max_students = models.PositiveIntegerField(default=500)
    max_staff = models.PositiveIntegerField(default=50)
    max_branches = models.PositiveIntegerField(default=1)
    custom_modules = models.JSONField(blank=True, null=True, help_text="List of enabled modules for custom package")
    license_key = models.CharField(max_length=100, unique=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        # Check if this is a new instance or an update
        is_new = self._state.adding

        # If it's an update, get the previous state before saving
        previous_state = None
        if not is_new:
            try:
                # Get the current state from the database
                current_obj = LicenseSubscription.objects.get(pk=self.pk)
                previous_state = {
                    'id': current_obj.id,
                    'school': current_obj.school.id,
                    'school_name': current_obj.school.name,
                    'package_type': current_obj.package_type,
                    'subscription_status': current_obj.subscription_status,
                    'start_date': current_obj.start_date.isoformat(),
                    'expiry_date': current_obj.expiry_date.isoformat(),
                    'max_students': current_obj.max_students,
                    'max_staff': current_obj.max_staff,
                    'max_branches': current_obj.max_branches,
                    'custom_modules': current_obj.custom_modules,
                    'created_at': current_obj.created_at.isoformat(),
                    'updated_at': current_obj.updated_at.isoformat(),
                }
            except LicenseSubscription.DoesNotExist:
                pass

        # Auto-generate license key if not provided
        if not self.license_key and self.school_id:
            self.license_key = generate_license_key(self.school, self.expiry_date)

        # Save the model
        super().save(*args, **kwargs)

        # Record the history after saving
        # Import here to avoid circular import
        from django.db import transaction
        transaction.on_commit(lambda: self._record_history(is_new, previous_state))

    def _record_history(self, is_new, previous_state=None):
        """Record history entry after save"""
        # Determine the action based on changes
        if is_new:
            action = 'CREATE'
            notes = 'License created'
        else:
            # Check what changed to determine the action
            if previous_state:
                if previous_state['subscription_status'] != self.subscription_status:
                    if self.subscription_status == 'ACTIVE' and previous_state['subscription_status'] in ['EXPIRED', 'CANCELLED']:
                        action = 'RENEW'
                        notes = 'License renewed'
                    elif self.subscription_status == 'DEACTIVATED':
                        action = 'DEACTIVATE'
                        notes = 'License deactivated'
                    else:
                        action = 'UPDATE'
                        notes = f'Status changed from {previous_state["subscription_status"]} to {self.subscription_status}'
                elif previous_state['package_type'] != self.package_type:
                    # Check if this is an upgrade or downgrade
                    package_tiers = {'basic': 1, 'standard': 2, 'premium': 3, 'custom': 4}
                    old_tier = package_tiers.get(previous_state['package_type'], 0)
                    new_tier = package_tiers.get(self.package_type, 0)

                    if new_tier > old_tier:
                        action = 'UPGRADE'
                        notes = f'Package upgraded from {previous_state["package_type"]} to {self.package_type}'
                    elif new_tier < old_tier:
                        action = 'DOWNGRADE'
                        notes = f'Package downgraded from {previous_state["package_type"]} to {self.package_type}'
                    else:
                        action = 'UPDATE'
                        notes = f'Package changed from {previous_state["package_type"]} to {self.package_type}'
                elif previous_state['expiry_date'] != self.expiry_date.isoformat():
                    action = 'UPDATE'
                    notes = f'Expiry date changed from {previous_state["expiry_date"]} to {self.expiry_date.isoformat()}'
                else:
                    action = 'UPDATE'
                    notes = 'License updated'
            else:
                action = 'UPDATE'
                notes = 'License updated'

        # Get the current user from our middleware
        user = None
        try:
            from settings_app.user_middleware import get_current_user
            user = get_current_user()
        except (ImportError, AttributeError):
            pass

        # Record the history
        LicenseHistory.log_change(
            license_obj=self,
            action=action,
            user=user,
            previous_state=previous_state,
            notes=notes
        )

    def __str__(self):
        return f"{self.school.name} - {self.get_package_type_display()} ({self.get_subscription_status_display()})"

    def update_status(self):
        """Update the subscription status based on expiry date"""
        today = timezone.now().date()

        # If the license has expired but status is still ACTIVE or TRIAL, update it to EXPIRED
        if self.expiry_date < today and self.subscription_status in ['ACTIVE', 'TRIAL']:
            self.subscription_status = 'EXPIRED'
            self.save(update_fields=['subscription_status', 'updated_at'])
            return True

        return False

    def is_active(self):
        """Check if the subscription is active"""
        # First update the status if needed
        self.update_status()

        return (
            self.subscription_status in ['ACTIVE', 'TRIAL'] and
            self.expiry_date >= timezone.now().date()
        )

    def get_enabled_modules(self):
        """Get list of enabled modules based on package type"""
        from .modules import get_package_modules

        if self.package_type == 'custom' and self.custom_modules:
            return self.custom_modules

        return get_package_modules(self.package_type)

    def is_module_enabled(self, module_code):
        """Check if a specific module is enabled for this subscription"""
        # Core modules are always enabled
        if module_code in AVAILABLE_MODULES and AVAILABLE_MODULES[module_code]['is_core']:
            return True

        return module_code in self.get_enabled_modules()


class ModuleActivation(models.Model):
    """Controls which modules are activated for a specific school branch"""
    school_branch = models.OneToOneField(SchoolBranch, on_delete=models.CASCADE, related_name='module_activation')
    enabled_modules = models.JSONField(default=list, help_text="List of enabled module codes")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Module Activation for {self.school_branch.name}"

    def is_module_enabled(self, module_code):
        """Check if a specific module is enabled for this branch"""
        # First check if the module is available in the school's subscription
        try:
            if not self.school_branch.school.license.is_module_enabled(module_code):
                return False
        except (AttributeError, LicenseSubscription.DoesNotExist):
            # If no license exists, only allow core modules
            if module_code in AVAILABLE_MODULES and AVAILABLE_MODULES[module_code]['is_core']:
                return True
            return False

        # Core modules are always enabled
        if module_code in AVAILABLE_MODULES and AVAILABLE_MODULES[module_code]['is_core']:
            return True

        # Check if the module is enabled for this branch
        return module_code in self.enabled_modules

    def is_module_visible(self, module_code, user=None):
        """Check if a module should be visible to a user"""
        # First check if the module is enabled
        if not self.is_module_enabled(module_code):
            return False

        # Check if the module is in development
        if not is_module_live(module_code):
            # Only show development modules to superusers
            return user and user.is_superuser

        # Module is live and enabled, so it's visible
        return True

    def enable_module(self, module_code):
        """Enable a module for this branch"""
        try:
            if not self.school_branch.school.license.is_module_enabled(module_code):
                return False
        except (AttributeError, LicenseSubscription.DoesNotExist):
            # If no license exists, only allow core modules
            if not (module_code in AVAILABLE_MODULES and AVAILABLE_MODULES[module_code]['is_core']):
                return False

        if module_code not in self.enabled_modules:
            self.enabled_modules.append(module_code)
            self.save()
        return True

    def disable_module(self, module_code):
        """Disable a module for this branch"""
        # Cannot disable core modules
        if module_code in AVAILABLE_MODULES and AVAILABLE_MODULES[module_code]['is_core']:
            return False

        if module_code in self.enabled_modules:
            self.enabled_modules.remove(module_code)
            self.save()
        return True


class LicenseHistory(models.Model):
    """Tracks the history of license changes including creation, updates, and renewals"""
    ACTION_CHOICES = [
        ('CREATE', 'Created'),
        ('UPDATE', 'Updated'),
        ('RENEW', 'Renewed'),
        ('DEACTIVATE', 'Deactivated'),
        ('UPGRADE', 'Upgraded'),
        ('DOWNGRADE', 'Downgraded'),
    ]

    license = models.ForeignKey(LicenseSubscription, on_delete=models.CASCADE, related_name='history')
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    previous_state = models.JSONField(null=True, blank=True, help_text="Previous license state before the change")
    new_state = models.JSONField(help_text="New license state after the change")
    notes = models.TextField(blank=True, help_text="Additional notes about the change")

    class Meta:
        ordering = ['-timestamp']
        verbose_name = "License History"
        verbose_name_plural = "License History"
        indexes = [
            models.Index(fields=['license']),
            models.Index(fields=['action']),
            models.Index(fields=['timestamp']),
        ]

    def __str__(self):
        return f"{self.get_action_display()} - {self.license.school.name} - {self.timestamp.strftime('%Y-%m-%d %H:%M')}"

    @classmethod
    def log_change(cls, license_obj, action, user=None, previous_state=None, notes=""):
        """Helper method to log a license change"""
        # Create a JSON representation of the current license state
        new_state = {
            'id': license_obj.id,
            'school': license_obj.school.id,
            'school_name': license_obj.school.name,
            'package_type': license_obj.package_type,
            'subscription_status': license_obj.subscription_status,
            'start_date': license_obj.start_date.isoformat(),
            'expiry_date': license_obj.expiry_date.isoformat(),
            'max_students': license_obj.max_students,
            'max_staff': license_obj.max_staff,
            'max_branches': license_obj.max_branches,
            'custom_modules': license_obj.custom_modules,
            'created_at': license_obj.created_at.isoformat(),
            'updated_at': license_obj.updated_at.isoformat(),
        }

        # Create the history entry
        history_entry = cls(
            license=license_obj,
            action=action,
            user=user,
            previous_state=previous_state,
            new_state=new_state,
            notes=notes
        )
        history_entry.save()

        return history_entry
