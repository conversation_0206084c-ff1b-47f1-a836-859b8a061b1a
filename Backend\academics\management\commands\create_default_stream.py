from django.core.management.base import BaseCommand
from academics.models import Stream
from schools.models import School

class Command(BaseCommand):
    help = 'Creates a default stream for each school if none exists'

    def handle(self, *args, **options):
        schools = School.objects.all()
        
        if not schools.exists():
            self.stdout.write(self.style.WARNING('No schools found. Please create a school first.'))
            return
            
        streams_created = 0
        
        for school in schools:
            # Check if the school already has streams
            existing_streams = Stream.objects.filter(school=school)
            
            if not existing_streams.exists():
                # Create a default stream for this school
                stream = Stream.objects.create(
                    name='General Stream',
                    code=f'GEN-{school.id}',
                    school=school,
                    description='Default general stream created by system'
                )
                
                self.stdout.write(
                    self.style.SUCCESS(f'Created default stream "{stream.name}" for school "{school.name}"')
                )
                streams_created += 1
            else:
                self.stdout.write(
                    self.style.SUCCESS(f'School "{school.name}" already has {existing_streams.count()} streams')
                )
                
        if streams_created > 0:
            self.stdout.write(
                self.style.SUCCESS(f'Successfully created {streams_created} default streams')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('All schools already have streams. No new streams created.')
            )
