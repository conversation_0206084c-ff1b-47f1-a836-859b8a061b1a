"""
Middleware to store the current user in thread local storage
This allows us to access the current user in model save methods
"""
from threading import local

# Thread local storage
_thread_locals = local()

class CurrentUserMiddleware:
    """
    Middleware that stores the current authenticated user in thread local storage
    """
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Store the user in thread local storage
        if hasattr(request, 'user') and request.user.is_authenticated:
            _thread_locals.user = request.user
        else:
            _thread_locals.user = None

        # Process the request
        response = self.get_response(request)

        # Clean up
        if hasattr(_thread_locals, 'user'):
            del _thread_locals.user

        return response

def get_current_user():
    """
    Returns the current user from thread local storage
    """
    return getattr(_thread_locals, 'user', None)
