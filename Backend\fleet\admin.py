from django.contrib import admin
from .models import (
    Vehicle, Driver, Route, Schedule, VehicleMaintenance,
    StudentTransport, TransportAttendance, FuelConsumption
)

@admin.register(Vehicle)
class VehicleAdmin(admin.ModelAdmin):
    list_display = ('name', 'registration_number', 'vehicle_type', 'status', 'school_branch')
    list_filter = ('status', 'vehicle_type', 'school_branch')
    search_fields = ('name', 'registration_number', 'model', 'manufacturer')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'registration_number', 'vehicle_type', 'model', 'manufacturer', 'year_of_manufacture')
        }),
        ('Capacity and Fuel', {
            'fields': ('seating_capacity', 'fuel_type')
        }),
        ('Purchase Information', {
            'fields': ('purchase_date', 'purchase_price')
        }),
        ('Status and Documents', {
            'fields': ('status', 'current_mileage', 'insurance_expiry', 'license_expiry', 'image', 'documents', 'notes')
        }),
        ('School Information', {
            'fields': ('school_branch',)
        }),
        ('System Fields', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(Driver)
class DriverAdmin(admin.ModelAdmin):
    list_display = ('user', 'license_number', 'status', 'assigned_vehicle', 'school_branch')
    list_filter = ('status', 'school_branch')
    search_fields = ('user__first_name', 'user__last_name', 'license_number')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('User Information', {
            'fields': ('user',)
        }),
        ('License Information', {
            'fields': ('license_number', 'license_class', 'license_expiry', 'experience_years')
        }),
        ('Personal Information', {
            'fields': ('date_of_birth', 'address')
        }),
        ('Emergency Contact', {
            'fields': ('emergency_contact_name', 'emergency_contact_phone')
        }),
        ('Status and Assignment', {
            'fields': ('status', 'assigned_vehicle', 'documents', 'notes')
        }),
        ('School Information', {
            'fields': ('school_branch',)
        }),
        ('System Fields', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(Route)
class RouteAdmin(admin.ModelAdmin):
    list_display = ('name', 'start_location', 'end_location', 'distance', 'is_active', 'school_branch')
    list_filter = ('is_active', 'school_branch')
    search_fields = ('name', 'start_location', 'end_location')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Route Information', {
            'fields': ('name', 'description', 'start_location', 'end_location', 'distance', 'estimated_time', 'stops')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('School Information', {
            'fields': ('school_branch',)
        }),
        ('System Fields', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(Schedule)
class ScheduleAdmin(admin.ModelAdmin):
    list_display = ('name', 'route', 'vehicle', 'driver', 'schedule_type', 'departure_time', 'is_active', 'school_branch')
    list_filter = ('schedule_type', 'is_active', 'school_branch')
    search_fields = ('name', 'route__name', 'vehicle__name', 'driver__user__first_name', 'driver__user__last_name')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Schedule Information', {
            'fields': ('name', 'route', 'vehicle', 'driver', 'schedule_type')
        }),
        ('Timing', {
            'fields': ('days_of_week', 'departure_time', 'arrival_time', 'start_date', 'end_date')
        }),
        ('Status', {
            'fields': ('is_active', 'notes')
        }),
        ('School Information', {
            'fields': ('school_branch',)
        }),
        ('System Fields', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(VehicleMaintenance)
class VehicleMaintenanceAdmin(admin.ModelAdmin):
    list_display = ('vehicle', 'maintenance_type', 'scheduled_date', 'completion_date', 'status', 'cost', 'school_branch')
    list_filter = ('maintenance_type', 'status', 'school_branch')
    search_fields = ('vehicle__name', 'vehicle__registration_number', 'service_provider')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Vehicle Information', {
            'fields': ('vehicle', 'maintenance_type')
        }),
        ('Maintenance Details', {
            'fields': ('description', 'service_provider', 'scheduled_date', 'completion_date', 'cost', 'mileage_at_service')
        }),
        ('Status', {
            'fields': ('status', 'notes', 'documents')
        }),
        ('School Information', {
            'fields': ('school_branch',)
        }),
        ('System Fields', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(StudentTransport)
class StudentTransportAdmin(admin.ModelAdmin):
    list_display = ('student', 'route', 'pickup_point', 'dropoff_point', 'is_active', 'fee_amount', 'school_branch')
    list_filter = ('is_active', 'payment_frequency', 'school_branch')
    search_fields = ('student__user__first_name', 'student__user__last_name', 'route__name')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Student and Route', {
            'fields': ('student', 'route')
        }),
        ('Transport Details', {
            'fields': ('pickup_point', 'dropoff_point', 'is_active', 'start_date', 'end_date')
        }),
        ('Fee Information', {
            'fields': ('fee_amount', 'payment_frequency', 'notes')
        }),
        ('School Information', {
            'fields': ('school_branch',)
        }),
        ('System Fields', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(TransportAttendance)
class TransportAttendanceAdmin(admin.ModelAdmin):
    list_display = ('student_transport', 'schedule', 'date', 'picked_up', 'dropped_off', 'school_branch')
    list_filter = ('picked_up', 'dropped_off', 'date', 'school_branch')
    search_fields = ('student_transport__student__user__first_name', 'student_transport__student__user__last_name')
    readonly_fields = ('created_at', 'updated_at', 'recorded_by')
    fieldsets = (
        ('Student and Schedule', {
            'fields': ('student_transport', 'schedule', 'date')
        }),
        ('Attendance Details', {
            'fields': ('picked_up', 'dropped_off', 'pickup_time', 'dropoff_time', 'notes')
        }),
        ('Recording Information', {
            'fields': ('recorded_by',)
        }),
        ('School Information', {
            'fields': ('school_branch',)
        }),
        ('System Fields', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(FuelConsumption)
class FuelConsumptionAdmin(admin.ModelAdmin):
    list_display = ('vehicle', 'date', 'fuel_amount', 'cost', 'odometer_reading', 'school_branch')
    list_filter = ('date', 'school_branch')
    search_fields = ('vehicle__name', 'vehicle__registration_number', 'fuel_station')
    readonly_fields = ('created_at', 'updated_at', 'filled_by')
    fieldsets = (
        ('Vehicle Information', {
            'fields': ('vehicle', 'date')
        }),
        ('Fuel Details', {
            'fields': ('fuel_amount', 'cost', 'odometer_reading', 'fuel_station')
        }),
        ('Additional Information', {
            'fields': ('filled_by', 'receipt', 'notes')
        }),
        ('School Information', {
            'fields': ('school_branch',)
        }),
        ('System Fields', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
