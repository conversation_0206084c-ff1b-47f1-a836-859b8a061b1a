from rest_framework import permissions

class IsTeacherOrAdmin(permissions.BasePermission):
    """
    Custom permission to only allow teachers and admins to access the view.
    """
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
            
        return request.user.role in ['TEACHER', 'ADMIN', 'HOD']

    def has_object_permission(self, request, view, obj):
        if not request.user.is_authenticated:
            return False
            
        if request.user.role == 'ADMIN':
            return True
            
        if request.user.role == 'HOD':
            # HOD can access objects in their department
            return obj.department == request.user.staff.department
            
        if request.user.role == 'TEACHER':
            # Teachers can access their own objects
            return obj.teacher == request.user.teacher
            
        return False
