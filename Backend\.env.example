# ShuleXcel MVP Production Environment Configuration
# Copy this file to .env and update the values for your deployment

# Django Settings
SECRET_KEY=your-secret-key-here-change-this-in-production
DEBUG=False
DJANGO_ENVIRONMENT=production
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# Database Configuration
# For SQLite (Development/Testing)
DATABASE_URL=sqlite:///db.sqlite3

# For PostgreSQL (Recommended for Production)
# DATABASE_URL=postgresql://username:password@localhost:5432/shulexcel_db
# DB_NAME=shulexcel_db
# DB_USER=shulexcel_user
# DB_PASSWORD=your-db-password
# DB_HOST=localhost
# DB_PORT=5432

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password-here
DEFAULT_FROM_EMAIL=ShuleXcel <<EMAIL>>

# Frontend URL
FRONTEND_URL=http://localhost:5173

# Redis Configuration (for caching and rate limiting)
REDIS_URL=redis://localhost:6379/0

# Security Settings
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False

# For production, set these to True:
# SECURE_SSL_REDIRECT=True
# SECURE_HSTS_SECONDS=31536000
# SESSION_COOKIE_SECURE=True
# CSRF_COOKIE_SECURE=True

# JWT Settings
JWT_ACCESS_TOKEN_LIFETIME=60
JWT_REFRESH_TOKEN_LIFETIME=1440

# File Upload Settings
MAX_UPLOAD_SIZE=10485760  # 10MB in bytes

# Logging
LOG_LEVEL=INFO
LOG_FILE=/app/logs/django.log

# CORS Settings
CORS_ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000,https://your-frontend-domain.com
CORS_ALLOW_CREDENTIALS=True

# Media and Static Files
MEDIA_URL=/media/
STATIC_URL=/static/
MEDIA_ROOT=/app/media
STATIC_ROOT=/app/staticfiles

# Time Zone
TIME_ZONE=Africa/Nairobi

# Default Admin User (for initial setup)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=change-this-secure-password

# License System
LICENSE_ENCRYPTION_KEY=your-license-encryption-key-here

# Session Settings
SESSION_COOKIE_AGE=86400
SESSION_EXPIRE_AT_BROWSER_CLOSE=True

# School Registration
ALLOW_SCHOOL_REGISTRATION=True
REQUIRE_EMAIL_VERIFICATION=True

# Feature Flags
ENABLE_API_DOCS=True
ENABLE_ADMIN_INTERFACE=True
ENABLE_DEBUG_TOOLBAR=False

# Performance Settings
DATABASE_CONN_MAX_AGE=60
CACHE_TIMEOUT=300

# Sentry (Error Tracking) - Optional
# SENTRY_DSN=your-sentry-dsn-here

# MPESA Configuration (if using)
MPESA_CONSUMER_KEY=your-mpesa-consumer-key
MPESA_CONSUMER_SECRET=your-mpesa-consumer-secret
MPESA_SHORTCODE=your-shortcode
MPESA_PASSKEY=your-passkey
MPESA_CALLBACK_URL=https://your-domain.com/api/mpesa/callback/
