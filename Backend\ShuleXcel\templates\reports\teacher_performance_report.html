<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teacher Performance Report</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
        }
        th {
            background-color: #f2f2f2;
            text-align: left;
        }
    </style>
</head>
<body>
    <h1>Teacher Performance Report</h1>
    <h2>School: {{ school.name }}</h2>
    <h3>Reporting Period: {{ reporting_period }}</h3>

    <table>
        <thead>
            <tr>
                <th>Teacher Name</th>
                <th>Subject</th>
                <th>Class Average</th>
                <th>Pass Rate</th>
                <th>Completion Rate</th>
                <th>Average Value Addition</th>
                <th>Number of Distinctions</th>
                <th>Number of Failures</th>
            </tr>
        </thead>
        <tbody>
            {% for metric in teacher_metrics %}
            <tr>
                <td>{{ metric.teacher.get_full_name }}</td>
                <td>{{ metric.subject.name }}</td>
                <td>{{ metric.class_average }}</td>
                <td>{{ metric.pass_rate }}%</td>
                <td>{{ metric.completion_rate }}%</td>
                <td>{{ metric.avg_value_addition }}</td>
                <td>{{ metric.number_of_distinctions }}</td>
                <td>{{ metric.number_of_failures }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</body>
</html>
