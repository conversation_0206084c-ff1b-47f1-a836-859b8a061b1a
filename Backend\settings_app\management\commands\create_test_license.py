from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from django.contrib.auth import get_user_model
from schools.models import School
from settings_app.license_models import LicenseSubscription, generate_license_key

User = get_user_model()

class Command(BaseCommand):
    help = 'Create a test license for a school'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user_id',
            type=int,
            default=1,
            help='User ID to check for school association',
        )

        parser.add_argument(
            '--school_id',
            type=int,
            help='School ID to create license for (optional, will use user\'s school if not specified)',
        )

        parser.add_argument(
            '--package',
            type=str,
            default='premium',
            help='Package type to assign (basic, standard, premium, custom)',
        )

    def handle(self, *args, **options):
        user_id = options.get('user_id')
        school_id = options.get('school_id')
        package_type = options.get('package')

        try:
            user = User.objects.get(id=user_id)
            self.stdout.write(f"User: {user.username}, Email: {user.email}")

            # Get school from user if not specified
            if not school_id:
                if hasattr(user, 'school_branch') and user.school_branch:
                    school = user.school_branch.school
                    self.stdout.write(f"Using user's school: {school.name} (ID: {school.id})")
                else:
                    self.stdout.write(self.style.ERROR("User is not associated with any school branch"))

                    # Get the first school as fallback
                    try:
                        school = School.objects.first()
                        self.stdout.write(f"Using first available school: {school.name} (ID: {school.id})")
                    except School.DoesNotExist:
                        self.stdout.write(self.style.ERROR("No schools found in the database"))
                        return
            else:
                try:
                    school = School.objects.get(id=school_id)
                    self.stdout.write(f"Using specified school: {school.name} (ID: {school.id})")
                except School.DoesNotExist:
                    self.stdout.write(self.style.ERROR(f"School with ID {school_id} not found"))
                    return

            # Check if license already exists
            existing_license = LicenseSubscription.objects.filter(school=school).first()
            if existing_license:
                self.stdout.write(f"License already exists for {school.name}: {existing_license.license_key}")
                self.stdout.write(f"Package: {existing_license.package_type}, Status: {existing_license.subscription_status}")
                self.stdout.write(f"Expiry: {existing_license.expiry_date}")
                return

            # Create license with enhanced security
            expiry_date = timezone.now().date() + timedelta(days=365)  # 1 year from now
            license_key = generate_license_key(school, expiry_date)  # Now using 8 random characters for better security

            license_sub = LicenseSubscription.objects.create(
                school=school,
                package_type=package_type,
                subscription_status='ACTIVE',
                start_date=timezone.now().date(),
                expiry_date=expiry_date,
                max_students=1000,
                max_staff=100,
                max_branches=5,
                license_key=license_key
            )

            self.stdout.write(self.style.SUCCESS(f"Created license for {school.name}"))
            self.stdout.write(f"License Key: {license_key}")
            self.stdout.write(f"Package: {package_type}, Status: {license_sub.subscription_status}")
            self.stdout.write(f"Expiry: {expiry_date}")

        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"User with ID {user_id} not found"))
