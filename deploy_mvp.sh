#!/bin/bash

# ShuleXcel MVP Deployment Script
# This script deploys the ShuleXcel MVP using Docker

set -e  # Exit on any error

echo "🚀 ShuleXcel MVP Deployment Script"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and Docker Compose are installed"
}

# Create environment file if it doesn't exist
setup_environment() {
    print_status "Setting up environment configuration..."
    
    if [ ! -f "Backend/.env" ]; then
        print_status "Creating .env file from template..."
        cp Backend/.env.example Backend/.env
        print_warning "Please edit Backend/.env file with your production settings before continuing"
        print_warning "Press Enter when you've updated the .env file..."
        read
    else
        print_success "Environment file already exists"
    fi
}

# Create docker-compose.yml for MVP deployment
create_docker_compose() {
    print_status "Creating Docker Compose configuration..."
    
    cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  backend:
    build: ./Backend
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - DJANGO_ENVIRONMENT=production
    volumes:
      - ./Backend:/app
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - logs_volume:/app/logs
    depends_on:
      - db
    restart: unless-stopped

  frontend:
    build: ./Frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: shulexcel_db
      POSTGRES_USER: shulexcel_user
      POSTGRES_PASSWORD: ${DB_PASSWORD:-changeme}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    restart: unless-stopped

volumes:
  postgres_data:
  static_volume:
  media_volume:
  logs_volume:
EOF

    print_success "Docker Compose configuration created"
}

# Build and deploy
deploy() {
    print_status "Building and deploying ShuleXcel MVP..."
    
    # Build images
    print_status "Building Docker images..."
    docker-compose build
    
    # Start services
    print_status "Starting services..."
    docker-compose up -d
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Run migrations and setup
    print_status "Running database migrations..."
    docker-compose exec backend python manage.py migrate
    
    print_status "Setting up MVP data..."
    docker-compose exec backend python setup_mvp.py
    
    print_success "Deployment completed!"
}

# Show deployment status
show_status() {
    print_status "Checking deployment status..."
    docker-compose ps
    
    echo ""
    print_success "ShuleXcel MVP is now running!"
    print_status "Frontend: http://localhost"
    print_status "Backend API: http://localhost:8000"
    print_status "Admin Panel: http://localhost:8000/admin"
    
    echo ""
    print_status "Default Admin Credentials:"
    print_status "Email: <EMAIL>"
    print_status "Password: admin123"
    
    echo ""
    print_warning "Remember to:"
    print_warning "1. Change default admin password"
    print_warning "2. Configure SSL/HTTPS for production"
    print_warning "3. Set up proper backup procedures"
    print_warning "4. Configure monitoring and logging"
}

# Main deployment process
main() {
    print_status "Starting ShuleXcel MVP deployment..."
    
    check_docker
    setup_environment
    create_docker_compose
    deploy
    show_status
    
    print_success "MVP deployment completed successfully! 🎉"
}

# Run main function
main
