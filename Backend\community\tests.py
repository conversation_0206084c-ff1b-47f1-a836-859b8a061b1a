from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, APIClient
from django.contrib.auth import get_user_model
from schools.models import School, SchoolBranch
from .models import (
    Community, CommunityMember, Post, Comment, ClubMeeting,
    MeetingAttendance, Reaction, Notification
)

User = get_user_model()

class CommunityModelTests(TestCase):
    def setUp(self):
        self.school = School.objects.create(name="Test School", code="TS001")
        self.branch = SchoolBranch.objects.create(
            name="Main Branch",
            school=self.school,
            address="123 Test St",
            phone="1234567890"
        )
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
            school_branch=self.branch
        )

    def test_create_community(self):
        """Test creating a community"""
        community = Community.objects.create(
            name="Test Community",
            description="A test community",
            community_type="GENERAL",
            created_by=self.user,
            school_branch=self.branch
        )
        self.assertEqual(community.name, "Test Community")
        self.assertEqual(community.community_type, "GENERAL")
        self.assertEqual(community.created_by, self.user)
        self.assertTrue(community.is_active)
        self.assertFalse(community.is_private)

class CommunityAPITests(APITestCase):
    def setUp(self):
        self.client = APIClient()
        self.school = School.objects.create(name="Test School", code="TS001")
        self.branch = SchoolBranch.objects.create(
            name="Main Branch",
            school=self.school,
            address="123 Test St",
            phone="1234567890"
        )
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
            school_branch=self.branch
        )
        self.client.force_authenticate(user=self.user)

        # Create a test community
        self.community = Community.objects.create(
            name="Test Community",
            description="A test community",
            community_type="GENERAL",
            created_by=self.user,
            school_branch=self.branch
        )

        # Create a community member
        self.member = CommunityMember.objects.create(
            community=self.community,
            user=self.user,
            role="ADMIN",
            school_branch=self.branch
        )

    def test_list_communities(self):
        """Test retrieving a list of communities"""
        url = reverse('community-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['name'], self.community.name)

    def test_create_community_api(self):
        """Test creating a community via API"""
        url = reverse('community-list')
        data = {
            'name': 'New Community',
            'description': 'A new test community',
            'community_type': 'CLUB'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Community.objects.count(), 2)
        new_community = Community.objects.get(name='New Community')
        self.assertEqual(new_community.community_type, 'CLUB')
        self.assertEqual(new_community.created_by, self.user)

    def test_create_post(self):
        """Test creating a post in a community"""
        url = reverse('post-list')
        data = {
            'community': self.community.id,
            'title': 'Test Post',
            'content': 'This is a test post'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Post.objects.count(), 1)
        post = Post.objects.first()
        self.assertEqual(post.title, 'Test Post')
        self.assertEqual(post.author, self.user)

    def test_create_comment(self):
        """Test creating a comment on a post"""
        # First create a post
        post = Post.objects.create(
            community=self.community,
            author=self.user,
            title='Test Post',
            content='This is a test post',
            school_branch=self.branch
        )

        url = reverse('comment-list')
        data = {
            'post': post.id,
            'content': 'This is a test comment'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Comment.objects.count(), 1)
        comment = Comment.objects.first()
        self.assertEqual(comment.content, 'This is a test comment')
        self.assertEqual(comment.author, self.user)