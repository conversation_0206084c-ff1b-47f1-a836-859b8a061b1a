# Generated by Django 5.2.1 on 2025-06-01 13:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_initial'),
    ]

    operations = [
        migrations.<PERSON>er<PERSON>ield(
            model_name='customuser',
            name='user_type',
            field=models.CharField(blank=True, choices=[('system_admin', 'System Administrator'), ('school_admin', 'School Administrator'), ('deputy_principal', 'Deputy Principal'), ('branch_admin', 'Branch Administrator'), ('department_head', 'Department Head'), ('ict_admin', 'ICT Administrator'), ('teacher', 'Teacher'), ('student', 'Student'), ('parent', 'Parent'), ('librarian', 'Librarian'), ('counselor', 'Counselor'), ('accountant', 'Accountant'), ('secretary', 'Secretary'), ('nurse', 'Nurse'), ('maintenance', 'Maintenance Staff'), ('security', 'Security Staff'), ('driver', 'Driver')], max_length=20, null=True),
        ),
    ]
