# Generated by Django 5.2.1 on 2025-06-01 13:25

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('fees', '0002_initial'),
        ('fleet', '0001_initial'),
        ('schools', '0001_initial'),
        ('users', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='studenttransport',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transport', to='users.student'),
        ),
        migrations.AddField(
            model_name='transportattendance',
            name='recorded_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='recorded_transport_attendance', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='transportattendance',
            name='schedule',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance', to='fleet.schedule'),
        ),
        migrations.AddField(
            model_name='transportattendance',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transport_attendance', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='transportattendance',
            name='student_transport',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance', to='fleet.studenttransport'),
        ),
        migrations.AddField(
            model_name='transportfee',
            name='fee_structure',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fees.feestructure'),
        ),
        migrations.AddField(
            model_name='transportfee',
            name='fee_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transport_routes', to='fees.feetype'),
        ),
        migrations.AddField(
            model_name='transportfee',
            name='route',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transport_fees', to='fleet.route'),
        ),
        migrations.AddField(
            model_name='transportfee',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='transportfeediscount',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='transportfeediscount',
            name='transport_fee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='discounts', to='fleet.transportfee'),
        ),
        migrations.AddField(
            model_name='vehicle',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vehicles', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='schedule',
            name='vehicle',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='fleet.vehicle'),
        ),
        migrations.AddField(
            model_name='fuelconsumption',
            name='vehicle',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fuel_records', to='fleet.vehicle'),
        ),
        migrations.AddField(
            model_name='driver',
            name='assigned_vehicle',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_drivers', to='fleet.vehicle'),
        ),
        migrations.AddField(
            model_name='vehiclemaintenance',
            name='school_branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vehicle_maintenance', to='schools.schoolbranch'),
        ),
        migrations.AddField(
            model_name='vehiclemaintenance',
            name='vehicle',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='maintenance_records', to='fleet.vehicle'),
        ),
        migrations.AddIndex(
            model_name='route',
            index=models.Index(fields=['is_active'], name='fleet_route_is_acti_3550c2_idx'),
        ),
        migrations.AddIndex(
            model_name='studenttransport',
            index=models.Index(fields=['is_active'], name='fleet_stude_is_acti_afb462_idx'),
        ),
        migrations.AddIndex(
            model_name='studenttransport',
            index=models.Index(fields=['start_date'], name='fleet_stude_start_d_999feb_idx'),
        ),
        migrations.AddIndex(
            model_name='transportattendance',
            index=models.Index(fields=['date'], name='fleet_trans_date_1fbc1f_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='transportattendance',
            unique_together={('student_transport', 'schedule', 'date')},
        ),
        migrations.AlterUniqueTogether(
            name='transportfee',
            unique_together={('route', 'fee_structure')},
        ),
        migrations.AddIndex(
            model_name='vehicle',
            index=models.Index(fields=['registration_number'], name='fleet_vehic_registr_12d518_idx'),
        ),
        migrations.AddIndex(
            model_name='vehicle',
            index=models.Index(fields=['status'], name='fleet_vehic_status_f5cfd7_idx'),
        ),
        migrations.AddIndex(
            model_name='schedule',
            index=models.Index(fields=['is_active'], name='fleet_sched_is_acti_2c9fa1_idx'),
        ),
        migrations.AddIndex(
            model_name='schedule',
            index=models.Index(fields=['schedule_type'], name='fleet_sched_schedul_330712_idx'),
        ),
        migrations.AddIndex(
            model_name='schedule',
            index=models.Index(fields=['start_date'], name='fleet_sched_start_d_b06a5a_idx'),
        ),
        migrations.AddIndex(
            model_name='fuelconsumption',
            index=models.Index(fields=['date'], name='fleet_fuelc_date_e5df27_idx'),
        ),
        migrations.AddIndex(
            model_name='driver',
            index=models.Index(fields=['license_number'], name='fleet_drive_license_445ac3_idx'),
        ),
        migrations.AddIndex(
            model_name='driver',
            index=models.Index(fields=['status'], name='fleet_drive_status_bfaf48_idx'),
        ),
        migrations.AddIndex(
            model_name='vehiclemaintenance',
            index=models.Index(fields=['status'], name='fleet_vehic_status_d35fb1_idx'),
        ),
        migrations.AddIndex(
            model_name='vehiclemaintenance',
            index=models.Index(fields=['scheduled_date'], name='fleet_vehic_schedul_d207f7_idx'),
        ),
    ]
