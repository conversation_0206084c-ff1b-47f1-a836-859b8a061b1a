"""
Custom exceptions for the fees module
"""
from rest_framework import status
from rest_framework.views import exception_handler
from rest_framework.response import Response
import logging

logger = logging.getLogger(__name__)


class FeeException(Exception):
    """Base exception for fee-related errors"""
    default_message = "An error occurred in the fee system"
    default_code = "FEE_ERROR"
    
    def __init__(self, message=None, code=None, details=None):
        self.message = message or self.default_message
        self.code = code or self.default_code
        self.details = details or {}
        super().__init__(self.message)


class InsufficientBalanceException(FeeException):
    """Raised when payment amount exceeds outstanding balance"""
    default_message = "Payment amount exceeds outstanding balance"
    default_code = "INSUFFICIENT_BALANCE"


class PaymentVerificationException(FeeException):
    """Raised when payment verification fails"""
    default_message = "Payment verification failed"
    default_code = "PAYMENT_VERIFICATION_FAILED"


class DuplicatePaymentException(FeeException):
    """Raised when duplicate payment is detected"""
    default_message = "Duplicate payment detected"
    default_code = "DUPLICATE_PAYMENT"


class InvalidPaymentMethodException(FeeException):
    """Raised when invalid payment method is used"""
    default_message = "Invalid payment method"
    default_code = "INVALID_PAYMENT_METHOD"


class FeeStructureNotFoundException(FeeException):
    """Raised when fee structure is not found"""
    default_message = "Fee structure not found"
    default_code = "FEE_STRUCTURE_NOT_FOUND"


class StudentNotEnrolledException(FeeException):
    """Raised when student is not enrolled for the term"""
    default_message = "Student is not enrolled for this term"
    default_code = "STUDENT_NOT_ENROLLED"


class PaymentGatewayException(FeeException):
    """Raised when payment gateway errors occur"""
    default_message = "Payment gateway error"
    default_code = "PAYMENT_GATEWAY_ERROR"


class ReceiptGenerationException(FeeException):
    """Raised when receipt generation fails"""
    default_message = "Receipt generation failed"
    default_code = "RECEIPT_GENERATION_FAILED"


class BillingError(FeeException):
    """General billing error for the fees module"""
    default_message = "A billing error occurred"
    default_code = "BILLING_ERROR"


def custom_exception_handler(exc, context):
    """
    Custom exception handler for fee-related exceptions
    """
    # Call REST framework's default exception handler first
    response = exception_handler(exc, context)
    
    # Log the exception
    logger.error(f"Exception in {context['view'].__class__.__name__}: {exc}", exc_info=True)
    
    if isinstance(exc, FeeException):
        custom_response_data = {
            'error': {
                'message': exc.message,
                'code': exc.code,
                'details': exc.details
            },
            'success': False,
            'timestamp': context['request'].META.get('HTTP_X_TIMESTAMP')
        }
        
        # Determine status code based on exception type
        status_code = status.HTTP_400_BAD_REQUEST
        if isinstance(exc, (FeeStructureNotFoundException, StudentNotEnrolledException)):
            status_code = status.HTTP_404_NOT_FOUND
        elif isinstance(exc, PaymentGatewayException):
            status_code = status.HTTP_502_BAD_GATEWAY
        elif isinstance(exc, PaymentVerificationException):
            status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        
        return Response(custom_response_data, status=status_code)
    
    # Handle other exceptions
    if response is not None:
        custom_response_data = {
            'error': {
                'message': 'An error occurred',
                'code': 'UNKNOWN_ERROR',
                'details': response.data if hasattr(response, 'data') else {}
            },
            'success': False,
            'timestamp': context['request'].META.get('HTTP_X_TIMESTAMP')
        }
        response.data = custom_response_data
    
    return response


class ValidationError(Exception):
    """Custom validation error for fee operations"""
    def __init__(self, field, message, code=None):
        self.field = field
        self.message = message
        self.code = code or 'VALIDATION_ERROR'
        super().__init__(f"{field}: {message}")


def validate_payment_amount(amount, outstanding_balance, allow_overpayment=False):
    """
    Validate payment amount against outstanding balance
    """
    if amount <= 0:
        raise ValidationError('amount', 'Payment amount must be greater than zero', 'INVALID_AMOUNT')
    
    if not allow_overpayment and amount > outstanding_balance:
        raise ValidationError(
            'amount', 
            f'Payment amount ({amount}) exceeds outstanding balance ({outstanding_balance})',
            'AMOUNT_EXCEEDS_BALANCE'
        )
    
    return True


def validate_payment_method(payment_method, allowed_methods=None):
    """
    Validate payment method
    """
    if allowed_methods is None:
        from .models import FeePayment
        allowed_methods = [choice[0] for choice in FeePayment.PAYMENT_METHODS]
    
    if payment_method not in allowed_methods:
        raise ValidationError(
            'payment_method',
            f'Invalid payment method. Allowed methods: {", ".join(allowed_methods)}',
            'INVALID_PAYMENT_METHOD'
        )
    
    return True


def validate_student_enrollment(student, term):
    """
    Validate that student is enrolled for the term
    """
    from .models import StudentFeeAccount
    
    try:
        StudentFeeAccount.objects.get(student=student, term=term)
    except StudentFeeAccount.DoesNotExist:
        raise ValidationError(
            'student',
            f'Student {student} is not enrolled for term {term}',
            'STUDENT_NOT_ENROLLED'
        )
    
    return True


def validate_duplicate_payment(reference_number, student=None):
    """
    Validate that payment reference is not duplicate
    """
    from .models import FeePayment
    
    query = FeePayment.objects.filter(reference_number=reference_number)
    if student:
        query = query.filter(student=student)
    
    if query.exists():
        raise ValidationError(
            'reference_number',
            f'Payment with reference {reference_number} already exists',
            'DUPLICATE_REFERENCE'
        )
    
    return True


class PaymentValidator:
    """
    Comprehensive payment validator
    """
    
    @staticmethod
    def validate_payment_data(data, student=None, term=None):
        """
        Validate all payment data
        """
        errors = []
        
        try:
            # Validate required fields
            required_fields = ['amount', 'payment_method', 'reference_number']
            for field in required_fields:
                if field not in data or not data[field]:
                    errors.append(ValidationError(field, f'{field} is required', 'REQUIRED_FIELD'))
            
            if errors:
                return errors
            
            # Validate amount
            try:
                validate_payment_amount(float(data['amount']), float('inf'))  # Basic amount validation
            except ValidationError as e:
                errors.append(e)
            
            # Validate payment method
            try:
                validate_payment_method(data['payment_method'])
            except ValidationError as e:
                errors.append(e)
            
            # Validate reference number
            try:
                validate_duplicate_payment(data['reference_number'], student)
            except ValidationError as e:
                errors.append(e)
            
            # Validate student enrollment if provided
            if student and term:
                try:
                    validate_student_enrollment(student, term)
                except ValidationError as e:
                    errors.append(e)
            
        except Exception as e:
            logger.error(f"Unexpected error in payment validation: {e}")
            errors.append(ValidationError('general', 'Validation failed due to unexpected error', 'VALIDATION_ERROR'))
        
        return errors
