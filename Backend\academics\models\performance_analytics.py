from django.db import models
from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator

class PerformanceAnalytics(models.Model):
    student = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='performance_analytics')
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE, related_name='performance_analytics')
    term = models.ForeignKey('academics.Term', on_delete=models.CASCADE, related_name='performance_analytics')
    
    # Academic Performance Metrics
    average_score = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(0), MaxValueValidator(100)])
    highest_score = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(0), MaxValueValidator(100)])
    lowest_score = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(0), MaxValueValidator(100)])
    standard_deviation = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    
    # Attendance Metrics
    attendance_rate = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(0), MaxValueValidator(100)])
    total_absences = models.PositiveIntegerField(default=0)
    total_late_attendance = models.PositiveIntegerField(default=0)
    
    # Engagement Metrics
    participation_rate = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(0), MaxValueValidator(100)])
    homework_completion_rate = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(0), MaxValueValidator(100)])
    
    # Progress Tracking
    improvement_rate = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    learning_gaps = models.JSONField(default=list, blank=True)
    strengths = models.JSONField(default=list, blank=True)
    weaknesses = models.JSONField(default=list, blank=True)
    
    # Additional Analytics
    study_time = models.DurationField(null=True, blank=True)
    resource_utilization = models.JSONField(default=dict, blank=True)
    assessment_patterns = models.JSONField(default=dict, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_analyzed = models.DateTimeField(null=True, blank=True)

    class Meta:
        app_label = 'academics'
        verbose_name = 'Performance Analytics'
        verbose_name_plural = 'Performance Analytics'
        unique_together = ['student', 'subject', 'term']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.student} - {self.subject} ({self.term})"

    def calculate_improvement_rate(self):
        """Calculate the rate of improvement over time"""
        # Implementation would compare current performance with historical data
        pass

    def identify_learning_gaps(self):
        """Identify specific areas where the student needs improvement"""
        # Implementation would analyze assessment results and identify weak areas
        pass

    def generate_strengths_weaknesses(self):
        """Generate lists of student's strengths and weaknesses"""
        # Implementation would analyze performance patterns
        pass

    def update_analytics(self):
        """Update all analytics metrics"""
        # Implementation would recalculate all metrics
        pass 