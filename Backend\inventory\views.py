from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from django.db.models import Q, Count, Sum, F, ExpressionWrapper, DecimalField
from django.shortcuts import get_object_or_404

from .models import (
    AssetCategory, Asset, SupplyCategory, Supply, SupplyTransaction,
    Maintenance, Supplier, PurchaseOrder, PurchaseOrderItem, InventoryReport
)
from .serializers import (
    AssetCategorySerializer, AssetSerializer, SupplyCategorySerializer,
    SupplySerializer, SupplyTransactionSerializer, MaintenanceSerializer,
    SupplierSerializer, PurchaseOrderSerializer, PurchaseOrderItemSerializer,
    InventoryReportSerializer
)
from core.permissions import BranchBasedPermission

class AssetViewSet(viewsets.ModelViewSet):
    serializer_class = AssetSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'asset_number', 'category__name', 'location']
    ordering_fields = ['name', 'purchase_date', 'status', 'created_at']
    ordering = ['name']

    def get_queryset(self):
        queryset = Asset.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by status
        status = self.request.query_params.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Filter by category
        category_id = self.request.query_params.get('category')
        if category_id:
            queryset = queryset.filter(category_id=category_id)

        # Filter by location
        location = self.request.query_params.get('location')
        if location:
            queryset = queryset.filter(location__icontains=location)

        # Filter by assigned user
        assigned_to = self.request.query_params.get('assigned_to')
        if assigned_to:
            queryset = queryset.filter(assigned_to_id=assigned_to)

        # Filter by purchase date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(purchase_date__range=[start_date, end_date])

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            added_by=self.request.user,
            school_branch=self.request.user.school_branch
        )

    @action(detail=True, methods=['get'])
    def maintenance_history(self, request, pk=None):
        asset = self.get_object()
        maintenance_records = Maintenance.objects.filter(asset=asset)
        serializer = MaintenanceSerializer(maintenance_records, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def categories(self, request):
        categories = AssetCategory.objects.filter(school_branch=request.user.school_branch)
        serializer = AssetCategorySerializer(categories, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def locations(self, request):
        locations = Asset.objects.filter(
            school_branch=request.user.school_branch
        ).values_list('location', flat=True).distinct()
        return Response(list(locations))

    @action(detail=False, methods=['get'])
    def status_summary(self, request):
        summary = Asset.objects.filter(
            school_branch=request.user.school_branch
        ).values('status').annotate(count=Count('id'))
        return Response(summary)

class AssetCategoryViewSet(viewsets.ModelViewSet):
    serializer_class = AssetCategorySerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']

    def get_queryset(self):
        queryset = AssetCategory.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        return queryset

    def perform_create(self, serializer):
        serializer.save(school_branch=self.request.user.school_branch)

    @action(detail=True, methods=['get'])
    def assets(self, request, pk=None):
        category = self.get_object()
        assets = Asset.objects.filter(category=category)
        serializer = AssetSerializer(assets, many=True)
        return Response(serializer.data)

class SupplyViewSet(viewsets.ModelViewSet):
    serializer_class = SupplySerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'category__name', 'location']
    ordering_fields = ['name', 'quantity', 'created_at']
    ordering = ['name']

    def get_queryset(self):
        queryset = Supply.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by category
        category_id = self.request.query_params.get('category')
        if category_id:
            queryset = queryset.filter(category_id=category_id)

        # Filter by location
        location = self.request.query_params.get('location')
        if location:
            queryset = queryset.filter(location__icontains=location)

        # Filter by reordering needs
        needs_reordering = self.request.query_params.get('needs_reordering')
        if needs_reordering == 'true':
            queryset = queryset.filter(quantity__lte=F('minimum_quantity'))

        # Filter by expiry date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(expiry_date__range=[start_date, end_date])

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            added_by=self.request.user,
            school_branch=self.request.user.school_branch
        )

    @action(detail=True, methods=['get'])
    def transaction_history(self, request, pk=None):
        supply = self.get_object()
        transactions = SupplyTransaction.objects.filter(supply=supply)
        serializer = SupplyTransactionSerializer(transactions, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def categories(self, request):
        categories = SupplyCategory.objects.filter(school_branch=request.user.school_branch)
        serializer = SupplyCategorySerializer(categories, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def locations(self, request):
        locations = Supply.objects.filter(
            school_branch=request.user.school_branch
        ).values_list('location', flat=True).distinct()
        return Response(list(locations))

    @action(detail=False, methods=['get'])
    def low_stock(self, request):
        low_stock_items = Supply.objects.filter(
            school_branch=request.user.school_branch,
            quantity__lte=F('minimum_quantity')
        )
        serializer = SupplySerializer(low_stock_items, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def expiring_soon(self, request):
        days = int(request.query_params.get('days', 30))
        expiry_threshold = timezone.now().date() + timezone.timedelta(days=days)

        expiring_items = Supply.objects.filter(
            school_branch=request.user.school_branch,
            expiry_date__lte=expiry_threshold,
            expiry_date__gte=timezone.now().date()
        )
        serializer = SupplySerializer(expiring_items, many=True)
        return Response(serializer.data)

class SupplyCategoryViewSet(viewsets.ModelViewSet):
    serializer_class = SupplyCategorySerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']

    def get_queryset(self):
        queryset = SupplyCategory.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        return queryset

    def perform_create(self, serializer):
        serializer.save(school_branch=self.request.user.school_branch)

    @action(detail=True, methods=['get'])
    def supplies(self, request, pk=None):
        category = self.get_object()
        supplies = Supply.objects.filter(category=category)
        serializer = SupplySerializer(supplies, many=True)
        return Response(serializer.data)

class SupplyTransactionViewSet(viewsets.ModelViewSet):
    serializer_class = SupplyTransactionSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['supply__name', 'notes']
    ordering_fields = ['transaction_date', 'transaction_type', 'created_at']
    ordering = ['-transaction_date']

    def get_queryset(self):
        queryset = SupplyTransaction.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by supply
        supply_id = self.request.query_params.get('supply')
        if supply_id:
            queryset = queryset.filter(supply_id=supply_id)

        # Filter by transaction type
        transaction_type = self.request.query_params.get('transaction_type')
        if transaction_type:
            queryset = queryset.filter(transaction_type=transaction_type)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(transaction_date__range=[start_date, end_date])

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            performed_by=self.request.user,
            school_branch=self.request.user.school_branch
        )

class MaintenanceViewSet(viewsets.ModelViewSet):
    serializer_class = MaintenanceSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['asset__name', 'asset__asset_number', 'description']
    ordering_fields = ['scheduled_date', 'status', 'maintenance_type', 'created_at']
    ordering = ['-scheduled_date']

    def get_queryset(self):
        queryset = Maintenance.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by asset
        asset_id = self.request.query_params.get('asset')
        if asset_id:
            queryset = queryset.filter(asset_id=asset_id)

        # Filter by status
        status_param = self.request.query_params.get('status')
        if status_param:
            queryset = queryset.filter(status=status_param)

        # Filter by maintenance type
        maintenance_type = self.request.query_params.get('maintenance_type')
        if maintenance_type:
            queryset = queryset.filter(maintenance_type=maintenance_type)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(scheduled_date__range=[start_date, end_date])

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            created_by=self.request.user,
            school_branch=self.request.user.school_branch
        )

    @action(detail=True, methods=['post'])
    def start_maintenance(self, request, pk=None):
        maintenance = self.get_object()

        if maintenance.status != 'SCHEDULED':
            return Response(
                {"detail": "Maintenance must be in SCHEDULED status to start."},
                status=status.HTTP_400_BAD_REQUEST
            )

        maintenance.status = 'IN_PROGRESS'
        maintenance.start_date = request.data.get('start_date', timezone.now().date())
        maintenance.save()

        serializer = self.get_serializer(maintenance)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def complete_maintenance(self, request, pk=None):
        maintenance = self.get_object()

        if maintenance.status != 'IN_PROGRESS':
            return Response(
                {"detail": "Maintenance must be in IN_PROGRESS status to complete."},
                status=status.HTTP_400_BAD_REQUEST
            )

        maintenance.status = 'COMPLETED'
        maintenance.completion_date = request.data.get('completion_date', timezone.now().date())
        maintenance.cost = request.data.get('cost', maintenance.cost)
        maintenance.notes = request.data.get('notes', maintenance.notes)
        maintenance.save()

        serializer = self.get_serializer(maintenance)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def upcoming(self, request):
        upcoming_maintenance = Maintenance.objects.filter(
            school_branch=request.user.school_branch,
            status='SCHEDULED',
            scheduled_date__gte=timezone.now().date()
        ).order_by('scheduled_date')

        serializer = self.get_serializer(upcoming_maintenance, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def overdue(self, request):
        overdue_maintenance = Maintenance.objects.filter(
            school_branch=request.user.school_branch,
            status='SCHEDULED',
            scheduled_date__lt=timezone.now().date()
        ).order_by('scheduled_date')

        serializer = self.get_serializer(overdue_maintenance, many=True)
        return Response(serializer.data)

class SupplierViewSet(viewsets.ModelViewSet):
    serializer_class = SupplierSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'contact_person', 'email', 'phone']
    ordering_fields = ['name', 'is_active', 'created_at']
    ordering = ['name']

    def get_queryset(self):
        queryset = Supplier.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=(is_active.lower() == 'true'))

        return queryset

    def perform_create(self, serializer):
        serializer.save(school_branch=self.request.user.school_branch)

    @action(detail=True, methods=['get'])
    def purchase_orders(self, request, pk=None):
        supplier = self.get_object()
        purchase_orders = PurchaseOrder.objects.filter(supplier=supplier)
        serializer = PurchaseOrderSerializer(purchase_orders, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def supplied_assets(self, request, pk=None):
        supplier = self.get_object()
        assets = Asset.objects.filter(supplier=supplier)
        serializer = AssetSerializer(assets, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def supplied_items(self, request, pk=None):
        supplier = self.get_object()
        supplies = Supply.objects.filter(supplier=supplier)
        serializer = SupplySerializer(supplies, many=True)
        return Response(serializer.data)

class PurchaseOrderViewSet(viewsets.ModelViewSet):
    serializer_class = PurchaseOrderSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['po_number', 'supplier__name', 'notes']
    ordering_fields = ['order_date', 'status', 'payment_status', 'created_at']
    ordering = ['-order_date']

    def get_queryset(self):
        queryset = PurchaseOrder.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by supplier
        supplier_id = self.request.query_params.get('supplier')
        if supplier_id:
            queryset = queryset.filter(supplier_id=supplier_id)

        # Filter by status
        status_param = self.request.query_params.get('status')
        if status_param:
            queryset = queryset.filter(status=status_param)

        # Filter by payment status
        payment_status = self.request.query_params.get('payment_status')
        if payment_status:
            queryset = queryset.filter(payment_status=payment_status)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(order_date__range=[start_date, end_date])

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            created_by=self.request.user,
            school_branch=self.request.user.school_branch
        )

    def create(self, request, *args, **kwargs):
        items_data = request.data.pop('items', [])
        serializer = self.get_serializer(data=request.data, context={'items': items_data})
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    @action(detail=True, methods=['get'])
    def items(self, request, pk=None):
        purchase_order = self.get_object()
        items = PurchaseOrderItem.objects.filter(purchase_order=purchase_order)
        serializer = PurchaseOrderItemSerializer(items, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        purchase_order = self.get_object()

        if purchase_order.status != 'PENDING_APPROVAL':
            return Response(
                {"detail": "Purchase order must be in PENDING_APPROVAL status to approve."},
                status=status.HTTP_400_BAD_REQUEST
            )

        purchase_order.status = 'APPROVED'
        purchase_order.approved_by = request.user
        purchase_order.save()

        serializer = self.get_serializer(purchase_order)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def receive_items(self, request, pk=None):
        purchase_order = self.get_object()

        if purchase_order.status not in ['ORDERED', 'PARTIALLY_RECEIVED']:
            return Response(
                {"detail": "Purchase order must be in ORDERED or PARTIALLY_RECEIVED status to receive items."},
                status=status.HTTP_400_BAD_REQUEST
            )

        items_data = request.data.get('items', [])
        all_received = True

        for item_data in items_data:
            item_id = item_data.get('id')
            received_quantity = item_data.get('received_quantity', 0)

            try:
                item = PurchaseOrderItem.objects.get(id=item_id, purchase_order=purchase_order)

                # Update received quantity
                item.received_quantity += received_quantity
                item.save()

                # Create supply transaction if linked to a supply
                if item.supply:
                    SupplyTransaction.objects.create(
                        supply=item.supply,
                        transaction_type='PURCHASE',
                        quantity=received_quantity,
                        transaction_date=timezone.now().date(),
                        unit_price=item.unit_price,
                        supplier=purchase_order.supplier,
                        purchase_order=purchase_order,
                        performed_by=request.user,
                        school_branch=request.user.school_branch,
                        notes=f"Received from PO #{purchase_order.po_number}"
                    )

                # Check if all items are fully received
                if item.received_quantity < item.quantity:
                    all_received = False

            except PurchaseOrderItem.DoesNotExist:
                return Response(
                    {"detail": f"Item with ID {item_id} not found in this purchase order."},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Update purchase order status
        if all_received:
            purchase_order.status = 'RECEIVED'
            purchase_order.delivery_date = request.data.get('delivery_date', timezone.now().date())
        else:
            purchase_order.status = 'PARTIALLY_RECEIVED'

        purchase_order.save()

        serializer = self.get_serializer(purchase_order)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def record_payment(self, request, pk=None):
        purchase_order = self.get_object()

        purchase_order.payment_status = 'PAID'
        purchase_order.payment_date = request.data.get('payment_date', timezone.now().date())
        purchase_order.payment_method = request.data.get('payment_method', 'BANK_TRANSFER')
        purchase_order.save()

        serializer = self.get_serializer(purchase_order)
        return Response(serializer.data)

class PurchaseOrderItemViewSet(viewsets.ModelViewSet):
    serializer_class = PurchaseOrderItemSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]

    def get_queryset(self):
        queryset = PurchaseOrderItem.objects.all()

        # Filter by purchase order
        purchase_order_id = self.request.query_params.get('purchase_order')
        if purchase_order_id:
            queryset = queryset.filter(purchase_order_id=purchase_order_id)

        return queryset

    def get_permissions(self):
        if self.action in ['update', 'partial_update', 'destroy']:
            # Check if the purchase order is in DRAFT status
            purchase_order_id = self.kwargs.get('pk')
            if purchase_order_id:
                try:
                    item = PurchaseOrderItem.objects.get(pk=purchase_order_id)
                    if item.purchase_order.status != 'DRAFT':
                        return [IsAuthenticated()]  # Only allow if PO is in DRAFT status
                except PurchaseOrderItem.DoesNotExist:
                    pass

        return super().get_permissions()

class InventoryReportViewSet(viewsets.ModelViewSet):
    serializer_class = InventoryReportSerializer
    permission_classes = [IsAuthenticated, BranchBasedPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'report_type']
    ordering_fields = ['created_at', 'start_date', 'end_date']
    ordering = ['-created_at']

    def get_queryset(self):
        queryset = InventoryReport.objects.all()

        # Filter by school branch
        if not self.request.user.is_superuser:
            queryset = queryset.filter(school_branch=self.request.user.school_branch)

        # Filter by report type
        report_type = self.request.query_params.get('report_type')
        if report_type:
            queryset = queryset.filter(report_type=report_type)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(
                Q(start_date__gte=start_date) & Q(end_date__lte=end_date)
            )

        return queryset

    def perform_create(self, serializer):
        serializer.save(
            generated_by=self.request.user,
            school_branch=self.request.user.school_branch
        )

    @action(detail=False, methods=['post'])
    def generate_asset_report(self, request):
        start_date = request.data.get('start_date')
        end_date = request.data.get('end_date')

        if not start_date or not end_date:
            return Response(
                {"detail": "Start date and end date are required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get assets in the date range
        assets = Asset.objects.filter(
            school_branch=request.user.school_branch,
            purchase_date__range=[start_date, end_date]
        )

        # Generate report data
        report_data = {
            'total_assets': assets.count(),
            'total_value': float(assets.aggregate(Sum('purchase_price'))['purchase_price__sum'] or 0),
            'status_summary': list(
                assets.values('status').annotate(count=Count('id')).order_by('status')
            ),
            'category_summary': list(
                assets.values('category__name').annotate(
                    count=Count('id'),
                    value=Sum('purchase_price')
                ).order_by('-count')
            ),
            'location_summary': list(
                assets.values('location').annotate(count=Count('id')).order_by('-count')
            ),
            'maintenance_summary': {
                'total_maintenance': Maintenance.objects.filter(
                    asset__in=assets
                ).count(),
                'maintenance_cost': float(
                    Maintenance.objects.filter(asset__in=assets).aggregate(
                        Sum('cost')
                    )['cost__sum'] or 0
                ),
                'by_type': list(
                    Maintenance.objects.filter(asset__in=assets).values(
                        'maintenance_type'
                    ).annotate(count=Count('id')).order_by('-count')
                )
            }
        }

        # Create report
        report = InventoryReport.objects.create(
            title=f"Asset Report ({start_date} to {end_date})",
            report_type='ASSET',
            start_date=start_date,
            end_date=end_date,
            generated_by=request.user,
            school_branch=request.user.school_branch,
            report_data=report_data
        )

        serializer = self.get_serializer(report)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=False, methods=['post'])
    def generate_supply_report(self, request):
        start_date = request.data.get('start_date')
        end_date = request.data.get('end_date')

        if not start_date or not end_date:
            return Response(
                {"detail": "Start date and end date are required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get supply transactions in the date range
        transactions = SupplyTransaction.objects.filter(
            school_branch=request.user.school_branch,
            transaction_date__range=[start_date, end_date]
        )

        # Get current supplies
        supplies = Supply.objects.filter(school_branch=request.user.school_branch)

        # Generate report data
        report_data = {
            'total_supplies': supplies.count(),
            'total_value': float(sum(supply.total_value for supply in supplies)),
            'low_stock_items': supplies.filter(quantity__lte=F('minimum_quantity')).count(),
            'transactions_summary': {
                'total_transactions': transactions.count(),
                'by_type': list(
                    transactions.values('transaction_type').annotate(count=Count('id')).order_by('-count')
                ),
                'total_purchases': float(
                    transactions.filter(transaction_type='PURCHASE').aggregate(
                        total=Sum(F('quantity') * F('unit_price'))
                    )['total'] or 0
                ),
                'total_usage': abs(float(
                    transactions.filter(transaction_type='USAGE').aggregate(
                        total=Sum('quantity')
                    )['total'] or 0
                )),
            },
            'category_summary': list(
                supplies.values('category__name').annotate(
                    count=Count('id'),
                    total_quantity=Sum('quantity'),
                    value=Sum(F('quantity') * F('unit_price'))
                ).order_by('-count')
            ),
            'expiring_soon': supplies.filter(
                expiry_date__gte=timezone.now().date(),
                expiry_date__lte=timezone.now().date() + timezone.timedelta(days=30)
            ).count()
        }

        # Create report
        report = InventoryReport.objects.create(
            title=f"Supply Report ({start_date} to {end_date})",
            report_type='SUPPLY',
            start_date=start_date,
            end_date=end_date,
            generated_by=request.user,
            school_branch=request.user.school_branch,
            report_data=report_data
        )

        serializer = self.get_serializer(report)
        return Response(serializer.data, status=status.HTTP_201_CREATED)