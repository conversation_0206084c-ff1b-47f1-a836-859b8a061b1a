import { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import { firstLoginService } from "../../services/firstLoginService";
import ProfileCompletionForm from "../../components/profile/ProfileCompletionForm";
import { toast } from "react-toastify";
import { getRoleBasedProfilePath } from "../../utils/routeUtils";

export default function CompleteProfilePage() {
  const navigate = useNavigate();
  const location = useLocation();
  const { userId } = useParams();
  const { user } = useAuth();
  
  const [loading, setLoading] = useState(true);
  const [isFirstLogin, setIsFirstLogin] = useState(false);
  const [userType, setUserType] = useState('');
  const [returnUrl, setReturnUrl] = useState('/dashboard');

  useEffect(() => {
    const initializePage = async () => {
      try {
        // Check if this is a first login scenario
        const isFirstLoginCheck = await firstLoginService.isFirstLogin();
        setIsFirstLogin(isFirstLoginCheck);

        // Get user type from various sources
        const currentUserType = location.state?.userType || user?.user_type || '';
        setUserType(currentUserType);

        // Set return URL - default to user's profile page
        const defaultReturnUrl = user ? getRoleBasedProfilePath(user) : '/dashboard';
        const savedReturnUrl = location.state?.returnUrl || defaultReturnUrl;
        setReturnUrl(savedReturnUrl);

        // If user already has a complete profile and this isn't first login, redirect
        if (!isFirstLoginCheck && user?.profile_status === 'complete') {
          toast.info('Your profile is already complete.');
          navigate(getRoleBasedProfilePath(user));
          return;
        }

      } catch (error) {
        console.error('Error initializing profile completion page:', error);
        toast.error('Error loading profile completion page.');
      } finally {
        setLoading(false);
      }
    };

    initializePage();
  }, [location.state, user, navigate]);

  const handleProfileCompleted = async () => {
    try {
      if (isFirstLogin) {
        // Mark first login as complete
        await firstLoginService.completeFirstLogin();
        toast.success('Welcome to ShuleXcel! Your profile has been completed.');

        // Redirect to the user's profile page or original URL
        const profilePath = user ? getRoleBasedProfilePath(user) : '/dashboard';
        navigate(returnUrl === '/dashboard' ? profilePath : returnUrl);
      } else {
        // For normal profile completion, redirect to user's profile page
        toast.success('Profile completed successfully!');
        const profilePath = user ? getRoleBasedProfilePath(user) : '/dashboard';
        navigate(profilePath);
      }
    } catch (error) {
      console.error('Error completing profile setup:', error);
      toast.error('Profile completed but there was an issue with the final setup.');
      const profilePath = user ? getRoleBasedProfilePath(user) : '/dashboard';
      navigate(profilePath);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <p className="mt-4 text-gray-600 dark:text-gray-400">Loading your profile...</p>
      </div>
    );
  }

  return (
    <>
      {/* Show first login banner if applicable */}
      {isFirstLogin && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700 dark:text-blue-300">
                <strong>Welcome to ShuleXcel!</strong> Please complete your profile to continue using the system.
              </p>
            </div>
          </div>
        </div>
      )}
      
      <ProfileCompletionForm
        userType={userType}
        userId={userId}
        onComplete={handleProfileCompleted}
        isFirstLogin={isFirstLogin}
      />
    </>
  );
}
