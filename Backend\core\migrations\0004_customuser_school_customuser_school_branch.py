# Generated by Django 5.2.1 on 2025-06-04 20:58

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0003_alter_customuser_user_type'),
        ('schools', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='customuser',
            name='school',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='core_users', to='schools.school'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='school_branch',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='core_users', to='schools.schoolbranch'),
        ),
    ]
