import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import PerformanceAnalytics from '../../components/admin/PerformanceAnalytics';
import FinancialMetrics from '../../components/admin/FinancialMetrics';
import CalendarIntegration from '../../components/admin/CalendarIntegration';
import AcademicOverview from '../../components/admin/AcademicOverview';
import CurriculumOverview from '../../components/admin/CurriculumOverview';
import SchoolProfileOverview from '../../components/admin/SchoolProfileOverview';
import SchoolSwitcher from '../../components/common/SchoolSwitcher';
import { getAllDashboardData } from '../../services/dashboardService';
import { useSchool } from '../../contexts/SchoolContext';

// Import Heroicons (as an alternative to Material UI icons)
import {
  UserGroupIcon,
  UserIcon,
  AcademicCapIcon,
  BookOpenIcon,
  CalendarIcon,
  MegaphoneIcon,
  ArrowRightIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  ArrowPathIcon,
  BuildingOfficeIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

// Define types for dashboard data
interface DashboardStats {
  totalStudents: number;
  totalTeachers: number;
  totalClasses: number;
  totalCourses: number;
  totalStreams: number;
  totalDepartments: number;
  totalEvents: number;
  totalAnnouncements: number;
}

interface Announcement {
  id: number;
  title: string;
  date: string;
  priority: string;
}

interface Event {
  id: number;
  title: string;
  date: string;
  location: string;
}

interface Activity {
  id: number;
  user: string;
  action: string;
  timestamp: string;
}

interface DashboardData {
  stats: DashboardStats;
  recentAnnouncements: Announcement[];
  upcomingEvents: Event[];
  recentActivities: Activity[] | undefined;
}

// Default dashboard data for when API calls fail
const defaultDashboardData: DashboardData = {
  stats: {
    totalStudents: 0,
    totalTeachers: 0,
    totalClasses: 0,
    totalCourses: 0,
    totalStreams: 0,
    totalDepartments: 0,
    totalEvents: 0,
    totalAnnouncements: 0
  },
  recentAnnouncements: [],
  upcomingEvents: [],
  recentActivities: []
};

const AdminDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData>(defaultDashboardData);
  const [loading, setLoading] = useState<boolean>(true);
  const navigate = useNavigate();

  // Get the selected school and branch from context
  const { selectedSchool, selectedBranch } = useSchool();

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Pass the selected school and branch IDs to the dashboard service
        const data = await getAllDashboardData({
          schoolId: selectedSchool?.id,
          branchId: selectedBranch?.id
        });

        if (data) {
          setDashboardData(data as DashboardData);
        } else {
          // If no data is returned, use default data
          setDashboardData(defaultDashboardData);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // If there's an error, use default data
        setDashboardData(defaultDashboardData);
      } finally {
        setLoading(false);
      }
    };

    // Only fetch data if we have a selected school and branch
    if (selectedSchool && selectedBranch) {
      setLoading(true);
      fetchData();
    } else {
      // If no school or branch is selected, use default data and stop loading
      setDashboardData(defaultDashboardData);
      setLoading(false);
    }
  }, [selectedSchool, selectedBranch]); // Re-fetch when school or branch changes

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'LOW':
        return 'bg-blue-100 text-blue-800';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800';
      case 'HIGH':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityIcon = (priority: string) => {
    return priority === 'HIGH' ? (
      <ChevronUpIcon className="h-4 w-4 text-red-600" />
    ) : (
      <ChevronDownIcon className="h-4 w-4 text-blue-600" />
    );
  };

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Admins Dashboard</h1>
        <div className="mt-4 md:mt-0">
          <button
            type="button"
            onClick={() => {
              setLoading(true);
              getAllDashboardData({
                schoolId: selectedSchool?.id,
                branchId: selectedBranch?.id
              }).then(data => {
                if (data) {
                  setDashboardData(data as DashboardData);
                } else {
                  setDashboardData(defaultDashboardData);
                }
                setLoading(false);
              });
            }}
            className="inline-flex items-center rounded-lg bg-indigo-600 px-3 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 mr-3"
          >
            <ArrowPathIcon className="-ml-0.5 mr-1.5 h-5 w-5" />
            Refresh Data
          </button>
        </div>
      </div>

      {/* School/Branch Selector */}
      <div className="bg-indigo-50 border border-indigo-100 rounded-xl p-4 shadow-sm mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h2 className="text-lg font-semibold text-gray-800 flex items-center">
              <BuildingOfficeIcon className="h-5 w-5 mr-2 text-indigo-600" />
              {selectedSchool && selectedBranch ? (
                <>Currently Viewing: {selectedSchool.name} - {selectedBranch.name}</>
              ) : (
                <>Select a School and Branch</>
              )}
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              {selectedSchool && selectedBranch ? (
                <>Dashboard data is filtered for this school and branch. Use the selector below to change.</>
              ) : (
                <>Please select a school and branch to view dashboard data.</>
              )}
            </p>
          </div>
          <div className="flex-shrink-0">
            <SchoolSwitcher className="w-full" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        {/* Summary Cards */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-blue-50">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-blue-100 rounded-lg">
                <UserGroupIcon className="h-6 w-6 text-blue-600" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 ml-3">Students</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                {dashboardData.stats && dashboardData.stats.totalStudents > 0 ? (
                  <p className="text-3xl font-bold text-gray-800">{dashboardData.stats.totalStudents}</p>
                ) : (
                  <p className="text-3xl font-bold text-gray-400">0</p>
                )}
                <p className="text-sm text-gray-500 mt-1">Total enrolled students</p>
              </div>
              {dashboardData.stats && dashboardData.stats.totalStudents > 0 ? (
                <div className="bg-blue-100 text-blue-600 rounded-full px-2 py-1 text-xs font-medium">
                  +5.2%
                </div>
              ) : (
                <button
                  type="button"
                  onClick={() => navigate('/AllStudents/add')}
                  className="bg-blue-100 text-blue-600 rounded-full px-2 py-1 text-xs font-medium hover:bg-blue-200"
                >
                  Add New
                </button>
              )}
            </div>
          </div>
          <div className="px-6 py-3 bg-white border-t border-gray-100">
            <button
              type="button"
              onClick={() => navigate('/AllStudents')}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
            >
              View Students
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-green-50">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-green-100 rounded-lg">
                <UserIcon className="h-6 w-6 text-green-600" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 ml-3">Teachers</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                {dashboardData.stats && dashboardData.stats.totalTeachers > 0 ? (
                  <p className="text-3xl font-bold text-gray-800">{dashboardData.stats.totalTeachers}</p>
                ) : (
                  <p className="text-3xl font-bold text-gray-400">0</p>
                )}
                <p className="text-sm text-gray-500 mt-1">Total teaching staff</p>
              </div>
              {dashboardData.stats && dashboardData.stats.totalTeachers > 0 ? (
                <div className="bg-green-100 text-green-600 rounded-full px-2 py-1 text-xs font-medium">
                  +2.8%
                </div>
              ) : (
                <button
                  type="button"
                  onClick={() => navigate('/teachers/add')}
                  className="bg-green-100 text-green-600 rounded-full px-2 py-1 text-xs font-medium hover:bg-green-200"
                >
                  Add New
                </button>
              )}
            </div>
          </div>
          <div className="px-6 py-3 bg-white border-t border-gray-100">
            <button
              type="button"
              onClick={() => navigate('/teachers')}
              className="text-green-600 hover:text-green-800 text-sm font-medium flex items-center"
            >
              View Teachers
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-purple-50">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-purple-100 rounded-lg">
                <AcademicCapIcon className="h-6 w-6 text-purple-600" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 ml-3">Classes</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                {dashboardData.stats && dashboardData.stats.totalClasses > 0 ? (
                  <p className="text-3xl font-bold text-gray-800">{dashboardData.stats.totalClasses}</p>
                ) : (
                  <p className="text-3xl font-bold text-gray-400">0</p>
                )}
                <p className="text-sm text-gray-500 mt-1">Active classes</p>
              </div>
              {dashboardData.stats && dashboardData.stats.totalClasses > 0 ? (
                <div className="bg-purple-100 text-purple-600 rounded-full px-2 py-1 text-xs font-medium">
                  +4.3%
                </div>
              ) : (
                <button
                  type="button"
                  onClick={() => navigate('/classes/add')}
                  className="bg-purple-100 text-purple-600 rounded-full px-2 py-1 text-xs font-medium hover:bg-purple-200"
                >
                  Add New
                </button>
              )}
            </div>
          </div>
          <div className="px-6 py-3 bg-white border-t border-gray-100">
            <button
              type="button"
              onClick={() => navigate('/classes')}
              className="text-purple-600 hover:text-purple-800 text-sm font-medium flex items-center"
            >
              View Classes
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-amber-50">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-amber-100 rounded-lg">
                <BookOpenIcon className="h-6 w-6 text-amber-600" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 ml-3">Courses</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                {dashboardData.stats && dashboardData.stats.totalCourses > 0 ? (
                  <p className="text-3xl font-bold text-gray-800">{dashboardData.stats.totalCourses}</p>
                ) : (
                  <p className="text-3xl font-bold text-gray-400">0</p>
                )}
                <p className="text-sm text-gray-500 mt-1">Total courses offered</p>
              </div>
              {dashboardData.stats && dashboardData.stats.totalCourses > 0 ? (
                <div className="bg-amber-100 text-amber-600 rounded-full px-2 py-1 text-xs font-medium">
                  +3.1%
                </div>
              ) : (
                <button
                  type="button"
                  onClick={() => navigate('/academics/subjects/add')}
                  className="bg-amber-100 text-amber-600 rounded-full px-2 py-1 text-xs font-medium hover:bg-amber-200"
                >
                  Add New
                </button>
              )}
            </div>
          </div>
          <div className="px-6 py-3 bg-white border-t border-gray-100">
            <button
              type="button"
              onClick={() => navigate('/academics/subjects')}
              className="text-amber-600 hover:text-amber-800 text-sm font-medium flex items-center"
            >
              View Courses
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        {/* Streams Card */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-indigo-50">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-indigo-100 rounded-lg">
                <AcademicCapIcon className="h-6 w-6 text-indigo-600" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 ml-3">Streams</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                {dashboardData.stats && dashboardData.stats.totalStreams > 0 ? (
                  <p className="text-3xl font-bold text-gray-800">{dashboardData.stats.totalStreams}</p>
                ) : (
                  <p className="text-3xl font-bold text-gray-400">0</p>
                )}
                <p className="text-sm text-gray-500 mt-1">Class streams</p>
              </div>
              {dashboardData.stats && dashboardData.stats.totalStreams > 0 ? (
                <div className="bg-indigo-100 text-indigo-600 rounded-full px-2 py-1 text-xs font-medium">
                  Active
                </div>
              ) : (
                <button
                  type="button"
                  onClick={() => navigate('/academics/streams/add')}
                  className="bg-indigo-100 text-indigo-600 rounded-full px-2 py-1 text-xs font-medium hover:bg-indigo-200"
                >
                  Add New
                </button>
              )}
            </div>
          </div>
          <div className="px-6 py-3 bg-white border-t border-gray-100">
            <button
              type="button"
              onClick={() => navigate('/academics/streams')}
              className="text-indigo-600 hover:text-indigo-800 text-sm font-medium flex items-center"
            >
              View Streams
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        {/* Departments Card */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 bg-teal-50">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-teal-100 rounded-lg">
                <BookOpenIcon className="h-6 w-6 text-teal-600" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800 ml-3">Departments</h2>
            </div>
            <div className="flex items-end justify-between">
              <div>
                {dashboardData.stats && dashboardData.stats.totalDepartments > 0 ? (
                  <p className="text-3xl font-bold text-gray-800">{dashboardData.stats.totalDepartments}</p>
                ) : (
                  <p className="text-3xl font-bold text-gray-400">0</p>
                )}
                <p className="text-sm text-gray-500 mt-1">Academic departments</p>
              </div>
              {dashboardData.stats && dashboardData.stats.totalDepartments > 0 ? (
                <div className="bg-teal-100 text-teal-600 rounded-full px-2 py-1 text-xs font-medium">
                  Active
                </div>
              ) : (
                <button
                  type="button"
                  onClick={() => navigate('/academics/departments/add')}
                  className="bg-teal-100 text-teal-600 rounded-full px-2 py-1 text-xs font-medium hover:bg-teal-200"
                >
                  Add New
                </button>
              )}
            </div>
          </div>
          <div className="px-6 py-3 bg-white border-t border-gray-100">
            <button
              type="button"
              onClick={() => navigate('/academics/departments')}
              className="text-teal-600 hover:text-teal-800 text-sm font-medium flex items-center"
            >
              View Departments
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        {/* Recent Announcements */}
        <div className="md:col-span-2 lg:col-span-3 xl:col-span-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 h-full">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold text-gray-800">Recent Announcements</h2>
                <button
                  type="button"
                  onClick={() => navigate('/announcements')}
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
                >
                  View All
                  <ArrowRightIcon className="h-4 w-4 ml-1" />
                </button>
              </div>
              <div className="border-t border-gray-100 -mx-6 px-6 py-2"></div>
              <div className="space-y-4 mt-4">
                {dashboardData.recentAnnouncements && dashboardData.recentAnnouncements.length > 0 ? (
                  dashboardData.recentAnnouncements.map((announcement) => (
                    <div key={announcement.id} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0">
                      <div className="flex items-start space-x-3">
                        <div className="p-2 bg-blue-50 rounded-lg mt-1">
                          <MegaphoneIcon className="h-5 w-5 text-blue-500" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-800">{announcement.title}</h3>
                          <p className="text-sm text-gray-500">{format(new Date(announcement.date), 'MMM dd, yyyy')}</p>
                        </div>
                      </div>
                      <div className={`flex items-center px-3 py-1 rounded-full text-xs font-medium ${getPriorityColor(announcement.priority)}`}>
                        {getPriorityIcon(announcement.priority)}
                        <span className="ml-1">{announcement.priority}</span>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <div className="p-3 bg-blue-50 rounded-full mb-3">
                      <MegaphoneIcon className="h-8 w-8 text-blue-500" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-800 mb-1">No announcements yet</h3>
                    <p className="text-sm text-gray-500 max-w-xs">Create your first announcement to keep everyone informed about important updates.</p>
                    <button
                      type="button"
                      onClick={() => navigate('/announcements')}
                      className="mt-4 px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors duration-200 flex items-center"
                    >
                      <MegaphoneIcon className="h-4 w-4 mr-2" />
                      Create Announcement
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Upcoming Events */}
        <div className="md:col-span-1 lg:col-span-2">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 h-full">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold text-gray-800">Upcoming Events</h2>
                <button
                  type="button"
                  onClick={() => navigate('/calendar')}
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
                >
                  View All
                  <ArrowRightIcon className="h-4 w-4 ml-1" />
                </button>
              </div>
              <div className="border-t border-gray-100 -mx-6 px-6 py-2"></div>
              <div className="space-y-4 mt-4">
                {dashboardData.upcomingEvents && dashboardData.upcomingEvents.length > 0 ? (
                  dashboardData.upcomingEvents.map((event) => (
                    <div key={event.id} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0">
                      <div className="flex items-start space-x-3">
                        <div className="p-2 bg-indigo-50 rounded-lg mt-1">
                          <CalendarIcon className="h-5 w-5 text-indigo-500" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-800">{event.title}</h3>
                          <p className="text-sm text-gray-500">Location: {event.location}</p>
                        </div>
                      </div>
                      <div className="text-sm font-medium text-indigo-600 bg-indigo-50 px-3 py-1 rounded-full">
                        {format(new Date(event.date), 'MMM dd')}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <div className="p-3 bg-indigo-50 rounded-full mb-3">
                      <CalendarIcon className="h-8 w-8 text-indigo-500" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-800 mb-1">No upcoming events</h3>
                    <p className="text-sm text-gray-500 max-w-xs">Schedule your first event to keep everyone informed about important dates.</p>
                    <button
                      type="button"
                      onClick={() => navigate('/calendar')}
                      className="mt-4 px-4 py-2 bg-indigo-100 text-indigo-700 rounded-lg hover:bg-indigo-200 transition-colors duration-200 flex items-center"
                    >
                      <CalendarIcon className="h-4 w-4 mr-2" />
                      Schedule Event
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="col-span-1 md:col-span-2 lg:col-span-4">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100">
            <div className="p-6">
              <h2 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                <button
                  type="button"
                  onClick={() => navigate('/AllStudents')}
                  className="flex items-center justify-center px-4 py-3 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors duration-200"
                >
                  <UserGroupIcon className="h-5 w-5 mr-2" />
                  Manage Students
                </button>
                <button
                  type="button"
                  onClick={() => navigate('/announcements')}
                  className="flex items-center justify-center px-4 py-3 bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors duration-200"
                >
                  <MegaphoneIcon className="h-5 w-5 mr-2" />
                  Post Announcement
                </button>
                <button
                  type="button"
                  onClick={() => navigate('/assessments')}
                  className="flex items-center justify-center px-4 py-3 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors duration-200"
                >
                  <AcademicCapIcon className="h-5 w-5 mr-2" />
                  Manage Assessments
                </button>
                <button
                  type="button"
                  onClick={() => navigate('/academics/subjects')}
                  className="flex items-center justify-center px-4 py-3 bg-indigo-50 text-indigo-700 rounded-lg hover:bg-indigo-100 transition-colors duration-200"
                >
                  <BookOpenIcon className="h-5 w-5 mr-2" />
                  Manage Subjects
                </button>
                <button
                  type="button"
                  onClick={() => navigate('/settings/school-profile')}
                  className="flex items-center justify-center px-4 py-3 bg-amber-50 text-amber-700 rounded-lg hover:bg-amber-100 transition-colors duration-200"
                >
                  <UserIcon className="h-5 w-5 mr-2" />
                  School Settings
                </button>
                <button
                  type="button"
                  onClick={() => navigate('/dashboard-test')}
                  className="relative flex items-center justify-center px-4 py-3 bg-teal-50 text-teal-700 rounded-lg hover:bg-teal-100 transition-colors duration-200"
                >
                  <ChartBarIcon className="h-5 w-5 mr-2" />
                  Test Dashboards
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full font-medium">
                    NEW
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Academic Data Overview Section */}
      <div className="mt-8">
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold text-gray-800 mb-2">Academic Data Overview</h2>
                <div className="flex items-center space-x-6">
                  <div>
                    <p className="text-sm text-gray-500">Total Classes</p>
                    <p className="text-lg font-medium text-gray-800">{dashboardData.stats.totalClasses}</p>
                  </div>
                  <div className="h-8 w-px bg-gray-300"></div>
                  <div>
                    <p className="text-sm text-gray-500">Active Subjects</p>
                    <p className="text-lg font-medium text-gray-800">{dashboardData.stats.totalCourses}</p>
                  </div>
                  <div className="h-8 w-px bg-gray-300"></div>
                  <div>
                    <p className="text-sm text-gray-500">Streams</p>
                    <p className="text-lg font-medium text-gray-800">{dashboardData.stats.totalStreams}</p>
                  </div>
                  <div className="h-8 w-px bg-gray-300"></div>
                  <div>
                    <p className="text-sm text-gray-500">Departments</p>
                    <p className="text-lg font-medium text-gray-800">{dashboardData.stats.totalDepartments}</p>
                  </div>
                  <div className="h-8 w-px bg-gray-300"></div>
                  <div>
                    <p className="text-sm text-gray-500">Dashboard Testing</p>
                    <button
                      type="button"
                      onClick={() => navigate('/dashboard-test')}
                      className="text-lg font-medium text-blue-600 hover:text-blue-800 transition-colors duration-200"
                    >
                      View All Types →
                    </button>
                  </div>
                </div>
              </div>
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => navigate('/academics')}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 flex items-center"
                >
                  Manage Academics
                  <ArrowRightIcon className="h-4 w-4 ml-2" />
                </button>
                <button
                  type="button"
                  onClick={() => navigate('/dashboard-test')}
                  className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors duration-200 flex items-center"
                >
                  Test Dashboards
                  <ArrowRightIcon className="h-4 w-4 ml-2" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Analytics Section */}
      <div className="mt-8">
        <PerformanceAnalytics />
      </div>

      {/* Financial Metrics Section */}
      <div className="mt-8">
        <FinancialMetrics />
      </div>

      {/* Calendar Integration Section */}
      <div className="mt-8">
        <CalendarIntegration />
      </div>

      {/* Academic Overview Section */}
      <div className="mt-8">
        <AcademicOverview />
      </div>

      {/* Curriculum Overview Section */}
      <div className="mt-8">
        <CurriculumOverview />
      </div>

      {/* School Profile Overview Section */}
      <div className="mt-8">
        <SchoolProfileOverview />
      </div>
    </div>
  );
};

export default AdminDashboard;
