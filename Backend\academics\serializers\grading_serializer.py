from rest_framework import serializers
from academics.models import GradingSystem, GradeScale

class GradeScaleSerializer(serializers.ModelSerializer):
    class Meta:
        model = GradeScale
        fields = '__all__'

class GradingSystemSerializer(serializers.ModelSerializer):
    grade_scales = GradeScaleSerializer(many=True, read_only=True)
    
    class Meta:
        model = GradingSystem
        fields = '__all__'
