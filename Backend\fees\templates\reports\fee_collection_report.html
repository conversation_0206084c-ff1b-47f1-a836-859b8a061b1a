{% extends "reports/base_report.html" %}

{% block title %}Fee Collection Report - {{ school.name }}{% endblock %}

{% block report_title %}Fee Collection Report{% endblock %}

{% block content %}
<div class="school-info">
    <table>
        <tr>
            <th>School Name:</th>
            <td>{{ school.name }}</td>
            <th>Term Period:</th>
            <td>{{ term.name }} {{ term.academic_year }}</td>
        </tr>
        <tr>
            <th>Registration No:</th>
            <td>{{ school.registration_number }}</td>
            <th>Generated Date:</th>
            <td>{{ current_date }}</td>
        </tr>
    </table>
</div>

<div class="report-summary">
    <h3>Collection Summary</h3>
    <table>
        <tr>
            <th>Total Expected</th>
            <td>KES {{ total_expected|default:0|floatformat:2 }}</td>
        </tr>
        <tr>
            <th>Total Collections</th>
            <td>KES {{ payments.total_amount|default:0|floatformat:2 }}</td>
        </tr>
        <tr>
            <th>Collection Rate</th>
            <td>{{ collection_rate|default:0|floatformat:1 }}%</td>
        </tr>
        <tr>
            <th>Number of Payments</th>
            <td>{{ payments|length }}</td>
        </tr>
    </table>
</div>

<div class="payment-details">
    <h3>Payment Details</h3>
    <table>
        <thead>
            <tr>
                <th>Date</th>
                <th>Student</th>
                <th>Class</th>
                <th>Fee Type</th>
                <th>Amount</th>
                <th>Method</th>
                <th>Reference</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            {% for payment in payments %}
            <tr>
                <td>{{ payment.payment_date|date:"d/m/Y" }}</td>
                <td>{{ payment.student.get_full_name }}</td>
                <td>{{ payment.student.class_name }}</td>
                <td>{{ payment.fee_type.name }}</td>
                <td>KES {{ payment.amount_paid|floatformat:2 }}</td>
                <td>{{ payment.get_payment_method_display }}</td>
                <td>{{ payment.reference_number }}</td>
                <td>{{ payment.verified|yesno:"Verified,Pending" }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="text-center">No payments recorded for this period.</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr>
                <th colspan="4">Total Collections</th>
                <th>KES {{ payments.total_amount|default:0|floatformat:2 }}</th>
                <th colspan="3"></th>
            </tr>
        </tfoot>
    </table>
</div>

<div class="payment-summary">
    <h3>Payment Method Summary</h3>
    <table>
        <thead>
            <tr>
                <th>Payment Method</th>
                <th>Number of Transactions</th>
                <th>Total Amount</th>
                <th>Percentage</th>
            </tr>
        </thead>
        <tbody>
            {% for method in payment_methods %}
            <tr>
                <td>{{ method.name }}</td>
                <td>{{ method.count }}</td>
                <td>KES {{ method.total|floatformat:2 }}</td>
                <td>{{ method.percentage|floatformat:1 }}%</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
<hr>
<div class="notes">
    <p><strong>Notes:</strong></p>
    <ul>
        <li>All amounts are in Kenya Shillings (KES)</li>
        <li>Verified payments have been confirmed by the finance office</li>
        <li>Collection rate is calculated as (Total Collections / Total Expected) × 100</li>
    </ul>
</div>
<hr>

<div class="signature-section">
    <div class="signature-box">
        <div class="signature-line">
            <hr style="width: 200px; border-top: 1px solid #000;">
            <p>Finance Officer's Signature</p>
        </div>
        <div class="officer-details">
            <p><strong>Name:</strong> {{ finance_officer.get_full_name }}</p>
            <p><strong>Position:</strong> {{ finance_officer.position|default:"Finance Officer" }}</p>
            <p><strong>Date:</strong> {{ current_date }}</p>
        </div>
    </div>
    <div class="signature-box">
        <div class="signature-line">
            <hr style="width: 200px; border-top: 1px solid #000;">
            <p>Principal's Signature</p>
        </div>
        <div class="officer-details">
            <p><strong>Name:</strong> _____________________</p>
            <p><strong>Date:</strong> _____________________</p>
        </div>
        <!-- We can have the stamp here. -->
         <br>
        <div class="stamp">
            <img src="{{ stamp_url }}" alt="Official Stamp" height="100">
        </div>
        

    </div>
</div>

<style>
    .signature-section {
        margin-top: 20px;
        display: flex;
        justify-content: space-between;
        padding: 10px 50px;
    }
    .signature-box {
        text-align: center;
    }
    .signature-line {
        margin-bottom: 10px;
    }
    .signature-line hr {
        margin: 0 auto;
    }
    .signature-line p {
        margin: 5px 0;
        font-weight: bold;
    }
    .officer-details {
        text-align: left;
    }
    .officer-details p {
        margin: 5px 0;
    }
</style>
{% endblock %}
