from decimal import Decimal
from django.utils import timezone
from .models import StudentTransport
from .transport_fees import TransportFee, TransportFeeDiscount

def calculate_transport_fee(student, term=None):
    """
    Calculate transport fee for a student based on their transport usage pattern.
    
    Args:
        student: The student object
        term: Optional term object to calculate fees for
        
    Returns:
        dict: A dictionary containing fee details or None if student doesn't use transport
    """
    # Check if student uses school transport
    try:
        transport = StudentTransport.objects.get(
            student=student,
            is_active=True
        )
    except StudentTransport.DoesNotExist:
        return None
    
    # Check if transport fee should be applied
    if not transport.apply_transport_fee:
        return None
    
    # Get the transport fee for this route and term
    transport_fee = None
    if term:
        transport_fee = TransportFee.objects.filter(
            route=transport.route,
            fee_structure__term=term,
            is_active=True
        ).first()
    
    if not transport_fee:
        # If no term-specific fee found, get the most recent active fee
        transport_fee = TransportFee.objects.filter(
            route=transport.route,
            is_active=True
        ).order_by('-created_at').first()
    
    if not transport_fee:
        # If no transport fee defined, use the fee amount from student transport
        base_fee = transport.fee_amount
    else:
        base_fee = transport_fee.amount
    
    # Calculate pro-rated fee based on usage type
    final_fee = base_fee
    
    if transport.usage_type == 'MORNING_ONLY' or transport.usage_type == 'AFTERNOON_ONLY':
        # Half price for one-way only
        final_fee = base_fee * Decimal('0.5')
    elif transport.usage_type == 'PARTIAL_DAYS':
        # Pro-rate based on days per week
        days_ratio = Decimal(transport.days_per_week) / Decimal('5')
        final_fee = base_fee * days_ratio
    
    # Apply any applicable discounts
    discount_amount = Decimal('0')
    
    if transport_fee:
        today = timezone.now().date()
        active_discounts = TransportFeeDiscount.objects.filter(
            transport_fee=transport_fee,
            is_active=True,
            start_date__lte=today,
            end_date__gte=today
        )
        
        for discount in active_discounts:
            if discount.discount_type == 'FIXED':
                discount_amount += min(discount.amount, final_fee)
            else:  # PERCENTAGE
                discount_amount += (discount.percentage / Decimal('100')) * final_fee
    
    # Ensure discount doesn't exceed the fee amount
    discount_amount = min(discount_amount, final_fee)
    discounted_fee = final_fee - discount_amount
    
    return {
        'type': 'transport',
        'name': f"Transport - {transport.route.name}",
        'description': f"{transport.usage_type.replace('_', ' ').title()} transport on {transport.route.name}",
        'base_amount': float(base_fee),
        'discount': float(discount_amount),
        'final_amount': float(discounted_fee),
        'mandatory': False,
        'frequency': transport.payment_frequency
    }

def should_include_transport_fee(student, school_branch):
    """
    Determine if transport fee should be included for a student
    
    Args:
        student: The student object
        school_branch: The school branch
        
    Returns:
        bool: True if transport fee should be included, False otherwise
    """
    # Check if fleet module is enabled for this school
    from settings_app.models import SystemConfiguration
    try:
        config = SystemConfiguration.objects.get(school_branch=school_branch)
        if not config.enable_fleet_module:
            return False
    except SystemConfiguration.DoesNotExist:
        return False
    
    # Check if student uses school transport
    try:
        transport = StudentTransport.objects.get(
            student=student,
            is_active=True
        )
        return transport.apply_transport_fee
    except StudentTransport.DoesNotExist:
        return False
