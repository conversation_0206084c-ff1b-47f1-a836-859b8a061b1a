from django.core.management.base import BaseCommand
from django.db import transaction
from academics.models import ClassRoom, Stream, AcademicYear
from academics.curriculum_models import EducationLevel, CurriculumSystem
from schools.models import School

class Command(BaseCommand):
    help = 'Seeds test classes with education levels for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--school_id',
            type=int,
            help='School ID to create test classes for',
        )

    @transaction.atomic
    def handle(self, *args, **options):
        school_id = options.get('school_id')

        if school_id:
            schools = School.objects.filter(id=school_id)
            if not schools.exists():
                self.stdout.write(self.style.ERROR(f'School with ID {school_id} not found'))
                return
        else:
            schools = School.objects.all()
            if not schools.exists():
                self.stdout.write(self.style.ERROR('No schools found in the database'))
                return

        # Get all curriculum systems
        curriculum_systems = CurriculumSystem.objects.all()
        if not curriculum_systems.exists():
            self.stdout.write(self.style.ERROR('No curriculum systems found. Run seed_curriculum_systems first.'))
            return

        # Process each school
        for school in schools:
            self.stdout.write(self.style.SUCCESS(f'Creating test classes for school: {school.name}'))

            # Get or create academic year
            try:
                academic_year = AcademicYear.objects.get(year='2023-2024')
            except AcademicYear.DoesNotExist:
                academic_year = AcademicYear.objects.create(
                    year=f'2023-2024-{school.id}',  # Make it unique per school
                    start_date='2023-01-01',
                    end_date='2024-12-31',
                    school_name=school
                )

            # We don't need school branch for this test data

            # Get or create streams
            stream_a, created = Stream.objects.get_or_create(
                name='Stream A',
                code='SA',
                school=school
            )

            stream_b, created = Stream.objects.get_or_create(
                name='Stream B',
                code='SB',
                school=school
            )

            # Process each curriculum system
            for curriculum in curriculum_systems:
                self.stdout.write(self.style.SUCCESS(f'Processing curriculum: {curriculum.name}'))

                # Get education levels for this curriculum
                levels = EducationLevel.objects.filter(curriculum_system=curriculum).order_by('sequence')
                if not levels.exists():
                    self.stdout.write(self.style.WARNING(f'No education levels found for {curriculum.name}'))
                    continue

                # Create classes for each education level
                for level in levels:
                    # Create class for Stream A
                    class_a, created = ClassRoom.objects.get_or_create(
                        name=f'{level.name} A',
                        grade_level=level.sequence,
                        academic_year=academic_year,
                        stream=stream_a,
                        education_level=level,
                        school=school
                    )

                    action = 'Created' if created else 'Already exists'
                    self.stdout.write(
                        self.style.SUCCESS(f'{action}: {class_a.name} ({level.curriculum_system.code})')
                    )

                    # Create class for Stream B
                    class_b, created = ClassRoom.objects.get_or_create(
                        name=f'{level.name} B',
                        grade_level=level.sequence,
                        academic_year=academic_year,
                        stream=stream_b,
                        education_level=level,
                        school=school
                    )

                    action = 'Created' if created else 'Already exists'
                    self.stdout.write(
                        self.style.SUCCESS(f'{action}: {class_b.name} ({level.curriculum_system.code})')
                    )

        self.stdout.write(self.style.SUCCESS('Successfully created test classes'))
