# Generated by Django 5.2.1 on 2025-06-01 13:25

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Announcement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('content', models.TextField()),
                ('announcement_type', models.Char<PERSON>ield(choices=[('GENERAL', 'General'), ('ACADEMIC', 'Academic'), ('EVENT', 'Event'), ('EMERGENCY', 'Emergency'), ('HOLIDAY', 'Holiday')], default='GENERAL', max_length=20)),
                ('priority', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('URGENT', 'Urgent')], default='MEDIUM', max_length=10)),
                ('target_audience', models.CharField(choices=[('ALL', 'All'), ('STAFF', 'Staff'), ('TEACHERS', 'Teachers'), ('STUDENTS', 'Students'), ('PARENTS', 'Parents'), ('SPECIFIC_CLASS', 'Specific Class')], default='ALL', max_length=20)),
                ('publish_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('expiry_date', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('attachment', models.FileField(blank=True, null=True, upload_to='announcements/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-publish_date'],
            },
        ),
        migrations.CreateModel(
            name='Email',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(max_length=255)),
                ('content', models.TextField()),
                ('recipients', models.TextField(help_text='Comma-separated list of email addresses')),
                ('cc', models.TextField(blank=True, help_text='Comma-separated list of CC email addresses', null=True)),
                ('bcc', models.TextField(blank=True, help_text='Comma-separated list of BCC email addresses', null=True)),
                ('attachments', models.FileField(blank=True, null=True, upload_to='email_attachments/')),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('QUEUED', 'Queued'), ('SENT', 'Sent'), ('FAILED', 'Failed')], default='DRAFT', max_length=10)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Event',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField()),
                ('event_type', models.CharField(choices=[('ACADEMIC', 'Academic'), ('SPORTS', 'Sports'), ('CULTURAL', 'Cultural'), ('MEETING', 'Meeting'), ('HOLIDAY', 'Holiday'), ('OTHER', 'Other')], default='OTHER', max_length=20)),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField()),
                ('location', models.CharField(blank=True, max_length=255, null=True)),
                ('is_all_day', models.BooleanField(default=False)),
                ('is_recurring', models.BooleanField(default=False)),
                ('recurrence_pattern', models.CharField(blank=True, help_text="e.g., 'RRULE:FREQ=WEEKLY;BYDAY=MO,WE,FR'", max_length=255, null=True)),
                ('is_public', models.BooleanField(default=True)),
                ('attachment', models.FileField(blank=True, null=True, upload_to='events/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['start_date'],
            },
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(blank=True, max_length=255, null=True)),
                ('content', models.TextField()),
                ('attachment', models.FileField(blank=True, null=True, upload_to='messages/')),
                ('status', models.CharField(choices=[('SENT', 'Sent'), ('DELIVERED', 'Delivered'), ('READ', 'Read'), ('FAILED', 'Failed')], default='SENT', max_length=10)),
                ('is_read', models.BooleanField(default=False)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Newsletter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('content', models.TextField()),
                ('issue_number', models.PositiveIntegerField(blank=True, null=True)),
                ('publish_date', models.DateField(default=django.utils.timezone.now)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('PUBLISHED', 'Published'), ('ARCHIVED', 'Archived')], default='DRAFT', max_length=10)),
                ('cover_image', models.ImageField(blank=True, null=True, upload_to='newsletter_covers/')),
                ('attachment', models.FileField(blank=True, null=True, upload_to='newsletters/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-publish_date'],
            },
        ),
        migrations.CreateModel(
            name='Notice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='ParentTeacherMeeting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('student_name', models.CharField(max_length=255)),
                ('scheduled_date', models.DateTimeField()),
                ('duration_minutes', models.PositiveIntegerField(default=30)),
                ('location', models.CharField(blank=True, max_length=255, null=True)),
                ('is_virtual', models.BooleanField(default=False)),
                ('meeting_link', models.URLField(blank=True, null=True)),
                ('status', models.CharField(choices=[('SCHEDULED', 'Scheduled'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled'), ('RESCHEDULED', 'Rescheduled')], default='SCHEDULED', max_length=15)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-scheduled_date'],
            },
        ),
        migrations.CreateModel(
            name='SMS',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField()),
                ('recipients', models.TextField(help_text='Comma-separated list of phone numbers')),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('QUEUED', 'Queued'), ('SENT', 'Sent'), ('FAILED', 'Failed')], default='DRAFT', max_length=10)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'SMS',
                'ordering': ['-created_at'],
            },
        ),
    ]
