from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from .views import (
    AnnouncementViewSet, MessageViewSet, EmailViewSet,
    SMSViewSet, NewsletterViewSet, EventViewSet,
    ParentTeacherMeetingViewSet
)

router = DefaultRouter()
router.register('announcements', AnnouncementViewSet, basename='announcement')
router.register('messages', MessageViewSet, basename='message')
router.register('emails', EmailViewSet, basename='email')
router.register('sms', SMSViewSet, basename='sms')
router.register('newsletters', NewsletterViewSet, basename='newsletter')
router.register('events', EventViewSet, basename='event')
router.register('parent-teacher-meetings', ParentTeacherMeetingViewSet, basename='parent-teacher-meeting')

urlpatterns = [
    path('', include(router.urls)),
]
