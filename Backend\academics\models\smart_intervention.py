from django.db import models
from django.conf import settings

class SmartIntervention(models.Model):
    TRIGGER_TYPES = [
        ('PERFORMANCE', 'Performance'),
        ('ATTENDANCE', 'Attendance'),
        ('BEHAVIOR', 'Behavior'),
        ('OTHER', 'Other'),
    ]
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE, related_name='interventions')
    student = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='interventions')
    trigger_type = models.Char<PERSON>ield(max_length=20, choices=TRIGGER_TYPES)
    risk_level = models.Char<PERSON>ield(max_length=20)
    automated_suggestions = models.JSONField(default=list, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'academics'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.student} - {self.subject} ({self.trigger_type})"

    def calculate_effectiveness(self):
        # Placeholder for actual effectiveness calculation
        return 0 