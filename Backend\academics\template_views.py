from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db import transaction, models

from .academic_templates import AcademicYearTemplate, TermTemplate
from .models import AcademicYear, Term
from .template_serializers import AcademicYearTemplateSerializer, TermTemplateSerializer, ApplyTemplateSerializer
from schools.models import SchoolBranch

class AcademicYearTemplateViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing academic year templates
    """
    queryset = AcademicYearTemplate.objects.all()
    serializer_class = AcademicYearTemplateSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """
        Filter templates by school if user is not superuser
        Include global templates for all users
        """
        queryset = AcademicYearTemplate.objects.all()

        # Superusers can see all templates
        if self.request.user.is_superuser:
            return queryset

        # Regular users can see templates from their school and global templates
        if hasattr(self.request.user, 'school') and self.request.user.school:
            return queryset.filter(
                # Templates from user's school OR global templates OR templates with no school
                models.Q(school=self.request.user.school) | models.Q(is_global=True) | models.Q(school__isnull=True)
            )

        # Users without a school can only see global templates or templates with no school
        return queryset.filter(models.Q(is_global=True) | models.Q(school__isnull=True))

    @action(detail=True, methods=['post'])
    def apply_to_branch(self, request, pk=None):
        """
        Apply this template to a specific branch
        """
        template = self.get_object()
        serializer = ApplyTemplateSerializer(data=request.data)

        if serializer.is_valid():
            branch_id = serializer.validated_data['school_branch_id']
            customize = serializer.validated_data.get('customize', False)

            try:
                branch = SchoolBranch.objects.get(pk=branch_id)

                # Check if branch belongs to template's school or if template is global or has no school
                if not template.is_global and template.school and branch.school.id != template.school.id:
                    # Only allow cross-school application for superusers
                    if not request.user.is_superuser:
                        return Response(
                            {"error": "This template can only be applied to branches in its own school. Only global templates can be applied across schools."},
                            status=status.HTTP_403_FORBIDDEN
                        )

                # Apply the template
                with transaction.atomic():
                    # Create academic year from template
                    # If template has no school, use the branch's school
                    school_to_use = template.school if template.school else branch.school

                    academic_year = AcademicYear.objects.create(
                        school_name=school_to_use,
                        school_branch=branch,
                        year=template.year,
                        start_date=template.start_date,
                        end_date=template.end_date,
                        is_current=False,  # Default to not current
                        is_archived=False,
                        template=template,
                        is_customized=customize
                    )

                    # Create terms from template
                    for term_template in template.term_templates.all():
                        Term.objects.create(
                            name=term_template.name,
                            academic_year=academic_year,
                            school=school_to_use,  # Use the same school as the academic year
                            school_branch=branch,
                            start_date=term_template.start_date,
                            end_date=term_template.end_date,
                            is_current=False,
                            is_archived=False,
                            template=term_template,
                            is_customized=customize
                        )

                return Response(
                    {"success": f"Template applied to {branch.name} successfully"},
                    status=status.HTTP_201_CREATED
                )

            except SchoolBranch.DoesNotExist:
                return Response(
                    {"error": "School branch not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def by_school(self, request):
        """
        Get templates for a specific school
        """
        school_id = request.query_params.get('school_id')
        if not school_id:
            return Response(
                {"error": "school_id parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        templates = AcademicYearTemplate.objects.filter(school_id=school_id)
        serializer = self.get_serializer(templates, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def make_global(self, request, pk=None):
        """
        Make a template global (only for superusers)
        """
        # Check if user is a superuser
        if not request.user.is_superuser:
            return Response(
                {"error": "Only system administrators can make templates global"},
                status=status.HTTP_403_FORBIDDEN
            )

        template = self.get_object()
        template.is_global = True
        template.save()

        serializer = self.get_serializer(template)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def make_local(self, request, pk=None):
        """
        Make a global template local again (only for superusers)
        """
        # Check if user is a superuser
        if not request.user.is_superuser:
            return Response(
                {"error": "Only system administrators can change template visibility"},
                status=status.HTTP_403_FORBIDDEN
            )

        template = self.get_object()
        template.is_global = False
        template.save()

        serializer = self.get_serializer(template)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def global_templates(self, request):
        """
        Get all global templates
        """
        templates = AcademicYearTemplate.objects.filter(is_global=True)
        serializer = self.get_serializer(templates, many=True)
        return Response(serializer.data)

class TermTemplateViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing term templates
    """
    queryset = TermTemplate.objects.all()
    serializer_class = TermTemplateSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """
        Filter templates by academic year template or school
        """
        queryset = TermTemplate.objects.all()

        academic_year_template_id = self.request.query_params.get('academic_year_template_id')
        if academic_year_template_id:
            queryset = queryset.filter(academic_year_template_id=academic_year_template_id)

        school_id = self.request.query_params.get('school_id')
        if school_id:
            queryset = queryset.filter(academic_year_template__school_id=school_id)

        return queryset
