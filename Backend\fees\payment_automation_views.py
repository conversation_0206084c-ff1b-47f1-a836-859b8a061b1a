"""
Payment Automation Views
Handles automated payment processing and configuration
"""

from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db import transaction
from django.db.models import Q, Count, Sum
from decimal import Decimal
import logging

from .school_billing_models import (
    SchoolPayment, AutomatedPaymentSettings, PaymentAuditLog,
    PaymentVerificationRule, PaymentReceipt, PaymentNotification
)
from .automated_payment_processor import payment_verification_engine
from .exceptions import BillingError
from schools.models import School
from .serializers import AutomatedPaymentSettingsSerializer

logger = logging.getLogger(__name__)


class PaymentAutomationViewSet(viewsets.ViewSet):
    """
    ViewSet for managing payment automation
    """
    permission_classes = [permissions.IsAdminUser]

    @action(detail=False, methods=['post'])
    def process_pending_payments(self, request):
        """
        Process all pending payments for automatic verification
        """
        try:
            # Get all pending payments
            pending_payments = SchoolPayment.objects.filter(
                status='PENDING'
            ).select_related('school', 'invoice')

            results = {
                'total_processed': 0,
                'auto_verified': 0,
                'flagged_for_review': 0,
                'failed': 0,
                'errors': []
            }

            for payment in pending_payments:
                try:
                    success = payment_verification_engine.process_payment_verification(
                        payment.id, 
                        verification_source='BULK_AUTO'
                    )
                    
                    results['total_processed'] += 1
                    
                    if success:
                        results['auto_verified'] += 1
                    else:
                        # Check if it was flagged for review
                        recent_audit = PaymentAuditLog.objects.filter(
                            payment=payment,
                            action='FLAGGED_FOR_REVIEW'
                        ).first()
                        
                        if recent_audit:
                            results['flagged_for_review'] += 1
                        else:
                            results['failed'] += 1
                
                except Exception as e:
                    results['failed'] += 1
                    results['errors'].append(f"Payment {payment.payment_reference}: {str(e)}")
                    logger.error(f"Error processing payment {payment.id}: {e}")

            return Response({
                'message': f'Processed {results["total_processed"]} payments',
                'results': results
            })

        except Exception as e:
            logger.error(f"Error in bulk payment processing: {e}")
            return Response({
                'error': 'Failed to process pending payments'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def verify_payment(self, request):
        """
        Manually trigger verification for a specific payment
        """
        try:
            payment_id = request.data.get('payment_id')
            if not payment_id:
                return Response({
                    'error': 'Payment ID is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            success = payment_verification_engine.process_payment_verification(
                payment_id, 
                verification_source='MANUAL_TRIGGER'
            )

            if success:
                return Response({
                    'message': 'Payment verified successfully',
                    'payment_id': payment_id
                })
            else:
                return Response({
                    'message': 'Payment verification failed or flagged for review',
                    'payment_id': payment_id
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error verifying payment: {e}")
            return Response({
                'error': 'Failed to verify payment'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def automation_stats(self, request):
        """
        Get automation statistics
        """
        try:
            # Get date range from query params
            days = int(request.query_params.get('days', 30))
            start_date = timezone.now() - timezone.timedelta(days=days)

            # Payment statistics
            total_payments = SchoolPayment.objects.filter(
                created_at__gte=start_date
            ).count()

            auto_verified = PaymentAuditLog.objects.filter(
                created_at__gte=start_date,
                action='AUTO_VERIFIED'
            ).count()

            manual_verified = PaymentAuditLog.objects.filter(
                created_at__gte=start_date,
                action='MANUAL_VERIFIED'
            ).count()

            flagged_for_review = PaymentAuditLog.objects.filter(
                created_at__gte=start_date,
                action='FLAGGED_FOR_REVIEW'
            ).count()

            verification_failed = PaymentAuditLog.objects.filter(
                created_at__gte=start_date,
                action='VERIFICATION_FAILED'
            ).count()

            # Automation rate
            automation_rate = (auto_verified / total_payments * 100) if total_payments > 0 else 0

            # Schools with automation enabled
            schools_with_automation = AutomatedPaymentSettings.objects.filter(
                auto_verification_enabled=True
            ).count()

            total_schools = School.objects.count()

            return Response({
                'period_days': days,
                'total_payments': total_payments,
                'auto_verified': auto_verified,
                'manual_verified': manual_verified,
                'flagged_for_review': flagged_for_review,
                'verification_failed': verification_failed,
                'automation_rate': round(automation_rate, 2),
                'schools_with_automation': schools_with_automation,
                'total_schools': total_schools,
                'automation_adoption_rate': round((schools_with_automation / total_schools * 100) if total_schools > 0 else 0, 2)
            })

        except Exception as e:
            logger.error(f"Error getting automation stats: {e}")
            return Response({
                'error': 'Failed to get automation statistics'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def flagged_payments(self, request):
        """
        Get payments flagged for manual review
        """
        try:
            # Get payments flagged for review
            flagged_audit_logs = PaymentAuditLog.objects.filter(
                action='FLAGGED_FOR_REVIEW'
            ).select_related('payment', 'payment__school').order_by('-created_at')

            flagged_payments = []
            for audit_log in flagged_audit_logs:
                payment = audit_log.payment
                
                # Skip if payment is already verified
                if payment.status == 'COMPLETED':
                    continue

                flagged_payments.append({
                    'payment_id': payment.id,
                    'payment_reference': payment.payment_reference,
                    'school_name': payment.school.name,
                    'amount': float(payment.amount),
                    'payment_method': payment.payment_method,
                    'payment_date': payment.payment_date,
                    'flagged_at': audit_log.created_at,
                    'reason': audit_log.reason,
                    'fraud_score': audit_log.details.get('fraud_score', 0),
                    'external_reference': payment.external_reference,
                })

            return Response({
                'flagged_payments': flagged_payments,
                'count': len(flagged_payments)
            })

        except Exception as e:
            logger.error(f"Error getting flagged payments: {e}")
            return Response({
                'error': 'Failed to get flagged payments'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def approve_flagged_payment(self, request):
        """
        Manually approve a payment that was flagged for review
        """
        try:
            payment_id = request.data.get('payment_id')
            approval_reason = request.data.get('reason', 'Manual approval by admin')

            if not payment_id:
                return Response({
                    'error': 'Payment ID is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            payment = get_object_or_404(SchoolPayment, id=payment_id)

            if payment.status == 'COMPLETED':
                return Response({
                    'error': 'Payment is already verified'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Manually verify the payment
            with transaction.atomic():
                payment.verify_payment(request.user)

                # Log the manual approval
                PaymentAuditLog.objects.create(
                    payment=payment,
                    action='MANUAL_VERIFIED',
                    reason=approval_reason,
                    automated=False,
                    user=request.user
                )

            return Response({
                'message': f'Payment {payment.payment_reference} approved successfully'
            })

        except Exception as e:
            logger.error(f"Error approving flagged payment: {e}")
            return Response({
                'error': 'Failed to approve payment'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def reject_flagged_payment(self, request):
        """
        Reject a payment that was flagged for review
        """
        try:
            payment_id = request.data.get('payment_id')
            rejection_reason = request.data.get('reason', 'Rejected by admin')

            if not payment_id:
                return Response({
                    'error': 'Payment ID is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            payment = get_object_or_404(SchoolPayment, id=payment_id)

            if payment.status == 'COMPLETED':
                return Response({
                    'error': 'Payment is already verified'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Mark payment as failed
            with transaction.atomic():
                payment.status = 'FAILED'
                payment.save()

                # Log the rejection
                PaymentAuditLog.objects.create(
                    payment=payment,
                    action='VERIFICATION_FAILED',
                    reason=rejection_reason,
                    automated=False,
                    user=request.user
                )

            return Response({
                'message': f'Payment {payment.payment_reference} rejected'
            })

        except Exception as e:
            logger.error(f"Error rejecting flagged payment: {e}")
            return Response({
                'error': 'Failed to reject payment'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AutomationSettingsViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing automation settings per school
    """
    queryset = AutomatedPaymentSettings.objects.all()
    serializer_class = AutomatedPaymentSettingsSerializer
    permission_classes = [permissions.IsAdminUser]

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by school if specified
        school_id = self.request.query_params.get('school_id')
        if school_id:
            queryset = queryset.filter(school_id=school_id)
        
        return queryset.select_related('school')

    @action(detail=False, methods=['post'])
    def bulk_enable_automation(self, request):
        """
        Enable automation for multiple schools
        """
        try:
            school_ids = request.data.get('school_ids', [])
            settings_data = request.data.get('settings', {})

            if not school_ids:
                return Response({
                    'error': 'School IDs are required'
                }, status=status.HTTP_400_BAD_REQUEST)

            updated_count = 0
            created_count = 0

            for school_id in school_ids:
                try:
                    school = School.objects.get(id=school_id)
                    
                    settings_obj, created = AutomatedPaymentSettings.objects.get_or_create(
                        school=school,
                        defaults={
                            'auto_verification_enabled': True,
                            'max_auto_verification_amount': Decimal(settings_data.get('max_amount', '50000.00')),
                            'allowed_auto_verification_methods': settings_data.get('allowed_methods', ['MOBILE_MONEY', 'BANK_TRANSFER']),
                            'fraud_threshold': settings_data.get('fraud_threshold', 70),
                        }
                    )

                    if created:
                        created_count += 1
                    else:
                        # Update existing settings
                        settings_obj.auto_verification_enabled = True
                        if 'max_amount' in settings_data:
                            settings_obj.max_auto_verification_amount = Decimal(settings_data['max_amount'])
                        if 'allowed_methods' in settings_data:
                            settings_obj.allowed_auto_verification_methods = settings_data['allowed_methods']
                        if 'fraud_threshold' in settings_data:
                            settings_obj.fraud_threshold = settings_data['fraud_threshold']
                        settings_obj.save()
                        updated_count += 1

                except School.DoesNotExist:
                    continue

            return Response({
                'message': f'Automation enabled for {len(school_ids)} schools',
                'created': created_count,
                'updated': updated_count
            })

        except Exception as e:
            logger.error(f"Error enabling bulk automation: {e}")
            return Response({
                'error': 'Failed to enable automation'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
