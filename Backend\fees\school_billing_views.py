"""
Super Admin School Billing Views
Manages billing for all schools in the system
"""

from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db import transaction
from django.db.models import Sum, Count, Q
from decimal import Decimal
import logging

from .school_billing_models import (
    SchoolBillingAccount, SchoolSubscriptionPlan, SchoolInvoice,
    SchoolInvoiceLineItem, SchoolPayment
)
from .school_billing_serializers import (
    SchoolBillingAccountSerializer, SchoolSubscriptionPlanSerializer,
    SchoolInvoiceSerializer, SchoolPaymentSerializer,
    BillingDashboardSerializer, SchoolBillingOverviewSerializer
)
from schools.models import School
from settings_app.license_models import LicenseSubscription
from .exceptions import BillingError, PaymentVerificationException

logger = logging.getLogger(__name__)


class SuperAdminPermission(permissions.BasePermission):
    """
    Custom permission for super admin only
    """
    def has_permission(self, request, view):
        return request.user and request.user.is_superuser


class SchoolBillingAccountViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing school billing accounts (Super Admin only)
    """
    queryset = SchoolBillingAccount.objects.all()
    serializer_class = SchoolBillingAccountSerializer
    permission_classes = [SuperAdminPermission]

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by billing status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(billing_status=status_filter)
        
        # Filter by overdue accounts
        overdue = self.request.query_params.get('overdue')
        if overdue == 'true':
            queryset = [account for account in queryset if account.is_overdue()]
        
        return queryset

    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """
        Get billing dashboard statistics
        """
        try:
            total_schools = School.objects.count()
            active_accounts = SchoolBillingAccount.objects.filter(billing_status='ACTIVE').count()
            overdue_accounts = len([acc for acc in SchoolBillingAccount.objects.all() if acc.is_overdue()])
            
            # Revenue statistics
            current_month = timezone.now().replace(day=1)
            monthly_revenue = SchoolPayment.objects.filter(
                payment_date__gte=current_month,
                status='COMPLETED'
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
            
            # Outstanding invoices
            outstanding_amount = SchoolInvoice.objects.filter(
                status__in=['SENT', 'OVERDUE']
            ).aggregate(total=Sum('total_amount'))['total'] or Decimal('0.00')
            
            data = {
                'total_schools': total_schools,
                'active_accounts': active_accounts,
                'overdue_accounts': overdue_accounts,
                'monthly_revenue': float(monthly_revenue),
                'outstanding_amount': float(outstanding_amount),
                'collection_rate': self.calculate_collection_rate()
            }
            
            serializer = BillingDashboardSerializer(data)
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error fetching dashboard stats: {e}")
            return Response({
                'error': 'Failed to fetch dashboard statistics'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def calculate_collection_rate(self):
        """Calculate the collection rate for the current month"""
        current_month = timezone.now().replace(day=1)
        
        total_invoiced = SchoolInvoice.objects.filter(
            invoice_date__gte=current_month
        ).aggregate(total=Sum('total_amount'))['total'] or Decimal('0.00')
        
        total_collected = SchoolPayment.objects.filter(
            payment_date__gte=current_month,
            status='COMPLETED'
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        if total_invoiced > 0:
            return float((total_collected / total_invoiced) * 100)
        return 0.0

    @action(detail=True, methods=['post'])
    def suspend_account(self, request, pk=None):
        """
        Suspend a school's billing account
        """
        try:
            account = self.get_object()
            account.billing_status = 'SUSPENDED'
            account.save()
            
            # Also suspend the school's license
            try:
                license_sub = account.school.license
                license_sub.subscription_status = 'EXPIRED'
                license_sub.save()
            except LicenseSubscription.DoesNotExist:
                pass
            
            return Response({
                'message': f'Account for {account.school.name} has been suspended'
            })
            
        except Exception as e:
            logger.error(f"Error suspending account {pk}: {e}")
            return Response({
                'error': 'Failed to suspend account'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'])
    def reactivate_account(self, request, pk=None):
        """
        Reactivate a suspended school account
        """
        try:
            account = self.get_object()
            account.billing_status = 'ACTIVE'
            account.save()
            
            # Reactivate the school's license if not expired
            try:
                license_sub = account.school.license
                if license_sub.expiry_date >= timezone.now().date():
                    license_sub.subscription_status = 'ACTIVE'
                    license_sub.save()
            except LicenseSubscription.DoesNotExist:
                pass
            
            return Response({
                'message': f'Account for {account.school.name} has been reactivated'
            })
            
        except Exception as e:
            logger.error(f"Error reactivating account {pk}: {e}")
            return Response({
                'error': 'Failed to reactivate account'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SchoolSubscriptionPlanViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing subscription plans (Super Admin only)
    """
    queryset = SchoolSubscriptionPlan.objects.all()
    serializer_class = SchoolSubscriptionPlanSerializer
    permission_classes = [SuperAdminPermission]

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by active plans
        active_only = self.request.query_params.get('active_only')
        if active_only == 'true':
            queryset = queryset.filter(is_active=True)
        
        return queryset


class SchoolInvoiceViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing school invoices (Super Admin only)
    """
    queryset = SchoolInvoice.objects.all()
    serializer_class = SchoolInvoiceSerializer
    permission_classes = [SuperAdminPermission]

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by school
        school_id = self.request.query_params.get('school')
        if school_id:
            queryset = queryset.filter(school_id=school_id)
        
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date:
            queryset = queryset.filter(invoice_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(invoice_date__lte=end_date)
        
        return queryset

    @action(detail=False, methods=['post'])
    def generate_monthly_invoices(self, request):
        """
        Generate monthly invoices for all active schools
        """
        try:
            with transaction.atomic():
                generated_count = 0
                errors = []
                
                # Get all active billing accounts
                active_accounts = SchoolBillingAccount.objects.filter(
                    billing_status='ACTIVE'
                )
                
                for account in active_accounts:
                    try:
                        # Check if invoice already exists for this month
                        current_month = timezone.now().replace(day=1).date()
                        existing_invoice = SchoolInvoice.objects.filter(
                            school=account.school,
                            billing_period_start=current_month
                        ).exists()
                        
                        if not existing_invoice:
                            invoice = self.create_monthly_invoice(account)
                            if invoice:
                                generated_count += 1
                    
                    except Exception as e:
                        errors.append(f"Error generating invoice for {account.school.name}: {str(e)}")
                        logger.error(f"Error generating invoice for school {account.school.id}: {e}")
                
                return Response({
                    'message': f'Generated {generated_count} invoices',
                    'errors': errors
                })
                
        except Exception as e:
            logger.error(f"Error in bulk invoice generation: {e}")
            return Response({
                'error': 'Failed to generate invoices'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def create_monthly_invoice(self, billing_account):
        """
        Create a monthly invoice for a billing account
        """
        try:
            # Get the school's current license to determine the plan
            license_sub = billing_account.school.license
            
            # Find the corresponding subscription plan
            plan = SchoolSubscriptionPlan.objects.filter(
                plan_type=license_sub.package_type.upper(),
                is_active=True
            ).first()
            
            if not plan:
                logger.warning(f"No subscription plan found for {billing_account.school.name}")
                return None
            
            # Calculate billing period
            current_date = timezone.now().date()
            if billing_account.billing_cycle == 'MONTHLY':
                period_start = current_date.replace(day=1)
                next_month = period_start.replace(month=period_start.month + 1) if period_start.month < 12 else period_start.replace(year=period_start.year + 1, month=1)
                period_end = next_month - timezone.timedelta(days=1)
            else:
                # Handle other billing cycles
                period_start = current_date
                period_end = current_date + timezone.timedelta(days=30)
            
            # Create invoice
            invoice = SchoolInvoice.objects.create(
                school=billing_account.school,
                billing_account=billing_account,
                due_date=period_end + timezone.timedelta(days=billing_account.payment_grace_period_days),
                billing_period_start=period_start,
                billing_period_end=period_end,
                subtotal=plan.get_price_for_cycle(billing_account.billing_cycle),
                created_by=None  # System generated
            )
            
            # Create line item
            SchoolInvoiceLineItem.objects.create(
                invoice=invoice,
                subscription_plan=plan,
                description=f"{plan.name} - {billing_account.get_billing_cycle_display()}",
                quantity=1,
                unit_price=plan.get_price_for_cycle(billing_account.billing_cycle),
                billing_period_start=period_start,
                billing_period_end=period_end
            )
            
            # Calculate totals
            invoice.calculate_totals()
            
            # Update billing account
            billing_account.last_billing_date = timezone.now()
            billing_account.next_billing_date = invoice.due_date
            billing_account.save()
            
            return invoice
            
        except Exception as e:
            logger.error(f"Error creating invoice for {billing_account.school.name}: {e}")
            return None

    @action(detail=True, methods=['post'])
    def send_invoice(self, request, pk=None):
        """
        Send an invoice to the school
        """
        try:
            invoice = self.get_object()
            
            # Update invoice status
            invoice.status = 'SENT'
            invoice.sent_at = timezone.now()
            invoice.save()
            
            # Here you would integrate with email service to send the invoice
            # For now, we'll just return success
            
            return Response({
                'message': f'Invoice {invoice.invoice_number} sent to {invoice.school.name}'
            })
            
        except Exception as e:
            logger.error(f"Error sending invoice {pk}: {e}")
            return Response({
                'error': 'Failed to send invoice'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SchoolPaymentViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing school payments (Super Admin only)
    """
    queryset = SchoolPayment.objects.all()
    serializer_class = SchoolPaymentSerializer
    permission_classes = [SuperAdminPermission]

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by school
        school_id = self.request.query_params.get('school')
        if school_id:
            queryset = queryset.filter(school_id=school_id)
        
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset

    @action(detail=True, methods=['post'])
    def verify_payment(self, request, pk=None):
        """
        Verify a payment and activate/extend license
        """
        try:
            payment = self.get_object()
            
            if payment.status != 'PENDING':
                raise PaymentVerificationException(
                    f"Payment {payment.payment_reference} is not in pending status"
                )
            
            # Verify the payment
            payment.verify_payment(request.user)
            
            return Response({
                'message': f'Payment {payment.payment_reference} verified successfully',
                'license_updated': True
            })
            
        except PaymentVerificationException as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error verifying payment {pk}: {e}")
            return Response({
                'error': 'Failed to verify payment'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def pending_verifications(self, request):
        """
        Get all payments pending verification
        """
        try:
            pending_payments = self.queryset.filter(status='PENDING')
            serializer = self.get_serializer(pending_payments, many=True)
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error fetching pending payments: {e}")
            return Response({
                'error': 'Failed to fetch pending payments'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
