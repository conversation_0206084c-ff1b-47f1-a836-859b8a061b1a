from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import GradingSystem, GradeScale
from .serializers.grading_serializer import GradingSystemSerializer, GradeScaleSerializer

class GradingSystemViewSet(viewsets.ModelViewSet):
    """
    API endpoint for grading systems
    """
    queryset = GradingSystem.objects.all()
    serializer_class = GradingSystemSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = GradingSystem.objects.all()

        # Filter by education level
        education_level = self.request.query_params.get('education_level')
        if education_level:
            queryset = queryset.filter(education_level=education_level)

        # Filter by school
        school = self.request.query_params.get('school')
        if school:
            queryset = queryset.filter(school=school)

        # Filter by school branch
        school_branch = self.request.query_params.get('school_branch')
        if school_branch:
            queryset = queryset.filter(school_branch=school_branch)

        return queryset

    @action(detail=True, methods=['get'])
    def scales(self, request, pk=None):
        """
        Get all grade scales for a grading system
        """
        grading_system = self.get_object()
        scales = grading_system.scales.all().order_by('min_score')
        serializer = GradeScaleSerializer(scales, many=True)
        return Response(serializer.data)

class GradeScaleViewSet(viewsets.ModelViewSet):
    """
    API endpoint for grade scales
    """
    queryset = GradeScale.objects.all()
    serializer_class = GradeScaleSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = GradeScale.objects.all()

        # Filter by grading system
        grading_system = self.request.query_params.get('grading_system')
        if grading_system:
            queryset = queryset.filter(grading_system=grading_system)

        # Order by min_score (descending)
        queryset = queryset.order_by('-min_score')

        return queryset
