from django.urls import path, include
from rest_framework.routers import DefaultRouter

# Import existing API viewsets
from .subjects_api import SubjectViewSet
from .departments_api import DepartmentViewSet
from .classes_api import ClassRoomViewSet
from .streams_api import StreamViewSet
from .curriculum_api import (
    CurriculumSystemViewSet,
    EducationLevelViewSet,
    SchoolCurriculumConfigViewSet,
    BranchCurriculumConfigViewSet,
    CurriculumConfigTemplateViewSet,
    CurriculumComplianceCheckViewSet,
    CurriculumComplianceNotificationViewSet
)
from .academic_years_api import AcademicYearViewSet
from .grading_api import GradingSystemViewSet, GradeScaleViewSet

# Import enhanced API viewsets
try:
    from .api.class_progression_views import (
        ClassProgressionRuleViewSet, StudentProgressionViewSet,
        CareerPathViewSet, StudentCareerGuidanceViewSet, SubjectCombinationViewSet
    )
    from .api.enhanced_exam_views import (
        ExamTypeViewSet, ExamSessionViewSet, EnhancedExamViewSet,
        ExamRegistrationViewSet, EnhancedExamResultViewSet, ExamMalpracticeViewSet
    )
    from .api.live_streaming_views import (
        LiveStreamingPlatformViewSet, LiveSessionViewSet, SessionParticipantViewSet,
        SessionAttendanceViewSet, SessionRecordingViewSet, SessionChatViewSet,
        ResultPublicationViewSet, PublicationAccessViewSet
    )
    ENHANCED_APIS_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Enhanced API views not available: {e}")
    ENHANCED_APIS_AVAILABLE = False

router = DefaultRouter()

# Existing routes
router.register(r'subjects', SubjectViewSet, basename='subject')
router.register(r'departments', DepartmentViewSet, basename='department')
router.register(r'classes', ClassRoomViewSet, basename='class')
router.register(r'streams', StreamViewSet, basename='stream')
router.register(r'curriculum/systems', CurriculumSystemViewSet, basename='curriculum-system')
router.register(r'curriculum/school-config', SchoolCurriculumConfigViewSet, basename='school-curriculum-config')
router.register(r'curriculum/branch-config', BranchCurriculumConfigViewSet, basename='branch-curriculum-config')
router.register(r'curriculum/templates', CurriculumConfigTemplateViewSet, basename='curriculum-template')
router.register(r'curriculum/compliance-checks', CurriculumComplianceCheckViewSet, basename='compliance-check')
router.register(r'curriculum/compliance-notifications', CurriculumComplianceNotificationViewSet, basename='compliance-notification')
router.register(r'education-levels', EducationLevelViewSet, basename='education-level')
router.register(r'academic-years', AcademicYearViewSet, basename='academic-year')
router.register(r'grading-systems', GradingSystemViewSet, basename='grading-system')

# Enhanced API routes (only if available)
if ENHANCED_APIS_AVAILABLE:
    # Class Progression routes
    router.register(r'progression/rules', ClassProgressionRuleViewSet, basename='progression-rule')
    router.register(r'progression/students', StudentProgressionViewSet, basename='student-progression')
    router.register(r'career-paths', CareerPathViewSet, basename='career-path')
    router.register(r'career-guidance', StudentCareerGuidanceViewSet, basename='career-guidance')
    router.register(r'subject-combinations', SubjectCombinationViewSet, basename='subject-combination')

    # Enhanced Exam routes
    router.register(r'exam-types', ExamTypeViewSet, basename='exam-type')
    router.register(r'exam-sessions', ExamSessionViewSet, basename='exam-session')
    router.register(r'enhanced-exams', EnhancedExamViewSet, basename='enhanced-exam')
    router.register(r'exam-registrations', ExamRegistrationViewSet, basename='exam-registration')
    router.register(r'exam-results', EnhancedExamResultViewSet, basename='exam-result')
    router.register(r'exam-malpractice', ExamMalpracticeViewSet, basename='exam-malpractice')

    # Live Streaming routes
    router.register(r'streaming/platforms', LiveStreamingPlatformViewSet, basename='streaming-platform')
    router.register(r'streaming/sessions', LiveSessionViewSet, basename='live-session')
    router.register(r'streaming/participants', SessionParticipantViewSet, basename='session-participant')
    router.register(r'streaming/attendance', SessionAttendanceViewSet, basename='session-attendance')
    router.register(r'streaming/recordings', SessionRecordingViewSet, basename='session-recording')
    router.register(r'streaming/chat', SessionChatViewSet, basename='session-chat')

    # Result Publication routes
    router.register(r'publications', ResultPublicationViewSet, basename='result-publication')
    router.register(r'publication-access', PublicationAccessViewSet, basename='publication-access')

urlpatterns = [
    path('', include(router.urls)),
]
