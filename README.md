# ShuleXcel - School Management System

A comprehensive school management system built with Django REST Framework and React, designed for modern educational institutions.

## 🚀 MVP Ready for Deployment

ShuleXcel MVP is production-ready with core features including user management, school administration, academic structure, and license management.

### ✅ MVP Features

- **Authentication & User Management** - Secure login with role-based access
- **School & Branch Management** - Multi-school and multi-branch support
- **Academic Foundation** - Classes, subjects, students, teachers
- **License Management** - Package-based licensing system
- **Dashboard** - Basic reporting and overview
- **API Documentation** - Comprehensive Swagger documentation

## 🏃‍♂️ Quick Start

### Development Setup

```bash
python setup_dev.py
python start_dev.py
```

### Production Deployment

```bash
# Linux/Mac
./deploy_mvp.sh

# Windows
.\deploy_mvp.ps1
```

### Default Credentials

- **Email**: <EMAIL>
- **Password**: admin123

⚠️ **Change these credentials immediately after first login!**

## 📚 Documentation

- [MVP Deployment Guide](MVP_DEPLOYMENT_GUIDE.md) - Complete deployment instructions
- [API Documentation](Backend/docs/) - Backend API documentation
- [User Documentation](docs/user/) - User guides and training materials

## 🛠️ Technology Stack

### Backend
- **Django 5.2** - Web framework
- **Django REST Framework** - API framework
- **PostgreSQL/SQLite** - Database
- **JWT Authentication** - Secure authentication
- **Docker** - Containerization

### Frontend
- **React 18** - UI framework
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **Redux Toolkit** - State management
- **Axios** - API client

## 🏗️ Architecture

```
ShuleXcelApp/
├── Backend/          # Django REST API
├── Frontend/         # React Application
├── docs/            # Documentation
├── deploy_mvp.sh    # Deployment script (Linux/Mac)
├── deploy_mvp.ps1   # Deployment script (Windows)
└── setup_dev.py     # Development setup
```

## 🔐 Security Features

- JWT-based authentication
- Multi-factor authentication (MFA)
- Role-based access control (RBAC)
- Password strength validation
- Session management
- HTTPS/SSL support
- CORS protection

## 📊 MVP Modules

| Module | Status | Description |
|--------|--------|-------------|
| Core | ✅ Ready | Authentication, users, schools |
| Dashboard | ✅ Ready | Basic reporting and overview |
| Academics | ✅ Ready | Classes, subjects, students |
| Settings | ✅ Ready | School profiles, configuration |
| License | ✅ Ready | Package-based licensing |

## 🚀 Post-MVP Roadmap

- Advanced Analytics & AI Features
- Live Streaming & Video Conferencing
- Advanced Fleet Management
- Complex Inventory Management
- Advanced Communication Features
- Mobile Applications
- Multi-language Support

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

- **Email**: <EMAIL>
- **Documentation**: [User Guides](docs/user/)
- **Issues**: [GitHub Issues](https://github.com/makaulucky/ShuleXcelApp/issues)

## 🎉 Success Stories

ShuleXcel is designed to help schools:
- Streamline administrative processes
- Improve student outcomes
- Enhance parent-school communication
- Reduce manual paperwork
- Make data-driven decisions

---

**Ready to transform your school management?** 🏫✨

Get started with the MVP deployment today!