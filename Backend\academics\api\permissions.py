from rest_framework import permissions

class IsSchoolStaff(permissions.BasePermission):
    """
    Custom permission to only allow school staff to access the view.
    """
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.is_staff

class IsTeacherOrAdmin(permissions.BasePermission):
    """
    Allow access only to teachers or administrators.
    """
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        return request.user.is_staff or hasattr(request.user, 'teacher')

    def has_object_permission(self, request, view, obj):
        if request.user.is_staff:
            return True
        return obj.subject in request.user.teacher.teaching_subjects.all()

class IsStudentOrParent(permissions.BasePermission):
    """
    Allow access only to student or their parent.
    """
    def has_object_permission(self, request, view, obj):
        if not request.user.is_authenticated:
            return False
        return (hasattr(request.user, 'student') and request.user.student == obj) or \
               (hasattr(request.user, 'parent') and obj in request.user.parent.students.all())
