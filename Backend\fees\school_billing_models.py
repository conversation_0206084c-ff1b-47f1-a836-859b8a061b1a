"""
Super Admin School Billing System Models
Integrates with the existing licensing system to manage school subscriptions
"""

from django.db import models
from django.utils import timezone
from django.contrib.auth import get_user_model
from decimal import Decimal
from schools.models import School
from settings_app.license_models import LicenseSubscription
from settings_app.modules import PACKAGE_TIERS
import uuid

User = get_user_model()


class SchoolBillingAccount(models.Model):
    """
    Billing account for each school - manages subscription billing
    """
    BILLING_STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('SUSPENDED', 'Suspended'),
        ('DELINQUENT', 'Delinquent'),
        ('CANCELLED', 'Cancelled'),
    ]

    BILLING_CYCLE_CHOICES = [
        ('MONTHLY', 'Monthly'),
        ('QUARTERLY', 'Quarterly'),
        ('ANNUALLY', 'Annually'),
        ('CUSTOM', 'Custom'),
    ]

    school = models.OneToOneField(School, on_delete=models.CASCADE, related_name='billing_account')
    billing_status = models.CharField(max_length=20, choices=BILLING_STATUS_CHOICES, default='ACTIVE')
    billing_cycle = models.CharField(max_length=20, choices=BILLING_CYCLE_CHOICES, default='ANNUALLY')
    
    # Contact Information
    billing_contact_name = models.CharField(max_length=200)
    billing_contact_email = models.EmailField()
    billing_contact_phone = models.CharField(max_length=20)
    
    # Billing Address
    billing_address = models.TextField()
    billing_city = models.CharField(max_length=100)
    billing_country = models.CharField(max_length=100, default='Kenya')
    billing_postal_code = models.CharField(max_length=20, blank=True)
    
    # Tax Information
    tax_id = models.CharField(max_length=50, blank=True, help_text="VAT/Tax ID Number")
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('16.00'), help_text="Tax rate percentage")
    
    # Payment Settings
    auto_billing_enabled = models.BooleanField(default=False)
    payment_grace_period_days = models.PositiveIntegerField(default=7)
    
    # Account Balance
    current_balance = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    credit_limit = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_billing_date = models.DateTimeField(null=True, blank=True)
    next_billing_date = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Billing Account - {self.school.name}"

    def calculate_next_billing_date(self):
        """Calculate the next billing date based on billing cycle"""
        if not self.last_billing_date:
            base_date = timezone.now()
        else:
            base_date = self.last_billing_date

        if self.billing_cycle == 'MONTHLY':
            return base_date + timezone.timedelta(days=30)
        elif self.billing_cycle == 'QUARTERLY':
            return base_date + timezone.timedelta(days=90)
        elif self.billing_cycle == 'ANNUALLY':
            return base_date + timezone.timedelta(days=365)
        else:
            return base_date + timezone.timedelta(days=30)  # Default to monthly

    def is_overdue(self):
        """Check if the account has overdue payments"""
        return (
            self.current_balance > 0 and 
            self.next_billing_date and 
            timezone.now() > self.next_billing_date + timezone.timedelta(days=self.payment_grace_period_days)
        )

    def suspend_for_non_payment(self):
        """Suspend the account for non-payment"""
        if self.is_overdue() and self.billing_status == 'ACTIVE':
            self.billing_status = 'DELINQUENT'
            self.save()
            
            # Also suspend the school's license
            try:
                license_sub = self.school.license
                if license_sub.subscription_status == 'ACTIVE':
                    license_sub.subscription_status = 'EXPIRED'
                    license_sub.save()
            except LicenseSubscription.DoesNotExist:
                pass


class SchoolSubscriptionPlan(models.Model):
    """
    Subscription plans available for schools
    """
    PLAN_TYPE_CHOICES = [
        ('BASIC', 'Basic'),
        ('STANDARD', 'Standard'),
        ('PREMIUM', 'Premium'),
        ('ENTERPRISE', 'Enterprise'),
        ('CUSTOM', 'Custom'),
    ]

    name = models.CharField(max_length=100)
    plan_type = models.CharField(max_length=20, choices=PLAN_TYPE_CHOICES)
    description = models.TextField()
    
    # Pricing
    monthly_price = models.DecimalField(max_digits=10, decimal_places=2)
    quarterly_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    annual_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    
    # Limits
    max_students = models.PositiveIntegerField()
    max_staff = models.PositiveIntegerField()
    max_branches = models.PositiveIntegerField(default=1)
    storage_limit_gb = models.PositiveIntegerField(default=10)
    
    # Features
    included_modules = models.JSONField(default=list, help_text="List of included module codes")
    features = models.JSONField(default=list, help_text="List of included features")
    
    # Settings
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False)
    sort_order = models.PositiveIntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['sort_order', 'name']

    def __str__(self):
        return f"{self.name} - {self.get_plan_type_display()}"

    def get_price_for_cycle(self, billing_cycle):
        """Get the price for a specific billing cycle"""
        if billing_cycle == 'MONTHLY':
            return self.monthly_price
        elif billing_cycle == 'QUARTERLY':
            return self.quarterly_price or (self.monthly_price * 3)
        elif billing_cycle == 'ANNUALLY':
            return self.annual_price or (self.monthly_price * 12)
        else:
            return self.monthly_price


class SchoolInvoice(models.Model):
    """
    Invoices for school subscriptions
    """
    INVOICE_STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('SENT', 'Sent'),
        ('PAID', 'Paid'),
        ('OVERDUE', 'Overdue'),
        ('CANCELLED', 'Cancelled'),
        ('REFUNDED', 'Refunded'),
    ]

    # Invoice Details
    invoice_number = models.CharField(max_length=50, unique=True)
    school = models.ForeignKey(School, on_delete=models.CASCADE, related_name='invoices')
    billing_account = models.ForeignKey(SchoolBillingAccount, on_delete=models.CASCADE, related_name='invoices')
    
    # Invoice Information
    invoice_date = models.DateField(default=timezone.now)
    due_date = models.DateField()
    status = models.CharField(max_length=20, choices=INVOICE_STATUS_CHOICES, default='DRAFT')
    
    # Amounts
    subtotal = models.DecimalField(max_digits=12, decimal_places=2)
    tax_amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    discount_amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    paid_amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    
    # Billing Period
    billing_period_start = models.DateField()
    billing_period_end = models.DateField()
    
    # Notes
    notes = models.TextField(blank=True)
    internal_notes = models.TextField(blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    sent_at = models.DateTimeField(null=True, blank=True)
    paid_at = models.DateTimeField(null=True, blank=True)
    
    # Generated by
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_invoices')

    class Meta:
        ordering = ['-invoice_date', '-created_at']

    def __str__(self):
        return f"Invoice {self.invoice_number} - {self.school.name}"

    def save(self, *args, **kwargs):
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()
        super().save(*args, **kwargs)

    def generate_invoice_number(self):
        """Generate a unique invoice number"""
        year = timezone.now().year
        month = timezone.now().month
        
        # Get the last invoice for this month
        last_invoice = SchoolInvoice.objects.filter(
            invoice_number__startswith=f"INV-{year}{month:02d}"
        ).order_by('-invoice_number').first()
        
        if last_invoice:
            # Extract the sequence number and increment
            last_seq = int(last_invoice.invoice_number.split('-')[-1])
            new_seq = last_seq + 1
        else:
            new_seq = 1
        
        return f"INV-{year}{month:02d}-{new_seq:04d}"

    def calculate_totals(self):
        """Calculate invoice totals from line items"""
        line_items = self.line_items.all()
        self.subtotal = sum(item.total_amount for item in line_items)
        
        # Calculate tax
        if self.billing_account.tax_rate:
            self.tax_amount = (self.subtotal * self.billing_account.tax_rate) / 100
        
        # Calculate total
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount
        self.save(update_fields=['subtotal', 'tax_amount', 'total_amount'])

    def mark_as_paid(self, payment_amount=None, payment_date=None):
        """Mark invoice as paid"""
        if payment_amount is None:
            payment_amount = self.total_amount
        
        self.paid_amount = payment_amount
        self.status = 'PAID'
        self.paid_at = payment_date or timezone.now()
        self.save()
        
        # Activate or extend the school's license
        self.activate_school_license()

    def activate_school_license(self):
        """Activate or extend the school's license after payment"""
        try:
            license_sub = self.school.license
            
            # If license is expired or cancelled, reactivate it
            if license_sub.subscription_status in ['EXPIRED', 'CANCELLED']:
                license_sub.subscription_status = 'ACTIVE'
                license_sub.start_date = timezone.now().date()
            
            # Extend the expiry date based on billing period
            billing_days = (self.billing_period_end - self.billing_period_start).days
            if license_sub.expiry_date < timezone.now().date():
                # If already expired, start from today
                license_sub.expiry_date = timezone.now().date() + timezone.timedelta(days=billing_days)
            else:
                # If still active, extend from current expiry
                license_sub.expiry_date += timezone.timedelta(days=billing_days)
            
            license_sub.save()
            
        except LicenseSubscription.DoesNotExist:
            # Create a new license if none exists
            # Get the subscription plan from the first line item
            line_item = self.line_items.first()
            if line_item and hasattr(line_item, 'subscription_plan'):
                billing_days = (self.billing_period_end - self.billing_period_start).days
                
                LicenseSubscription.objects.create(
                    school=self.school,
                    package_type=line_item.subscription_plan.plan_type.lower(),
                    subscription_status='ACTIVE',
                    start_date=timezone.now().date(),
                    expiry_date=timezone.now().date() + timezone.timedelta(days=billing_days),
                    max_students=line_item.subscription_plan.max_students,
                    max_staff=line_item.subscription_plan.max_staff,
                    max_branches=line_item.subscription_plan.max_branches,
                    custom_modules=line_item.subscription_plan.included_modules
                )


class SchoolInvoiceLineItem(models.Model):
    """
    Line items for school invoices
    """
    invoice = models.ForeignKey(SchoolInvoice, on_delete=models.CASCADE, related_name='line_items')
    subscription_plan = models.ForeignKey(SchoolSubscriptionPlan, on_delete=models.CASCADE, null=True, blank=True)
    
    description = models.CharField(max_length=200)
    quantity = models.PositiveIntegerField(default=1)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    
    # Additional details
    billing_period_start = models.DateField()
    billing_period_end = models.DateField()
    
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.description} - {self.total_amount}"

    def save(self, *args, **kwargs):
        self.total_amount = self.quantity * self.unit_price
        super().save(*args, **kwargs)


class SchoolPayment(models.Model):
    """
    Payments made by schools for their subscriptions
    """
    PAYMENT_METHOD_CHOICES = [
        ('BANK_TRANSFER', 'Bank Transfer'),
        ('MOBILE_MONEY', 'Mobile Money'),
        ('CREDIT_CARD', 'Credit Card'),
        ('CASH', 'Cash'),
        ('CHEQUE', 'Cheque'),
        ('OTHER', 'Other'),
    ]

    PAYMENT_STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('REFUNDED', 'Refunded'),
    ]

    # Payment Details
    payment_reference = models.CharField(max_length=100, unique=True)
    school = models.ForeignKey(School, on_delete=models.CASCADE, related_name='payments')
    invoice = models.ForeignKey(SchoolInvoice, on_delete=models.CASCADE, related_name='payments', null=True, blank=True)
    
    # Payment Information
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES)
    payment_date = models.DateTimeField(default=timezone.now)
    status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='PENDING')
    
    # External References
    external_reference = models.CharField(max_length=200, blank=True, help_text="External payment gateway reference")
    transaction_id = models.CharField(max_length=200, blank=True)
    
    # Notes
    notes = models.TextField(blank=True)
    
    # Verification
    verified_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='verified_payments')
    verified_at = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Payment {self.payment_reference} - {self.school.name} - {self.amount}"

    def save(self, *args, **kwargs):
        if not self.payment_reference:
            self.payment_reference = self.generate_payment_reference()
        super().save(*args, **kwargs)

    def generate_payment_reference(self):
        """Generate a unique payment reference"""
        return f"PAY-{timezone.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:8].upper()}"

    def verify_payment(self, verified_by_user):
        """Verify the payment and update related invoice"""
        self.status = 'COMPLETED'
        self.verified_by = verified_by_user
        self.verified_at = timezone.now()
        self.save()

        # Update the invoice if linked
        if self.invoice:
            self.invoice.paid_amount += self.amount
            if self.invoice.paid_amount >= self.invoice.total_amount:
                self.invoice.mark_as_paid(self.invoice.paid_amount, self.payment_date)
            else:
                self.invoice.save(update_fields=['paid_amount'])


class AutomatedPaymentSettings(models.Model):
    """
    Configuration for automated payment verification per school
    """
    school = models.OneToOneField(School, on_delete=models.CASCADE, related_name='auto_payment_settings')

    # Auto-verification settings
    auto_verification_enabled = models.BooleanField(default=True)
    max_auto_verification_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('50000.00'),
        help_text="Maximum amount for automatic verification"
    )

    # Allowed payment methods for auto-verification
    allowed_auto_verification_methods = models.JSONField(
        default=list,
        help_text="List of payment methods allowed for auto-verification"
    )

    # Fraud detection settings
    fraud_detection_enabled = models.BooleanField(default=True)
    fraud_threshold = models.IntegerField(
        default=70,
        help_text="Fraud score threshold (0-100) above which payments are flagged"
    )

    # Time limits
    max_payment_age_days = models.PositiveIntegerField(
        default=7,
        help_text="Maximum age of payment in days for auto-verification"
    )

    # Notification settings
    send_auto_verification_notifications = models.BooleanField(default=True)
    notification_email = models.EmailField(blank=True)

    # Receipt settings
    auto_generate_receipts = models.BooleanField(default=True)
    receipt_template = models.CharField(max_length=50, default='default')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Auto Payment Settings - {self.school.name}"

    def save(self, *args, **kwargs):
        # Set default allowed methods if empty
        if not self.allowed_auto_verification_methods:
            self.allowed_auto_verification_methods = ['MOBILE_MONEY', 'BANK_TRANSFER']
        super().save(*args, **kwargs)


class PaymentVerificationRule(models.Model):
    """
    Custom verification rules for different scenarios
    """
    RULE_TYPE_CHOICES = [
        ('AMOUNT_LIMIT', 'Amount Limit'),
        ('PAYMENT_METHOD', 'Payment Method'),
        ('TIME_WINDOW', 'Time Window'),
        ('FREQUENCY_LIMIT', 'Frequency Limit'),
        ('CUSTOM', 'Custom Rule'),
    ]

    name = models.CharField(max_length=100)
    rule_type = models.CharField(max_length=20, choices=RULE_TYPE_CHOICES)
    description = models.TextField()

    # Rule configuration
    conditions = models.JSONField(help_text="Rule conditions in JSON format")
    actions = models.JSONField(help_text="Actions to take when rule is triggered")

    # Scope
    applies_to_all_schools = models.BooleanField(default=False)
    schools = models.ManyToManyField(School, blank=True)

    # Status
    is_active = models.BooleanField(default=True)
    priority = models.PositiveIntegerField(default=0, help_text="Higher numbers = higher priority")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-priority', 'name']

    def __str__(self):
        return f"{self.name} ({self.get_rule_type_display()})"


class PaymentAuditLog(models.Model):
    """
    Audit log for all payment verification activities
    """
    ACTION_CHOICES = [
        ('AUTO_VERIFIED', 'Automatically Verified'),
        ('MANUAL_VERIFIED', 'Manually Verified'),
        ('FLAGGED_FOR_REVIEW', 'Flagged for Review'),
        ('VERIFICATION_FAILED', 'Verification Failed'),
        ('VERIFICATION_ERROR', 'Verification Error'),
        ('FRAUD_DETECTED', 'Fraud Detected'),
        ('RULE_TRIGGERED', 'Rule Triggered'),
    ]

    payment = models.ForeignKey(SchoolPayment, on_delete=models.CASCADE, related_name='audit_logs')
    action = models.CharField(max_length=30, choices=ACTION_CHOICES)
    reason = models.TextField()

    # Context
    automated = models.BooleanField(default=False)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)

    # Additional details
    details = models.JSONField(default=dict, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_action_display()} - {self.payment.payment_reference}"


class PaymentReceipt(models.Model):
    """
    Generated payment receipts
    """
    payment = models.OneToOneField(SchoolPayment, on_delete=models.CASCADE, related_name='receipt')
    receipt_number = models.CharField(max_length=50, unique=True)

    # Receipt content
    receipt_data = models.JSONField(help_text="Complete receipt data in JSON format")
    pdf_file = models.FileField(upload_to='receipts/', null=True, blank=True)

    # Generation details
    template_used = models.CharField(max_length=50, default='default')
    generated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    generated_at = models.DateTimeField(auto_now_add=True)

    # Email tracking
    emailed_to = models.EmailField(blank=True)
    emailed_at = models.DateTimeField(null=True, blank=True)
    email_status = models.CharField(max_length=20, default='PENDING')

    def __str__(self):
        return f"Receipt {self.receipt_number} - {self.payment.payment_reference}"

    def save(self, *args, **kwargs):
        if not self.receipt_number:
            self.receipt_number = self.generate_receipt_number()
        super().save(*args, **kwargs)

    def generate_receipt_number(self):
        """Generate a unique receipt number"""
        year = timezone.now().year
        month = timezone.now().month

        # Get the last receipt for this month
        last_receipt = PaymentReceipt.objects.filter(
            receipt_number__startswith=f"RCP-{year}{month:02d}"
        ).order_by('-receipt_number').first()

        if last_receipt:
            # Extract the sequence number and increment
            last_seq = int(last_receipt.receipt_number.split('-')[-1])
            new_seq = last_seq + 1
        else:
            new_seq = 1

        return f"RCP-{year}{month:02d}-{new_seq:04d}"


class PaymentNotification(models.Model):
    """
    Track payment notifications sent
    """
    NOTIFICATION_TYPE_CHOICES = [
        ('PAYMENT_CONFIRMED', 'Payment Confirmed'),
        ('RECEIPT_GENERATED', 'Receipt Generated'),
        ('LICENSE_ACTIVATED', 'License Activated'),
        ('FRAUD_ALERT', 'Fraud Alert'),
        ('VERIFICATION_FAILED', 'Verification Failed'),
    ]

    CHANNEL_CHOICES = [
        ('EMAIL', 'Email'),
        ('SMS', 'SMS'),
        ('PUSH', 'Push Notification'),
        ('WEBHOOK', 'Webhook'),
    ]

    payment = models.ForeignKey(SchoolPayment, on_delete=models.CASCADE, related_name='notifications')
    notification_type = models.CharField(max_length=30, choices=NOTIFICATION_TYPE_CHOICES)
    channel = models.CharField(max_length=20, choices=CHANNEL_CHOICES)

    # Recipient details
    recipient = models.CharField(max_length=255)  # Email, phone, or webhook URL
    subject = models.CharField(max_length=255, blank=True)
    message = models.TextField()

    # Status tracking
    status = models.CharField(max_length=20, default='PENDING')
    sent_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True)

    # Metadata
    metadata = models.JSONField(default=dict, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.get_notification_type_display()} - {self.recipient}"
