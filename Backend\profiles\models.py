from django.db import models

# UserProfile model temporarily removed to break migration cycle.

# Create your models here.

class UserProfile(models.Model):
    user = models.OneToOneField(
        'core.CustomUser',
        on_delete=models.CASCADE,
        related_name='profile'
    )
    # school = models.ForeignKey(
    #     'schools.School',
    #     on_delete=models.SET_NULL,
    #     null=True,
    #     blank=True,
    #     related_name='school_users'
    # )
    # school_branch = models.ForeignKey(
    #     'schools.SchoolBranch',
    #     on_delete=models.SET_NULL,
    #     null=True,
    #     blank=True,
    #     related_name='branch_users'
    # )

    class Meta:
        app_label = 'profiles'
        verbose_name = 'User Profile'
        verbose_name_plural = 'User Profiles'

    def __str__(self):
        return f"{self.user.get_full_name()}'s Profile"
